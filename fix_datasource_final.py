#!/usr/bin/env python3
"""
最终修复数据源和仪表板问题
"""

import requests
import json
import mysql.connector

def test_mysql_direct():
    """直接测试MySQL连接"""
    print("🔍 直接测试MySQL连接...")
    
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='Linuxtest',
            database='depth_db'
        )
        cursor = connection.cursor()
        
        cursor.execute("SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1")
        result = cursor.fetchone()
        
        if result:
            print(f"   ✅ MySQL连接正常，最新买一量: {result[0]}")
            cursor.close()
            connection.close()
            return True
        else:
            print(f"   ❌ 无数据")
            return False
            
    except Exception as e:
        print(f"   ❌ MySQL连接失败: {e}")
        return False

def delete_all_datasources():
    """删除所有现有数据源"""
    print("🗑️ 删除现有数据源...")
    
    session = requests.Session()
    session.auth = ("admin", "admin")
    
    try:
        response = session.get("http://localhost:3000/api/datasources")
        if response.status_code == 200:
            datasources = response.json()
            
            for ds in datasources:
                if ds['name'].startswith('DepthDB'):
                    delete_response = session.delete(f"http://localhost:3000/api/datasources/{ds['id']}")
                    if delete_response.status_code == 200:
                        print(f"   ✅ 删除数据源: {ds['name']}")
                    else:
                        print(f"   ❌ 删除失败: {ds['name']}")
        
    except Exception as e:
        print(f"   ❌ 删除数据源失败: {e}")

def create_fresh_datasource():
    """创建全新的数据源"""
    print("📊 创建全新数据源...")
    
    session = requests.Session()
    session.auth = ("admin", "admin")
    
    datasource_config = {
        "name": "FreshDepthDB",
        "type": "mysql",
        "url": "localhost:3306",
        "access": "proxy",
        "database": "depth_db",
        "user": "root",
        "password": "Linuxtest",
        "basicAuth": False,
        "isDefault": False,
        "jsonData": {
            "maxOpenConns": 10,
            "maxIdleConns": 2,
            "connMaxLifetime": 14400
        }
    }
    
    try:
        response = session.post(
            "http://localhost:3000/api/datasources",
            json=datasource_config,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            datasource_uid = result['datasource']['uid']
            datasource_id = result['datasource']['id']
            print(f"   ✅ 数据源创建成功")
            print(f"   📊 UID: {datasource_uid}")
            print(f"   📊 ID: {datasource_id}")
            
            # 测试数据源连接
            test_response = session.get(f"http://localhost:3000/api/datasources/{datasource_id}/health")
            if test_response.status_code == 200:
                health_result = test_response.json()
                print(f"   ✅ 数据源健康检查: {health_result}")
            else:
                print(f"   ⚠️ 健康检查失败: {test_response.status_code}")
            
            return datasource_uid
        else:
            print(f"   ❌ 创建数据源失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 创建数据源异常: {e}")
        return None

def create_simple_test_dashboard(datasource_uid):
    """创建最简单的测试仪表板"""
    print("🎨 创建最简单测试仪表板...")
    
    session = requests.Session()
    session.auth = ("admin", "admin")
    
    dashboard_config = {
        "dashboard": {
            "id": None,
            "title": "🔧 最简单测试",
            "tags": ["test"],
            "timezone": "browser",
            "panels": [
                {
                    "id": 1,
                    "title": "📊 测试查询",
                    "type": "stat",
                    "gridPos": {"h": 6, "w": 24, "x": 0, "y": 0},
                    "targets": [
                        {
                            "datasource": {
                                "type": "mysql",
                                "uid": datasource_uid
                            },
                            "format": "table",
                            "rawSql": "SELECT 'Hello World' as test",
                            "refId": "A"
                        }
                    ],
                    "options": {
                        "reduceOptions": {
                            "values": False,
                            "calcs": ["lastNotNull"]
                        },
                        "textMode": "auto"
                    }
                }
            ],
            "time": {"from": "now-1h", "to": "now"},
            "refresh": "5s",
            "schemaVersion": 30,
            "version": 1
        },
        "overwrite": True
    }
    
    try:
        response = session.post(
            "http://localhost:3000/api/dashboards/db",
            json=dashboard_config,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            dashboard_url = f"http://localhost:3000{result['url']}"
            print(f"   ✅ 测试仪表板创建成功")
            print(f"   🌐 地址: {dashboard_url}")
            return dashboard_url
        else:
            print(f"   ❌ 创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return None

def create_working_dashboard(datasource_uid):
    """创建能工作的深度仪表板"""
    print("🎨 创建工作的深度仪表板...")
    
    session = requests.Session()
    session.auth = ("admin", "admin")
    
    dashboard_config = {
        "dashboard": {
            "id": None,
            "title": "✅ 工作的深度对比",
            "tags": ["depth", "working"],
            "timezone": "browser",
            "panels": [
                {
                    "id": 1,
                    "title": "📊 BTCUSDT最新买一量",
                    "type": "stat",
                    "gridPos": {"h": 6, "w": 8, "x": 0, "y": 0},
                    "targets": [
                        {
                            "datasource": {
                                "type": "mysql",
                                "uid": datasource_uid
                            },
                            "format": "table",
                            "rawSql": "SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1",
                            "refId": "A"
                        }
                    ],
                    "options": {
                        "reduceOptions": {
                            "values": False,
                            "calcs": ["lastNotNull"]
                        },
                        "textMode": "auto",
                        "colorMode": "background"
                    }
                },
                {
                    "id": 2,
                    "title": "📊 BTCUSDT最新卖一量",
                    "type": "stat",
                    "gridPos": {"h": 6, "w": 8, "x": 8, "y": 0},
                    "targets": [
                        {
                            "datasource": {
                                "type": "mysql",
                                "uid": datasource_uid
                            },
                            "format": "table",
                            "rawSql": "SELECT ask_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1",
                            "refId": "A"
                        }
                    ],
                    "options": {
                        "reduceOptions": {
                            "values": False,
                            "calcs": ["lastNotNull"]
                        },
                        "textMode": "auto",
                        "colorMode": "background"
                    }
                },
                {
                    "id": 3,
                    "title": "📊 数据更新时间",
                    "type": "stat",
                    "gridPos": {"h": 6, "w": 8, "x": 16, "y": 0},
                    "targets": [
                        {
                            "datasource": {
                                "type": "mysql",
                                "uid": datasource_uid
                            },
                            "format": "table",
                            "rawSql": "SELECT FROM_UNIXTIME(MAX(timestamp)/1000) FROM bitda_depth WHERE symbol = 'BTCUSDT'",
                            "refId": "A"
                        }
                    ],
                    "options": {
                        "reduceOptions": {
                            "values": False,
                            "calcs": ["lastNotNull"]
                        },
                        "textMode": "auto",
                        "colorMode": "background"
                    }
                }
            ],
            "time": {"from": "now-1h", "to": "now"},
            "refresh": "5s",
            "schemaVersion": 30,
            "version": 1
        },
        "overwrite": True
    }
    
    try:
        response = session.post(
            "http://localhost:3000/api/dashboards/db",
            json=dashboard_config,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            dashboard_url = f"http://localhost:3000{result['url']}"
            print(f"   ✅ 工作仪表板创建成功")
            print(f"   🌐 地址: {dashboard_url}")
            return dashboard_url
        else:
            print(f"   ❌ 创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return None

def main():
    """主函数"""
    print("🔧 最终修复数据源和仪表板")
    print("=" * 50)
    
    # 1. 测试MySQL
    if not test_mysql_direct():
        print("❌ MySQL连接失败，无法继续")
        return
    
    # 2. 删除现有数据源
    delete_all_datasources()
    
    # 3. 创建新数据源
    datasource_uid = create_fresh_datasource()
    if not datasource_uid:
        print("❌ 数据源创建失败")
        return
    
    # 4. 创建测试仪表板
    test_url = create_simple_test_dashboard(datasource_uid)
    if test_url:
        print(f"\n🧪 测试仪表板: {test_url}")
        
        # 5. 创建工作仪表板
        work_url = create_working_dashboard(datasource_uid)
        if work_url:
            print(f"✅ 工作仪表板: {work_url}")
            
            # 打开浏览器
            try:
                import webbrowser
                webbrowser.open(work_url)
                print(f"🌐 已自动打开浏览器")
            except:
                pass
            
            print(f"\n🎉 修复完成！")
            print(f"📊 如果仍显示'No data'，请检查Grafana日志")
        else:
            print(f"❌ 工作仪表板创建失败")
    else:
        print(f"❌ 测试仪表板创建失败")

if __name__ == "__main__":
    main()
