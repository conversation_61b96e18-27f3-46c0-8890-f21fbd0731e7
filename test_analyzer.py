#!/usr/bin/env python3
"""
数据分析器测试脚本
测试各个分析器模块是否正常工作
"""

import asyncio
import sys
from datetime import datetime
from analyzer import (
    LatencyAnalyzer, KlineAnalyzer, DepthAnalyzer, 
    PriceAnalyzer, FundingAnalyzer
)
from visualizer.dashboard import Dashboard
from utils.logging import setup_logger

logger = setup_logger(__name__)

class AnalyzerTester:
    """分析器测试类"""
    
    def __init__(self):
        self.dashboard = Dashboard()
        self.test_results = {}
        
    async def test_all_analyzers(self):
        """测试所有分析器"""
        print("🧪 开始测试数据分析器...")
        print("="*60)
        
        # 测试各个分析器
        tests = [
            ("延时分析器", self.test_latency_analyzer),
            ("K线分析器", self.test_kline_analyzer),
            ("深度分析器", self.test_depth_analyzer),
            ("价格分析器", self.test_price_analyzer),
            ("资金费率分析器", self.test_funding_analyzer),
            ("仪表板", self.test_dashboard)
        ]
        
        for test_name, test_func in tests:
            print(f"\n📋 测试 {test_name}...")
            try:
                result = await test_func()
                if result:
                    print(f"✅ {test_name} 测试通过")
                    self.test_results[test_name] = "PASS"
                else:
                    print(f"❌ {test_name} 测试失败")
                    self.test_results[test_name] = "FAIL"
            except Exception as e:
                print(f"❌ {test_name} 测试异常: {e}")
                self.test_results[test_name] = f"ERROR: {e}"
        
        # 打印测试总结
        self.print_test_summary()
    
    async def test_latency_analyzer(self):
        """测试延时分析器"""
        try:
            analyzer = LatencyAnalyzer()
            result = await analyzer.analyze_price_latency(hours=1)
            
            # 检查结果结构
            required_keys = ['total_matches', 'avg_latency_ms', 'analysis_time']
            eth_data = result.get('results', {}).get('ETHUSDT', {})
            
            for key in required_keys:
                if key not in eth_data:
                    print(f"  ⚠️  缺少字段: {key}")
                    return False
            
            print(f"  📊 匹配次数: {eth_data.get('total_matches', 0)}")
            print(f"  ⏱️  平均延时: {eth_data.get('avg_latency_ms', 0):.2f}ms")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 延时分析器错误: {e}")
            return False
    
    async def test_kline_analyzer(self):
        """测试K线分析器"""
        try:
            analyzer = KlineAnalyzer()
            result = await analyzer.analyze_consecutive_identical_klines(hours=1)
            
            # 检查结果结构
            if 'results' not in result:
                print("  ⚠️  缺少results字段")
                return False
            
            for symbol in ['BTCUSDT', 'ETHUSDT']:
                symbol_data = result['results'].get(symbol, {})
                sequences = symbol_data.get('total_sequences', 0)
                rate = symbol_data.get('identical_rate_pct', 0)
                
                print(f"  📈 {symbol}: {sequences}个序列, {rate:.2f}%相同率")
            
            return True
            
        except Exception as e:
            print(f"  ❌ K线分析器错误: {e}")
            return False
    
    async def test_depth_analyzer(self):
        """测试深度分析器"""
        try:
            analyzer = DepthAnalyzer()
            result = await analyzer.analyze_depth_comparison(hours=1)
            
            # 检查结果结构
            if 'results' not in result:
                print("  ⚠️  缺少results字段")
                return False
            
            for symbol in ['BTCUSDT', 'ETHUSDT']:
                symbol_data = result['results'].get(symbol, {})
                comparisons = symbol_data.get('total_comparisons', 0)
                ratio = symbol_data.get('avg_depth_ratio', 0)
                
                print(f"  📊 {symbol}: {comparisons}次对比, 平均比值{ratio:.4f}")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 深度分析器错误: {e}")
            return False
    
    async def test_price_analyzer(self):
        """测试价格分析器"""
        try:
            analyzer = PriceAnalyzer()
            result = await analyzer.analyze_mark_price_difference(hours=1)
            
            # 检查结果结构
            if 'results' not in result:
                print("  ⚠️  缺少results字段")
                return False
            
            for symbol in ['BTCUSDT', 'ETHUSDT']:
                symbol_data = result['results'].get(symbol, {})
                records = symbol_data.get('total_records', 0)
                avg_diff = symbol_data.get('avg_rel_diff_pct', 0)
                
                print(f"  💰 {symbol}: {records}条记录, 平均差值{avg_diff:.4f}%")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 价格分析器错误: {e}")
            return False
    
    async def test_funding_analyzer(self):
        """测试资金费率分析器"""
        try:
            analyzer = FundingAnalyzer()
            result = await analyzer.analyze_funding_rates(days=1)
            
            # 检查结果结构
            if 'results' not in result:
                print("  ⚠️  缺少results字段")
                return False
            
            for symbol in ['BTCUSDT', 'ETHUSDT']:
                symbol_data = result['results'].get(symbol, {})
                current_rate = symbol_data.get('current_funding_rate', 0) * 100
                records = symbol_data.get('total_records', 0)
                
                print(f"  📋 {symbol}: {records}条记录, 当前费率{current_rate:.4f}%")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 资金费率分析器错误: {e}")
            return False
    
    async def test_dashboard(self):
        """测试仪表板"""
        try:
            # 测试综合报告生成
            report = await self.dashboard.generate_comprehensive_report(hours=1)
            
            # 检查报告结构
            required_sections = [
                'latency_analysis', 'kline_analysis', 'depth_analysis',
                'price_analysis', 'funding_analysis', 'executive_summary'
            ]
            
            for section in required_sections:
                if section not in report:
                    print(f"  ⚠️  缺少报告部分: {section}")
                    return False
            
            # 检查执行摘要
            summary = report.get('executive_summary', {})
            status = summary.get('overall_status', 'unknown')
            findings = len(summary.get('key_findings', []))
            alerts = len(summary.get('alerts', []))
            
            print(f"  🎯 整体状态: {status}")
            print(f"  🔍 关键发现: {findings}项")
            print(f"  ⚠️  警告信息: {alerts}项")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 仪表板错误: {e}")
            return False
    
    def print_test_summary(self):
        """打印测试总结"""
        print("\n" + "="*60)
        print("📊 测试总结")
        print("="*60)
        
        passed = 0
        failed = 0
        errors = 0
        
        for test_name, result in self.test_results.items():
            if result == "PASS":
                print(f"✅ {test_name}: 通过")
                passed += 1
            elif result == "FAIL":
                print(f"❌ {test_name}: 失败")
                failed += 1
            else:
                print(f"⚠️  {test_name}: {result}")
                errors += 1
        
        total = len(self.test_results)
        print(f"\n📈 测试统计:")
        print(f"  总计: {total}")
        print(f"  通过: {passed}")
        print(f"  失败: {failed}")
        print(f"  错误: {errors}")
        
        if passed == total:
            print("\n🎉 所有测试通过！分析器系统运行正常。")
            return True
        else:
            print(f"\n⚠️  有{failed + errors}个测试未通过，请检查系统配置。")
            return False

async def test_database_connection():
    """测试数据库连接"""
    print("🔌 测试数据库连接...")
    
    try:
        from utils.db import db_manager
        
        # 测试基本查询
        result = db_manager.execute_query(
            "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'depth_db'",
            fetch=True
        )
        
        if result:
            table_count = result[0][0]
            print(f"✅ 数据库连接成功，找到{table_count}个表")
            return True
        else:
            print("❌ 数据库查询失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

async def main():
    """主函数"""
    print("🚀 数据分析器系统测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # 测试数据库连接
    db_ok = await test_database_connection()
    if not db_ok:
        print("\n❌ 数据库连接失败，无法继续测试")
        sys.exit(1)
    
    # 测试分析器
    tester = AnalyzerTester()
    await tester.test_all_analyzers()
    
    print("\n🏁 测试完成")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  测试被中断")
    except Exception as e:
        logger.error(f"测试失败: {e}")
        print(f"❌ 测试失败: {e}")
