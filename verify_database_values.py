#!/usr/bin/env python3
"""
验证数据库取值是否正确
专门检查从数据库获取的原始数据是否准确
"""

import mysql.connector
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def verify_database_values():
    """验证数据库取值是否正确"""
    print("🔍 验证数据库取值正确性")
    print("=" * 50)
    print("🎯 验证目标:")
    print("   1. 确认Bitda最新数据的各档位数量")
    print("   2. 确认Binance最新数据的各档位数量") 
    print("   3. 验证数据获取逻辑是否正确")
    print("   4. 检查数据库字段映射是否准确")
    print()
    
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'Linuxtest',
        'database': 'depth_db'
    }
    
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        # 1. 检查Bitda表结构
        print("📋 检查Bitda表结构...")
        cursor.execute("DESCRIBE bitda_depth")
        bitda_columns = cursor.fetchall()
        print("   Bitda表字段:")
        for col in bitda_columns:
            if 'qty' in col[0] or 'price' in col[0]:
                print(f"      {col[0]}: {col[1]}")
        
        # 2. 检查Binance表结构
        print("\n📋 检查Binance表结构...")
        cursor.execute("DESCRIBE binance_depth_5")
        binance_columns = cursor.fetchall()
        print("   Binance表字段:")
        for col in binance_columns:
            if 'qty' in col[0] or 'price' in col[0]:
                print(f"      {col[0]}: {col[1]}")
        
        # 3. 获取Bitda最新数据并详细显示
        print("\n📊 获取Bitda最新BTCUSDT数据...")
        cursor.execute("""
            SELECT timestamp, symbol,
                   bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
                   bid_price_2, ask_price_2, bid_qty_2, ask_qty_2,
                   bid_price_3, ask_price_3, bid_qty_3, ask_qty_3,
                   bid_price_4, ask_price_4, bid_qty_4, ask_qty_4,
                   bid_price_5, ask_price_5, bid_qty_5, ask_qty_5
            FROM bitda_depth 
            WHERE symbol = 'BTCUSDT' 
            AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
            ORDER BY timestamp DESC 
            LIMIT 1
        """)
        
        bitda_result = cursor.fetchone()
        if bitda_result:
            bitda_time = datetime.fromtimestamp(bitda_result[0] / 1000)
            print(f"   ✅ 时间: {bitda_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
            print(f"   📊 原始数据:")
            print(f"      买一: 价格={bitda_result[2]}, 数量={bitda_result[4]}")
            print(f"      卖一: 价格={bitda_result[3]}, 数量={bitda_result[5]}")
            print(f"      买二: 价格={bitda_result[6]}, 数量={bitda_result[8]}")
            print(f"      卖二: 价格={bitda_result[7]}, 数量={bitda_result[9]}")
            print(f"      买三: 价格={bitda_result[10]}, 数量={bitda_result[12]}")
            print(f"      卖三: 价格={bitda_result[11]}, 数量={bitda_result[13]}")
            print(f"      买四: 价格={bitda_result[14]}, 数量={bitda_result[16]}")
            print(f"      卖四: 价格={bitda_result[15]}, 数量={bitda_result[17]}")
            print(f"      买五: 价格={bitda_result[18]}, 数量={bitda_result[20]}")
            print(f"      卖五: 价格={bitda_result[19]}, 数量={bitda_result[21]}")
            
            bitda_timestamp = bitda_result[0]
        else:
            print("   ❌ 无Bitda数据")
            return False
        
        # 4. 获取时间最接近的Binance数据
        print(f"\n📊 获取与Bitda时间最接近的Binance数据...")
        cursor.execute("""
            SELECT event_time, symbol,
                   bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
                   bid_price_2, ask_price_2, bid_qty_2, ask_qty_2,
                   bid_price_3, ask_price_3, bid_qty_3, ask_qty_3,
                   bid_price_4, ask_price_4, bid_qty_4, ask_qty_4,
                   bid_price_5, ask_price_5, bid_qty_5, ask_qty_5
            FROM binance_depth_5 
            WHERE symbol = 'BTCUSDT' 
            AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
            ORDER BY ABS(event_time - %s) ASC
            LIMIT 1
        """, (bitda_timestamp,))
        
        binance_result = cursor.fetchone()
        if binance_result:
            binance_time = datetime.fromtimestamp(binance_result[0] / 1000)
            time_diff = abs(bitda_timestamp - binance_result[0])
            print(f"   ✅ 时间: {binance_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
            print(f"   ⏰ 时间差: {time_diff}ms")
            print(f"   📊 原始数据:")
            print(f"      买一: 价格={binance_result[2]}, 数量={binance_result[4]}")
            print(f"      卖一: 价格={binance_result[3]}, 数量={binance_result[5]}")
            print(f"      买二: 价格={binance_result[6]}, 数量={binance_result[8]}")
            print(f"      卖二: 价格={binance_result[7]}, 数量={binance_result[9]}")
            print(f"      买三: 价格={binance_result[10]}, 数量={binance_result[12]}")
            print(f"      卖三: 价格={binance_result[11]}, 数量={binance_result[13]}")
            print(f"      买四: 价格={binance_result[14]}, 数量={binance_result[16]}")
            print(f"      卖四: 价格={binance_result[15]}, 数量={binance_result[17]}")
            print(f"      买五: 价格={binance_result[18]}, 数量={binance_result[20]}")
            print(f"      卖五: 价格={binance_result[19]}, 数量={binance_result[21]}")
        else:
            print("   ❌ 无Binance数据")
            return False
        
        # 5. 验证数据取值逻辑
        print(f"\n🧮 验证数据取值逻辑:")
        
        # 提取数值并转换
        bitda_bid1 = float(bitda_result[4]) if bitda_result[4] else 0
        bitda_ask1 = float(bitda_result[5]) if bitda_result[5] else 0
        bitda_bid2 = float(bitda_result[8]) if bitda_result[8] else 0
        bitda_ask2 = float(bitda_result[9]) if bitda_result[9] else 0
        bitda_bid3 = float(bitda_result[12]) if bitda_result[12] else 0
        bitda_ask3 = float(bitda_result[13]) if bitda_result[13] else 0
        bitda_bid4 = float(bitda_result[16]) if bitda_result[16] else 0
        bitda_ask4 = float(bitda_result[17]) if bitda_result[17] else 0
        bitda_bid5 = float(bitda_result[20]) if bitda_result[20] else 0
        bitda_ask5 = float(bitda_result[21]) if bitda_result[21] else 0
        
        binance_bid1 = float(binance_result[4]) if binance_result[4] else 0
        binance_ask1 = float(binance_result[5]) if binance_result[5] else 0
        binance_bid2 = float(binance_result[8]) if binance_result[8] else 0
        binance_ask2 = float(binance_result[9]) if binance_result[9] else 0
        binance_bid3 = float(binance_result[12]) if binance_result[12] else 0
        binance_ask3 = float(binance_result[13]) if binance_result[13] else 0
        binance_bid4 = float(binance_result[16]) if binance_result[16] else 0
        binance_ask4 = float(binance_result[17]) if binance_result[17] else 0
        binance_bid5 = float(binance_result[20]) if binance_result[20] else 0
        binance_ask5 = float(binance_result[21]) if binance_result[21] else 0
        
        print(f"   📊 Bitda数值:")
        print(f"      买一量: {bitda_bid1:.2f}")
        print(f"      卖一量: {bitda_ask1:.2f}")
        print(f"      买二量: {bitda_bid2:.2f}")
        print(f"      卖二量: {bitda_ask2:.2f}")
        print(f"      买三量: {bitda_bid3:.2f}")
        print(f"      卖三量: {bitda_ask3:.2f}")
        print(f"      买四量: {bitda_bid4:.2f}")
        print(f"      卖四量: {bitda_ask4:.2f}")
        print(f"      买五量: {bitda_bid5:.2f}")
        print(f"      卖五量: {bitda_ask5:.2f}")
        
        print(f"   📊 Binance数值:")
        print(f"      买一量: {binance_bid1:.2f}")
        print(f"      卖一量: {binance_ask1:.2f}")
        print(f"      买二量: {binance_bid2:.2f}")
        print(f"      卖二量: {binance_ask2:.2f}")
        print(f"      买三量: {binance_bid3:.2f}")
        print(f"      卖三量: {binance_ask3:.2f}")
        print(f"      买四量: {binance_bid4:.2f}")
        print(f"      卖四量: {binance_ask4:.2f}")
        print(f"      买五量: {binance_bid5:.2f}")
        print(f"      卖五量: {binance_ask5:.2f}")
        
        # 6. 计算仪表板显示的各项指标
        print(f"\n📈 计算仪表板显示指标:")
        
        # 买一量
        print(f"   买一量: Bitda {bitda_bid1:.2f}, Binance {binance_bid1:.2f}")
        
        # 卖一量  
        print(f"   卖一量: Bitda {bitda_ask1:.2f}, Binance {binance_ask1:.2f}")
        
        # 买一量卖一量
        bitda_bid_ask1 = bitda_bid1 + bitda_ask1
        binance_bid_ask1 = binance_bid1 + binance_ask1
        print(f"   买一量卖一量: Bitda {bitda_bid_ask1:.2f}, Binance {binance_bid_ask1:.2f}")
        
        # 买卖前两档量
        bitda_bid_ask2 = bitda_bid1 + bitda_bid2 + bitda_ask1 + bitda_ask2
        binance_bid_ask2 = binance_bid1 + binance_bid2 + binance_ask1 + binance_ask2
        print(f"   买卖前两档量: Bitda {bitda_bid_ask2:.2f}, Binance {binance_bid_ask2:.2f}")
        
        # 买卖前五档量
        bitda_bid_ask5 = bitda_bid1 + bitda_bid2 + bitda_bid3 + bitda_bid4 + bitda_bid5 + bitda_ask1 + bitda_ask2 + bitda_ask3 + bitda_ask4 + bitda_ask5
        binance_bid_ask5 = binance_bid1 + binance_bid2 + binance_bid3 + binance_bid4 + binance_bid5 + binance_ask1 + binance_ask2 + binance_ask3 + binance_ask4 + binance_ask5
        print(f"   买卖前五档量: Bitda {bitda_bid_ask5:.2f}, Binance {binance_bid_ask5:.2f}")
        
        # 7. 对比目标数据
        print(f"\n✅ 对比目标数据:")
        print("=" * 50)
        
        target_data = {
            '买一量': {'bitda': 9.77, 'binance': 4.31},
            '卖一量': {'bitda': 9.86, 'binance': 1.55},
            '买一量卖一量': {'bitda': 19.63, 'binance': 5.86},
            '买卖前两档量': {'bitda': 35.92, 'binance': 5.98},
            '买卖前五档量': {'bitda': 81.76, 'binance': 6.05}
        }
        
        actual_data = {
            '买一量': {'bitda': bitda_bid1, 'binance': binance_bid1},
            '卖一量': {'bitda': bitda_ask1, 'binance': binance_ask1},
            '买一量卖一量': {'bitda': bitda_bid_ask1, 'binance': binance_bid_ask1},
            '买卖前两档量': {'bitda': bitda_bid_ask2, 'binance': binance_bid_ask2},
            '买卖前五档量': {'bitda': bitda_bid_ask5, 'binance': binance_bid_ask5}
        }
        
        all_match = True
        
        for item in target_data:
            target = target_data[item]
            actual = actual_data[item]
            
            print(f"\n📊 {item}:")
            print(f"   目标: Bitda {target['bitda']:.2f}, Binance {target['binance']:.2f}")
            print(f"   实际: Bitda {actual['bitda']:.2f}, Binance {actual['binance']:.2f}")
            
            # 检查是否匹配
            bitda_match = abs(actual['bitda'] - target['bitda']) < 0.01
            binance_match = abs(actual['binance'] - target['binance']) < 0.01
            
            if bitda_match and binance_match:
                print(f"   ✅ 数据库取值正确")
            else:
                print(f"   ❌ 数据库取值不匹配")
                if not bitda_match:
                    print(f"      Bitda差异: {actual['bitda'] - target['bitda']:.4f}")
                if not binance_match:
                    print(f"      Binance差异: {actual['binance'] - target['binance']:.4f}")
                all_match = False
        
        cursor.close()
        connection.close()
        
        print(f"\n🎯 数据库取值验证结论:")
        if all_match:
            print("✅ 数据库取值完全正确！")
            print("   - 字段映射正确")
            print("   - 数据获取逻辑正确") 
            print("   - 数值计算准确")
            print(f"   - 时间差: {time_diff}ms")
        else:
            print("❌ 数据库取值存在差异")
            print("💡 可能原因:")
            print("   1. 数据获取的时间点不同")
            print("   2. 字段映射有误")
            print("   3. 数据库中的数据已更新")
        
        return all_match
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 数据库取值正确性验证器")
    print("=" * 50)
    print("🎯 专门验证:")
    print("   1. 数据库字段映射是否正确")
    print("   2. 数据获取SQL是否准确")
    print("   3. 原始数据是否与目标一致")
    print("   4. 时间匹配逻辑是否正确")
    print()
    
    success = verify_database_values()
    
    if success:
        print("\n🎉 数据库取值验证通过！")
        print("✅ 可以确认仪表板显示的数据来源正确")
    else:
        print("\n⚠️ 数据库取值需要进一步检查")
        print("🔧 建议检查数据获取逻辑和时间匹配")

if __name__ == "__main__":
    main()
