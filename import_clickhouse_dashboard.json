{"dashboard": {"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "ceojb4i91n8jke"}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [{"options": {"Ask Qty 1": {"text": "卖一量"}, "Bid Qty 1": {"text": "买一量"}, "Bid+Ask Qty 1": {"text": "买一量卖一量"}, "Bid+Ask Qty 1-2": {"text": "买卖前两档量"}, "Bid+Ask Qty 1-5": {"text": "买卖前五档量"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "item"}, "properties": [{"id": "displayName", "value": "项目"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "ratio"}, "properties": [{"id": "displayName", "value": "深度比"}]}]}, "gridPos": {"h": 12, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "12.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "ceojb4i91n8jke"}, "format": "table", "rawSql": "SELECT '卖一量' as item, round((SELECT ask_qty_1 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda, round((SELECT ask_qty FROM crypto.binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance, round((SELECT ask_qty_1 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) / (SELECT ask_qty FROM crypto.binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as ratio UNION ALL SELECT '买一量' as item, round((SELECT bid_qty_1 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda, round((SELECT bid_qty FROM crypto.binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance, round((SELECT bid_qty_1 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) / (SELECT bid_qty FROM crypto.binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as ratio UNION ALL SELECT '买一量卖一量' as item, round((SELECT bid_qty_1 + ask_qty_1 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda, round((SELECT bid_qty + ask_qty FROM crypto.binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance, round((SELECT bid_qty_1 + ask_qty_1 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) / (SELECT bid_qty + ask_qty FROM crypto.binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as ratio UNION ALL SELECT '买卖前两档量' as item, round((SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda, round((SELECT bid_qty + ask_qty FROM crypto.binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance, round((SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) / (SELECT bid_qty + ask_qty FROM crypto.binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as ratio UNION ALL SELECT '买卖前五档量' as item, round((SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda, round((SELECT bid_qty + ask_qty FROM crypto.binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance, round((SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) / (SELECT bid_qty + ask_qty FROM crypto.binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as ratio", "refId": "A"}], "title": "📊 BTCUSDT 深度对比", "type": "table"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "ceojb4i91n8jke"}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [{"options": {"Ask Qty 1": {"text": "卖一量"}, "Bid Qty 1": {"text": "买一量"}, "Bid+Ask Qty 1": {"text": "买一量卖一量"}, "Bid+Ask Qty 1-2": {"text": "买卖前两档量"}, "Bid+Ask Qty 1-5": {"text": "买卖前五档量"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "item"}, "properties": [{"id": "displayName", "value": "项目"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "ratio"}, "properties": [{"id": "displayName", "value": "深度比"}]}]}, "gridPos": {"h": 12, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "12.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "ceojb4i91n8jke"}, "format": "table", "rawSql": "SELECT '卖一量' as item, round((SELECT ask_qty_1 FROM crypto.bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda, round((SELECT ask_qty FROM crypto.binance_bookticker WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance, round((SELECT ask_qty_1 FROM crypto.bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) / (SELECT ask_qty FROM crypto.binance_bookticker WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as ratio UNION ALL SELECT '买一量' as item, round((SELECT bid_qty_1 FROM crypto.bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda, round((SELECT bid_qty FROM crypto.binance_bookticker WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance, round((SELECT bid_qty_1 FROM crypto.bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) / (SELECT bid_qty FROM crypto.binance_bookticker WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as ratio UNION ALL SELECT '买一量卖一量' as item, round((SELECT bid_qty_1 + ask_qty_1 FROM crypto.bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda, round((SELECT bid_qty + ask_qty FROM crypto.binance_bookticker WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance, round((SELECT bid_qty_1 + ask_qty_1 FROM crypto.bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) / (SELECT bid_qty + ask_qty FROM crypto.binance_bookticker WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as ratio UNION ALL SELECT '买卖前两档量' as item, round((SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM crypto.bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda, round((SELECT bid_qty + ask_qty FROM crypto.binance_bookticker WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance, round((SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM crypto.bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) / (SELECT bid_qty + ask_qty FROM crypto.binance_bookticker WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as ratio UNION ALL SELECT '买卖前五档量' as item, round((SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM crypto.bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda, round((SELECT bid_qty + ask_qty FROM crypto.binance_bookticker WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance, round((SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM crypto.bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) / (SELECT bid_qty + ask_qty FROM crypto.binance_bookticker WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as ratio", "refId": "A"}], "title": "📊 ETHUSDT 深度对比", "type": "table"}], "preload": false, "refresh": "1m", "schemaVersion": 41, "tags": ["depth-comparison", "clickhouse", "crypto"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "browser", "title": "ClickHouse深度对比仪表板", "uid": "clickhouse-depth-comparison", "version": 1, "weekStart": "monday"}, "overwrite": true, "inputs": []}