#!/bin/bash

# 数据分析快捷运行脚本
# 用于快速执行各种数据分析任务

echo "🚀 数据分析快捷运行工具"
echo "========================"

# 显示菜单
show_menu() {
    echo ""
    echo "请选择分析类型:"
    echo "1) 延时分析 (ETHUSDT价格匹配延时)"
    echo "2) 深度对比分析 (Bitda vs Binance)"
    echo "3) 标记价格偏差分析"
    echo "4) 资金费率展示"
    echo "5) K线分析 (连续相同检测)"
    echo "6) 综合分析报告 (所有类型)"
    echo "7) 清理测试数据"
    echo "8) 生成Grafana仪表板"
    echo "9) 退出"
    echo ""
    read -p "请输入选项 (1-9): " choice
}

# 延时分析
run_latency_analysis() {
    echo "🚀 执行延时分析..."
    python -c "
from analyzer.latency_analyzer import LatencyAnalyzer
analyzer = LatencyAnalyzer()
result = analyzer.analyze_ethusdt_latency(hours=1)
print('延时分析完成')
print(f'匹配记录数: {result.get(\"match_count\", 0)}')
print(f'平均延时: {result.get(\"avg_latency\", 0):.2f}ms')
print(f'最小延时: {result.get(\"min_latency\", 0)}ms')
print(f'最大延时: {result.get(\"max_latency\", 0)}ms')
"
}

# 深度对比分析
run_depth_analysis() {
    echo "📊 执行深度对比分析..."
    python -c "
from analyzer.depth_analyzer import DepthAnalyzer
analyzer = DepthAnalyzer()
result = analyzer.analyze_depth_comparison(hours=1)
print('深度对比分析完成')
for symbol in ['BTCUSDT', 'ETHUSDT']:
    if symbol in result:
        data = result[symbol]
        print(f'{symbol}:')
        print(f'  买一卖一量比值: {data.get(\"bid_ask_ratio\", 0):.2f}')
        print(f'  买一量比值: {data.get(\"bid_ratio\", 0):.2f}')
        print(f'  卖一量比值: {data.get(\"ask_ratio\", 0):.2f}')
"
}

# 标记价格偏差分析
run_price_analysis() {
    echo "💰 执行标记价格偏差分析..."
    python -c "
from analyzer.price_analyzer import PriceAnalyzer
analyzer = PriceAnalyzer()
result = analyzer.analyze_mark_price_deviation(hours=1)
print('标记价格偏差分析完成')
for symbol in ['BTCUSDT', 'ETHUSDT']:
    if symbol in result:
        data = result[symbol]
        print(f'{symbol}:')
        print(f'  最大偏差: {data.get(\"max_deviation\", 0):.4f}')
        print(f'  最小偏差: {data.get(\"min_deviation\", 0):.4f}')
        print(f'  中位数偏差: {data.get(\"median_deviation\", 0):.4f}')
"
}

# 资金费率展示
run_funding_analysis() {
    echo "📋 执行资金费率分析..."
    python -c "
from analyzer.funding_analyzer import FundingAnalyzer
analyzer = FundingAnalyzer()
result = analyzer.get_latest_funding_rates()
print('资金费率数据:')
for symbol in ['BTCUSDT', 'ETHUSDT']:
    if symbol in result:
        data = result[symbol]
        print(f'{symbol}:')
        print(f'  当前费率: {data.get(\"current_rate\", 0)*100:.5f}%')
        print(f'  下期费率: {data.get(\"next_rate\", 0)*100:.5f}%')
        print(f'  预测费率: {data.get(\"predict_rate\", 0)*100:.5f}%')
"
}

# K线分析
run_kline_analysis() {
    echo "📈 执行K线分析..."
    python -c "
from analyzer.kline_analyzer import KlineAnalyzer
analyzer = KlineAnalyzer()
result = analyzer.detect_consecutive_identical_klines(hours=24)
print('K线分析完成')
for symbol in ['BTCUSDT', 'ETHUSDT']:
    if symbol in result:
        count = result[symbol].get('consecutive_count', 0)
        print(f'{symbol}: 发现 {count} 组连续相同K线')
"
}

# 综合分析
run_comprehensive_analysis() {
    echo "🎯 执行综合分析..."
    python analyzer_main.py --mode single --hours 1
}

# 清理测试数据
clean_test_data() {
    echo "🧹 清理测试数据..."
    read -p "确认要删除测试数据吗? (y/N): " confirm
    if [[ $confirm == [yY] ]]; then
        mysql -u root -pLinuxtest -e "
        DELETE FROM bitda_ticker WHERE id IN (1, 2);
        DELETE FROM depth_matches WHERE id < 100;
        " depth_db 2>/dev/null && echo "✅ 测试数据清理完成" || echo "❌ 清理失败"
    else
        echo "取消清理操作"
    fi
}

# 生成Grafana仪表板
generate_grafana_dashboard() {
    echo "📊 生成Grafana仪表板..."
    python setup_grafana.py
}

# 主循环
while true; do
    show_menu
    
    case $choice in
        1)
            run_latency_analysis
            ;;
        2)
            run_depth_analysis
            ;;
        3)
            run_price_analysis
            ;;
        4)
            run_funding_analysis
            ;;
        5)
            run_kline_analysis
            ;;
        6)
            run_comprehensive_analysis
            ;;
        7)
            clean_test_data
            ;;
        8)
            generate_grafana_dashboard
            ;;
        9)
            echo "👋 退出分析工具"
            exit 0
            ;;
        *)
            echo "❌ 无效选项，请重新选择"
            ;;
    esac
    
    echo ""
    read -p "按回车键继续..."
done
