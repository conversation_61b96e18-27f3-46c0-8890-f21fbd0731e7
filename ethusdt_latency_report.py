#!/usr/bin/env python3
"""
ETHUSDT延时数据详细报告
"""

import os
from datetime import datetime, timedelta

# 设置环境变量
os.environ['DB_HOST'] = 'localhost'
os.environ['DB_USER'] = 'root'
os.environ['DB_PASSWORD'] = 'Linuxtest'
os.environ['DB_NAME'] = 'depth_db'

class ETHUSDTLatencyReporter:
    """ETHUSDT延时数据报告生成器"""
    
    def __init__(self):
        pass
    
    def get_detailed_latency_stats(self):
        """获取详细的延时统计数据"""
        try:
            from utils.db import db_manager
            
            # 获取不同时间段的统计数据
            queries = {
                '1小时': """
                SELECT 
                    COUNT(*) as total_records,
                    MIN(message_latency_ms) as min_latency,
                    MAX(message_latency_ms) as max_latency,
                    AVG(message_latency_ms) as avg_latency,
                    STDDEV(message_latency_ms) as std_latency,
                    MIN(timestamp) as start_time,
                    MAX(timestamp) as end_time,
                    AVG(engine_latency_ms) as avg_engine_latency
                FROM depth_matches 
                WHERE symbol = 'ETHUSDT' 
                AND timestamp >= NOW() - INTERVAL 1 HOUR
                AND message_latency_ms BETWEEN 1 AND 10000
                """,
                '6小时': """
                SELECT 
                    COUNT(*) as total_records,
                    MIN(message_latency_ms) as min_latency,
                    MAX(message_latency_ms) as max_latency,
                    AVG(message_latency_ms) as avg_latency,
                    STDDEV(message_latency_ms) as std_latency,
                    MIN(timestamp) as start_time,
                    MAX(timestamp) as end_time,
                    AVG(engine_latency_ms) as avg_engine_latency
                FROM depth_matches 
                WHERE symbol = 'ETHUSDT' 
                AND timestamp >= NOW() - INTERVAL 6 HOUR
                AND message_latency_ms BETWEEN 1 AND 10000
                """,
                '24小时': """
                SELECT 
                    COUNT(*) as total_records,
                    MIN(message_latency_ms) as min_latency,
                    MAX(message_latency_ms) as max_latency,
                    AVG(message_latency_ms) as avg_latency,
                    STDDEV(message_latency_ms) as std_latency,
                    MIN(timestamp) as start_time,
                    MAX(timestamp) as end_time,
                    AVG(engine_latency_ms) as avg_engine_latency
                FROM depth_matches 
                WHERE symbol = 'ETHUSDT' 
                AND timestamp >= NOW() - INTERVAL 24 HOUR
                AND message_latency_ms BETWEEN 1 AND 10000
                """
            }
            
            results = {}
            for period, query in queries.items():
                result = db_manager.execute_query(query, fetch=True)
                if result and result[0]:
                    row = result[0]
                    results[period] = {
                        'total_records': int(row[0]) if row[0] else 0,
                        'min_latency': int(row[1]) if row[1] else 0,
                        'max_latency': int(row[2]) if row[2] else 0,
                        'avg_latency': round(float(row[3]), 2) if row[3] else 0,
                        'std_latency': round(float(row[4]), 2) if row[4] else 0,
                        'start_time': row[5].strftime('%Y-%m-%d %H:%M:%S') if row[5] else 'Unknown',
                        'end_time': row[6].strftime('%Y-%m-%d %H:%M:%S') if row[6] else 'Unknown',
                        'avg_engine_latency': round(float(row[7]), 2) if row[7] else 0
                    }
                else:
                    results[period] = None
            
            return results
            
        except Exception as e:
            print(f"❌ 获取延时统计失败: {e}")
            return {}
    
    def get_latency_distribution(self):
        """获取延时分布数据"""
        try:
            from utils.db import db_manager
            
            query = """
            SELECT 
                CASE 
                    WHEN message_latency_ms <= 50 THEN '0-50ms'
                    WHEN message_latency_ms <= 100 THEN '51-100ms'
                    WHEN message_latency_ms <= 200 THEN '101-200ms'
                    WHEN message_latency_ms <= 500 THEN '201-500ms'
                    WHEN message_latency_ms <= 1000 THEN '501-1000ms'
                    ELSE '1000ms+'
                END as latency_range,
                COUNT(*) as count,
                ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM depth_matches 
                    WHERE symbol = 'ETHUSDT' 
                    AND timestamp >= NOW() - INTERVAL 24 HOUR
                    AND message_latency_ms BETWEEN 1 AND 10000), 2) as percentage
            FROM depth_matches 
            WHERE symbol = 'ETHUSDT' 
            AND timestamp >= NOW() - INTERVAL 24 HOUR
            AND message_latency_ms BETWEEN 1 AND 10000
            GROUP BY latency_range
            ORDER BY 
                CASE 
                    WHEN latency_range = '0-50ms' THEN 1
                    WHEN latency_range = '51-100ms' THEN 2
                    WHEN latency_range = '101-200ms' THEN 3
                    WHEN latency_range = '201-500ms' THEN 4
                    WHEN latency_range = '501-1000ms' THEN 5
                    ELSE 6
                END
            """
            
            data = db_manager.execute_query(query, fetch=True)
            
            if data:
                return [
                    {
                        'range': row[0],
                        'count': int(row[1]),
                        'percentage': float(row[2])
                    }
                    for row in data
                ]
            else:
                return []
                
        except Exception as e:
            print(f"❌ 获取延时分布失败: {e}")
            return []
    
    def get_recent_samples(self, limit=10):
        """获取最近的延时样本"""
        try:
            from utils.db import db_manager
            
            query = """
            SELECT 
                timestamp,
                message_latency_ms,
                engine_latency_ms,
                depth_ratio,
                binance_bid_price,
                binance_ask_price
            FROM depth_matches 
            WHERE symbol = 'ETHUSDT' 
            AND timestamp >= NOW() - INTERVAL 1 HOUR
            AND message_latency_ms BETWEEN 1 AND 10000
            ORDER BY timestamp DESC
            LIMIT %s
            """
            
            data = db_manager.execute_query(query, (limit,), fetch=True)
            
            if data:
                return [
                    {
                        'timestamp': row[0].strftime('%Y-%m-%d %H:%M:%S') if row[0] else 'Unknown',
                        'message_latency': int(row[1]),
                        'engine_latency': int(row[2]),
                        'depth_ratio': round(float(row[3]), 4),
                        'binance_bid': float(row[4]),
                        'binance_ask': float(row[5])
                    }
                    for row in data
                ]
            else:
                return []
                
        except Exception as e:
            print(f"❌ 获取最近样本失败: {e}")
            return []
    
    def generate_report(self):
        """生成完整的延时报告"""
        print("⚡ 开始生成ETHUSDT延时分析报告...")
        
        # 获取数据
        stats = self.get_detailed_latency_stats()
        distribution = self.get_latency_distribution()
        samples = self.get_recent_samples(10)
        
        # 生成报告
        report = f"""
# ⚡ ETHUSDT延时分析详细报告

**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**数据源**: MySQL数据库 (depth_db)  
**数据表**: depth_matches  
**交易对**: ETHUSDT  
**交易所**: Bitda vs Binance  

---

## 📊 延时统计数据 (不同时间段)

"""
        
        if stats:
            for period, data in stats.items():
                if data:
                    report += f"""
### {period}内统计
- **数据时间范围**: {data['start_time']} ~ {data['end_time']}
- **总记录数**: {data['total_records']:,} 条
- **消息延时**:
  - 平均值: **{data['avg_latency']} ms**
  - 最小值: {data['min_latency']} ms
  - 最大值: {data['max_latency']} ms
  - 标准差: {data['std_latency']} ms
- **引擎延时**: 平均 {data['avg_engine_latency']} ms

"""
                else:
                    report += f"\n### {period}内统计\n⚠️ 暂无数据\n"
        else:
            report += "\n⚠️ 无法获取统计数据\n"
        
        # 延时分布
        report += """
---

## 📈 延时分布分析 (24小时)

| 延时范围 | 记录数量 | 占比 |
|----------|----------|------|
"""
        
        if distribution:
            for item in distribution:
                report += f"| {item['range']} | {item['count']:,} | {item['percentage']}% |\n"
        else:
            report += "| - | - | - |\n"
        
        # 最近样本
        report += """
---

## 🔍 最近延时样本 (最新10条)

| 时间 | 消息延时(ms) | 引擎延时(ms) | 深度比值 | Binance买一价 | Binance卖一价 |
|------|-------------|-------------|----------|---------------|---------------|
"""
        
        if samples:
            for sample in samples:
                report += f"| {sample['timestamp']} | {sample['message_latency']} | {sample['engine_latency']} | {sample['depth_ratio']} | ${sample['binance_bid']:.2f} | ${sample['binance_ask']:.2f} |\n"
        else:
            report += "| - | - | - | - | - | - |\n"
        
        # 数据质量分析
        if stats and stats.get('24小时'):
            data_24h = stats['24小时']
            total_records = data_24h['total_records']
            avg_latency = data_24h['avg_latency']
            max_latency = data_24h['max_latency']
            
            # 计算数据质量指标
            expected_records_per_hour = total_records / 24
            
            report += f"""
---

## 📋 数据质量分析

### 📊 数据完整性
- **24小时总记录**: {total_records:,} 条
- **平均每小时记录**: {expected_records_per_hour:.0f} 条
- **数据覆盖率**: {'✅ 良好' if total_records > 1000 else '⚠️ 偏少'}

### ⚡ 延时性能评估
- **平均延时**: {avg_latency} ms {'✅ 优秀' if avg_latency < 100 else '⚠️ 需关注' if avg_latency < 200 else '❌ 较差'}
- **最大延时**: {max_latency} ms {'✅ 正常' if max_latency < 1000 else '⚠️ 偏高' if max_latency < 5000 else '❌ 异常'}
- **延时稳定性**: {'✅ 稳定' if data_24h['std_latency'] < avg_latency else '⚠️ 波动较大'}

### 🎯 性能建议
"""
            
            if avg_latency < 50:
                report += "- ✅ 延时表现优秀，系统运行良好\n"
            elif avg_latency < 100:
                report += "- ✅ 延时表现良好，可继续监控\n"
            elif avg_latency < 200:
                report += "- ⚠️ 延时偏高，建议优化网络或系统配置\n"
            else:
                report += "- ❌ 延时过高，需要立即优化系统性能\n"
            
            if max_latency > 5000:
                report += "- ❌ 存在异常高延时，需要排查网络或系统问题\n"
            elif max_latency > 1000:
                report += "- ⚠️ 偶有高延时，建议监控网络状况\n"
            else:
                report += "- ✅ 最大延时在合理范围内\n"
        
        report += f"""
---

## 🔗 相关链接

- **Grafana仪表板**: http://localhost:3000/d/4b1b972a-70df-4371-9f55-23b3cd15f1a0/a9bd002
- **数据库查询**: `SELECT * FROM depth_matches WHERE symbol='ETHUSDT' ORDER BY timestamp DESC LIMIT 10;`
- **实时API**: http://localhost:8001/api/real-latency

---

## 📝 说明

- **数据来源**: 真实的Bitda与Binance深度匹配记录
- **延时计算**: 基于WebSocket消息时间戳差值
- **数据过滤**: 已过滤异常值 (1-10000ms范围)
- **更新频率**: 实时数据，每秒多次更新
- **精度**: 毫秒级精度

**报告生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        return report
    
    def save_report(self, report):
        """保存报告到文件"""
        filename = f"ethusdt_latency_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report)
            
            # 同时保存最新版本
            with open("latest_ethusdt_latency_report.md", 'w', encoding='utf-8') as f:
                f.write(report)
            
            print(f"✅ 报告已保存: {filename}")
            print(f"✅ 最新版本: latest_ethusdt_latency_report.md")
            return filename
            
        except Exception as e:
            print(f"❌ 保存报告失败: {e}")
            return None

def main():
    """主函数"""
    reporter = ETHUSDTLatencyReporter()
    
    print("⚡ ETHUSDT延时分析报告生成器")
    print("=" * 50)
    
    # 生成报告
    report = reporter.generate_report()
    
    # 保存报告
    filename = reporter.save_report(report)
    
    if filename:
        print("\n✅ ETHUSDT延时报告生成完成！")
        print(f"📄 文件名: {filename}")
        print("📄 最新版本: latest_ethusdt_latency_report.md")
        print("\n📊 报告包含:")
        print("  - 不同时间段的延时统计 (1小时、6小时、24小时)")
        print("  - 延时分布分析")
        print("  - 最近10条延时样本")
        print("  - 数据质量评估和性能建议")
        print("  - 具体的时间戳和数据源信息")
        
        # 显示关键统计信息
        try:
            stats = reporter.get_detailed_latency_stats()
            if stats and stats.get('24小时'):
                data = stats['24小时']
                print(f"\n📈 关键指标 (24小时):")
                print(f"  - 总记录数: {data['total_records']:,} 条")
                print(f"  - 平均延时: {data['avg_latency']} ms")
                print(f"  - 延时范围: {data['min_latency']}-{data['max_latency']} ms")
                print(f"  - 数据时间: {data['start_time']} ~ {data['end_time']}")
        except:
            pass
    else:
        print("\n❌ 报告生成失败")

if __name__ == "__main__":
    main()
