#!/usr/bin/env python3
"""
调试Grafana连接问题
"""

import requests
import json
import mysql.connector

def test_mysql_direct():
    """直接测试MySQL"""
    print("🔍 直接测试MySQL...")
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='Linuxtest',
            database='depth_db'
        )
        cursor = connection.cursor()
        
        cursor.execute("SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1")
        result = cursor.fetchone()
        
        if result:
            print(f"   ✅ MySQL直连成功: {result[0]}")
            cursor.close()
            connection.close()
            return True
        else:
            print(f"   ❌ 无数据")
            return False
            
    except Exception as e:
        print(f"   ❌ MySQL连接失败: {e}")
        return False

def test_grafana_auth():
    """测试Grafana认证"""
    print("🔍 测试Grafana认证...")
    
    session = requests.Session()
    session.auth = ("admin", "admin")
    
    try:
        response = session.get("http://localhost:3000/api/user", timeout=10)
        if response.status_code == 200:
            user_info = response.json()
            print(f"   ✅ Grafana认证成功: {user_info.get('login', 'unknown')}")
            return session
        else:
            print(f"   ❌ Grafana认证失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"   ❌ Grafana连接异常: {e}")
        return None

def test_datasource_health(session):
    """测试数据源健康状态"""
    print("🔍 测试数据源健康状态...")
    
    try:
        # 获取数据源列表
        response = session.get("http://localhost:3000/api/datasources", timeout=10)
        if response.status_code != 200:
            print(f"   ❌ 获取数据源失败: {response.status_code}")
            return False
        
        datasources = response.json()
        working_ds = None
        
        for ds in datasources:
            if ds['name'] == 'WorkingDepthDB':
                working_ds = ds
                break
        
        if not working_ds:
            print(f"   ❌ 未找到WorkingDepthDB数据源")
            return False
        
        print(f"   📊 找到数据源: {working_ds['name']} (ID: {working_ds['id']})")
        
        # 测试数据源健康状态
        health_response = session.get(f"http://localhost:3000/api/datasources/{working_ds['id']}/health", timeout=10)
        print(f"   📊 健康检查状态码: {health_response.status_code}")
        
        if health_response.status_code == 200:
            health_result = health_response.json()
            print(f"   ✅ 数据源健康: {health_result}")
            return True
        else:
            print(f"   ⚠️ 数据源健康检查失败: {health_response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试数据源异常: {e}")
        return False

def test_simple_query(session):
    """测试最简单的查询"""
    print("🔍 测试最简单的查询...")
    
    query_data = {
        "queries": [{
            "datasource": {
                "type": "mysql",
                "uid": "cenigejcatslce"
            },
            "rawSql": "SELECT 1 as test",
            "refId": "A",
            "format": "table"
        }]
    }
    
    try:
        response = session.post(
            "http://localhost:3000/api/ds/query",
            json=query_data,
            headers={"Content-Type": "application/json"},
            timeout=15
        )
        
        print(f"   📊 查询状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 简单查询成功: {result}")
            return True
        else:
            print(f"   ❌ 简单查询失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 查询异常: {e}")
        return False

def test_real_query(session):
    """测试真实数据查询"""
    print("🔍 测试真实数据查询...")
    
    query_data = {
        "queries": [{
            "datasource": {
                "type": "mysql",
                "uid": "cenigejcatslce"
            },
            "rawSql": "SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1",
            "refId": "A",
            "format": "table"
        }]
    }
    
    try:
        response = session.post(
            "http://localhost:3000/api/ds/query",
            json=query_data,
            headers={"Content-Type": "application/json"},
            timeout=20
        )
        
        print(f"   📊 查询状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 真实查询成功: {result}")
            return True
        else:
            print(f"   ❌ 真实查询失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 查询异常: {e}")
        return False

def main():
    """主函数"""
    print("🔧 调试Grafana连接问题")
    print("=" * 50)
    
    # 1. 测试MySQL直连
    if not test_mysql_direct():
        print("❌ MySQL直连失败，停止测试")
        return
    
    # 2. 测试Grafana认证
    session = test_grafana_auth()
    if not session:
        print("❌ Grafana认证失败，停止测试")
        return
    
    # 3. 测试数据源健康状态
    if not test_datasource_health(session):
        print("❌ 数据源健康检查失败")
    
    # 4. 测试简单查询
    if not test_simple_query(session):
        print("❌ 简单查询失败")
    
    # 5. 测试真实查询
    if not test_real_query(session):
        print("❌ 真实查询失败")
    
    print("\n🎯 调试完成")

if __name__ == "__main__":
    main()
