#!/usr/bin/env python3
"""
验证BTCUSDT深度对比数据的取值和正确性
"""

import mysql.connector
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def verify_btcusdt_depth_data():
    """验证BTCUSDT深度对比数据"""
    print("🔍 验证BTCUSDT深度对比数据")
    print("=" * 50)
    print("📊 目标验证数据:")
    print("   买一量: Bitda 9.77, Binance 4.31, 深度比 2.27")
    print("   卖一量: Bitda 9.86, Binance 1.55, 深度比 6.37")
    print("   买一量卖一量: Bitda 19.63, Binance 5.86, 深度比 3.35")
    print("   买卖前两档量: Bitda 35.92, Binance 5.98, 深度比 6.01")
    print("   买卖前五档量: Bitda 81.76, Binance 6.05, 深度比 13.52")
    print()
    
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'Linuxtest',
        'database': 'depth_db'
    }
    
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        # 获取Bitda最新数据
        print("📊 获取Bitda最新数据...")
        cursor.execute("""
            SELECT timestamp, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
                   bid_qty_2, ask_qty_2, bid_qty_3, ask_qty_3, bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5
            FROM bitda_depth 
            WHERE symbol = 'BTCUSDT' AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
            ORDER BY timestamp DESC 
            LIMIT 1
        """)
        
        bitda_latest = cursor.fetchone()
        if not bitda_latest:
            print("❌ 无Bitda最新数据")
            return False
        
        bitda_timestamp = bitda_latest[0]
        bitda_time = datetime.fromtimestamp(bitda_timestamp / 1000)
        print(f"   ✅ Bitda最新时间: {bitda_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
        
        # 获取与Bitda时间最接近的Binance数据
        print("📊 获取Binance最接近数据...")
        cursor.execute("""
            SELECT event_time, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
                   bid_qty_2, ask_qty_2, bid_qty_3, ask_qty_3, bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5
            FROM binance_depth_5 
            WHERE symbol = 'BTCUSDT' AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
            ORDER BY ABS(event_time - %s) ASC
            LIMIT 1
        """, (bitda_timestamp,))
        
        binance_latest = cursor.fetchone()
        if not binance_latest:
            print("❌ 无Binance最新数据")
            return False
        
        binance_timestamp = binance_latest[0]
        binance_time = datetime.fromtimestamp(binance_timestamp / 1000)
        time_diff_ms = abs(bitda_timestamp - binance_timestamp)
        
        print(f"   ✅ Binance最近时间: {binance_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
        print(f"   ⏰ 时间差: {time_diff_ms}ms")
        
        # 解析Bitda数据
        print("\n📈 解析Bitda原始数据:")
        bitda_bid1_qty = float(bitda_latest[3]) if bitda_latest[3] else 0
        bitda_ask1_qty = float(bitda_latest[4]) if bitda_latest[4] else 0
        bitda_bid2_qty = float(bitda_latest[5]) if bitda_latest[5] else 0
        bitda_ask2_qty = float(bitda_latest[6]) if bitda_latest[6] else 0
        bitda_bid3_qty = float(bitda_latest[7]) if bitda_latest[7] else 0
        bitda_ask3_qty = float(bitda_latest[8]) if bitda_latest[8] else 0
        bitda_bid4_qty = float(bitda_latest[9]) if bitda_latest[9] else 0
        bitda_ask4_qty = float(bitda_latest[10]) if bitda_latest[10] else 0
        bitda_bid5_qty = float(bitda_latest[11]) if bitda_latest[11] else 0
        bitda_ask5_qty = float(bitda_latest[12]) if bitda_latest[12] else 0
        
        print(f"   买一量: {bitda_bid1_qty:.2f}")
        print(f"   卖一量: {bitda_ask1_qty:.2f}")
        print(f"   买二量: {bitda_bid2_qty:.2f}")
        print(f"   卖二量: {bitda_ask2_qty:.2f}")
        print(f"   买三量: {bitda_bid3_qty:.2f}")
        print(f"   卖三量: {bitda_ask3_qty:.2f}")
        print(f"   买四量: {bitda_bid4_qty:.2f}")
        print(f"   卖四量: {bitda_ask4_qty:.2f}")
        print(f"   买五量: {bitda_bid5_qty:.2f}")
        print(f"   卖五量: {bitda_ask5_qty:.2f}")
        
        # 解析Binance数据
        print("\n📈 解析Binance原始数据:")
        binance_bid1_qty = float(binance_latest[3]) if binance_latest[3] else 0
        binance_ask1_qty = float(binance_latest[4]) if binance_latest[4] else 0
        binance_bid2_qty = float(binance_latest[5]) if binance_latest[5] else 0
        binance_ask2_qty = float(binance_latest[6]) if binance_latest[6] else 0
        binance_bid3_qty = float(binance_latest[7]) if binance_latest[7] else 0
        binance_ask3_qty = float(binance_latest[8]) if binance_latest[8] else 0
        binance_bid4_qty = float(binance_latest[9]) if binance_latest[9] else 0
        binance_ask4_qty = float(binance_latest[10]) if binance_latest[10] else 0
        binance_bid5_qty = float(binance_latest[11]) if binance_latest[11] else 0
        binance_ask5_qty = float(binance_latest[12]) if binance_latest[12] else 0
        
        print(f"   买一量: {binance_bid1_qty:.2f}")
        print(f"   卖一量: {binance_ask1_qty:.2f}")
        print(f"   买二量: {binance_bid2_qty:.2f}")
        print(f"   卖二量: {binance_ask2_qty:.2f}")
        print(f"   买三量: {binance_bid3_qty:.2f}")
        print(f"   卖三量: {binance_ask3_qty:.2f}")
        print(f"   买四量: {binance_bid4_qty:.2f}")
        print(f"   卖四量: {binance_ask4_qty:.2f}")
        print(f"   买五量: {binance_bid5_qty:.2f}")
        print(f"   卖五量: {binance_ask5_qty:.2f}")
        
        # 计算各项指标
        print("\n🧮 计算深度对比指标:")
        
        # 1. 买一量
        bid1_ratio = bitda_bid1_qty / binance_bid1_qty if binance_bid1_qty > 0 else 0
        print(f"   买一量: {bitda_bid1_qty:.2f} / {binance_bid1_qty:.2f} = {bid1_ratio:.2f}")
        
        # 2. 卖一量
        ask1_ratio = bitda_ask1_qty / binance_ask1_qty if binance_ask1_qty > 0 else 0
        print(f"   卖一量: {bitda_ask1_qty:.2f} / {binance_ask1_qty:.2f} = {ask1_ratio:.2f}")
        
        # 3. 买一量卖一量
        bitda_bid_ask1 = bitda_bid1_qty + bitda_ask1_qty
        binance_bid_ask1 = binance_bid1_qty + binance_ask1_qty
        bid_ask1_ratio = bitda_bid_ask1 / binance_bid_ask1 if binance_bid_ask1 > 0 else 0
        print(f"   买一量卖一量: {bitda_bid_ask1:.2f} / {binance_bid_ask1:.2f} = {bid_ask1_ratio:.2f}")
        
        # 4. 买卖前两档量
        bitda_bid_ask2 = bitda_bid1_qty + bitda_bid2_qty + bitda_ask1_qty + bitda_ask2_qty
        binance_bid_ask2 = binance_bid1_qty + binance_bid2_qty + binance_ask1_qty + binance_ask2_qty
        bid_ask2_ratio = bitda_bid_ask2 / binance_bid_ask2 if binance_bid_ask2 > 0 else 0
        print(f"   买卖前两档量: {bitda_bid_ask2:.2f} / {binance_bid_ask2:.2f} = {bid_ask2_ratio:.2f}")
        
        # 5. 买卖前五档量
        bitda_bid5_total = bitda_bid1_qty + bitda_bid2_qty + bitda_bid3_qty + bitda_bid4_qty + bitda_bid5_qty
        bitda_ask5_total = bitda_ask1_qty + bitda_ask2_qty + bitda_ask3_qty + bitda_ask4_qty + bitda_ask5_qty
        bitda_bid_ask5 = bitda_bid5_total + bitda_ask5_total
        
        binance_bid5_total = binance_bid1_qty + binance_bid2_qty + binance_bid3_qty + binance_bid4_qty + binance_bid5_qty
        binance_ask5_total = binance_ask1_qty + binance_ask2_qty + binance_ask3_qty + binance_ask4_qty + binance_ask5_qty
        binance_bid_ask5 = binance_bid5_total + binance_ask5_total
        
        bid_ask5_ratio = bitda_bid_ask5 / binance_bid_ask5 if binance_bid_ask5 > 0 else 0
        print(f"   买卖前五档量: {bitda_bid_ask5:.2f} / {binance_bid_ask5:.2f} = {bid_ask5_ratio:.2f}")
        
        # 验证结果
        print("\n✅ 验证结果对比:")
        print("=" * 50)
        
        target_data = {
            '买一量': {'bitda': 9.77, 'binance': 4.31, 'ratio': 2.27},
            '卖一量': {'bitda': 9.86, 'binance': 1.55, 'ratio': 6.37},
            '买一量卖一量': {'bitda': 19.63, 'binance': 5.86, 'ratio': 3.35},
            '买卖前两档量': {'bitda': 35.92, 'binance': 5.98, 'ratio': 6.01},
            '买卖前五档量': {'bitda': 81.76, 'binance': 6.05, 'ratio': 13.52}
        }
        
        actual_data = {
            '买一量': {'bitda': bitda_bid1_qty, 'binance': binance_bid1_qty, 'ratio': bid1_ratio},
            '卖一量': {'bitda': bitda_ask1_qty, 'binance': binance_ask1_qty, 'ratio': ask1_ratio},
            '买一量卖一量': {'bitda': bitda_bid_ask1, 'binance': binance_bid_ask1, 'ratio': bid_ask1_ratio},
            '买卖前两档量': {'bitda': bitda_bid_ask2, 'binance': binance_bid_ask2, 'ratio': bid_ask2_ratio},
            '买卖前五档量': {'bitda': bitda_bid_ask5, 'binance': binance_bid_ask5, 'ratio': bid_ask5_ratio}
        }
        
        all_correct = True
        
        for item in target_data:
            target = target_data[item]
            actual = actual_data[item]
            
            print(f"\n📊 {item}:")
            print(f"   目标: Bitda {target['bitda']:.2f}, Binance {target['binance']:.2f}, 比值 {target['ratio']:.2f}")
            print(f"   实际: Bitda {actual['bitda']:.2f}, Binance {actual['binance']:.2f}, 比值 {actual['ratio']:.2f}")
            
            # 检查是否匹配 (允许小的浮点误差)
            bitda_match = abs(actual['bitda'] - target['bitda']) < 0.01
            binance_match = abs(actual['binance'] - target['binance']) < 0.01
            ratio_match = abs(actual['ratio'] - target['ratio']) < 0.01
            
            if bitda_match and binance_match and ratio_match:
                print(f"   ✅ 数据匹配")
            else:
                print(f"   ❌ 数据不匹配")
                if not bitda_match:
                    print(f"      Bitda差异: {actual['bitda'] - target['bitda']:.4f}")
                if not binance_match:
                    print(f"      Binance差异: {actual['binance'] - target['binance']:.4f}")
                if not ratio_match:
                    print(f"      比值差异: {actual['ratio'] - target['ratio']:.4f}")
                all_correct = False
        
        cursor.close()
        connection.close()
        
        print("\n🎯 验证总结:")
        if all_correct:
            print("✅ 所有数据完全匹配，计算正确")
        else:
            print("❌ 数据不匹配，可能是时间点不同或计算逻辑有差异")
            print("💡 建议检查:")
            print("   1. 数据获取的时间点是否一致")
            print("   2. 深度数据的字段映射是否正确")
            print("   3. 计算逻辑是否与仪表板一致")
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 BTCUSDT深度对比数据验证器")
    print("=" * 50)
    print("🎯 验证目标:")
    print("   1. 检查原始数据获取是否正确")
    print("   2. 验证各项计算逻辑是否准确")
    print("   3. 对比目标数据与实际数据")
    print("   4. 确认深度比值计算公式")
    print()
    
    success = verify_btcusdt_depth_data()
    
    if success:
        print("\n🎉 验证成功！数据取值和计算完全正确")
    else:
        print("\n⚠️ 验证发现差异，需要进一步检查")

if __name__ == "__main__":
    main()
