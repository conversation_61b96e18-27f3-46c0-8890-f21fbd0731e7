#!/bin/bash

# ClickHouse + Grafana 深度对比仪表板启动脚本
# 作者: AI Assistant
# 用途: 一键启动ClickHouse、Grafana并配置深度对比仪表板

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务状态
check_service() {
    local service_name=$1
    local port=$2
    
    if nc -z localhost $port 2>/dev/null; then
        log_success "$service_name 服务已运行 (端口: $port)"
        return 0
    else
        log_warning "$service_name 服务未运行 (端口: $port)"
        return 1
    fi
}

# 启动ClickHouse
start_clickhouse() {
    log_info "🗄️ 启动ClickHouse服务..."
    
    if check_service "ClickHouse" 9000; then
        return 0
    fi
    
    # 尝试启动ClickHouse
    if command -v systemctl >/dev/null 2>&1; then
        sudo systemctl start clickhouse-server
        log_info "等待ClickHouse启动..."
        sleep 5
        
        if check_service "ClickHouse" 9000; then
            log_success "ClickHouse启动成功"
            return 0
        else
            log_error "ClickHouse启动失败"
            return 1
        fi
    else
        log_error "无法启动ClickHouse - systemctl不可用"
        return 1
    fi
}

# 启动Grafana
start_grafana() {
    log_info "📊 启动Grafana服务..."
    
    if check_service "Grafana" 3000; then
        return 0
    fi
    
    # 尝试启动Grafana
    if command -v systemctl >/dev/null 2>&1; then
        sudo systemctl start grafana-server
        log_info "等待Grafana启动..."
        sleep 10
        
        if check_service "Grafana" 3000; then
            log_success "Grafana启动成功"
            return 0
        else
            log_error "Grafana启动失败"
            return 1
        fi
    else
        log_error "无法启动Grafana - systemctl不可用"
        return 1
    fi
}

# 检查ClickHouse插件
check_clickhouse_plugin() {
    log_info "🔌 检查Grafana ClickHouse插件..."
    
    # 检查插件是否已安装
    if grafana-cli plugins ls | grep -q "grafana-clickhouse-datasource"; then
        log_success "ClickHouse插件已安装"
        return 0
    else
        log_warning "ClickHouse插件未安装，正在安装..."
        
        # 安装插件
        sudo grafana-cli plugins install grafana-clickhouse-datasource
        
        if [ $? -eq 0 ]; then
            log_success "ClickHouse插件安装成功"
            log_info "重启Grafana以加载插件..."
            sudo systemctl restart grafana-server
            sleep 10
            return 0
        else
            log_error "ClickHouse插件安装失败"
            return 1
        fi
    fi
}

# 配置数据源和仪表板
setup_grafana() {
    log_info "⚙️ 配置Grafana数据源和仪表板..."
    
    # 运行Python配置脚本
    if [ -f "setup_grafana_clickhouse.py" ]; then
        python3 setup_grafana_clickhouse.py
        
        if [ $? -eq 0 ]; then
            log_success "Grafana配置完成"
            return 0
        else
            log_error "Grafana配置失败"
            return 1
        fi
    else
        log_error "配置脚本不存在: setup_grafana_clickhouse.py"
        return 1
    fi
}

# 验证数据
verify_data() {
    log_info "🔍 验证ClickHouse数据..."
    
    # 检查bitda_depth表
    bitda_count=$(clickhouse-client --query "SELECT COUNT(*) FROM crypto.bitda_depth" 2>/dev/null || echo "0")
    log_info "Bitda深度数据记录数: $bitda_count"
    
    # 检查binance_depth_5表
    binance_count=$(clickhouse-client --query "SELECT COUNT(*) FROM crypto.binance_depth_5" 2>/dev/null || echo "0")
    log_info "Binance深度数据记录数: $binance_count"
    
    if [ "$bitda_count" -gt 0 ] && [ "$binance_count" -gt 0 ]; then
        log_success "数据验证通过"
        return 0
    else
        log_warning "数据可能不完整，请检查数据采集服务"
        return 1
    fi
}

# 显示访问信息
show_access_info() {
    log_success "🎉 启动完成！"
    echo ""
    echo "📋 访问信息:"
    echo "  🌐 Grafana Web界面: http://localhost:3000"
    echo "  👤 用户名: admin"
    echo "  🔑 密码: admin"
    echo ""
    echo "📊 仪表板:"
    echo "  📈 ClickHouse深度对比仪表板"
    echo "  📈 ClickHouse Crypto Test Dashboard"
    echo ""
    echo "🔧 数据源:"
    echo "  🗄️ ClickHouse (主要)"
    echo "  🗄️ MySQL_Backup (备份)"
    echo ""
    echo "💡 提示:"
    echo "  - 首次登录可能需要修改密码"
    echo "  - 如果数据不显示，请检查数据采集服务"
    echo "  - 仪表板每1分钟自动刷新"
}

# 主函数
main() {
    echo "🚀 启动ClickHouse + Grafana 深度对比仪表板"
    echo "=" * 60
    
    # 1. 启动ClickHouse
    if ! start_clickhouse; then
        log_error "ClickHouse启动失败，退出"
        exit 1
    fi
    
    # 2. 启动Grafana
    if ! start_grafana; then
        log_error "Grafana启动失败，退出"
        exit 1
    fi
    
    # 3. 检查ClickHouse插件
    if ! check_clickhouse_plugin; then
        log_error "ClickHouse插件检查失败，退出"
        exit 1
    fi
    
    # 4. 配置Grafana
    if ! setup_grafana; then
        log_error "Grafana配置失败，退出"
        exit 1
    fi
    
    # 5. 验证数据
    verify_data
    
    # 6. 显示访问信息
    show_access_info
    
    echo ""
    echo "=" * 60
    log_success "所有服务已启动并配置完成！"
}

# 检查依赖
check_dependencies() {
    local missing_deps=()
    
    # 检查必要的命令
    for cmd in clickhouse-client grafana-cli python3 nc; do
        if ! command -v $cmd >/dev/null 2>&1; then
            missing_deps+=($cmd)
        fi
    done
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_error "缺少依赖: ${missing_deps[*]}"
        log_info "请安装缺少的依赖后重试"
        exit 1
    fi
}

# 脚本入口
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  --check-only   仅检查服务状态"
    echo ""
    echo "功能:"
    echo "  - 启动ClickHouse服务"
    echo "  - 启动Grafana服务"
    echo "  - 安装ClickHouse插件"
    echo "  - 配置数据源和仪表板"
    echo "  - 验证数据完整性"
    exit 0
elif [ "$1" = "--check-only" ]; then
    echo "🔍 检查服务状态..."
    check_service "ClickHouse" 9000
    check_service "Grafana" 3000
    exit 0
fi

# 检查依赖并运行主函数
check_dependencies
main
