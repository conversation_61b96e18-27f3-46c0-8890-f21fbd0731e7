#!/usr/bin/env python3
"""
检查延时处理服务的稳定运行状态
"""

import subprocess
import mysql.connector
from datetime import datetime, timedelta
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ServiceStatusChecker:
    """服务状态检查器"""
    
    def __init__(self):
        self.latency_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'ethusdt_latency_db'
        }
        
        self.source_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'depth_db'
        }
    
    def check_process_status(self):
        """检查进程状态"""
        logger.info("🔍 检查延时处理器进程状态...")
        
        try:
            # 检查进程是否运行
            result = subprocess.run(
                ['ps', 'aux'], 
                capture_output=True, 
                text=True
            )
            
            processes = []
            for line in result.stdout.split('\n'):
                if 'ethusdt_realtime_processor' in line and 'grep' not in line:
                    processes.append(line)
            
            if processes:
                logger.info(f"   ✅ 找到 {len(processes)} 个延时处理器进程:")
                for i, process in enumerate(processes):
                    parts = process.split()
                    pid = parts[1]
                    cpu = parts[2]
                    mem = parts[3]
                    start_time = parts[8]
                    logger.info(f"      进程{i+1}: PID={pid}, CPU={cpu}%, 内存={mem}%, 启动时间={start_time}")
                return True
            else:
                logger.warning("   ❌ 未找到延时处理器进程")
                return False
                
        except Exception as e:
            logger.error(f"   ❌ 检查进程状态失败: {e}")
            return False
    
    def check_log_status(self):
        """检查日志状态"""
        logger.info("\n📋 检查日志状态...")
        
        log_file = "/home/<USER>/project/WS_DATA_ALL/ethusdt_realtime_processor.log"
        
        try:
            if os.path.exists(log_file):
                # 获取日志文件信息
                stat = os.stat(log_file)
                file_size = stat.st_size
                mod_time = datetime.fromtimestamp(stat.st_mtime)
                
                logger.info(f"   📄 日志文件: {log_file}")
                logger.info(f"   📊 文件大小: {file_size} 字节")
                logger.info(f"   ⏰ 最后修改: {mod_time}")
                
                # 检查最近的日志
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                    if lines:
                        last_line = lines[-1].strip()
                        logger.info(f"   📝 最后一行: {last_line}")
                        
                        # 检查是否有最近的活动
                        recent_lines = lines[-10:]
                        recent_activity = False
                        for line in recent_lines:
                            if '🔄 开始处理' in line:
                                recent_activity = True
                                break
                        
                        if recent_activity:
                            logger.info("   ✅ 日志显示最近有处理活动")
                        else:
                            logger.warning("   ⚠️  日志中未发现最近的处理活动")
                    else:
                        logger.warning("   ⚠️  日志文件为空")
                
                return True
            else:
                logger.warning(f"   ❌ 日志文件不存在: {log_file}")
                return False
                
        except Exception as e:
            logger.error(f"   ❌ 检查日志状态失败: {e}")
            return False
    
    def check_database_activity(self):
        """检查数据库活动"""
        logger.info("\n💾 检查数据库活动...")
        
        try:
            connection = mysql.connector.connect(**self.latency_config)
            cursor = connection.cursor()
            
            # 检查总记录数
            cursor.execute("SELECT COUNT(*) FROM ethusdt_latency_matches")
            total_count = cursor.fetchone()[0]
            
            # 检查最近30分钟的记录
            cursor.execute("""
                SELECT COUNT(*) FROM ethusdt_latency_matches 
                WHERE created_at >= NOW() - INTERVAL 30 MINUTE
            """)
            recent_30min = cursor.fetchone()[0]
            
            # 检查最近10分钟的记录
            cursor.execute("""
                SELECT COUNT(*) FROM ethusdt_latency_matches 
                WHERE created_at >= NOW() - INTERVAL 10 MINUTE
            """)
            recent_10min = cursor.fetchone()[0]
            
            # 检查最近5分钟的记录
            cursor.execute("""
                SELECT COUNT(*) FROM ethusdt_latency_matches 
                WHERE created_at >= NOW() - INTERVAL 5 MINUTE
            """)
            recent_5min = cursor.fetchone()[0]
            
            # 检查最新记录时间
            cursor.execute("""
                SELECT MAX(created_at) FROM ethusdt_latency_matches
            """)
            latest_time = cursor.fetchone()[0]
            
            logger.info(f"   📊 总记录数: {total_count}")
            logger.info(f"   📊 最近30分钟: {recent_30min} 条")
            logger.info(f"   📊 最近10分钟: {recent_10min} 条")
            logger.info(f"   📊 最近5分钟: {recent_5min} 条")
            logger.info(f"   ⏰ 最新记录: {latest_time}")
            
            # 分析活动状态
            if latest_time:
                time_diff = datetime.now() - latest_time
                minutes_ago = time_diff.total_seconds() / 60
                
                if minutes_ago < 5:
                    logger.info(f"   ✅ 数据库活跃: 最新记录在 {minutes_ago:.1f} 分钟前")
                elif minutes_ago < 15:
                    logger.warning(f"   ⚠️  数据库较少活动: 最新记录在 {minutes_ago:.1f} 分钟前")
                else:
                    logger.error(f"   ❌ 数据库无活动: 最新记录在 {minutes_ago:.1f} 分钟前")
            
            cursor.close()
            connection.close()
            
            return recent_5min > 0
            
        except Exception as e:
            logger.error(f"   ❌ 检查数据库活动失败: {e}")
            return False
    
    def check_data_source_health(self):
        """检查数据源健康状态"""
        logger.info("\n📡 检查数据源健康状态...")
        
        try:
            connection = mysql.connector.connect(**self.source_config)
            cursor = connection.cursor()
            
            # 检查Bitda数据
            cursor.execute("""
                SELECT COUNT(*) FROM bitda_depth 
                WHERE symbol = 'ETHUSDT' 
                AND created_at >= NOW() - INTERVAL 10 MINUTE
                AND bid_price_1 IS NOT NULL
            """)
            bitda_recent = cursor.fetchone()[0]
            
            # 检查Binance数据
            cursor.execute("""
                SELECT COUNT(*) FROM binance_bookticker 
                WHERE symbol = 'ETHUSDT' 
                AND created_at >= NOW() - INTERVAL 10 MINUTE
            """)
            binance_recent = cursor.fetchone()[0]
            
            logger.info(f"   📊 最近10分钟Bitda数据: {bitda_recent} 条")
            logger.info(f"   📊 最近10分钟Binance数据: {binance_recent} 条")
            
            # 检查数据质量
            if bitda_recent > 0 and binance_recent > 0:
                logger.info("   ✅ 数据源正常，有新数据流入")
                data_healthy = True
            elif bitda_recent > 0:
                logger.warning("   ⚠️  只有Bitda数据，Binance数据缺失")
                data_healthy = False
            elif binance_recent > 0:
                logger.warning("   ⚠️  只有Binance数据，Bitda数据缺失")
                data_healthy = False
            else:
                logger.error("   ❌ 两个数据源都无新数据")
                data_healthy = False
            
            cursor.close()
            connection.close()
            
            return data_healthy
            
        except Exception as e:
            logger.error(f"   ❌ 检查数据源健康状态失败: {e}")
            return False
    
    def check_service_performance(self):
        """检查服务性能"""
        logger.info("\n⚡ 检查服务性能...")
        
        try:
            connection = mysql.connector.connect(**self.latency_config)
            cursor = connection.cursor()
            
            # 检查最近1小时的统计
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_matches,
                    AVG(latency_ms) as avg_latency,
                    MIN(latency_ms) as min_latency,
                    MAX(latency_ms) as max_latency,
                    COUNT(DISTINCT DATE_FORMAT(created_at, '%Y-%m-%d %H:%i')) as active_minutes
                FROM ethusdt_latency_matches 
                WHERE created_at >= NOW() - INTERVAL 1 HOUR
            """)
            
            result = cursor.fetchone()
            if result and result[0] > 0:
                total, avg_lat, min_lat, max_lat, active_min = result
                
                logger.info(f"   📊 最近1小时统计:")
                logger.info(f"      总匹配数: {total}")
                logger.info(f"      平均延时: {avg_lat:.1f}ms")
                logger.info(f"      延时范围: {min_lat}-{max_lat}ms")
                logger.info(f"      活跃分钟数: {active_min}/60")
                
                # 计算匹配率
                match_rate = active_min / 60 * 100
                if match_rate > 50:
                    logger.info(f"   ✅ 匹配率良好: {match_rate:.1f}%")
                elif match_rate > 20:
                    logger.warning(f"   ⚠️  匹配率一般: {match_rate:.1f}%")
                else:
                    logger.error(f"   ❌ 匹配率较低: {match_rate:.1f}%")
                
                performance_good = match_rate > 20
            else:
                logger.warning("   ⚠️  最近1小时无匹配数据")
                performance_good = False
            
            cursor.close()
            connection.close()
            
            return performance_good
            
        except Exception as e:
            logger.error(f"   ❌ 检查服务性能失败: {e}")
            return False
    
    def comprehensive_status_check(self):
        """综合状态检查"""
        logger.info("🔍 开始延时处理服务综合状态检查...")
        logger.info("=" * 80)
        
        # 各项检查
        process_ok = self.check_process_status()
        log_ok = self.check_log_status()
        db_ok = self.check_database_activity()
        source_ok = self.check_data_source_health()
        perf_ok = self.check_service_performance()
        
        # 综合评估
        logger.info("\n" + "=" * 80)
        logger.info("📋 综合状态评估:")
        
        checks = [
            ("进程状态", process_ok),
            ("日志状态", log_ok),
            ("数据库活动", db_ok),
            ("数据源健康", source_ok),
            ("服务性能", perf_ok)
        ]
        
        passed = 0
        for name, status in checks:
            status_icon = "✅" if status else "❌"
            logger.info(f"   {status_icon} {name}: {'正常' if status else '异常'}")
            if status:
                passed += 1
        
        overall_score = passed / len(checks) * 100
        
        logger.info(f"\n🎯 总体评分: {overall_score:.0f}% ({passed}/{len(checks)} 项通过)")
        
        if overall_score >= 80:
            logger.info("🎉 服务状态优秀，稳定运行中")
            return "excellent"
        elif overall_score >= 60:
            logger.info("✅ 服务状态良好，基本正常")
            return "good"
        elif overall_score >= 40:
            logger.warning("⚠️  服务状态一般，需要关注")
            return "fair"
        else:
            logger.error("❌ 服务状态异常，需要立即处理")
            return "poor"

def main():
    """主函数"""
    print("🔍 延时处理服务状态检查工具")
    print("=" * 50)
    print("功能:")
    print("  - 检查进程运行状态")
    print("  - 检查日志活动")
    print("  - 检查数据库活动")
    print("  - 检查数据源健康")
    print("  - 评估服务性能")
    print()
    
    checker = ServiceStatusChecker()
    status = checker.comprehensive_status_check()
    
    print(f"\n📊 检查完成，服务状态: {status}")

if __name__ == "__main__":
    main()
