#!/usr/bin/env python3
"""
MySQL到ClickHouse完整迁移脚本
一键完成所有迁移工作
"""

import mysql.connector
from clickhouse_driver import Client
import time
import sys
from datetime import datetime

# 数据库配置
MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'Linuxtest',
    'database': 'depth_db'
}

CLICKHOUSE_CONFIG = {
    'host': 'localhost',
    'user': 'default',
    'password': 'Linuxtest',
    'database': 'crypto'
}

def log(message):
    """日志输出"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def test_connections():
    """测试数据库连接"""
    log("🔍 测试数据库连接...")
    
    # 测试MySQL连接
    try:
        mysql_conn = mysql.connector.connect(**MYSQL_CONFIG)
        mysql_cursor = mysql_conn.cursor()
        mysql_cursor.execute("SELECT 1")
        mysql_cursor.fetchone()
        mysql_cursor.close()
        mysql_conn.close()
        log("✅ MySQL连接成功")
    except Exception as e:
        log(f"❌ MySQL连接失败: {e}")
        return False
    
    # 测试ClickHouse连接
    try:
        ch_client = Client(**CLICKHOUSE_CONFIG)
        ch_client.execute("SELECT 1")
        log("✅ ClickHouse连接成功")
    except Exception as e:
        log(f"❌ ClickHouse连接失败: {e}")
        return False
    
    return True

def create_clickhouse_database():
    """创建ClickHouse数据库"""
    log("📊 创建ClickHouse数据库...")
    
    try:
        # 连接到默认数据库
        ch_client = Client(host='localhost', user='default', password='Linuxtest')
        ch_client.execute("CREATE DATABASE IF NOT EXISTS crypto")
        log("✅ crypto数据库创建成功")
        return True
    except Exception as e:
        log(f"❌ 创建数据库失败: {e}")
        return False

def create_clickhouse_tables():
    """创建ClickHouse表结构"""
    log("🏗️ 创建ClickHouse表结构...")
    
    ch_client = Client(**CLICKHOUSE_CONFIG)
    
    # 表结构定义
    tables = {
        'bitda_depth': """
            CREATE TABLE IF NOT EXISTS bitda_depth (
                id UInt64,
                symbol String,
                timestamp UInt64,
                bid_price_1 Decimal(15,2),
                ask_price_1 Decimal(15,2),
                bid_qty_1 Decimal(20,4),
                ask_qty_1 Decimal(20,4),
                bid_price_2 Decimal(15,2),
                ask_price_2 Decimal(15,2),
                bid_qty_2 Decimal(20,4),
                ask_qty_2 Decimal(20,4),
                bid_price_3 Decimal(15,2),
                ask_price_3 Decimal(15,2),
                bid_qty_3 Decimal(20,4),
                ask_qty_3 Decimal(20,4),
                bid_price_4 Decimal(15,2),
                ask_price_4 Decimal(15,2),
                bid_qty_4 Decimal(20,4),
                ask_qty_4 Decimal(20,4),
                bid_price_5 Decimal(15,2),
                ask_price_5 Decimal(15,2),
                bid_qty_5 Decimal(20,4),
                ask_qty_5 Decimal(20,4),
                asks String,
                bids String,
                created_at DateTime DEFAULT now()
            ) ENGINE = MergeTree()
            ORDER BY (symbol, timestamp)
            PARTITION BY toYYYYMM(toDateTime(timestamp/1000))
        """,
        
        'binance_depth_5': """
            CREATE TABLE IF NOT EXISTS binance_depth_5 (
                id UInt64,
                symbol String,
                event_time UInt64,
                bid_price_1 Decimal(15,2),
                ask_price_1 Decimal(15,2),
                bid_qty_1 Decimal(20,4),
                ask_qty_1 Decimal(20,4),
                bid_price_2 Decimal(15,2),
                ask_price_2 Decimal(15,2),
                bid_qty_2 Decimal(20,4),
                ask_qty_2 Decimal(20,4),
                bid_price_3 Decimal(15,2),
                ask_price_3 Decimal(15,2),
                bid_qty_3 Decimal(20,4),
                ask_qty_3 Decimal(20,4),
                bid_price_4 Decimal(15,2),
                ask_price_4 Decimal(15,2),
                bid_qty_4 Decimal(20,4),
                ask_qty_4 Decimal(20,4),
                bid_price_5 Decimal(15,2),
                ask_price_5 Decimal(15,2),
                bid_qty_5 Decimal(20,4),
                ask_qty_5 Decimal(20,4),
                created_at DateTime DEFAULT now()
            ) ENGINE = MergeTree()
            ORDER BY (symbol, event_time)
            PARTITION BY toYYYYMM(toDateTime(event_time/1000))
        """,
        
        'bitda_ticker': """
            CREATE TABLE IF NOT EXISTS bitda_ticker (
                id UInt64,
                symbol String,
                last_price Decimal(15,2),
                sign_price Decimal(15,2),
                index_price Decimal(15,2),
                created_at DateTime DEFAULT now()
            ) ENGINE = MergeTree()
            ORDER BY (symbol, created_at)
            PARTITION BY toYYYYMM(created_at)
        """,
        
        'binance_bookticker': """
            CREATE TABLE IF NOT EXISTS binance_bookticker (
                id UInt64,
                symbol String,
                bid_price Decimal(15,2),
                ask_price Decimal(15,2),
                bid_qty Decimal(20,4),
                ask_qty Decimal(20,4),
                event_time UInt64,
                created_at DateTime DEFAULT now()
            ) ENGINE = MergeTree()
            ORDER BY (symbol, event_time)
            PARTITION BY toYYYYMM(toDateTime(event_time/1000))
        """,
        
        'fundrate': """
            CREATE TABLE IF NOT EXISTS fundrate (
                id UInt64,
                symbol String,
                funding_rate Decimal(10,8),
                next_funding_time UInt64,
                created_at DateTime DEFAULT now()
            ) ENGINE = MergeTree()
            ORDER BY (symbol, created_at)
            PARTITION BY toYYYYMM(created_at)
        """
    }
    
    for table_name, create_sql in tables.items():
        try:
            ch_client.execute(create_sql)
            log(f"✅ 表 {table_name} 创建成功")
        except Exception as e:
            log(f"❌ 表 {table_name} 创建失败: {e}")
            return False
    
    return True

def migrate_table_data(table_name, batch_size=10000):
    """迁移单个表的数据"""
    log(f"📦 开始迁移表 {table_name}...")
    
    mysql_conn = mysql.connector.connect(**MYSQL_CONFIG)
    mysql_cursor = mysql_conn.cursor()
    ch_client = Client(**CLICKHOUSE_CONFIG)
    
    try:
        # 获取总记录数
        mysql_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        total_records = mysql_cursor.fetchone()[0]
        log(f"   📊 表 {table_name} 总记录数: {total_records:,}")
        
        if total_records == 0:
            log(f"   ⚠️ 表 {table_name} 无数据，跳过")
            return True
        
        # 获取列信息
        mysql_cursor.execute(f"DESCRIBE {table_name}")
        columns = [row[0] for row in mysql_cursor.fetchall()]
        columns_str = ', '.join(columns)
        
        # 分批迁移数据
        offset = 0
        migrated = 0
        
        while offset < total_records:
            # 从MySQL读取数据
            query = f"SELECT {columns_str} FROM {table_name} LIMIT {batch_size} OFFSET {offset}"
            mysql_cursor.execute(query)
            rows = mysql_cursor.fetchall()
            
            if not rows:
                break
            
            # 写入ClickHouse
            placeholders = ', '.join(['%s'] * len(columns))
            insert_query = f"INSERT INTO {table_name} ({columns_str}) VALUES"
            
            ch_client.execute(insert_query, rows)
            
            migrated += len(rows)
            offset += batch_size
            
            progress = (migrated / total_records) * 100
            log(f"   📈 {table_name}: {migrated:,}/{total_records:,} ({progress:.1f}%)")
        
        log(f"✅ 表 {table_name} 迁移完成: {migrated:,} 条记录")
        return True
        
    except Exception as e:
        log(f"❌ 表 {table_name} 迁移失败: {e}")
        return False
    finally:
        mysql_cursor.close()
        mysql_conn.close()

def verify_migration():
    """验证迁移结果"""
    log("🔍 验证迁移结果...")
    
    mysql_conn = mysql.connector.connect(**MYSQL_CONFIG)
    mysql_cursor = mysql_conn.cursor()
    ch_client = Client(**CLICKHOUSE_CONFIG)
    
    tables = ['bitda_depth', 'binance_depth_5', 'bitda_ticker', 'binance_bookticker', 'fundrate']
    
    for table in tables:
        try:
            # MySQL记录数
            mysql_cursor.execute(f"SELECT COUNT(*) FROM {table}")
            mysql_count = mysql_cursor.fetchone()[0]
            
            # ClickHouse记录数
            ch_count = ch_client.execute(f"SELECT COUNT(*) FROM {table}")[0][0]
            
            if mysql_count == ch_count:
                log(f"✅ {table}: MySQL({mysql_count:,}) = ClickHouse({ch_count:,})")
            else:
                log(f"❌ {table}: MySQL({mysql_count:,}) ≠ ClickHouse({ch_count:,})")
                
        except Exception as e:
            log(f"❌ 验证表 {table} 失败: {e}")
    
    mysql_cursor.close()
    mysql_conn.close()

def main():
    """主函数"""
    log("🚀 开始MySQL到ClickHouse完整迁移")
    log("=" * 60)
    
    # 1. 测试连接
    if not test_connections():
        log("❌ 数据库连接测试失败，退出")
        sys.exit(1)
    
    # 2. 创建数据库
    if not create_clickhouse_database():
        log("❌ 创建ClickHouse数据库失败，退出")
        sys.exit(1)
    
    # 3. 创建表结构
    if not create_clickhouse_tables():
        log("❌ 创建ClickHouse表结构失败，退出")
        sys.exit(1)
    
    # 4. 迁移数据
    tables = ['bitda_depth', 'binance_depth_5', 'bitda_ticker', 'binance_bookticker', 'fundrate']
    
    start_time = time.time()
    
    for table in tables:
        if not migrate_table_data(table):
            log(f"❌ 迁移表 {table} 失败，继续下一个表")
    
    # 5. 验证迁移
    verify_migration()
    
    end_time = time.time()
    duration = end_time - start_time
    
    log("=" * 60)
    log(f"🎉 迁移完成！总耗时: {duration:.1f} 秒")
    log("📋 下一步:")
    log("  1. 运行 update_code_for_clickhouse.py 更新代码")
    log("  2. 运行 setup_grafana_clickhouse.py 配置Grafana")
    log("  3. 停止MySQL服务 (可选)")

if __name__ == "__main__":
    main()
