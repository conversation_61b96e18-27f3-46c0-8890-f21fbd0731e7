{"dashboard": {"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "mysql", "uid": "fenikp5swzgu8d"}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "项目"}, "properties": [{"id": "custom.width", "value": 150}]}]}, "gridPos": {"h": 12, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "12.0.1", "targets": [{"datasource": {"type": "mysql", "uid": "fenikp5swzgu8d"}, "format": "table", "rawSql": "SELECT \n  '卖一量' as 项目,\n  ROUND((SELECT ask_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1), 2) as Bitda,\n  ROUND((SELECT ask_qty FROM binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,\n  ROUND((SELECT ask_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1) / \n        (SELECT ask_qty FROM binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比\nUNION ALL\nSELECT \n  '买一量' as 项目,\n  ROUND((SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1), 2) as Bitda,\n  ROUND((SELECT bid_qty FROM binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,\n  ROUND((SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1) / \n        (SELECT bid_qty FROM binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比\nUNION ALL\nSELECT \n  '买一量卖一量' as 项目,\n  ROUND((SELECT bid_qty_1 + ask_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1), 2) as Bitda,\n  ROUND((SELECT bid_qty + ask_qty FROM binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,\n  ROUND((SELECT bid_qty_1 + ask_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1) / \n        (SELECT bid_qty + ask_qty FROM binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比\nUNION ALL\nSELECT \n  '买卖前两档量' as 项目,\n  ROUND((SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1), 2) as Bitda,\n  ROUND((SELECT bid_qty + ask_qty FROM binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,\n  ROUND((SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1) / \n        (SELECT bid_qty + ask_qty FROM binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比\nUNION ALL\nSELECT \n  '买卖前五档量' as 项目,\n  ROUND((SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1), 2) as Bitda,\n  ROUND((SELECT bid_qty + ask_qty FROM binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,\n  ROUND((SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1) / \n        (SELECT bid_qty + ask_qty FROM binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比", "refId": "A"}], "title": "📊 BTCUSDT 深度对比", "type": "table"}, {"datasource": {"type": "mysql", "uid": "fenikp5swzgu8d"}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "项目"}, "properties": [{"id": "custom.width", "value": 150}]}]}, "gridPos": {"h": 12, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "12.0.1", "targets": [{"datasource": {"type": "mysql", "uid": "fenikp5swzgu8d"}, "format": "table", "rawSql": "SELECT \n  '卖一量' as 项目,\n  ROUND((SELECT ask_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY id DESC LIMIT 1), 2) as Bitda,\n  ROUND((SELECT ask_qty FROM binance_bookticker WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,\n  ROUND((SELECT ask_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY id DESC LIMIT 1) / \n        (SELECT ask_qty FROM binance_bookticker WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比\nUNION ALL\nSELECT \n  '买一量' as 项目,\n  ROUND((SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY id DESC LIMIT 1), 2) as Bitda,\n  ROUND((SELECT bid_qty FROM binance_bookticker WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,\n  ROUND((SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY id DESC LIMIT 1) / \n        (SELECT bid_qty FROM binance_bookticker WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比\nUNION ALL\nSELECT \n  '买一量卖一量' as 项目,\n  ROUND((SELECT bid_qty_1 + ask_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY id DESC LIMIT 1), 2) as Bitda,\n  ROUND((SELECT bid_qty + ask_qty FROM binance_bookticker WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,\n  ROUND((SELECT bid_qty_1 + ask_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY id DESC LIMIT 1) / \n        (SELECT bid_qty + ask_qty FROM binance_bookticker WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比\nUNION ALL\nSELECT \n  '买卖前两档量' as 项目,\n  ROUND((SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY id DESC LIMIT 1), 2) as Bitda,\n  ROUND((SELECT bid_qty + ask_qty FROM binance_bookticker WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,\n  ROUND((SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY id DESC LIMIT 1) / \n        (SELECT bid_qty + ask_qty FROM binance_bookticker WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比\nUNION ALL\nSELECT \n  '买卖前五档量' as 项目,\n  ROUND((SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY id DESC LIMIT 1), 2) as Bitda,\n  ROUND((SELECT bid_qty + ask_qty FROM binance_bookticker WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,\n  ROUND((SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY id DESC LIMIT 1) / \n        (SELECT bid_qty + ask_qty FROM binance_bookticker WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比", "refId": "A"}], "title": "📊 ETHUSDT 深度对比", "type": "table"}], "preload": false, "refresh": "1m", "schemaVersion": 41, "tags": ["depth-comparison", "mysql", "working"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "browser", "title": "MySQL深度对比仪表板(工作版)", "uid": "mysql-depth-comparison-working", "version": 1, "weekStart": "monday"}, "overwrite": true, "inputs": []}