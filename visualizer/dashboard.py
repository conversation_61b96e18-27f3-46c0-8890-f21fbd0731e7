#!/usr/bin/env python3
"""
数据分析仪表板
整合所有分析器，生成综合报告和可视化
"""

import asyncio
from datetime import datetime
from typing import Dict, Any
import json
from analyzer import (
    LatencyAnalyzer, KlineAnalyzer, DepthAnalyzer, 
    PriceAnalyzer, FundingAnalyzer
)
from .charts import ChartGenerator
from utils.logging import setup_logger

logger = setup_logger(__name__)

class Dashboard:
    """数据分析仪表板"""
    
    def __init__(self):
        # 初始化分析器
        self.latency_analyzer = LatencyAnalyzer()
        self.kline_analyzer = KlineAnalyzer()
        self.depth_analyzer = DepthAnalyzer()
        self.price_analyzer = PriceAnalyzer()
        self.funding_analyzer = FundingAnalyzer()
        
        # 初始化图表生成器
        self.chart_generator = ChartGenerator()
        
    async def generate_comprehensive_report(self, hours: int = 1) -> Dict[str, Any]:
        """
        生成综合分析报告
        
        Args:
            hours: 分析时间范围（小时）
            
        Returns:
            综合分析结果
        """
        logger.info(f"开始生成{hours}小时的综合分析报告...")
        
        try:
            # 并行执行所有分析
            tasks = [
                self.latency_analyzer.analyze_price_latency(hours),
                self.kline_analyzer.analyze_consecutive_identical_klines(hours),
                self.depth_analyzer.analyze_depth_comparison(hours),
                self.price_analyzer.analyze_mark_price_difference(hours),
                self.funding_analyzer.analyze_funding_rates(days=1)  # 资金费率使用天为单位
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 整理结果
            report = {
                'report_time': datetime.now().isoformat(),
                'analysis_period_hours': hours,
                'latency_analysis': results[0] if not isinstance(results[0], Exception) else {},
                'kline_analysis': results[1] if not isinstance(results[1], Exception) else {},
                'depth_analysis': results[2] if not isinstance(results[2], Exception) else {},
                'price_analysis': results[3] if not isinstance(results[3], Exception) else {},
                'funding_analysis': results[4] if not isinstance(results[4], Exception) else {}
            }
            
            # 生成执行摘要
            report['executive_summary'] = self._generate_executive_summary(report)
            
            logger.info("综合分析报告生成完成")
            return report
            
        except Exception as e:
            logger.error(f"生成综合报告失败: {e}")
            return self._empty_report()
    
    def _generate_executive_summary(self, report: Dict) -> Dict:
        """生成执行摘要"""
        try:
            summary = {
                'overall_status': 'normal',
                'key_findings': [],
                'alerts': [],
                'recommendations': []
            }
            
            # 分析延时情况
            latency_data = report.get('latency_analysis', {})
            eth_latency = latency_data.get('results', {}).get('ETHUSDT', {})
            
            if eth_latency.get('total_matches', 0) > 0:
                avg_latency = eth_latency.get('avg_latency_ms', 0)
                if avg_latency > 100:  # 延时超过100ms
                    summary['alerts'].append(f"ETHUSDT平均延时较高: {avg_latency:.1f}ms")
                    summary['overall_status'] = 'warning'
                else:
                    summary['key_findings'].append(f"ETHUSDT延时正常: {avg_latency:.1f}ms")
            
            # 分析K线情况
            kline_data = report.get('kline_analysis', {})
            for symbol in ['BTCUSDT', 'ETHUSDT']:
                symbol_data = kline_data.get('results', {}).get(symbol, {})
                identical_rate = symbol_data.get('identical_rate_pct', 0)
                
                if identical_rate > 5:  # 相同K线比例超过5%
                    summary['alerts'].append(f"{symbol}连续相同K线比例较高: {identical_rate:.2f}%")
                    summary['overall_status'] = 'warning'
                elif identical_rate > 0:
                    summary['key_findings'].append(f"{symbol}发现{symbol_data.get('total_sequences', 0)}个连续相同K线序列")
            
            # 分析深度情况
            depth_data = report.get('depth_analysis', {})
            for symbol in ['BTCUSDT', 'ETHUSDT']:
                symbol_data = depth_data.get('results', {}).get(symbol, {})
                avg_ratio = symbol_data.get('avg_depth_ratio', 0)
                advantage_pct = symbol_data.get('bitda_advantage_pct', 0)
                
                if avg_ratio < 0.8:  # Bitda深度明显劣势
                    summary['alerts'].append(f"{symbol} Bitda深度劣势明显，比值: {avg_ratio:.2f}")
                    summary['overall_status'] = 'warning'
                elif avg_ratio > 1.2:  # Bitda深度明显优势
                    summary['key_findings'].append(f"{symbol} Bitda深度优势明显，比值: {avg_ratio:.2f}")
                
                if advantage_pct > 60:
                    summary['key_findings'].append(f"{symbol} Bitda在{advantage_pct:.1f}%的时间内深度更好")
            
            # 分析价格差值情况
            price_data = report.get('price_analysis', {})
            for symbol in ['BTCUSDT', 'ETHUSDT']:
                symbol_data = price_data.get('results', {}).get(symbol, {})
                avg_diff = symbol_data.get('avg_rel_diff_pct', 0)
                high_diff_rate = symbol_data.get('high_diff_rate_pct', 0)
                
                if avg_diff > 0.1:  # 平均差值超过0.1%
                    summary['alerts'].append(f"{symbol}标记价格差值较大: {avg_diff:.4f}%")
                    summary['overall_status'] = 'warning'
                
                if high_diff_rate > 10:  # 异常差值比例超过10%
                    summary['alerts'].append(f"{symbol}异常价格差值频率较高: {high_diff_rate:.1f}%")
            
            # 分析资金费率情况
            funding_data = report.get('funding_analysis', {})
            btc_funding = funding_data.get('results', {}).get('BTCUSDT', {})
            eth_funding = funding_data.get('results', {}).get('ETHUSDT', {})
            
            btc_rate = btc_funding.get('current_funding_rate', 0) * 100
            eth_rate = eth_funding.get('current_funding_rate', 0) * 100
            
            if abs(btc_rate) > 0.1 or abs(eth_rate) > 0.1:  # 资金费率绝对值超过0.1%
                summary['alerts'].append(f"资金费率较高 - BTC: {btc_rate:.4f}%, ETH: {eth_rate:.4f}%")
            
            summary['key_findings'].append(f"当前资金费率 - BTC: {btc_rate:.4f}%, ETH: {eth_rate:.4f}%")
            
            # 生成建议
            if summary['overall_status'] == 'warning':
                summary['recommendations'].append("建议密切监控系统性能指标")
                summary['recommendations'].append("检查网络连接和数据同步状态")
            else:
                summary['recommendations'].append("系统运行正常，继续监控")
            
            return summary
            
        except Exception as e:
            logger.error(f"生成执行摘要失败: {e}")
            return {
                'overall_status': 'error',
                'key_findings': [],
                'alerts': ['执行摘要生成失败'],
                'recommendations': ['请检查系统状态']
            }
    
    async def generate_visual_report(self, hours: int = 1) -> Dict[str, str]:
        """
        生成可视化报告
        
        Args:
            hours: 分析时间范围
            
        Returns:
            图表文件路径字典
        """
        logger.info("开始生成可视化报告...")
        
        try:
            # 获取分析数据
            report = await self.generate_comprehensive_report(hours)
            
            # 生成各类图表
            chart_files = {}
            
            # 延时分析图表
            if report.get('latency_analysis'):
                chart_files['latency'] = self.chart_generator.create_latency_chart(
                    report['latency_analysis']
                )
            
            # K线分析图表
            if report.get('kline_analysis'):
                chart_files['kline'] = self.chart_generator.create_kline_chart(
                    report['kline_analysis']
                )
            
            # 深度分析图表
            if report.get('depth_analysis'):
                chart_files['depth'] = self.chart_generator.create_depth_chart(
                    report['depth_analysis']
                )
            
            # 价格分析图表
            if report.get('price_analysis'):
                chart_files['price'] = self.chart_generator.create_price_chart(
                    report['price_analysis']
                )
            
            # 资金费率分析图表
            if report.get('funding_analysis'):
                chart_files['funding'] = self.chart_generator.create_funding_chart(
                    report['funding_analysis']
                )
            
            # 综合仪表板
            dashboard_data = {
                'latency': report.get('latency_analysis', {}),
                'kline': report.get('kline_analysis', {}),
                'depth': report.get('depth_analysis', {}),
                'price': report.get('price_analysis', {}),
                'funding': report.get('funding_analysis', {})
            }
            
            chart_files['dashboard'] = self.chart_generator.create_summary_dashboard(dashboard_data)
            
            logger.info(f"可视化报告生成完成，共生成{len(chart_files)}个图表")
            return chart_files
            
        except Exception as e:
            logger.error(f"生成可视化报告失败: {e}")
            return {}
    
    async def save_report_to_file(self, report: Dict, filename: str = None) -> str:
        """
        保存报告到文件
        
        Args:
            report: 报告数据
            filename: 文件名（可选）
            
        Returns:
            保存的文件路径
        """
        try:
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"crypto_analysis_report_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"报告已保存到: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"保存报告失败: {e}")
            return ""
    
    def _empty_report(self) -> Dict:
        """返回空报告"""
        return {
            'report_time': datetime.now().isoformat(),
            'analysis_period_hours': 0,
            'latency_analysis': {},
            'kline_analysis': {},
            'depth_analysis': {},
            'price_analysis': {},
            'funding_analysis': {},
            'executive_summary': {
                'overall_status': 'error',
                'key_findings': [],
                'alerts': ['报告生成失败'],
                'recommendations': ['请检查系统状态']
            }
        }
    
    async def run_realtime_monitoring(self, interval_minutes: int = 15):
        """
        运行实时监控
        
        Args:
            interval_minutes: 监控间隔（分钟）
        """
        logger.info(f"开始实时监控，间隔: {interval_minutes}分钟")
        
        while True:
            try:
                # 生成实时报告
                report = await self.generate_comprehensive_report(hours=1)
                
                # 检查是否有警告
                summary = report.get('executive_summary', {})
                if summary.get('overall_status') == 'warning':
                    logger.warning("检测到系统警告:")
                    for alert in summary.get('alerts', []):
                        logger.warning(f"  - {alert}")
                
                # 保存报告
                await self.save_report_to_file(report)
                
                # 等待下一次监控
                await asyncio.sleep(interval_minutes * 60)
                
            except Exception as e:
                logger.error(f"实时监控出错: {e}")
                await asyncio.sleep(60)  # 出错时等待1分钟后重试
