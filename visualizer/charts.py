#!/usr/bin/env python3
"""
图表生成器
生成简洁易懂的图表，让管理者一目了然地了解问题和差异
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any
import numpy as np
from utils.logging import setup_logger

logger = setup_logger(__name__)

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
sns.set_palette("husl")

class ChartGenerator:
    """图表生成器"""

    def __init__(self):
        self.figure_size = (12, 8)
        self.dpi = 100

    def create_latency_chart(self, latency_data: Dict) -> str:
        """
        创建延时分析图表

        Args:
            latency_data: 延时分析数据

        Returns:
            图表文件路径
        """
        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=self.figure_size, dpi=self.dpi)
            fig.suptitle('ETHUSDT 价格延时分析', fontsize=16, fontweight='bold')

            # 1. 延时统计概览
            if latency_data.get('results', {}).get('ETHUSDT', {}).get('total_matches', 0) > 0:
                eth_data = latency_data['results']['ETHUSDT']

                # 延时分布
                ax1.bar(['平均延时', '最小延时', '最大延时'],
                       [eth_data['avg_latency_ms'], eth_data['min_latency_ms'], eth_data['max_latency_ms']],
                       color=['#2E8B57', '#4169E1', '#DC143C'])
                ax1.set_title('延时分布 (毫秒)', fontweight='bold')
                ax1.set_ylabel('延时 (ms)')

                # 成功率
                success_rate = eth_data['success_rate']
                ax2.pie([success_rate, 100-success_rate], labels=['成功匹配', '未匹配'],
                       colors=['#32CD32', '#FFB6C1'], autopct='%1.1f%%')
                ax2.set_title(f'价格匹配成功率\n总匹配次数: {success_rate}', fontweight='bold')

                # 价格差异分布
                ax3.hist([match['price_diff_pct'] for match in eth_data.get('matches', [])],
                        bins=20, color='skyblue', alpha=0.7, edgecolor='black')
                ax3.set_title('价格差异分布', fontweight='bold')
                ax3.set_xlabel('价格差异 (%)')
                ax3.set_ylabel('频次')

                # 延时趋势
                matches = eth_data.get('matches', [])
                if matches:
                    times = [i for i in range(len(matches))]
                    latencies = [match['latency_ms'] for match in matches]
                    ax4.plot(times, latencies, marker='o', linewidth=2, markersize=4)
                    ax4.set_title('延时趋势', fontweight='bold')
                    ax4.set_xlabel('样本序号')
                    ax4.set_ylabel('延时 (ms)')
                else:
                    ax4.text(0.5, 0.5, '暂无数据', ha='center', va='center', transform=ax4.transAxes)
                    ax4.set_title('延时趋势', fontweight='bold')
            else:
                for ax in [ax1, ax2, ax3, ax4]:
                    ax.text(0.5, 0.5, '暂无数据', ha='center', va='center', transform=ax.transAxes)

            plt.tight_layout()
            filename = f'latency_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
            plt.savefig(filename, bbox_inches='tight', dpi=self.dpi)
            plt.close()

            logger.info(f"延时分析图表已保存: {filename}")
            return filename

        except Exception as e:
            logger.error(f"创建延时图表失败: {e}")
            return ""

    def create_kline_chart(self, kline_data: Dict) -> str:
        """创建K线分析图表"""
        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=self.figure_size, dpi=self.dpi)
            fig.suptitle('K线连续相同分析 (0插针检测)', fontsize=16, fontweight='bold')

            symbols = ['BTCUSDT', 'ETHUSDT']
            colors = ['#FF6B6B', '#4ECDC4']

            # 1. 连续相同K线统计
            sequence_counts = []
            identical_rates = []

            for symbol in symbols:
                symbol_data = kline_data.get('results', {}).get(symbol, {})
                sequence_counts.append(symbol_data.get('total_sequences', 0))
                identical_rates.append(symbol_data.get('identical_rate_pct', 0))

            ax1.bar(symbols, sequence_counts, color=colors)
            ax1.set_title('连续相同K线序列数量', fontweight='bold')
            ax1.set_ylabel('序列数量')

            # 2. 相同K线比例
            ax2.bar(symbols, identical_rates, color=colors)
            ax2.set_title('相同K线比例', fontweight='bold')
            ax2.set_ylabel('比例 (%)')

            # 3. 序列长度分布
            btc_data = kline_data.get('results', {}).get('BTCUSDT', {})
            eth_data = kline_data.get('results', {}).get('ETHUSDT', {})

            lengths_data = []
            if btc_data.get('sequences'):
                lengths_data.extend([seq['count'] for seq in btc_data['sequences']])
            if eth_data.get('sequences'):
                lengths_data.extend([seq['count'] for seq in eth_data['sequences']])

            if lengths_data:
                ax3.hist(lengths_data, bins=10, color='lightcoral', alpha=0.7, edgecolor='black')
                ax3.set_title('序列长度分布', fontweight='bold')
                ax3.set_xlabel('连续K线数量')
                ax3.set_ylabel('频次')
            else:
                ax3.text(0.5, 0.5, '暂无连续相同K线', ha='center', va='center', transform=ax3.transAxes)
                ax3.set_title('序列长度分布', fontweight='bold')

            # 4. 对比分析
            metrics = ['最大序列长度', '平均序列长度', '总相同K线数']
            btc_values = [
                btc_data.get('max_sequence_length', 0),
                btc_data.get('avg_sequence_length', 0),
                btc_data.get('total_identical_klines', 0)
            ]
            eth_values = [
                eth_data.get('max_sequence_length', 0),
                eth_data.get('avg_sequence_length', 0),
                eth_data.get('total_identical_klines', 0)
            ]

            x = np.arange(len(metrics))
            width = 0.35

            ax4.bar(x - width/2, btc_values, width, label='BTCUSDT', color=colors[0])
            ax4.bar(x + width/2, eth_values, width, label='ETHUSDT', color=colors[1])
            ax4.set_title('BTC vs ETH 对比', fontweight='bold')
            ax4.set_xticks(x)
            ax4.set_xticklabels(metrics, rotation=45)
            ax4.legend()

            plt.tight_layout()
            filename = f'kline_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
            plt.savefig(filename, bbox_inches='tight', dpi=self.dpi)
            plt.close()

            logger.info(f"K线分析图表已保存: {filename}")
            return filename

        except Exception as e:
            logger.error(f"创建K线图表失败: {e}")
            return ""

    def create_depth_chart(self, depth_data: Dict) -> str:
        """创建深度对比图表"""
        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=self.figure_size, dpi=self.dpi)
            fig.suptitle('Bitda vs Binance 深度对比分析', fontsize=16, fontweight='bold')

            symbols = ['BTCUSDT', 'ETHUSDT']
            colors = ['#FF6B6B', '#4ECDC4']

            # 1. 深度比值统计
            avg_ratios = []
            advantage_pcts = []

            for symbol in symbols:
                symbol_data = depth_data.get('results', {}).get(symbol, {})
                avg_ratios.append(symbol_data.get('avg_depth_ratio', 0))
                advantage_pcts.append(symbol_data.get('bitda_advantage_pct', 0))

            ax1.bar(symbols, avg_ratios, color=colors)
            ax1.axhline(y=1.0, color='red', linestyle='--', label='平衡线')
            ax1.set_title('平均深度比值 (Bitda/Binance)', fontweight='bold')
            ax1.set_ylabel('比值')
            ax1.legend()

            # 2. Bitda优势百分比
            ax2.bar(symbols, advantage_pcts, color=colors)
            ax2.set_title('Bitda深度优势比例', fontweight='bold')
            ax2.set_ylabel('优势比例 (%)')

            # 3. 深度比值分布（以BTCUSDT为例）
            btc_data = depth_data.get('results', {}).get('BTCUSDT', {})
            samples = btc_data.get('samples', [])

            if samples:
                ratios = [sample['depth_ratio'] for sample in samples]
                ax3.hist(ratios, bins=15, color='lightblue', alpha=0.7, edgecolor='black')
                ax3.axvline(x=1.0, color='red', linestyle='--', label='平衡线')
                ax3.set_title('BTCUSDT 深度比值分布', fontweight='bold')
                ax3.set_xlabel('深度比值')
                ax3.set_ylabel('频次')
                ax3.legend()
            else:
                ax3.text(0.5, 0.5, '暂无数据', ha='center', va='center', transform=ax3.transAxes)
                ax3.set_title('BTCUSDT 深度比值分布', fontweight='bold')

            # 4. 对比总结
            metrics = ['平均比值', 'Bitda优势%', '对比次数']
            btc_values = [
                btc_data.get('avg_depth_ratio', 0),
                btc_data.get('bitda_advantage_pct', 0),
                btc_data.get('total_comparisons', 0)
            ]

            eth_data = depth_data.get('results', {}).get('ETHUSDT', {})
            eth_values = [
                eth_data.get('avg_depth_ratio', 0),
                eth_data.get('bitda_advantage_pct', 0),
                eth_data.get('total_comparisons', 0)
            ]

            x = np.arange(len(metrics))
            width = 0.35

            ax4.bar(x - width/2, btc_values, width, label='BTCUSDT', color=colors[0])
            ax4.bar(x + width/2, eth_values, width, label='ETHUSDT', color=colors[1])
            ax4.set_title('深度对比总结', fontweight='bold')
            ax4.set_xticks(x)
            ax4.set_xticklabels(metrics)
            ax4.legend()

            plt.tight_layout()
            filename = f'depth_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
            plt.savefig(filename, bbox_inches='tight', dpi=self.dpi)
            plt.close()

            logger.info(f"深度分析图表已保存: {filename}")
            return filename

        except Exception as e:
            logger.error(f"创建深度图表失败: {e}")
            return ""

    def create_price_chart(self, price_data: Dict) -> str:
        """创建价格差值分析图表"""
        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=self.figure_size, dpi=self.dpi)
            fig.suptitle('标记价格 vs 最新价格差值分析', fontsize=16, fontweight='bold')

            symbols = ['BTCUSDT', 'ETHUSDT']
            colors = ['#FF6B6B', '#4ECDC4']

            # 1. 平均差值对比
            avg_diffs = []
            max_diffs = []

            for symbol in symbols:
                symbol_data = price_data.get('results', {}).get(symbol, {})
                avg_diffs.append(symbol_data.get('avg_rel_diff_pct', 0))
                max_diffs.append(symbol_data.get('max_rel_diff_pct', 0))

            x = np.arange(len(symbols))
            width = 0.35

            ax1.bar(x - width/2, avg_diffs, width, label='平均差值', color=colors[0])
            ax1.bar(x + width/2, max_diffs, width, label='最大差值', color=colors[1])
            ax1.set_title('价格差值统计 (%)', fontweight='bold')
            ax1.set_ylabel('差值 (%)')
            ax1.set_xticks(x)
            ax1.set_xticklabels(symbols)
            ax1.legend()

            # 2. 异常情况统计
            high_diff_rates = []
            for symbol in symbols:
                symbol_data = price_data.get('results', {}).get(symbol, {})
                high_diff_rates.append(symbol_data.get('high_diff_rate_pct', 0))

            ax2.bar(symbols, high_diff_rates, color=['orange', 'red'])
            ax2.set_title('异常差值比例 (>0.1%)', fontweight='bold')
            ax2.set_ylabel('异常比例 (%)')

            # 3. 趋势分析
            trend_data = []
            trend_labels = []

            for symbol in symbols:
                symbol_data = price_data.get('results', {}).get(symbol, {})
                early_avg = symbol_data.get('early_period_avg_pct', 0)
                recent_avg = symbol_data.get('recent_period_avg_pct', 0)
                trend_data.append([early_avg, recent_avg])
                trend_labels.append(symbol)

            if trend_data:
                x = np.arange(len(trend_labels))
                width = 0.35

                early_values = [data[0] for data in trend_data]
                recent_values = [data[1] for data in trend_data]

                ax3.bar(x - width/2, early_values, width, label='早期平均', color='lightblue')
                ax3.bar(x + width/2, recent_values, width, label='近期平均', color='darkblue')
                ax3.set_title('价格差值趋势对比', fontweight='bold')
                ax3.set_ylabel('差值 (%)')
                ax3.set_xticks(x)
                ax3.set_xticklabels(trend_labels)
                ax3.legend()

            # 4. 差值分布（以BTCUSDT为例）
            btc_data = price_data.get('results', {}).get('BTCUSDT', {})
            samples = btc_data.get('samples', [])

            if samples:
                diff_values = [sample['rel_diff_pct'] for sample in samples]
                ax4.hist(diff_values, bins=15, color='lightgreen', alpha=0.7, edgecolor='black')
                ax4.set_title('BTCUSDT 价格差值分布', fontweight='bold')
                ax4.set_xlabel('相对差值 (%)')
                ax4.set_ylabel('频次')
            else:
                ax4.text(0.5, 0.5, '暂无数据', ha='center', va='center', transform=ax4.transAxes)
                ax4.set_title('BTCUSDT 价格差值分布', fontweight='bold')

            plt.tight_layout()
            filename = f'price_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
            plt.savefig(filename, bbox_inches='tight', dpi=self.dpi)
            plt.close()

            logger.info(f"价格分析图表已保存: {filename}")
            return filename

        except Exception as e:
            logger.error(f"创建价格图表失败: {e}")
            return ""

    def create_funding_chart(self, funding_data: Dict) -> str:
        """创建资金费率分析图表"""
        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=self.figure_size, dpi=self.dpi)
            fig.suptitle('资金费率分析 (一天3次收取)', fontsize=16, fontweight='bold')

            symbols = ['BTCUSDT', 'ETHUSDT']
            colors = ['#FF6B6B', '#4ECDC4']

            # 1. 当前资金费率对比
            current_rates = []
            avg_rates = []

            for symbol in symbols:
                symbol_data = funding_data.get('results', {}).get(symbol, {})
                current_rates.append(symbol_data.get('current_funding_rate', 0) * 100)  # 转换为百分比
                avg_rates.append(symbol_data.get('avg_funding_rate', 0) * 100)

            x = np.arange(len(symbols))
            width = 0.35

            ax1.bar(x - width/2, current_rates, width, label='当前费率', color=colors[0])
            ax1.bar(x + width/2, avg_rates, width, label='平均费率', color=colors[1])
            ax1.set_title('资金费率对比 (%)', fontweight='bold')
            ax1.set_ylabel('费率 (%)')
            ax1.set_xticks(x)
            ax1.set_xticklabels(symbols)
            ax1.legend()

            # 2. 费率波动性
            volatilities = []
            for symbol in symbols:
                symbol_data = funding_data.get('results', {}).get(symbol, {})
                volatilities.append(symbol_data.get('funding_rate_volatility', 0) * 100)

            ax2.bar(symbols, volatilities, color=['purple', 'orange'])
            ax2.set_title('资金费率波动性', fontweight='bold')
            ax2.set_ylabel('波动性 (%)')

            # 3. 资金费率时间分布（以BTCUSDT为例）
            btc_data = funding_data.get('results', {}).get('BTCUSDT', {})
            funding_periods = btc_data.get('funding_periods', {})

            if funding_periods:
                times = ['00:00', '08:00', '16:00']
                rates = []

                for time_str in times:
                    key = f'funding_{time_str}_avg'
                    rate = funding_periods.get(key, 0) * 100
                    rates.append(rate)

                ax3.bar(times, rates, color=['darkblue', 'darkgreen', 'darkred'])
                ax3.set_title('BTCUSDT 分时段资金费率', fontweight='bold')
                ax3.set_ylabel('费率 (%)')
                ax3.set_xlabel('时间 (UTC)')
            else:
                ax3.text(0.5, 0.5, '暂无分时段数据', ha='center', va='center', transform=ax3.transAxes)
                ax3.set_title('BTCUSDT 分时段资金费率', fontweight='bold')

            # 4. 趋势分析
            trend_info = []
            trend_strengths = []

            for symbol in symbols:
                symbol_data = funding_data.get('results', {}).get(symbol, {})
                trend_direction = symbol_data.get('trend_direction', 'unknown')
                trend_strength = symbol_data.get('trend_strength_pct', 0)

                trend_info.append(trend_direction)
                trend_strengths.append(trend_strength)

            # 趋势强度条形图
            colors_trend = ['green' if trend == 'increasing' else 'red' if trend == 'decreasing' else 'gray'
                           for trend in trend_info]

            ax4.bar(symbols, trend_strengths, color=colors_trend)
            ax4.set_title('资金费率趋势强度', fontweight='bold')
            ax4.set_ylabel('趋势强度 (%)')

            # 添加趋势方向标注
            for i, (symbol, direction) in enumerate(zip(symbols, trend_info)):
                ax4.text(i, trend_strengths[i] + 0.01, direction, ha='center', va='bottom')

            plt.tight_layout()
            filename = f'funding_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
            plt.savefig(filename, bbox_inches='tight', dpi=self.dpi)
            plt.close()

            logger.info(f"资金费率分析图表已保存: {filename}")
            return filename

        except Exception as e:
            logger.error(f"创建资金费率图表失败: {e}")
            return ""

    def create_summary_dashboard(self, all_data: Dict) -> str:
        """创建综合仪表板"""
        try:
            fig = plt.figure(figsize=(16, 12), dpi=self.dpi)
            fig.suptitle('加密货币数据分析综合仪表板', fontsize=20, fontweight='bold')

            # 创建网格布局
            gs = fig.add_gridspec(3, 4, hspace=0.3, wspace=0.3)

            # 1. 延时概览
            ax1 = fig.add_subplot(gs[0, 0])
            latency_data = all_data.get('latency', {})
            eth_latency = latency_data.get('results', {}).get('ETHUSDT', {})

            if eth_latency.get('total_matches', 0) > 0:
                ax1.text(0.5, 0.7, f"匹配次数: {eth_latency['total_matches']}",
                        ha='center', va='center', transform=ax1.transAxes, fontsize=12)
                ax1.text(0.5, 0.5, f"平均延时: {eth_latency['avg_latency_ms']:.1f}ms",
                        ha='center', va='center', transform=ax1.transAxes, fontsize=12)
                ax1.text(0.5, 0.3, f"价格差异: {eth_latency['avg_price_diff_pct']:.3f}%",
                        ha='center', va='center', transform=ax1.transAxes, fontsize=12)
            else:
                ax1.text(0.5, 0.5, '暂无延时数据', ha='center', va='center', transform=ax1.transAxes)
            ax1.set_title('ETHUSDT 延时', fontweight='bold')
            ax1.set_xticks([])
            ax1.set_yticks([])

            # 2-3. K线分析
            kline_data = all_data.get('kline', {})
            for i, symbol in enumerate(['BTCUSDT', 'ETHUSDT']):
                ax = fig.add_subplot(gs[0, 1+i])
                symbol_data = kline_data.get('results', {}).get(symbol, {})

                sequences = symbol_data.get('total_sequences', 0)
                rate = symbol_data.get('identical_rate_pct', 0)

                ax.text(0.5, 0.7, f"连续序列: {sequences}",
                       ha='center', va='center', transform=ax.transAxes, fontsize=12)
                ax.text(0.5, 0.3, f"相同比例: {rate:.2f}%",
                       ha='center', va='center', transform=ax.transAxes, fontsize=12)
                ax.set_title(f'{symbol} K线', fontweight='bold')
                ax.set_xticks([])
                ax.set_yticks([])

            # 4. 资金费率对比
            ax4 = fig.add_subplot(gs[0, 3])
            funding_data = all_data.get('funding', {})
            btc_funding = funding_data.get('results', {}).get('BTCUSDT', {}).get('current_funding_rate', 0) * 100
            eth_funding = funding_data.get('results', {}).get('ETHUSDT', {}).get('current_funding_rate', 0) * 100

            ax4.text(0.5, 0.7, f"BTC: {btc_funding:.4f}%",
                    ha='center', va='center', transform=ax4.transAxes, fontsize=12)
            ax4.text(0.5, 0.3, f"ETH: {eth_funding:.4f}%",
                    ha='center', va='center', transform=ax4.transAxes, fontsize=12)
            ax4.set_title('当前资金费率', fontweight='bold')
            ax4.set_xticks([])
            ax4.set_yticks([])

            # 5-6. 深度对比
            depth_data = all_data.get('depth', {})
            for i, symbol in enumerate(['BTCUSDT', 'ETHUSDT']):
                ax = fig.add_subplot(gs[1, i*2:(i+1)*2])
                symbol_data = depth_data.get('results', {}).get(symbol, {})

                ratio = symbol_data.get('avg_depth_ratio', 0)
                advantage = symbol_data.get('bitda_advantage_pct', 0)

                # 创建简单的比较图
                categories = ['深度比值', 'Bitda优势%']
                values = [ratio, advantage]
                colors = ['lightblue', 'lightcoral']

                bars = ax.bar(categories, values, color=colors)
                ax.set_title(f'{symbol} 深度对比', fontweight='bold')
                ax.set_ylabel('数值')

                # 添加数值标签
                for bar, value in zip(bars, values):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                           f'{value:.2f}', ha='center', va='bottom')

            # 7-8. 价格差值
            price_data = all_data.get('price', {})
            for i, symbol in enumerate(['BTCUSDT', 'ETHUSDT']):
                ax = fig.add_subplot(gs[2, i*2:(i+1)*2])
                symbol_data = price_data.get('results', {}).get(symbol, {})

                avg_diff = symbol_data.get('avg_rel_diff_pct', 0)
                max_diff = symbol_data.get('max_rel_diff_pct', 0)
                trend = symbol_data.get('trend_direction', 'unknown')

                # 创建差值对比图
                categories = ['平均差值%', '最大差值%']
                values = [avg_diff, max_diff]
                colors = ['lightgreen', 'orange']

                bars = ax.bar(categories, values, color=colors)
                ax.set_title(f'{symbol} 价格差值 ({trend})', fontweight='bold')
                ax.set_ylabel('差值 (%)')

                # 添加数值标签
                for bar, value in zip(bars, values):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                           f'{value:.4f}', ha='center', va='bottom')

            filename = f'dashboard_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
            plt.savefig(filename, bbox_inches='tight', dpi=self.dpi)
            plt.close()

            logger.info(f"综合仪表板已保存: {filename}")
            return filename

        except Exception as e:
            logger.error(f"创建综合仪表板失败: {e}")
            return ""
