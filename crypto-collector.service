[Unit]
Description=Crypto Data Collector Service
Documentation=https://github.com/crypto-data-collector
After=network.target mysql.service
Wants=network.target

[Service]
Type=simple
User=code
Group=code
WorkingDirectory=/home/<USER>/project/WS_DATA_ALL
Environment=PATH=/home/<USER>/anaconda3/bin:/usr/local/bin:/usr/bin:/bin
Environment=PYTHONPATH=/home/<USER>/project/WS_DATA_ALL
ExecStart=/home/<USER>/anaconda3/bin/python main.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=append:/home/<USER>/project/WS_DATA_ALL/crypto_collector.log
StandardError=append:/home/<USER>/project/WS_DATA_ALL/crypto_collector_error.log

# 资源限制
LimitNOFILE=65536
MemoryMax=2G

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/home/<USER>/project/WS_DATA_ALL

[Install]
WantedBy=multi-user.target
