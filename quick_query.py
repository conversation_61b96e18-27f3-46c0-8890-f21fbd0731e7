#!/usr/bin/env python3
"""
快速查询指定时间段的数据
"""

import mysql.connector
from datetime import datetime, timedelta

def quick_query():
    """快速查询"""
    
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'Linuxtest',
        'database': 'depth_db'
    }
    
    # UTC+8: 12:00-13:00 = UTC: 04:00-05:00
    start_time = '2025-05-30 04:00:00'
    end_time = '2025-05-30 05:00:00'
    
    print("📊 快速数据查询")
    print(f"时间段 (UTC): {start_time} ~ {end_time}")
    print(f"时间段 (UTC+8): 2025-05-30 12:00:00 ~ 2025-05-30 13:00:00")
    
    try:
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        # 查询Bitda数据数量
        print("\n📋 Bitda数据:")
        cursor.execute("""
            SELECT COUNT(*) as total_records,
                   MIN(created_at) as earliest,
                   MAX(created_at) as latest
            FROM bitda_depth 
            WHERE symbol = 'ETHUSDT'
            AND created_at >= %s 
            AND created_at < %s
        """, (start_time, end_time))
        
        result = cursor.fetchone()
        if result:
            total, earliest, latest = result
            print(f"  - 总记录数: {total}")
            if earliest and latest:
                print(f"  - 时间范围: {earliest} ~ {latest} (UTC)")
                print(f"  - 时间范围: {earliest + timedelta(hours=8)} ~ {latest + timedelta(hours=8)} (UTC+8)")
        
        # 查询Binance数据数量
        print("\n📊 Binance数据:")
        cursor.execute("""
            SELECT COUNT(*) as total_records,
                   COUNT(DISTINCT CONCAT(bid_price, '-', ask_price)) as unique_price_pairs
            FROM binance_bookticker 
            WHERE symbol = 'ETHUSDT'
            AND FROM_UNIXTIME(event_time/1000) >= %s 
            AND FROM_UNIXTIME(event_time/1000) < %s
        """, (start_time, end_time))
        
        result = cursor.fetchone()
        if result:
            total, unique_pairs = result
            print(f"  - 总记录数: {total}")
            print(f"  - 唯一价格对: {unique_pairs}")
        
        # 查询一些样本数据
        print("\n📋 Bitda样本数据 (前3条):")
        cursor.execute("""
            SELECT timestamp, created_at, 
                   JSON_EXTRACT(bids, '$[0][0]') as bid_price_1,
                   JSON_EXTRACT(asks, '$[0][0]') as ask_price_1
            FROM bitda_depth 
            WHERE symbol = 'ETHUSDT'
            AND created_at >= %s 
            AND created_at < %s
            ORDER BY created_at
            LIMIT 3
        """, (start_time, end_time))
        
        results = cursor.fetchall()
        for i, row in enumerate(results, 1):
            timestamp, created_at, bid_price, ask_price = row
            utc8_time = created_at + timedelta(hours=8)
            print(f"  {i}. {utc8_time.strftime('%H:%M:%S')} 买一:{bid_price} 卖一:{ask_price}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")

if __name__ == "__main__":
    quick_query()
