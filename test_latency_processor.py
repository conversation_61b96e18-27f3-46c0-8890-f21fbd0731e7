#!/usr/bin/env python3
"""
测试版延时数据处理器 - 处理少量数据进行验证
"""

import mysql.connector
import json
from datetime import datetime, timedelta

class TestLatencyProcessor:
    """测试版延时处理器"""

    def __init__(self):
        self.source_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'depth_db'
        }

        self.target_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'ethusdt_latency_db'
        }

    def parse_bitda_depth(self, asks_json, bids_json):
        """解析Bitda深度数据"""
        try:
            if not asks_json or not bids_json:
                return None, None, None, None

            asks_data = json.loads(asks_json) if isinstance(asks_json, str) else asks_json
            bids_data = json.loads(bids_json) if isinstance(bids_json, str) else bids_json

            if not asks_data or not bids_data:
                return None, None, None, None

            # 卖一价格 = asks中价格最小的
            ask_prices = [float(item[0]) for item in asks_data]
            ask_price_1 = min(ask_prices)
            ask_qty_1 = float([item[1] for item in asks_data if float(item[0]) == ask_price_1][0])

            # 买一价格 = bids中价格最大的
            bid_prices = [float(item[0]) for item in bids_data]
            bid_price_1 = max(bid_prices)
            bid_qty_1 = float([item[1] for item in bids_data if float(item[0]) == bid_price_1][0])

            return ask_price_1, ask_qty_1, bid_price_1, bid_qty_1

        except Exception as e:
            print(f"解析失败: {e}")
            return None, None, None, None

    def test_small_batch(self):
        """测试处理少量数据"""
        print("🧪 测试延时数据处理...")

        try:
            # 连接数据库
            source_conn = mysql.connector.connect(**self.source_config)
            target_conn = mysql.connector.connect(**self.target_config)

            source_cursor = source_conn.cursor()
            target_cursor = target_conn.cursor()

            # 获取最近的10条Bitda记录
            print("📊 获取Bitda数据...")
            bitda_query = """
            SELECT timestamp, asks, bids, created_at
            FROM bitda_depth
            WHERE symbol = 'ETHUSDT'
            AND asks IS NOT NULL
            AND bids IS NOT NULL
            ORDER BY created_at DESC
            LIMIT 10
            """

            source_cursor.execute(bitda_query)
            bitda_records = source_cursor.fetchall()

            print(f"获取到 {len(bitda_records)} 条Bitda记录")

            matches_found = 0

            for i, record in enumerate(bitda_records):
                bitda_timestamp, asks_json, bids_json, created_at = record

                print(f"\n处理记录 {i+1}: {created_at}")

                # 解析深度数据
                ask_price_1, ask_qty_1, bid_price_1, bid_qty_1 = self.parse_bitda_depth(asks_json, bids_json)

                if ask_price_1 is None or bid_price_1 is None:
                    print("  ❌ 解析失败")
                    continue

                print(f"  📈 Bitda买一: {bid_price_1}, 卖一: {ask_price_1}")

                # 查找买一卖一价格完全匹配的Binance数据 (使用首次出现时间)
                binance_query = """
                SELECT bid_price, ask_price, bid_qty, ask_qty, event_time
                FROM binance_bookticker
                WHERE symbol = 'ETHUSDT'
                AND bid_price = %s
                AND ask_price = %s
                AND event_time < %s
                ORDER BY event_time ASC
                LIMIT 1
                """

                source_cursor.execute(binance_query, (bid_price_1, ask_price_1, bitda_timestamp))
                binance_match = source_cursor.fetchone()

                if binance_match:
                    binance_bid_price, binance_ask_price, binance_bid_qty, binance_ask_qty, binance_timestamp = binance_match
                    latency = bitda_timestamp - binance_timestamp

                    print(f"  ✅ 完全匹配: Binance买一={binance_bid_price}, 卖一={binance_ask_price}, 延时={latency}ms")

                    if 10 <= latency <= 2000:
                        # 插入完全匹配记录
                        insert_query = """
                        INSERT INTO ethusdt_latency_matches
                        (bitda_timestamp, binance_timestamp, latency_ms, match_type,
                         bitda_price, binance_price, bitda_qty, binance_qty,
                         price_spread, match_quality)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """

                        # 计算价差 (卖一价 - 买一价)
                        price_spread = ask_price_1 - bid_price_1

                        target_cursor.execute(insert_query, (
                            bitda_timestamp, binance_timestamp, latency, 'complete',
                            (bid_price_1 + ask_price_1) / 2,  # 使用中间价作为代表价格
                            (binance_bid_price + binance_ask_price) / 2,  # Binance中间价
                            (bid_qty_1 + ask_qty_1) / 2,  # 平均数量
                            (binance_bid_qty + binance_ask_qty) / 2,  # Binance平均数量
                            price_spread, 1.0000
                        ))

                        matches_found += 1
                        print(f"  💾 已保存完全匹配记录 (价差: {price_spread:.2f})")
                    else:
                        if latency < 10:
                            print(f"  ⚠️  延时过小被过滤: {latency}ms")
                        else:
                            print(f"  ⚠️  延时过大被过滤: {latency}ms")
                else:
                    print(f"  ❌ 买一卖一价格无完全匹配")

            # 提交事务
            target_conn.commit()

            print(f"\n📈 测试完成:")
            print(f"  - 处理记录: {len(bitda_records)}")
            print(f"  - 找到匹配: {matches_found}")

            # 检查插入结果
            target_cursor.execute("SELECT COUNT(*) FROM ethusdt_latency_matches")
            total_matches = target_cursor.fetchone()[0]
            print(f"  - 数据库总匹配数: {total_matches}")

            if total_matches > 0:
                # 显示最新的几条记录
                target_cursor.execute("""
                    SELECT latency_ms, match_type, bitda_price, binance_price, created_at
                    FROM ethusdt_latency_matches
                    ORDER BY created_at DESC
                    LIMIT 5
                """)

                recent_matches = target_cursor.fetchall()
                print(f"\n📋 最新匹配记录:")
                for match in recent_matches:
                    latency, match_type, bitda_price, binance_price, created_at = match
                    print(f"  - {created_at}: {match_type} {latency}ms (Bitda:{bitda_price} vs Binance:{binance_price})")

            source_cursor.close()
            target_cursor.close()
            source_conn.close()
            target_conn.close()

            return matches_found > 0

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False

def main():
    """主函数"""
    print("🧪 延时数据处理测试")
    print("=" * 40)

    processor = TestLatencyProcessor()

    if processor.test_small_batch():
        print("\n✅ 测试成功！延时数据处理正常")
    else:
        print("\n❌ 测试失败！需要检查数据或逻辑")

if __name__ == "__main__":
    main()
