#!/usr/bin/env python3
"""
测试新的数据获取逻辑
"""

import mysql.connector
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_latest_data_points(symbol='BTCUSDT'):
    """测试获取最新数据点"""
    print(f"🔍 测试获取 {symbol} 最新数据点")
    print("=" * 40)
    
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'Linuxtest',
        'database': 'depth_db'
    }
    
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        # 获取Bitda最新数据
        print("📊 获取Bitda最新数据...")
        cursor.execute("""
            SELECT timestamp, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1
            FROM bitda_depth 
            WHERE symbol = %s AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
            ORDER BY timestamp DESC 
            LIMIT 1
        """, (symbol,))
        
        bitda_latest = cursor.fetchone()
        if not bitda_latest:
            print("❌ 无Bitda数据")
            return
        
        bitda_timestamp = bitda_latest[0]
        bitda_dt = datetime.fromtimestamp(bitda_timestamp / 1000)
        print(f"   ✅ Bitda最新: {bitda_dt.strftime('%H:%M:%S.%f')[:-3]}")
        print(f"   📈 买一价: {bitda_latest[1]}, 卖一价: {bitda_latest[2]}")
        print(f"   📊 价差: {float(bitda_latest[2]) - float(bitda_latest[1]):.4f}")
        
        # 获取与Bitda时间最接近的Binance数据
        print("\n📊 获取Binance最接近数据...")
        cursor.execute("""
            SELECT event_time, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1
            FROM binance_depth_5 
            WHERE symbol = %s AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
            ORDER BY ABS(event_time - %s) ASC
            LIMIT 1
        """, (symbol, bitda_timestamp))
        
        binance_latest = cursor.fetchone()
        if not binance_latest:
            print("❌ 无Binance数据")
            return
        
        binance_timestamp = binance_latest[0]
        binance_dt = datetime.fromtimestamp(binance_timestamp / 1000)
        time_diff_ms = abs(bitda_timestamp - binance_timestamp)
        
        print(f"   ✅ Binance最近: {binance_dt.strftime('%H:%M:%S.%f')[:-3]}")
        print(f"   📈 买一价: {binance_latest[1]}, 卖一价: {binance_latest[2]}")
        print(f"   📊 价差: {float(binance_latest[2]) - float(binance_latest[1]):.4f}")
        print(f"   ⏰ 时间差: {time_diff_ms}ms")
        
        cursor.close()
        connection.close()
        
        print("\n🎯 深度对比数据:")
        print(f"   买一比值: {float(bitda_latest[3]) / float(binance_latest[3]):.2f}")
        print(f"   卖一比值: {float(bitda_latest[4]) / float(binance_latest[4]):.2f}")
        
        return {
            'bitda_timestamp': bitda_timestamp,
            'binance_timestamp': binance_timestamp,
            'time_diff_ms': time_diff_ms,
            'bitda_spread': float(bitda_latest[2]) - float(bitda_latest[1]),
            'binance_spread': float(binance_latest[2]) - float(binance_latest[1])
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None

def test_statistical_data(symbol='BTCUSDT'):
    """测试获取统计数据"""
    print(f"\n🔍 测试获取 {symbol} 统计数据")
    print("=" * 40)
    
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'Linuxtest',
        'database': 'depth_db'
    }
    
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        # 获取最新时间戳
        cursor.execute("""
            SELECT MAX(timestamp) FROM bitda_depth 
            WHERE symbol = %s
        """, (symbol,))
        latest_timestamp = cursor.fetchone()[0]
        
        # 计算统计时间范围 (最近5分钟)
        latest_dt = datetime.fromtimestamp(latest_timestamp / 1000)
        start_dt = latest_dt - timedelta(minutes=5)
        
        start_ts = int(start_dt.timestamp() * 1000)
        end_ts = latest_timestamp
        
        print(f"📅 统计时间范围: {start_dt.strftime('%H:%M:%S')} - {latest_dt.strftime('%H:%M:%S')}")
        
        # 获取Bitda统计数据
        cursor.execute("""
            SELECT timestamp, bid_price_1, ask_price_1
            FROM bitda_depth 
            WHERE symbol = %s AND timestamp BETWEEN %s AND %s
            AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
            ORDER BY timestamp DESC
            LIMIT 50
        """, (symbol, start_ts, end_ts))
        
        bitda_results = cursor.fetchall()
        
        # 获取Binance统计数据
        cursor.execute("""
            SELECT event_time, bid_price_1, ask_price_1
            FROM binance_depth_5 
            WHERE symbol = %s AND event_time BETWEEN %s AND %s
            AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
            ORDER BY event_time DESC
            LIMIT 50
        """, (symbol, start_ts, end_ts))
        
        binance_results = cursor.fetchall()
        
        print(f"📊 统计样本: Bitda {len(bitda_results)}条, Binance {len(binance_results)}条")
        
        if bitda_results and binance_results:
            # 计算Bitda价差统计
            bitda_spreads = []
            for row in bitda_results:
                spread = float(row[2]) - float(row[1])
                bitda_spreads.append(spread)
            
            # 计算Binance价差统计
            binance_spreads = []
            for row in binance_results:
                spread = float(row[2]) - float(row[1])
                binance_spreads.append(spread)
            
            print(f"\n📈 Bitda价差统计:")
            print(f"   最大: {max(bitda_spreads):.4f}")
            print(f"   最小: {min(bitda_spreads):.4f}")
            print(f"   平均: {sum(bitda_spreads)/len(bitda_spreads):.4f}")
            
            print(f"\n📈 Binance价差统计:")
            print(f"   最大: {max(binance_spreads):.4f}")
            print(f"   最小: {min(binance_spreads):.4f}")
            print(f"   平均: {sum(binance_spreads)/len(binance_spreads):.4f}")
        
        cursor.close()
        connection.close()
        
        return {
            'bitda_sample_count': len(bitda_results),
            'binance_sample_count': len(binance_results)
        }
        
    except Exception as e:
        print(f"❌ 统计测试失败: {e}")
        return None

def main():
    """主函数"""
    print("🔍 测试新的数据获取逻辑")
    print("=" * 50)
    print("测试内容:")
    print("  1. 深度对比: 取最新的一个数据点，计算时间差")
    print("  2. 深度统计: 统计某个时间范围内的值，不需要时间差")
    print("  3. 价差对比: 最近/最大/最小价差需要时间差，平均/中位数不需要")
    print()
    
    # 测试BTCUSDT
    latest_data = test_latest_data_points('BTCUSDT')
    stats_data = test_statistical_data('BTCUSDT')
    
    if latest_data and stats_data:
        print("\n🎯 验证结果:")
        print("  ✅ 深度对比: 使用最新数据点，时间差正确计算")
        print("  ✅ 深度统计: 使用统计数据，无需时间差")
        print("  ✅ 价差对比: 最近价差有时间差，统计价差无时间差")
        print(f"  📊 实际时间差: {latest_data['time_diff_ms']}ms")
        print(f"  📈 样本数量: Bitda {stats_data['bitda_sample_count']}, Binance {stats_data['binance_sample_count']}")
    else:
        print("\n❌ 测试失败")

if __name__ == "__main__":
    main()
