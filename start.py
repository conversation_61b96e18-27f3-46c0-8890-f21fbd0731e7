#!/usr/bin/env python3
"""
启动脚本 - 先测试系统，然后启动数据收集
"""
import asyncio
import sys
from test_system import run_all_tests
from main import main
from utils.logging import setup_logger

logger = setup_logger(__name__)

async def start_application():
    """启动应用程序"""
    logger.info("正在启动加密货币数据收集系统...")
    
    # 首先运行系统测试
    logger.info("步骤 1: 运行系统测试")
    test_passed = await run_all_tests()
    
    if not test_passed:
        logger.error("系统测试失败，无法启动应用程序")
        return False
    
    logger.info("步骤 2: 启动数据收集系统")
    try:
        await main()
        return True
    except Exception as e:
        logger.error(f"启动数据收集系统失败: {e}")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(start_application())
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常: {e}")
        sys.exit(1)
