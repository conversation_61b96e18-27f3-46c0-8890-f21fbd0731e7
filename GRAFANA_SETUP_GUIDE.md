# Grafana设置指南 - 查看数据变化

## 🎯 目标
让您在Grafana中看到实时的加密货币数据分析结果

## 📋 完整操作步骤

### 第1步: 启动数据导出器

```bash
# 启动数据导出器
python simple_data_exporter.py
```

您应该看到：
```
🚀 启动数据导出器...
📡 监听端口: 8000
🌐 访问地址: http://localhost:8000
📊 API文档: http://localhost:8000
按 Ctrl+C 停止服务
```

### 第2步: 验证数据导出器

在浏览器中访问: http://localhost:8000

您应该看到数据导出器的主页，显示所有可用的API端点。

测试API端点：
- http://localhost:8000/api/funding-rates (资金费率)
- http://localhost:8000/api/latency (延时数据)
- http://localhost:8000/api/depth-comparison (深度对比)
- http://localhost:8000/api/all-data (所有数据)

### 第3步: 访问Grafana

在浏览器中访问: http://localhost:3000

默认登录信息：
- 用户名: admin
- 密码: admin

### 第4步: 添加数据源

1. **点击左侧菜单的齿轮图标 ⚙️ (Configuration)**
2. **选择 "Data Sources"**
3. **点击 "Add data source"**
4. **选择 "JSON API" 或 "Infinity"** (如果没有，选择"TestData DB"用于演示)

配置数据源：
- **Name**: Crypto Data API
- **URL**: http://localhost:8000/api/all-data
- **Access**: Server (default)

5. **点击 "Save & Test"**

### 第5步: 导入仪表板

#### 方法1: 使用生成的JSON文件

1. **点击左侧菜单的 + 号**
2. **选择 "Import"**
3. **点击 "Upload JSON file"**
4. **选择项目目录中的 `crypto_dashboard.json` 文件**
5. **点击 "Load"**
6. **选择数据源为刚才创建的 "Crypto Data API"**
7. **点击 "Import"**

#### 方法2: 手动创建仪表板

1. **点击左侧菜单的 + 号**
2. **选择 "Dashboard"**
3. **点击 "Add new panel"**

### 第6步: 创建资金费率表格面板

1. **在Panel编辑器中:**
   - **Visualization**: 选择 "Table"
   - **Data source**: 选择 "Crypto Data API"
   - **Query**: 输入 `funding_rates`

2. **配置表格:**
   - **Title**: "资金费率对比"
   - **在 "Field" 选项卡中设置列名**

3. **点击 "Apply" 保存面板**

### 第7步: 创建延时分析面板

1. **添加新面板**
2. **配置:**
   - **Visualization**: 选择 "Stat" 或 "Time series"
   - **Data source**: 选择 "Crypto Data API"
   - **Query**: 输入 `latency`
   - **Title**: "ETHUSDT延时分析"

### 第8步: 创建深度对比面板

1. **添加新面板**
2. **配置:**
   - **Visualization**: 选择 "Table"
   - **Data source**: 选择 "Crypto Data API"
   - **Query**: 输入 `depth_comparison`
   - **Title**: "深度对比"

## 🔧 如果看不到数据变化

### 检查清单：

1. **数据导出器是否运行？**
   ```bash
   curl http://localhost:8000/api/all-data
   ```

2. **数据库是否有数据？**
   ```bash
   python -c "
   from analyzer.funding_analyzer import FundingAnalyzer
   analyzer = FundingAnalyzer()
   result = analyzer.get_latest_funding_rates()
   print(result)
   "
   ```

3. **Grafana数据源是否配置正确？**
   - 检查URL是否为 http://localhost:8000/api/all-data
   - 测试连接是否成功

4. **面板刷新频率**
   - 在仪表板右上角设置自动刷新（如30秒）

## 🚀 快速启动脚本

创建一个一键启动脚本：

```bash
#!/bin/bash
echo "🚀 启动完整的数据分析和可视化系统..."

# 启动数据导出器（后台运行）
echo "📡 启动数据导出器..."
python simple_data_exporter.py &
DATA_EXPORTER_PID=$!

# 等待数据导出器启动
sleep 3

# 检查数据导出器是否运行
if curl -s http://localhost:8000 > /dev/null; then
    echo "✅ 数据导出器启动成功"
else
    echo "❌ 数据导出器启动失败"
    exit 1
fi

# 打开浏览器
echo "🌐 打开Grafana..."
if command -v open &> /dev/null; then
    # macOS
    open http://localhost:3000
elif command -v xdg-open &> /dev/null; then
    # Linux
    xdg-open http://localhost:3000
fi

echo "📊 系统已启动！"
echo "Grafana: http://localhost:3000 (admin/admin)"
echo "数据API: http://localhost:8000"
echo ""
echo "按任意键停止服务..."
read -n 1

# 停止数据导出器
kill $DATA_EXPORTER_PID
echo "⏹️  服务已停止"
```

## 📊 预期看到的数据

### 资金费率表格
```
Symbol   | Current Rate | Next Rate    | Predict Rate
---------|--------------|--------------|-------------
BTCUSDT  | 0.05200%     | 0.00781%     | 0.00591%
ETHUSDT  | -0.04906%    | 0.00835%     | 0.00123%
```

### 延时分析
```
匹配记录数: 1234
平均延时: 45.67ms
最小延时: 12ms
最大延时: 156ms
中位数延时: 42.34ms
```

### 深度对比
```
Symbol   | Bitda Sum | Binance Sum | Ratio
---------|-----------|-------------|-------
BTCUSDT  | 15.0      | 5.0         | 3.0
ETHUSDT  | 20.0      | 4.0         | 5.0
```

## 🔍 故障排除

### 常见问题：

1. **"No data" 显示**
   - 检查数据源URL
   - 确认API返回数据
   - 检查查询语法

2. **连接失败**
   - 确认数据导出器在运行
   - 检查端口8000是否被占用
   - 确认防火墙设置

3. **数据不更新**
   - 设置面板自动刷新
   - 检查数据库是否有新数据
   - 重启数据导出器

### 调试命令：

```bash
# 检查数据导出器状态
curl http://localhost:8000/api/all-data | jq

# 检查数据库连接
python test_analysis.py

# 查看进程状态
./check_processes.sh
```

## 🎉 成功标志

当一切正常时，您应该能在Grafana中看到：
- 实时更新的资金费率数据
- ETHUSDT延时分析图表
- Bitda vs Binance深度对比表格
- 标记价格偏差统计

数据会根据您设置的刷新频率自动更新！
