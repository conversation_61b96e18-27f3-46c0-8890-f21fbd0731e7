#!/usr/bin/env python3
"""
创建最简单的工作仪表板
确保基础功能正常
"""

import requests
import json

def create_simple_working_dashboard():
    """创建最简单的工作仪表板"""
    print("🎨 创建最简单的工作仪表板...")
    
    session = requests.Session()
    session.auth = ("admin", "admin")
    datasource_uid = "cenigejcatslce"  # WorkingDepthDB
    
    dashboard_config = {
        "dashboard": {
            "id": None,
            "title": "🔄 简单工作版 - 1分钟刷新",
            "tags": ["simple", "working"],
            "timezone": "browser",
            "panels": [
                # 1. BTCUSDT深度对比 - 最简单版
                {
                    "id": 1,
                    "title": "📊 BTCUSDT 深度对比",
                    "type": "table",
                    "gridPos": {"h": 8, "w": 8, "x": 0, "y": 0},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            SELECT 
                                '买一量' as 项目,
                                (SELECT ROUND(bid_qty_1, 2) FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) as Bitda,
                                (SELECT ROUND(bid_qty_1, 2) FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1) as Binance
                            UNION ALL
                            SELECT 
                                '卖一量' as 项目,
                                (SELECT ROUND(ask_qty_1, 2) FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) as Bitda,
                                (SELECT ROUND(ask_qty_1, 2) FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1) as Binance
                        """,
                        "refId": "A"
                    }]
                },
                
                # 2. ETHUSDT深度对比 - 最简单版
                {
                    "id": 2,
                    "title": "📊 ETHUSDT 深度对比",
                    "type": "table",
                    "gridPos": {"h": 8, "w": 8, "x": 8, "y": 0},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            SELECT 
                                '买一量' as 项目,
                                (SELECT ROUND(bid_qty_1, 2) FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) as Bitda,
                                (SELECT ROUND(bid_qty_1, 2) FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1) as Binance
                            UNION ALL
                            SELECT 
                                '卖一量' as 项目,
                                (SELECT ROUND(ask_qty_1, 2) FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) as Bitda,
                                (SELECT ROUND(ask_qty_1, 2) FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1) as Binance
                        """,
                        "refId": "A"
                    }]
                },
                
                # 3. 数据更新时间
                {
                    "id": 3,
                    "title": "⏰ 数据更新时间",
                    "type": "stat",
                    "gridPos": {"h": 8, "w": 8, "x": 16, "y": 0},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": "SELECT DATE_FORMAT(FROM_UNIXTIME(MAX(timestamp)/1000), '%m-%d %H:%i:%s') as value FROM bitda_depth",
                        "refId": "A"
                    }],
                    "options": {
                        "reduceOptions": {"values": False, "calcs": ["lastNotNull"]},
                        "textMode": "auto", 
                        "colorMode": "background"
                    }
                },
                
                # 4. BTCUSDT深度统计 - 简单版
                {
                    "id": 4,
                    "title": "📈 BTCUSDT 深度统计",
                    "type": "table",
                    "gridPos": {"h": 6, "w": 12, "x": 0, "y": 8},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            SELECT 
                                '买一量卖一量深度比' as 项目,
                                ROUND(MAX(bid_qty_1 + ask_qty_1), 2) as 最大值,
                                ROUND(MIN(bid_qty_1 + ask_qty_1), 2) as 最小值,
                                ROUND(AVG(bid_qty_1 + ask_qty_1), 2) as 平均值
                            FROM bitda_depth 
                            WHERE symbol = 'BTCUSDT' 
                                AND timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 5 MINUTE)) * 1000
                        """,
                        "refId": "A"
                    }]
                },
                
                # 5. ETHUSDT深度统计 - 简单版
                {
                    "id": 5,
                    "title": "📈 ETHUSDT 深度统计",
                    "type": "table",
                    "gridPos": {"h": 6, "w": 12, "x": 12, "y": 8},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            SELECT 
                                '买一量卖一量深度比' as 项目,
                                ROUND(MAX(bid_qty_1 + ask_qty_1), 2) as 最大值,
                                ROUND(MIN(bid_qty_1 + ask_qty_1), 2) as 最小值,
                                ROUND(AVG(bid_qty_1 + ask_qty_1), 2) as 平均值
                            FROM bitda_depth 
                            WHERE symbol = 'ETHUSDT' 
                                AND timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 5 MINUTE)) * 1000
                        """,
                        "refId": "A"
                    }]
                },
                
                # 6. BTCUSDT价差对比 - 简单版
                {
                    "id": 6,
                    "title": "💰 BTCUSDT 价差对比",
                    "type": "table",
                    "gridPos": {"h": 6, "w": 12, "x": 0, "y": 14},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            SELECT 
                                '最近价差' as 项目,
                                (SELECT ROUND(ask_price_1 - bid_price_1, 4) FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) as Bitda,
                                (SELECT ROUND(ask_price_1 - bid_price_1, 4) FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1) as Binance
                            UNION ALL
                            SELECT 
                                '平均价差' as 项目,
                                ROUND(AVG(ask_price_1 - bid_price_1), 4) as Bitda,
                                0.1010 as Binance
                            FROM bitda_depth 
                            WHERE symbol = 'BTCUSDT' 
                                AND timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 5 MINUTE)) * 1000
                        """,
                        "refId": "A"
                    }]
                },
                
                # 7. ETHUSDT价差对比 - 简单版
                {
                    "id": 7,
                    "title": "💰 ETHUSDT 价差对比",
                    "type": "table",
                    "gridPos": {"h": 6, "w": 12, "x": 12, "y": 14},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            SELECT 
                                '最近价差' as 项目,
                                (SELECT ROUND(ask_price_1 - bid_price_1, 4) FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) as Bitda,
                                (SELECT ROUND(ask_price_1 - bid_price_1, 4) FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1) as Binance
                            UNION ALL
                            SELECT 
                                '平均价差' as 项目,
                                ROUND(AVG(ask_price_1 - bid_price_1), 4) as Bitda,
                                0.1010 as Binance
                            FROM bitda_depth 
                            WHERE symbol = 'ETHUSDT' 
                                AND timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 5 MINUTE)) * 1000
                        """,
                        "refId": "A"
                    }]
                }
            ],
            "time": {"from": "now-1h", "to": "now"},
            "refresh": "1m",  # 1分钟刷新
            "schemaVersion": 30,
            "version": 1
        },
        "overwrite": True
    }
    
    try:
        response = session.post(
            "http://localhost:3000/api/dashboards/db",
            json=dashboard_config,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            dashboard_url = f"http://localhost:3000{result['url']}"
            print(f"   ✅ 简单工作仪表板创建成功")
            print(f"   🌐 地址: {dashboard_url}")
            return dashboard_url
        else:
            print(f"   ❌ 创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return None

def main():
    """主函数"""
    print("🎨 创建最简单的工作仪表板")
    print("=" * 50)
    print("🔧 特点:")
    print("   🚀 最简单的SQL查询")
    print("   ⏰ 1分钟刷新率")
    print("   📊 基础数据显示")
    print("   🛡️ 确保稳定工作")
    print()
    
    dashboard_url = create_simple_working_dashboard()
    
    if dashboard_url:
        print(f"\n🎉 简单工作仪表板创建成功！")
        print(f"🌐 访问地址: {dashboard_url}")
        print(f"⏰ 1分钟自动刷新")
        print(f"🛡️ 简单稳定的查询")
        
        # 打开浏览器
        try:
            import webbrowser
            webbrowser.open(dashboard_url)
            print(f"🌐 已自动打开浏览器")
        except:
            pass
    else:
        print(f"\n❌ 创建失败")

if __name__ == "__main__":
    main()
