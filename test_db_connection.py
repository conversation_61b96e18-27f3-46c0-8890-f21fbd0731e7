#!/usr/bin/env python3
"""
测试数据库连接和数据可用性
"""

import mysql.connector
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_database_connection():
    """测试数据库连接和数据"""
    print("🔍 测试数据库连接和数据可用性")
    print("=" * 50)
    
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'Linuxtest',
        'database': 'depth_db'
    }
    
    try:
        print("📊 连接数据库...")
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        print("✅ 数据库连接成功")
        
        # 检查表是否存在
        print("\n📋 检查表结构...")
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        print(f"   发现表: {[table[0] for table in tables]}")
        
        # 检查Bitda数据
        print("\n📊 检查Bitda数据...")
        cursor.execute("""
            SELECT COUNT(*), MIN(timestamp), MAX(timestamp) 
            FROM bitda_depth 
            WHERE symbol IN ('BTCUSDT', 'ETHUSDT')
        """)
        bitda_result = cursor.fetchone()
        if bitda_result[0] > 0:
            min_time = datetime.fromtimestamp(bitda_result[1] / 1000)
            max_time = datetime.fromtimestamp(bitda_result[2] / 1000)
            print(f"   ✅ Bitda数据: {bitda_result[0]}条")
            print(f"   📅 时间范围: {min_time.strftime('%Y-%m-%d %H:%M:%S')} - {max_time.strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            print("   ❌ 无Bitda数据")
        
        # 检查Binance数据
        print("\n📊 检查Binance数据...")
        cursor.execute("""
            SELECT COUNT(*), MIN(event_time), MAX(event_time) 
            FROM binance_depth_5 
            WHERE symbol IN ('BTCUSDT', 'ETHUSDT')
        """)
        binance_result = cursor.fetchone()
        if binance_result[0] > 0:
            min_time = datetime.fromtimestamp(binance_result[1] / 1000)
            max_time = datetime.fromtimestamp(binance_result[2] / 1000)
            print(f"   ✅ Binance数据: {binance_result[0]}条")
            print(f"   📅 时间范围: {min_time.strftime('%Y-%m-%d %H:%M:%S')} - {max_time.strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            print("   ❌ 无Binance数据")
        
        # 检查最近5分钟的数据
        print("\n🕐 检查最近5分钟数据...")
        latest_timestamp = max(bitda_result[2] if bitda_result[2] else 0, 
                              binance_result[2] if binance_result[2] else 0)
        
        if latest_timestamp > 0:
            latest_dt = datetime.fromtimestamp(latest_timestamp / 1000)
            start_dt = latest_dt - timedelta(minutes=5)
            start_ts = int(start_dt.timestamp() * 1000)
            
            print(f"   📅 检查时间范围: {start_dt.strftime('%H:%M:%S')} - {latest_dt.strftime('%H:%M:%S')}")
            
            # 检查BTCUSDT最近5分钟数据
            for symbol in ['BTCUSDT', 'ETHUSDT']:
                print(f"\n   🔍 {symbol} 最近5分钟数据:")
                
                # Bitda数据
                cursor.execute("""
                    SELECT COUNT(*) FROM bitda_depth 
                    WHERE symbol = %s AND timestamp BETWEEN %s AND %s
                    AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
                """, (symbol, start_ts, latest_timestamp))
                bitda_count = cursor.fetchone()[0]
                print(f"      Bitda: {bitda_count}条")
                
                # Binance数据
                cursor.execute("""
                    SELECT COUNT(*) FROM binance_depth_5 
                    WHERE symbol = %s AND event_time BETWEEN %s AND %s
                    AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
                """, (symbol, start_ts, latest_timestamp))
                binance_count = cursor.fetchone()[0]
                print(f"      Binance: {binance_count}条")
                
                if bitda_count > 0 and binance_count > 0:
                    print(f"      ✅ {symbol} 数据充足")
                else:
                    print(f"      ❌ {symbol} 数据不足")
        
        # 获取样本数据
        print("\n📋 获取样本数据...")
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            print(f"\n   🔍 {symbol} 最新数据样本:")
            
            # Bitda最新数据
            cursor.execute("""
                SELECT timestamp, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1
                FROM bitda_depth 
                WHERE symbol = %s AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
                ORDER BY timestamp DESC 
                LIMIT 1
            """, (symbol,))
            
            bitda_latest = cursor.fetchone()
            if bitda_latest:
                bitda_time = datetime.fromtimestamp(bitda_latest[0] / 1000)
                bitda_spread = float(bitda_latest[2]) - float(bitda_latest[1])
                print(f"      Bitda最新: {bitda_time.strftime('%H:%M:%S')}, 价差: {bitda_spread:.4f}, 买一量: {bitda_latest[3]}")
            else:
                print(f"      ❌ 无Bitda最新数据")
            
            # Binance最新数据
            cursor.execute("""
                SELECT event_time, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1
                FROM binance_depth_5 
                WHERE symbol = %s AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
                ORDER BY event_time DESC 
                LIMIT 1
            """, (symbol,))
            
            binance_latest = cursor.fetchone()
            if binance_latest:
                binance_time = datetime.fromtimestamp(binance_latest[0] / 1000)
                binance_spread = float(binance_latest[2]) - float(binance_latest[1])
                print(f"      Binance最新: {binance_time.strftime('%H:%M:%S')}, 价差: {binance_spread:.4f}, 买一量: {binance_latest[3]}")
                
                if bitda_latest and binance_latest:
                    time_diff = abs(bitda_latest[0] - binance_latest[0])
                    print(f"      ⏰ 时间差: {time_diff}ms")
            else:
                print(f"      ❌ 无Binance最新数据")
        
        cursor.close()
        connection.close()
        
        print("\n🎯 数据库测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def main():
    """主函数"""
    success = test_database_connection()
    
    if success:
        print("\n✅ 数据库连接和数据检查完成")
        print("📊 可以继续测试真实数据仪表板")
    else:
        print("\n❌ 数据库连接或数据有问题")
        print("🔧 请检查数据库配置和数据可用性")

if __name__ == "__main__":
    main()
