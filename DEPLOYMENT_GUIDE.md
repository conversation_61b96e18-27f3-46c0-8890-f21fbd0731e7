# ClickHouse + Grafana 深度对比仪表板部署指南

## 🎯 部署目标

创建基于ClickHouse数据库的Grafana深度对比仪表板，显示BTCUSDT和ETHUSDT的深度数据对比，包含以下5个关键指标：

- 卖一量
- 买一量  
- 买一量卖一量
- 买卖前两档量
- 买卖前五档量

## 📋 前置条件

### 系统要求
- Ubuntu 20.04+ / CentOS 7+
- 至少4GB RAM
- 至少10GB可用磁盘空间

### 必需软件
- ClickHouse Server
- Grafana
- Python 3.8+
- pip3

## 🚀 快速部署

### 步骤1: 检查环境配置

```bash
# 查看当前配置
cat .env
```

确保包含以下配置：
```bash
# ClickHouse配置
CH_HOST=localhost
CH_USER=default
CH_PASSWORD=Linuxtest
CH_DATABASE=crypto
CH_PORT=9000

# Grafana配置
GRAFANA_HOST=localhost
GRAFANA_PORT=3000
GRAFANA_USER=admin
GRAFANA_PASSWORD=admin
GRAFANA_URL=http://localhost:3000
```

### 步骤2: 一键部署

```bash
# 运行部署脚本
./start_grafana_clickhouse.sh
```

### 步骤3: 验证部署

```bash
# 测试ClickHouse查询
python3 test_clickhouse_queries.py

# 检查服务状态
./start_grafana_clickhouse.sh --check-only
```

### 步骤4: 访问仪表板

1. 打开浏览器访问: http://localhost:3000
2. 使用用户名: `admin`，密码: `admin` 登录
3. 查看 "ClickHouse深度对比仪表板"

## 🔧 手动部署

如果自动部署失败，可以按以下步骤手动部署：

### 1. 启动ClickHouse

```bash
# 启动服务
sudo systemctl start clickhouse-server
sudo systemctl enable clickhouse-server

# 验证连接
clickhouse-client --query "SELECT 1"
```

### 2. 启动Grafana

```bash
# 启动服务
sudo systemctl start grafana-server
sudo systemctl enable grafana-server

# 验证服务
curl -s http://localhost:3000/api/health
```

### 3. 安装ClickHouse插件

```bash
# 安装插件
sudo grafana-cli plugins install grafana-clickhouse-datasource

# 重启Grafana
sudo systemctl restart grafana-server

# 等待服务启动
sleep 10
```

### 4. 配置数据源和仪表板

```bash
# 运行配置脚本
python3 setup_grafana_clickhouse.py
```

## 📊 仪表板配置详情

### 数据源配置

- **名称**: ClickHouse-Crypto
- **类型**: grafana-clickhouse-datasource
- **URL**: http://localhost:8123
- **数据库**: crypto
- **用户名**: default
- **密码**: Linuxtest

### 面板配置

#### BTCUSDT深度对比面板
- **位置**: 左侧 (12x12)
- **查询**: 5个UNION ALL子查询
- **刷新**: 1分钟
- **格式**: 表格

#### ETHUSDT深度对比面板  
- **位置**: 右侧 (12x12)
- **查询**: 5个UNION ALL子查询
- **刷新**: 1分钟
- **格式**: 表格

## 🔍 故障排除

### 问题1: ClickHouse连接失败

**症状**: 无法连接到ClickHouse数据库

**解决方案**:
```bash
# 检查服务状态
sudo systemctl status clickhouse-server

# 检查端口
netstat -tlnp | grep 9000

# 查看日志
sudo tail -f /var/log/clickhouse-server/clickhouse-server.log

# 重启服务
sudo systemctl restart clickhouse-server
```

### 问题2: Grafana插件未安装

**症状**: 无法创建ClickHouse数据源

**解决方案**:
```bash
# 检查已安装插件
grafana-cli plugins ls

# 安装ClickHouse插件
sudo grafana-cli plugins install grafana-clickhouse-datasource

# 重启Grafana
sudo systemctl restart grafana-server
```

### 问题3: 数据不显示

**症状**: 仪表板显示"No data"

**解决方案**:
```bash
# 检查数据
clickhouse-client --query "SELECT COUNT(*) FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT'"
clickhouse-client --query "SELECT COUNT(*) FROM crypto.binance_depth_5 WHERE symbol = 'BTCUSDT'"

# 检查最新数据
clickhouse-client --query "SELECT * FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1"
```

### 问题4: 权限错误

**症状**: 无法访问数据库或创建数据源

**解决方案**:
```bash
# 检查ClickHouse用户权限
clickhouse-client --query "SHOW GRANTS FOR default"

# 检查Grafana权限
sudo chown -R grafana:grafana /var/lib/grafana
sudo systemctl restart grafana-server
```

## 📈 性能优化

### ClickHouse优化

1. **内存设置**:
```xml
<!-- /etc/clickhouse-server/config.xml -->
<max_memory_usage>4000000000</max_memory_usage>
<max_bytes_before_external_group_by>2000000000</max_bytes_before_external_group_by>
```

2. **查询优化**:
```sql
-- 添加索引
ALTER TABLE crypto.bitda_depth ADD INDEX idx_symbol_timestamp (symbol, timestamp) TYPE minmax GRANULARITY 1;
ALTER TABLE crypto.binance_depth_5 ADD INDEX idx_symbol_event_time (symbol, event_time) TYPE minmax GRANULARITY 1;
```

### Grafana优化

1. **查询缓存**: 在数据源设置中启用查询缓存
2. **刷新间隔**: 根据需要调整为30秒或2分钟
3. **面板限制**: 避免同时显示过多面板

## 🔄 维护指南

### 日常维护

```bash
# 检查服务状态
sudo systemctl status clickhouse-server grafana-server

# 检查磁盘空间
df -h

# 检查数据增长
clickhouse-client --query "SELECT COUNT(*) FROM crypto.bitda_depth"
```

### 备份策略

```bash
# 备份ClickHouse数据
clickhouse-client --query "BACKUP TABLE crypto.bitda_depth TO Disk('default', 'backup/bitda_depth')"

# 备份Grafana配置
sudo cp -r /var/lib/grafana /backup/grafana-$(date +%Y%m%d)
```

### 日志轮转

```bash
# ClickHouse日志轮转
sudo logrotate /etc/logrotate.d/clickhouse-server

# Grafana日志轮转  
sudo logrotate /etc/logrotate.d/grafana-server
```

## 📞 技术支持

### 检查清单

在寻求帮助前，请确认：

- [ ] ClickHouse服务正常运行
- [ ] Grafana服务正常运行
- [ ] ClickHouse插件已安装
- [ ] 数据源配置正确
- [ ] 数据表存在且有数据
- [ ] 网络连接正常
- [ ] 防火墙设置正确

### 收集诊断信息

```bash
# 生成诊断报告
echo "=== 系统信息 ===" > diagnosis.txt
uname -a >> diagnosis.txt
echo "=== 服务状态 ===" >> diagnosis.txt
sudo systemctl status clickhouse-server grafana-server >> diagnosis.txt
echo "=== 端口检查 ===" >> diagnosis.txt
netstat -tlnp | grep -E "(3000|8123|9000)" >> diagnosis.txt
echo "=== 数据检查 ===" >> diagnosis.txt
clickhouse-client --query "SELECT COUNT(*) FROM crypto.bitda_depth" >> diagnosis.txt 2>&1
```

---

**版本**: 1.0  
**更新时间**: 2024年  
**维护者**: AI Assistant
