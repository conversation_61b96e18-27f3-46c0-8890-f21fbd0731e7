#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库初始化脚本
创建数据库和表，设置自动清理7天前的数据
"""

import mysql.connector
import os
from dotenv import load_dotenv
import logging

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', 'Mactest168'),
    'port': int(os.getenv('DB_PORT', '3306')),
    'charset': 'utf8mb4',
    'use_unicode': True,
}

DB_NAME = os.getenv('DB_NAME', 'depth_db')

def create_database():
    """创建数据库"""
    try:
        # 连接MySQL服务器（不指定数据库）
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # 创建数据库
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {DB_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        logger.info(f"数据库 {DB_NAME} 创建成功或已存在")

        cursor.close()
        conn.close()

    except Exception as e:
        logger.error(f"创建数据库失败: {e}")
        raise

def create_tables():
    """创建表"""
    try:
        # 连接到指定数据库
        config = DB_CONFIG.copy()
        config['database'] = DB_NAME
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()

        # 深度匹配数据表
        # BTCUSDT: 价格精度1位小数，数量精度4位小数
        # ETHUSDT: 价格精度2位小数，数量精度3位小数
        depth_matches_sql = """
        CREATE TABLE IF NOT EXISTS depth_matches (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            symbol VARCHAR(20) NOT NULL,
            environment VARCHAR(20) NOT NULL,
            timestamp DATETIME(3) NOT NULL,
            binance_bid_price DECIMAL(12,2) NOT NULL COMMENT '币安买一价格',
            binance_ask_price DECIMAL(12,2) NOT NULL COMMENT '币安卖一价格',
            binance_bid_qty DECIMAL(15,4) NOT NULL COMMENT '币安买一数量',
            binance_ask_qty DECIMAL(15,4) NOT NULL COMMENT '币安卖一数量',
            exchange_bid_qty DECIMAL(15,4) NOT NULL COMMENT '交易所买一数量',
            exchange_ask_qty DECIMAL(15,4) NOT NULL COMMENT '交易所卖一数量',
            binance_depth_sum DECIMAL(15,4) NOT NULL COMMENT '币安深度总和',
            exchange_depth_sum DECIMAL(15,4) NOT NULL COMMENT '交易所深度总和',
            binance_message_time BIGINT NOT NULL COMMENT '币安消息时间戳',
            exchange_time BIGINT NOT NULL COMMENT '交易所时间戳',
            message_latency_ms BIGINT NOT NULL COMMENT '消息延迟毫秒',
            engine_latency_ms BIGINT NOT NULL COMMENT '引擎延迟毫秒',
            depth_ratio DECIMAL(10,4) NOT NULL COMMENT '深度比值',
            INDEX idx_symbol_env_time (symbol, environment, timestamp),
            INDEX idx_timestamp (timestamp)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        COMMENT='深度匹配数据表'
        """

        # Binance原始数据表
        binance_raw_sql = """
        CREATE TABLE IF NOT EXISTS binance_raw_data (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            symbol VARCHAR(20) NOT NULL,
            trade_time BIGINT NOT NULL COMMENT '交易时间戳',
            event_time BIGINT NOT NULL COMMENT '事件时间戳',
            bid_price DECIMAL(12,2) NOT NULL COMMENT '买一价格',
            bid_qty DECIMAL(15,4) NOT NULL COMMENT '买一数量',
            ask_price DECIMAL(12,2) NOT NULL COMMENT '卖一价格',
            ask_qty DECIMAL(15,4) NOT NULL COMMENT '卖一数量',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_symbol_time (symbol, event_time),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        COMMENT='Binance原始数据表'
        """

        # 交易所原始数据表
        exchange_raw_sql = """
        CREATE TABLE IF NOT EXISTS exchange_raw_data (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            symbol VARCHAR(20) NOT NULL,
            environment VARCHAR(20) NOT NULL,
            time BIGINT NOT NULL COMMENT '时间戳',
            index_price DECIMAL(12,2) DEFAULT 0 COMMENT '指数价格',
            sign_price DECIMAL(12,2) DEFAULT 0 COMMENT '标记价格',
            last_price DECIMAL(12,2) DEFAULT 0 COMMENT '最新价格',
            asks JSON COMMENT '卖单深度',
            bids JSON COMMENT '买单深度',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_symbol_env_time (symbol, environment, time),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        COMMENT='交易所原始数据表'
        """

        # 统计数据表
        depth_stats_sql = """
        CREATE TABLE IF NOT EXISTS depth_stats (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            symbol VARCHAR(20) NOT NULL,
            environment VARCHAR(20) NOT NULL,
            timestamp DATETIME NOT NULL,
            avg_message_latency DECIMAL(10,2) NOT NULL COMMENT '平均消息延迟',
            min_message_latency BIGINT NOT NULL COMMENT '最小消息延迟',
            max_message_latency BIGINT NOT NULL COMMENT '最大消息延迟',
            avg_engine_latency DECIMAL(10,2) NOT NULL COMMENT '平均引擎延迟',
            min_engine_latency BIGINT NOT NULL COMMENT '最小引擎延迟',
            max_engine_latency BIGINT NOT NULL COMMENT '最大引擎延迟',
            avg_depth_ratio DECIMAL(10,4) NOT NULL COMMENT '平均深度比值',
            min_depth_ratio DECIMAL(10,4) NOT NULL COMMENT '最小深度比值',
            max_depth_ratio DECIMAL(10,4) NOT NULL COMMENT '最大深度比值',
            match_count INT NOT NULL COMMENT '匹配次数',
            INDEX idx_symbol_env_time (symbol, environment, timestamp)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        COMMENT='深度统计数据表'
        """

        # 执行创建表的SQL
        tables = [
            ('depth_matches', depth_matches_sql),
            ('binance_raw_data', binance_raw_sql),
            ('exchange_raw_data', exchange_raw_sql),
            ('depth_stats', depth_stats_sql)
        ]

        for table_name, sql in tables:
            cursor.execute(sql)
            logger.info(f"表 {table_name} 创建成功或已存在")

        cursor.close()
        conn.close()

    except Exception as e:
        logger.error(f"创建表失败: {e}")
        raise

def create_cleanup_event():
    """创建自动清理事件，删除1天前的数据"""
    try:
        config = DB_CONFIG.copy()
        config['database'] = DB_NAME
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()

        # 启用事件调度器
        cursor.execute("SET GLOBAL event_scheduler = ON")

        # 删除已存在的事件
        cursor.execute("DROP EVENT IF EXISTS cleanup_old_data")

        # 创建清理事件，每天凌晨2点执行
        cleanup_event_sql = """
        CREATE EVENT cleanup_old_data
        ON SCHEDULE EVERY 1 DAY
        STARTS TIMESTAMP(CURDATE() + INTERVAL 1 DAY, '02:00:00')
        DO
        BEGIN
            DELETE FROM depth_matches WHERE timestamp < DATE_SUB(NOW(), INTERVAL 1 DAY);
            DELETE FROM binance_raw_data WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 DAY);
            DELETE FROM exchange_raw_data WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 DAY);
            DELETE FROM depth_stats WHERE timestamp < DATE_SUB(NOW(), INTERVAL 1 DAY);
        END
        """

        cursor.execute(cleanup_event_sql)
        logger.info("自动清理事件创建成功，将每天凌晨2点清理1天前的数据")

        cursor.close()
        conn.close()

    except Exception as e:
        logger.error(f"创建清理事件失败: {e}")
        # 不抛出异常，因为这不是关键功能

def main():
    """主函数"""
    try:
        logger.info("开始初始化数据库...")

        # 创建数据库
        create_database()

        # 创建表
        create_tables()

        # 创建清理事件
        create_cleanup_event()

        logger.info("数据库初始化完成！")

    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return False

    return True

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
