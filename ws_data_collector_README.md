# WebSocket数据收集器

## 简介

这个程序是一个WebSocket数据收集器，用于实时监控和比较不同交易平台的加密货币价格和深度数据。它连接到Binance和其他交易所的WebSocket服务，收集实时市场数据，并进行比较分析。

## 文件信息

- **文件名**: `ws_data_collector.py`
- **位置**: `/home/<USER>/code/Bit/bit_rest/ws_data_collector.py`
- **日志文件**: `/data/ws_data/ws_data_collector.log`
- **启动脚本**: `/home/<USER>/code/Bit/bit_rest/start_ws_collector.sh`

## 主要功能

1. **实时数据收集**：同时连接到多个数据源，包括：
   - Binance期货市场
   - 生产环境交易所
   - 测试环境交易所

2. **价格比较**：比较不同平台之间的买一卖一价格，只有当价格完全相等时才记录数据。

3. **延迟监控**：计算消息传输延迟，帮助评估网络性能。

4. **深度比较**：计算不同平台之间的市场深度比值，帮助分析流动性差异。

5. **数据存储**：将收集到的数据保存到MySQL数据库中，方便后续分析。

## 工作原理

1. 程序启动后，会同时连接到Binance和其他交易所的WebSocket服务。
2. 接收并处理实时市场数据，包括价格和深度信息。
3. 当发现Binance和交易所的价格完全匹配时，计算延迟和深度比值。
4. 将匹配的数据和统计信息保存到数据库中。
5. 每5分钟自动生成一次统计数据，包括平均延迟、最小延迟、最大延迟等。

## 连接地址

| 服务 | WebSocket地址 | 说明 |
|------|--------------|------|
| Binance | wss://stream.binance.com:9443/ws/ | Binance公共WebSocket API |
| 生产环境 | wss://api.example.com/ws | 生产环境WebSocket地址 |
| 测试环境 | wss://test-api.example.com/ws | 测试环境WebSocket地址 |

## 监控的交易对

| 交易对 | 说明 | 价格范围 |
|-------|------|---------|
| BTCUSDT | 比特币/USDT | 通常>10,000 USDT |
| ETHUSDT | 以太坊/USDT | 通常<10,000 USDT |

## 数据流程图

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Binance   │    │ 生产环境交易所 │    │ 测试环境交易所 │
└──────┬──────┘    └──────┬──────┘    └──────┬──────┘
       │                  │                  │
       ▼                  ▼                  ▼
┌─────────────────────────────────────────────────┐
│                WebSocket连接                     │
└──────────────────────────┬──────────────────────┘
                           │
                           ▼
┌─────────────────────────────────────────────────┐
│                 原始数据存储                      │
│     (binance_raw_data和exchange_raw_data表)      │
└──────────────────────────┬──────────────────────┘
                           │
                           ▼
┌─────────────────────────────────────────────────┐
│                 价格匹配检查                      │
│          (只有完全相同的价格才会被记录)             │
└──────────────────────────┬──────────────────────┘
                           │
                           ▼
┌─────────────────────────────────────────────────┐
│                 计算关键指标                      │
│         (延迟、深度比值、匹配次数等)                │
└──────────────────────────┬──────────────────────┘
                           │
                           ▼
┌─────────────────────────────────────────────────┐
│                 匹配数据存储                      │
│         (depth_matches和depth_stats表)           │
└─────────────────────────────────────────────────┘
```

## 数据库表结构

### depth_matches表

存储每次价格匹配的详细数据。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | bigint | 自增主键 |
| symbol | varchar(20) | 交易对符号，如BTCUSDT |
| environment | varchar(20) | 环境：testing或production |
| timestamp | datetime(3) | 匹配时间，精确到毫秒 |
| binance_bid_price | decimal(20,8) | Binance买一价 |
| binance_ask_price | decimal(20,8) | Binance卖一价 |
| binance_bid_qty | decimal(20,8) | Binance买一量 |
| binance_ask_qty | decimal(20,8) | Binance卖一量 |
| exchange_bid_qty | decimal(20,8) | 交易所买一量 |
| exchange_ask_qty | decimal(20,8) | 交易所卖一量 |
| binance_depth_sum | decimal(20,8) | Binance深度总和 |
| exchange_depth_sum | decimal(20,8) | 交易所深度总和 |
| binance_message_time | bigint | Binance消息时间戳 |
| exchange_time | bigint | 交易所时间戳 |
| message_latency_ms | int | 消息延迟(ms) |
| engine_latency_ms | int | 引擎延迟(ms) |
| depth_ratio | decimal(10,6) | 深度比值 |
| created_at | timestamp | 记录创建时间 |

### depth_stats表

存储每5分钟的统计数据。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | bigint | 自增主键 |
| symbol | varchar(20) | 交易对符号，如BTCUSDT |
| environment | varchar(20) | 环境：testing或production |
| timestamp | datetime | 统计时间（按分钟汇总） |
| avg_message_latency | decimal(10,2) | 平均消息延迟(ms) |
| min_message_latency | int | 最小消息延迟(ms) |
| max_message_latency | int | 最大消息延迟(ms) |
| avg_engine_latency | decimal(10,2) | 平均引擎延迟(ms) |
| min_engine_latency | int | 最小引擎延迟(ms) |
| max_engine_latency | int | 最大引擎延迟(ms) |
| avg_depth_ratio | decimal(10,6) | 平均深度比值 |
| min_depth_ratio | decimal(10,6) | 最小深度比值 |
| max_depth_ratio | decimal(10,6) | 最大深度比值 |
| match_count | int | 匹配次数 |
| created_at | timestamp | 记录创建时间 |

### binance_raw_data表

存储Binance原始数据。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | bigint | 自增主键 |
| symbol | varchar(20) | 交易对符号，如BTCUSDT |
| trade_time | bigint | 撮合时间戳(T) |
| event_time | bigint | 事件推送时间戳(E) |
| bid_price | decimal(30,8) | 买单最优挂单价格(b) |
| bid_qty | decimal(30,8) | 买单最优挂单数量(B) |
| ask_price | decimal(30,8) | 卖单最优挂单价格(a) |
| ask_qty | decimal(30,8) | 卖单最优挂单数量(A) |
| created_at | timestamp | 记录创建时间 |

### exchange_raw_data表

存储交易所（生产环境和测试环境）原始数据。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | bigint | 自增主键 |
| symbol | varchar(20) | 交易对符号，如BTCUSDT |
| environment | enum('production', 'testing') | 环境：production或testing |
| time | bigint | 交易所时间戳 |
| index_price | decimal(30,8) | 指数价格 |
| sign_price | decimal(30,8) | 标记价格 |
| last_price | decimal(30,8) | 最新成交价 |
| asks | JSON | 卖单列表，从卖一价开始，由小到大排列到卖50 |
| bids | JSON | 买单列表，从买一价开始，由大到小排列到买50 |
| created_at | timestamp | 记录创建时间 |

## 关键算法

### 深度比值计算

深度比值是衡量不同交易所之间流动性差异的重要指标，计算公式如下：

```
depth_ratio = exchange_depth_sum / binance_depth_sum

其中：
exchange_depth_sum = exchange_bid_qty + exchange_ask_qty
binance_depth_sum = binance_bid_qty + binance_ask_qty
```

### 延迟计算

消息延迟是衡量数据传输速度的重要指标，计算公式如下：

```
message_latency_ms = exchange_time - binance_time
```

## 日志文件

程序运行日志保存在：`/data/ws_data/ws_data_collector.log`

日志文件会自动轮转，当大小超过100MB时，会自动备份并压缩，格式为：
`/data/ws_data/ws_data_collector.log.YYYYMMDD_HHMMSS.gz`

## 启动和停止

### 启动程序

```bash
bash /home/<USER>/code/Bit/bit_rest/start_ws_collector.sh
```

### 查看程序状态

```bash
ps aux | grep ws_data_collector.py | grep -v grep
```

### 查看日志

```bash
tail -f /data/ws_data/ws_data_collector.log
```

### 停止程序

```bash
pkill -f ws_data_collector.py
```

## 故障排除

| 问题 | 可能原因 | 解决方法 |
|------|---------|---------|
| WebSocket连接失败 | 网络问题或服务器不可用 | 检查网络连接和服务器状态，程序会自动尝试重连 |
| 数据库连接失败 | MySQL服务未启动或凭据错误 | 确保MySQL服务正在运行，检查连接凭据 |
| 日志文件过大 | 日志轮转未正常工作 | 手动运行日志轮转脚本：`bash /home/<USER>/code/Bit/bit_rest/rotate_logs.sh` |
| 程序占用内存过高 | 长时间运行导致内存泄漏 | 重启程序，如果问题持续，检查代码中的内存管理 |

## 依赖项

| 依赖 | 版本 | 用途 |
|------|------|------|
| websockets | >= 10.0 | WebSocket客户端 |
| mysql-connector-python | >= 8.0 | MySQL数据库连接 |
| asyncio | 内置 | 异步I/O处理 |
| json | 内置 | JSON数据处理 |
| logging | 内置 | 日志记录 |
| time | 内置 | 时间处理 |
| datetime | 内置 | 日期时间处理 |
| random | 内置 | 随机数生成 |

## 注意事项

- 程序设计为长时间运行，会自动处理连接断开和重连。
- 如果数据库连接失败，程序会记录错误但会继续尝试运行。
- 日志文件会自动轮转，防止占用过多磁盘空间。
- 程序只在价格完全相等（精确到最后一位小数）时才会记录数据，确保比较的准确性。
- 深度比值可能会有极端值，特别是当Binance深度很小时，这是正常现象。

## 数据查询示例

### 查询最近的匹配记录

```sql
SELECT * FROM depth_matches
ORDER BY timestamp DESC
LIMIT 10;
```

### 查询特定交易对的统计数据

```sql
SELECT * FROM depth_stats
WHERE symbol = 'BTCUSDT' AND environment = 'production'
ORDER BY timestamp DESC
LIMIT 24;
```

### 查询延迟异常的记录

```sql
SELECT * FROM depth_matches
WHERE message_latency_ms > 500
ORDER BY timestamp DESC;
```

### 查询深度比值异常的记录

```sql
SELECT * FROM depth_matches
WHERE depth_ratio > 10 OR depth_ratio < 0.1
ORDER BY timestamp DESC;
```

### 查询Binance原始数据

```sql
SELECT * FROM binance_raw_data
WHERE symbol = 'BTCUSDT'
ORDER BY event_time DESC
LIMIT 10;
```

### 查询交易所原始数据

```sql
SELECT id, symbol, environment, time, index_price, sign_price, last_price,
       JSON_LENGTH(asks) as asks_count, JSON_LENGTH(bids) as bids_count,
       JSON_EXTRACT(asks, '$[0]') as ask1,
       JSON_EXTRACT(bids, '$[0]') as bid1
FROM exchange_raw_data
WHERE symbol = 'BTCUSDT' AND environment = 'production'
ORDER BY time DESC
LIMIT 10;
```

### 查询交易所深度数据

```sql
-- 查询卖一价和卖一量
SELECT id, symbol, environment, time,
       JSON_EXTRACT(asks, '$[0][0]') as ask1_price,
       JSON_EXTRACT(asks, '$[0][1]') as ask1_qty
FROM exchange_raw_data
WHERE symbol = 'BTCUSDT' AND environment = 'production'
ORDER BY time DESC
LIMIT 10;

-- 查询买一价和买一量
SELECT id, symbol, environment, time,
       JSON_EXTRACT(bids, '$[0][0]') as bid1_price,
       JSON_EXTRACT(bids, '$[0][1]') as bid1_qty
FROM exchange_raw_data
WHERE symbol = 'BTCUSDT' AND environment = 'production'
ORDER BY time DESC
LIMIT 10;
```

## 版本历史

| 版本 | 日期 | 变更内容 |
|------|------|---------|
| 1.0.0 | 2025-04-01 | 初始版本 |
| 1.1.0 | 2025-04-15 | 添加深度比值计算 |
| 1.2.0 | 2025-04-30 | 添加统计数据生成 |
| 1.3.0 | 2025-05-02 | 修改价格比较逻辑，要求完全相等 |
| 1.4.0 | 2025-05-03 | 添加原始数据存储功能，保存完整的Binance和交易所数据 |
