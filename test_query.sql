WITH bitda_latest AS (
  SELECT bid_qty_1, ask_qty_1, bid_qty_2, ask_qty_2, bid_qty_3, ask_qty_3, bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5
  FROM crypto.bitda_depth 
  WHERE symbol = 'BTCUSDT' 
  ORDER BY timestamp DESC 
  LIMIT 1
),
binance_latest AS (
  SELECT bid_qty, ask_qty
  FROM crypto.binance_bookticker 
  WHERE symbol = 'BTCUSDT' 
  ORDER BY event_time DESC 
  LIMIT 1
)
SELECT 
  '买一量' as 项目,
  round(b.bid_qty_1, 2) as Bitda,
  round(bn.bid_qty, 2) as Binance,
  round(b.bid_qty_1 / bn.bid_qty, 2) as 深度比
FROM bitda_latest b, binance_latest bn
