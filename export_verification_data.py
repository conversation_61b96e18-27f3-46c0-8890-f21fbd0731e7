#!/usr/bin/env python3
"""
导出验证数据到Excel表格
包含所有验证和取值的详细信息
"""

import mysql.connector
import pandas as pd
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def export_verification_to_excel():
    """导出验证数据到Excel"""
    print("📊 导出验证数据到Excel表格...")
    print("=" * 60)
    
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'Linuxtest',
        'database': 'depth_db'
    }
    
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        # 计算前一分钟的时间戳范围
        now = datetime.now()
        one_minute_ago = now - timedelta(minutes=1)
        start_timestamp = int(one_minute_ago.timestamp() * 1000)
        end_timestamp = int(now.timestamp() * 1000)
        
        print(f"📅 时间范围: {one_minute_ago.strftime('%Y-%m-%d %H:%M:%S')} - {now.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 创建Excel写入器
        excel_filename = f"depth_verification_{now.strftime('%Y%m%d_%H%M%S')}.xlsx"
        writer = pd.ExcelWriter(excel_filename, engine='openpyxl')
        
        # 处理BTCUSDT和ETHUSDT
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            print(f"\n📊 处理{symbol}数据...")
            
            # 获取Bitda数据
            cursor.execute("""
                SELECT timestamp, bid_qty_1, ask_qty_1, bid_qty_2, ask_qty_2,
                       bid_qty_3, ask_qty_3, bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5
                FROM bitda_depth
                WHERE symbol = %s AND timestamp BETWEEN %s AND %s
                AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
                ORDER BY timestamp ASC
            """, (symbol, start_timestamp, end_timestamp))
            
            bitda_records = cursor.fetchall()
            print(f"   获取{len(bitda_records)}条Bitda数据")
            
            # 准备数据列表
            verification_data = []
            
            for i, bitda_record in enumerate(bitda_records):
                bitda_timestamp = bitda_record[0]
                bitda_time = datetime.fromtimestamp(bitda_timestamp / 1000)
                
                # 获取对应的Binance数据
                cursor.execute("""
                    SELECT event_time, bid_qty_1, ask_qty_1, bid_qty_2, ask_qty_2,
                           bid_qty_3, ask_qty_3, bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5
                    FROM binance_depth_5
                    WHERE symbol = %s AND event_time <= %s
                    AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
                    ORDER BY event_time DESC
                    LIMIT 1
                """, (symbol, bitda_timestamp))
                
                binance_record = cursor.fetchone()
                if not binance_record:
                    continue
                
                binance_timestamp = binance_record[0]
                binance_time = datetime.fromtimestamp(binance_timestamp / 1000)
                time_diff = bitda_timestamp - binance_timestamp
                
                # 解析数据
                bitda_bid1 = float(bitda_record[1]) if bitda_record[1] else 0
                bitda_ask1 = float(bitda_record[2]) if bitda_record[2] else 0
                bitda_bid2 = float(bitda_record[3]) if bitda_record[3] else 0
                bitda_ask2 = float(bitda_record[4]) if bitda_record[4] else 0
                bitda_bid3 = float(bitda_record[5]) if bitda_record[5] else 0
                bitda_ask3 = float(bitda_record[6]) if bitda_record[6] else 0
                bitda_bid4 = float(bitda_record[7]) if bitda_record[7] else 0
                bitda_ask4 = float(bitda_record[8]) if bitda_record[8] else 0
                bitda_bid5 = float(bitda_record[9]) if bitda_record[9] else 0
                bitda_ask5 = float(bitda_record[10]) if bitda_record[10] else 0
                
                binance_bid1 = float(binance_record[1]) if binance_record[1] else 0
                binance_ask1 = float(binance_record[2]) if binance_record[2] else 0
                binance_bid2 = float(binance_record[3]) if binance_record[3] else 0
                binance_ask2 = float(binance_record[4]) if binance_record[4] else 0
                binance_bid3 = float(binance_record[5]) if binance_record[5] else 0
                binance_ask3 = float(binance_record[6]) if binance_record[6] else 0
                binance_bid4 = float(binance_record[7]) if binance_record[7] else 0
                binance_ask4 = float(binance_record[8]) if binance_record[8] else 0
                binance_bid5 = float(binance_record[9]) if binance_record[9] else 0
                binance_ask5 = float(binance_record[10]) if binance_record[10] else 0
                
                # 计算各项指标
                bitda_bid_ask1 = bitda_bid1 + bitda_ask1
                binance_bid_ask1 = binance_bid1 + binance_ask1
                bid_ask1_ratio = bitda_bid_ask1 / binance_bid_ask1 if binance_bid_ask1 > 0 else 0
                
                bitda_bid_ask2 = bitda_bid1 + bitda_bid2 + bitda_ask1 + bitda_ask2
                binance_bid_ask2 = binance_bid1 + binance_bid2 + binance_ask1 + binance_ask2
                bid_ask2_ratio = bitda_bid_ask2 / binance_bid_ask2 if binance_bid_ask2 > 0 else 0
                
                bitda_bid_ask5 = (bitda_bid1 + bitda_bid2 + bitda_bid3 + bitda_bid4 + bitda_bid5 +
                                 bitda_ask1 + bitda_ask2 + bitda_ask3 + bitda_ask4 + bitda_ask5)
                binance_bid_ask5 = (binance_bid1 + binance_bid2 + binance_bid3 + binance_bid4 + binance_bid5 +
                                   binance_ask1 + binance_ask2 + binance_ask3 + binance_ask4 + binance_ask5)
                bid_ask5_ratio = bitda_bid_ask5 / binance_bid_ask5 if binance_bid_ask5 > 0 else 0
                
                # 添加到数据列表
                verification_data.append({
                    '序号': i + 1,
                    'Bitda时间': bitda_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],
                    'Binance时间': binance_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],
                    '时间差(ms)': time_diff,
                    'Bitda买一量': bitda_bid1,
                    'Bitda卖一量': bitda_ask1,
                    'Bitda买二量': bitda_bid2,
                    'Bitda卖二量': bitda_ask2,
                    'Bitda买三量': bitda_bid3,
                    'Bitda卖三量': bitda_ask3,
                    'Bitda买四量': bitda_bid4,
                    'Bitda卖四量': bitda_ask4,
                    'Bitda买五量': bitda_bid5,
                    'Bitda卖五量': bitda_ask5,
                    'Binance买一量': binance_bid1,
                    'Binance卖一量': binance_ask1,
                    'Binance买二量': binance_bid2,
                    'Binance卖二量': binance_ask2,
                    'Binance买三量': binance_bid3,
                    'Binance卖三量': binance_ask3,
                    'Binance买四量': binance_bid4,
                    'Binance卖四量': binance_ask4,
                    'Binance买五量': binance_bid5,
                    'Binance卖五量': binance_ask5,
                    'Bitda买一量卖一量': bitda_bid_ask1,
                    'Binance买一量卖一量': binance_bid_ask1,
                    '买一量卖一量深度比': bid_ask1_ratio,
                    'Bitda买卖前两档量': bitda_bid_ask2,
                    'Binance买卖前两档量': binance_bid_ask2,
                    '买卖前两档量深度比': bid_ask2_ratio,
                    'Bitda买卖前五档量': bitda_bid_ask5,
                    'Binance买卖前五档量': binance_bid_ask5,
                    '买卖前五档量深度比': bid_ask5_ratio
                })
            
            # 创建DataFrame并写入Excel
            df = pd.DataFrame(verification_data)
            df.to_excel(writer, sheet_name=f'{symbol}_详细数据', index=False)
            
            # 创建统计汇总表
            if len(verification_data) > 0:
                valid_bid_ask1 = [row['买一量卖一量深度比'] for row in verification_data if row['买一量卖一量深度比'] > 0]
                valid_bid_ask2 = [row['买卖前两档量深度比'] for row in verification_data if row['买卖前两档量深度比'] > 0]
                valid_bid_ask5 = [row['买卖前五档量深度比'] for row in verification_data if row['买卖前五档量深度比'] > 0]
                
                summary_data = [
                    {
                        '项目': '买一量卖一量深度比',
                        '最大值': max(valid_bid_ask1) if valid_bid_ask1 else 0,
                        '最小值': min(valid_bid_ask1) if valid_bid_ask1 else 0,
                        '平均值': sum(valid_bid_ask1) / len(valid_bid_ask1) if valid_bid_ask1 else 0,
                        '样本数量': len(valid_bid_ask1)
                    },
                    {
                        '项目': '买卖前两档量深度比',
                        '最大值': max(valid_bid_ask2) if valid_bid_ask2 else 0,
                        '最小值': min(valid_bid_ask2) if valid_bid_ask2 else 0,
                        '平均值': sum(valid_bid_ask2) / len(valid_bid_ask2) if valid_bid_ask2 else 0,
                        '样本数量': len(valid_bid_ask2)
                    },
                    {
                        '项目': '买卖前五档量深度比',
                        '最大值': max(valid_bid_ask5) if valid_bid_ask5 else 0,
                        '最小值': min(valid_bid_ask5) if valid_bid_ask5 else 0,
                        '平均值': sum(valid_bid_ask5) / len(valid_bid_ask5) if valid_bid_ask5 else 0,
                        '样本数量': len(valid_bid_ask5)
                    }
                ]
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name=f'{symbol}_统计汇总', index=False)
                
                print(f"   ✅ {symbol}统计结果:")
                print(f"      买一量卖一量深度比: 最大{summary_data[0]['最大值']:.2f}, 最小{summary_data[0]['最小值']:.2f}, 平均{summary_data[0]['平均值']:.2f}")
                print(f"      买卖前两档量深度比: 最大{summary_data[1]['最大值']:.2f}, 最小{summary_data[1]['最小值']:.2f}, 平均{summary_data[1]['平均值']:.2f}")
                print(f"      买卖前五档量深度比: 最大{summary_data[2]['最大值']:.2f}, 最小{summary_data[2]['最小值']:.2f}, 平均{summary_data[2]['平均值']:.2f}")
        
        # 创建验证说明表
        explanation_data = [
            {'说明项目': '数据时间范围', '说明内容': f"{one_minute_ago.strftime('%Y-%m-%d %H:%M:%S')} - {now.strftime('%Y-%m-%d %H:%M:%S')}"},
            {'说明项目': '取数逻辑', '说明内容': '1. 获取前一分钟内所有Bitda数据'},
            {'说明项目': '取数逻辑', '说明内容': '2. 为每条Bitda数据找到该时间点之前最近的Binance数据'},
            {'说明项目': '取数逻辑', '说明内容': '3. 计算深度对比数据'},
            {'说明项目': '取数逻辑', '说明内容': '4. 对所有深度比值进行统计'},
            {'说明项目': '计算公式', '说明内容': '买一量卖一量深度比 = (Bitda买一量 + Bitda卖一量) ÷ (Binance买一量 + Binance卖一量)'},
            {'说明项目': '计算公式', '说明内容': '买卖前两档量深度比 = (Bitda前两档总量) ÷ (Binance前两档总量)'},
            {'说明项目': '计算公式', '说明内容': '买卖前五档量深度比 = (Bitda前五档总量) ÷ (Binance前五档总量)'},
            {'说明项目': '统计方法', '说明内容': '最大值: 所有深度比值中的最大值'},
            {'说明项目': '统计方法', '说明内容': '最小值: 所有深度比值中的最小值'},
            {'说明项目': '统计方法', '说明内容': '平均值: 所有深度比值的算术平均'},
            {'说明项目': '数据验证', '说明内容': '每行数据都包含完整的原始数据库表值和计算过程'},
            {'说明项目': '时间差', '说明内容': 'Bitda时间戳 - Binance时间戳，单位毫秒'}
        ]
        
        explanation_df = pd.DataFrame(explanation_data)
        explanation_df.to_excel(writer, sheet_name='验证说明', index=False)
        
        # 保存Excel文件
        writer.close()
        
        cursor.close()
        connection.close()
        
        print(f"\n✅ Excel文件已生成: {excel_filename}")
        print("📊 包含的工作表:")
        print("   1. BTCUSDT_详细数据 - 所有原始数据和计算过程")
        print("   2. BTCUSDT_统计汇总 - 统计结果汇总")
        print("   3. ETHUSDT_详细数据 - 所有原始数据和计算过程")
        print("   4. ETHUSDT_统计汇总 - 统计结果汇总")
        print("   5. 验证说明 - 取数逻辑和计算公式说明")
        
        return excel_filename
        
    except Exception as e:
        print(f"❌ 导出失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    print("📊 深度验证数据导出器")
    print("=" * 60)
    print("🎯 导出内容:")
    print("   1. 所有原始数据库表值")
    print("   2. 完整计算过程")
    print("   3. 统计结果汇总")
    print("   4. 验证说明文档")
    print()
    
    excel_file = export_verification_to_excel()
    
    if excel_file:
        print(f"\n🎉 导出成功！")
        print(f"📁 文件位置: {excel_file}")
        print("💡 您可以打开Excel文件查看所有验证数据")
    else:
        print("\n❌ 导出失败")

if __name__ == "__main__":
    main()
