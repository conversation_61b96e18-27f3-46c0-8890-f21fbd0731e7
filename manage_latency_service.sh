#!/bin/bash

# ETHUSDT延时处理服务管理脚本

SERVICE_NAME="ethusdt-latency-processor"
SERVICE_FILE="ethusdt-latency-processor.service"
SCRIPT_FILE="ethusdt_realtime_processor.py"
LOG_FILE="ethusdt_realtime_processor.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请使用root权限运行此脚本"
        exit 1
    fi
}

# 安装服务
install_service() {
    print_info "安装ETHUSDT延时处理服务..."
    
    # 检查文件是否存在
    if [ ! -f "$SERVICE_FILE" ]; then
        print_error "服务文件 $SERVICE_FILE 不存在"
        exit 1
    fi
    
    if [ ! -f "$SCRIPT_FILE" ]; then
        print_error "脚本文件 $SCRIPT_FILE 不存在"
        exit 1
    fi
    
    # 复制服务文件
    cp "$SERVICE_FILE" /etc/systemd/system/
    
    # 设置权限
    chmod 644 /etc/systemd/system/$SERVICE_FILE
    chmod +x "$SCRIPT_FILE"
    
    # 重新加载systemd
    systemctl daemon-reload
    
    # 启用服务
    systemctl enable $SERVICE_NAME
    
    print_success "服务安装完成"
}

# 卸载服务
uninstall_service() {
    print_info "卸载ETHUSDT延时处理服务..."
    
    # 停止服务
    systemctl stop $SERVICE_NAME 2>/dev/null
    
    # 禁用服务
    systemctl disable $SERVICE_NAME 2>/dev/null
    
    # 删除服务文件
    rm -f /etc/systemd/system/$SERVICE_FILE
    
    # 重新加载systemd
    systemctl daemon-reload
    
    print_success "服务卸载完成"
}

# 启动服务
start_service() {
    print_info "启动ETHUSDT延时处理服务..."
    systemctl start $SERVICE_NAME
    
    if [ $? -eq 0 ]; then
        print_success "服务启动成功"
    else
        print_error "服务启动失败"
        exit 1
    fi
}

# 停止服务
stop_service() {
    print_info "停止ETHUSDT延时处理服务..."
    systemctl stop $SERVICE_NAME
    
    if [ $? -eq 0 ]; then
        print_success "服务停止成功"
    else
        print_error "服务停止失败"
        exit 1
    fi
}

# 重启服务
restart_service() {
    print_info "重启ETHUSDT延时处理服务..."
    systemctl restart $SERVICE_NAME
    
    if [ $? -eq 0 ]; then
        print_success "服务重启成功"
    else
        print_error "服务重启失败"
        exit 1
    fi
}

# 查看服务状态
status_service() {
    print_info "ETHUSDT延时处理服务状态:"
    systemctl status $SERVICE_NAME --no-pager
}

# 查看服务日志
logs_service() {
    print_info "查看服务日志 (按 Ctrl+C 退出):"
    journalctl -u $SERVICE_NAME -f
}

# 查看应用日志
app_logs() {
    print_info "查看应用日志:"
    if [ -f "$LOG_FILE" ]; then
        tail -f "$LOG_FILE"
    else
        print_warning "日志文件 $LOG_FILE 不存在"
    fi
}

# 检查服务健康状态
health_check() {
    print_info "检查服务健康状态..."
    
    # 检查服务是否运行
    if systemctl is-active --quiet $SERVICE_NAME; then
        print_success "✅ 服务正在运行"
    else
        print_error "❌ 服务未运行"
        return 1
    fi
    
    # 检查最近的日志
    if [ -f "$LOG_FILE" ]; then
        recent_errors=$(tail -n 50 "$LOG_FILE" | grep -c "ERROR")
        recent_success=$(tail -n 50 "$LOG_FILE" | grep -c "处理完成")
        
        print_info "最近50行日志统计:"
        echo "  - 成功处理: $recent_success 次"
        echo "  - 错误次数: $recent_errors 次"
        
        if [ $recent_errors -gt 5 ]; then
            print_warning "⚠️  最近错误较多，建议检查日志"
        fi
    fi
    
    # 检查数据库连接
    python3 -c "
import mysql.connector
try:
    conn = mysql.connector.connect(
        host='localhost',
        user='root', 
        password='Linuxtest',
        database='ethusdt_latency_db'
    )
    conn.close()
    print('✅ 数据库连接正常')
except Exception as e:
    print(f'❌ 数据库连接失败: {e}')
    exit(1)
"
}

# 显示帮助信息
show_help() {
    echo "ETHUSDT延时处理服务管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  install     安装服务"
    echo "  uninstall   卸载服务"
    echo "  start       启动服务"
    echo "  stop        停止服务"
    echo "  restart     重启服务"
    echo "  status      查看服务状态"
    echo "  logs        查看系统日志"
    echo "  app-logs    查看应用日志"
    echo "  health      健康检查"
    echo "  help        显示帮助信息"
    echo ""
    echo "示例:"
    echo "  sudo $0 install    # 安装并启动服务"
    echo "  sudo $0 status     # 查看服务状态"
    echo "  sudo $0 logs       # 查看实时日志"
}

# 主函数
main() {
    case "$1" in
        install)
            check_root
            install_service
            start_service
            status_service
            ;;
        uninstall)
            check_root
            uninstall_service
            ;;
        start)
            check_root
            start_service
            ;;
        stop)
            check_root
            stop_service
            ;;
        restart)
            check_root
            restart_service
            ;;
        status)
            status_service
            ;;
        logs)
            logs_service
            ;;
        app-logs)
            app_logs
            ;;
        health)
            health_check
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
