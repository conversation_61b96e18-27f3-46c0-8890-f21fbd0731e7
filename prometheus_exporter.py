#!/usr/bin/env python3
"""
Prometheus指标导出器
将加密货币数据分析结果导出为Prometheus格式的指标
"""

import asyncio
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
import threading
from datetime import datetime
from analyzer import (
    LatencyAnalyzer, KlineAnalyzer, DepthAnalyzer, 
    PriceAnalyzer, FundingAnalyzer
)
from utils.logging import setup_logger

logger = setup_logger(__name__)

class PrometheusExporter:
    """Prometheus指标导出器"""
    
    def __init__(self, port=8000):
        self.port = port
        self.metrics = {}
        self.last_update = 0
        self.update_interval = 300  # 5分钟更新一次
        
        # 初始化分析器
        self.latency_analyzer = LatencyAnalyzer()
        self.kline_analyzer = KlineAnalyzer()
        self.depth_analyzer = DepthAnalyzer()
        self.price_analyzer = PriceAnalyzer()
        self.funding_analyzer = FundingAnalyzer()
        
    async def collect_metrics(self):
        """收集指标数据"""
        try:
            logger.info("开始收集指标数据...")
            
            # 收集各种分析数据
            latency_data = await self.latency_analyzer.analyze_price_latency(hours=1)
            kline_data = await self.kline_analyzer.analyze_consecutive_identical_klines(hours=1)
            depth_data = await self.depth_analyzer.analyze_depth_comparison(hours=1)
            price_data = await self.price_analyzer.analyze_mark_price_difference(hours=1)
            funding_data = await self.funding_analyzer.analyze_funding_rates(days=1)
            
            # 转换为Prometheus指标格式
            self.metrics = self._convert_to_prometheus_metrics(
                latency_data, kline_data, depth_data, price_data, funding_data
            )
            
            self.last_update = time.time()
            logger.info("指标数据收集完成")
            
        except Exception as e:
            logger.error(f"收集指标数据失败: {e}")
    
    def _convert_to_prometheus_metrics(self, latency_data, kline_data, depth_data, price_data, funding_data):
        """将分析数据转换为Prometheus指标格式"""
        metrics = []
        
        # 延时指标
        eth_latency = latency_data.get('results', {}).get('ETHUSDT', {})
        metrics.extend([
            f'crypto_latency_matches_total{{symbol="ETHUSDT"}} {eth_latency.get("total_matches", 0)}',
            f'crypto_latency_avg_ms{{symbol="ETHUSDT"}} {eth_latency.get("avg_latency_ms", 0)}',
            f'crypto_latency_max_ms{{symbol="ETHUSDT"}} {eth_latency.get("max_latency_ms", 0)}',
        ])
        
        # K线指标
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            symbol_data = kline_data.get('results', {}).get(symbol, {})
            metrics.extend([
                f'crypto_kline_sequences_total{{symbol="{symbol}"}} {symbol_data.get("total_sequences", 0)}',
                f'crypto_kline_identical_rate_percent{{symbol="{symbol}"}} {symbol_data.get("identical_rate_pct", 0)}',
                f'crypto_kline_max_sequence_length{{symbol="{symbol}"}} {symbol_data.get("max_sequence_length", 0)}',
            ])
        
        # 深度指标
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            symbol_data = depth_data.get('results', {}).get(symbol, {})
            metrics.extend([
                f'crypto_depth_comparisons_total{{symbol="{symbol}"}} {symbol_data.get("total_comparisons", 0)}',
                f'crypto_depth_ratio_avg{{symbol="{symbol}"}} {symbol_data.get("avg_depth_ratio", 0)}',
                f'crypto_depth_bitda_advantage_percent{{symbol="{symbol}"}} {symbol_data.get("bitda_advantage_pct", 0)}',
            ])
        
        # 价格差值指标
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            symbol_data = price_data.get('results', {}).get(symbol, {})
            metrics.extend([
                f'crypto_price_records_total{{symbol="{symbol}"}} {symbol_data.get("total_records", 0)}',
                f'crypto_price_diff_avg_percent{{symbol="{symbol}"}} {symbol_data.get("avg_rel_diff_pct", 0)}',
                f'crypto_price_diff_max_percent{{symbol="{symbol}"}} {symbol_data.get("max_rel_diff_pct", 0)}',
                f'crypto_price_high_diff_rate_percent{{symbol="{symbol}"}} {symbol_data.get("high_diff_rate_pct", 0)}',
            ])
        
        # 资金费率指标
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            symbol_data = funding_data.get('results', {}).get(symbol, {})
            metrics.extend([
                f'crypto_funding_rate_current{{symbol="{symbol}"}} {symbol_data.get("current_funding_rate", 0)}',
                f'crypto_funding_rate_avg{{symbol="{symbol}"}} {symbol_data.get("avg_funding_rate", 0)}',
                f'crypto_funding_rate_volatility{{symbol="{symbol}"}} {symbol_data.get("funding_rate_volatility", 0)}',
                f'crypto_funding_records_total{{symbol="{symbol}"}} {symbol_data.get("total_records", 0)}',
            ])
        
        # 系统指标
        metrics.extend([
            f'crypto_exporter_last_update_timestamp {self.last_update}',
            f'crypto_exporter_update_interval_seconds {self.update_interval}',
        ])
        
        return metrics
    
    def get_metrics_text(self):
        """获取Prometheus格式的指标文本"""
        if not self.metrics:
            return "# No metrics available\n"
        
        # 添加帮助信息和类型信息
        output = []
        output.append("# HELP crypto_latency_matches_total Total number of price matches for latency analysis")
        output.append("# TYPE crypto_latency_matches_total counter")
        output.append("# HELP crypto_latency_avg_ms Average latency in milliseconds")
        output.append("# TYPE crypto_latency_avg_ms gauge")
        output.append("# HELP crypto_kline_sequences_total Total number of consecutive identical kline sequences")
        output.append("# TYPE crypto_kline_sequences_total counter")
        output.append("# HELP crypto_kline_identical_rate_percent Percentage of identical klines")
        output.append("# TYPE crypto_kline_identical_rate_percent gauge")
        output.append("# HELP crypto_depth_ratio_avg Average depth ratio between exchanges")
        output.append("# TYPE crypto_depth_ratio_avg gauge")
        output.append("# HELP crypto_funding_rate_current Current funding rate")
        output.append("# TYPE crypto_funding_rate_current gauge")
        output.append("")
        
        # 添加指标数据
        output.extend(self.metrics)
        
        return "\n".join(output) + "\n"

class MetricsHandler(BaseHTTPRequestHandler):
    """HTTP请求处理器"""
    
    def __init__(self, exporter, *args, **kwargs):
        self.exporter = exporter
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/metrics':
            # 检查是否需要更新指标
            current_time = time.time()
            if current_time - self.exporter.last_update > self.exporter.update_interval:
                # 在后台更新指标
                threading.Thread(
                    target=lambda: asyncio.run(self.exporter.collect_metrics())
                ).start()
            
            # 返回指标数据
            metrics_text = self.exporter.get_metrics_text()
            
            self.send_response(200)
            self.send_header('Content-Type', 'text/plain; charset=utf-8')
            self.end_headers()
            self.wfile.write(metrics_text.encode('utf-8'))
        else:
            self.send_response(404)
            self.end_headers()
    
    def log_message(self, format, *args):
        """禁用默认日志"""
        pass

async def main():
    """主函数"""
    exporter = PrometheusExporter(port=8000)
    
    # 初始收集一次指标
    await exporter.collect_metrics()
    
    # 创建HTTP服务器
    def handler(*args, **kwargs):
        return MetricsHandler(exporter, *args, **kwargs)
    
    server = HTTPServer(('0.0.0.0', exporter.port), handler)
    
    print(f"🚀 Prometheus指标导出器启动")
    print(f"📊 监听端口: {exporter.port}")
    print(f"🔗 指标地址: http://localhost:{exporter.port}/metrics")
    print(f"⏱️  更新间隔: {exporter.update_interval}秒")
    print("按 Ctrl+C 停止服务")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n⏹️  服务已停止")
        server.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
