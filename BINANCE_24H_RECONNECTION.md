# 币安WebSocket 24小时重连机制

## 问题描述
币安WebSocket连接有24小时的有效期限制，超过24小时后会自动断开连接。为了保持不间断的数据收集，需要实现主动重连机制。

## 解决方案

### 1. 主要配置参数
```python
# 币安WebSocket连接配置
BINANCE_CONNECTION_TIMEOUT = 23 * 60 * 60  # 23小时后主动重连，避免24小时限制
BINANCE_PING_INTERVAL = 30  # 每30秒发送一次ping保持连接活跃
```

### 2. 实现机制

#### A. 连接时间跟踪
- 记录每个连接的开始时间 `connection_start_time`
- 在消息处理循环中持续检查连接持续时间
- 当连接时间接近23小时时主动断开并重连

#### B. Ping保活机制
```python
async def binance_ping_task(ws, symbol):
    """定期向Binance WebSocket发送ping消息保持连接活跃"""
    while not SHUTDOWN_FLAG:
        try:
            await asyncio.sleep(BINANCE_PING_INTERVAL)
            if not SHUTDOWN_FLAG:
                await ws.ping()
                logger.debug(f"向Binance {symbol} 发送ping")
        except Exception as e:
            logger.warning(f"Binance {symbol} ping任务出错: {e}")
            break
```

#### C. 主动重连逻辑
```python
# 检查连接时间，如果接近24小时则主动重连
current_time = time.time()
connection_duration = current_time - connection_start_time

if connection_duration >= BINANCE_CONNECTION_TIMEOUT:
    logger.info(f"Binance {symbol} 连接已运行 {connection_duration/3600:.1f} 小时，主动重连避免24小时限制")
    break
```

### 3. 监控和日志

#### A. 连接时长显示
- 每100条消息显示一次连接时长
- 格式：`Binance ETHUSDT 消息率: 214.12 消息/秒，连接时长: 2.5小时`

#### B. 重连日志
- 主动重连时记录详细日志
- 被动重连（连接断开）时也有相应日志

#### C. 监控脚本
使用 `monitor_binance_connection.py` 脚本可以实时监控连接状态：
```bash
python monitor_binance_connection.py
```

### 4. 关键特性

#### A. 无数据丢失
- 在23小时主动重连，避免被动断开
- 重连过程中数据队列继续处理
- 多个交易对独立重连，避免同时断开

#### B. 错误处理
- 网络异常时自动重连
- 连接失败时指数退避重试
- 异常情况下的优雅降级

#### C. 性能优化
- 使用异步ping任务，不影响数据接收
- 超时机制避免长时间阻塞
- 独立的ping任务管理

### 5. 使用说明

#### A. 启动程序
```bash
cd WS_DATA_Collector
python ws_data_collector.py
```

#### B. 监控连接
```bash
# 查看实时日志
tail -f /tmp/ws_data_collector.log

# 运行监控脚本
python monitor_binance_connection.py
```

#### C. 检查重连
```bash
# 查看重连日志
grep "主动重连" /tmp/ws_data_collector.log
grep "连接时长" /tmp/ws_data_collector.log
```

### 6. 预期行为

1. **正常运行**：连接建立后持续接收数据
2. **定期ping**：每30秒发送ping保持连接活跃
3. **时长监控**：每100条消息显示连接时长
4. **主动重连**：23小时后主动断开重连
5. **异常重连**：网络问题时自动重连

### 7. 故障排除

#### A. 连接频繁断开
- 检查网络稳定性
- 确认币安API访问权限
- 查看错误日志

#### B. 重连失败
- 检查币安服务状态
- 验证网络连接
- 查看详细错误信息

#### C. 数据丢失
- 检查数据库连接
- 查看队列处理状态
- 验证数据保存逻辑

这个机制确保了币安WebSocket连接的稳定性和连续性，避免了24小时限制导致的数据中断。
