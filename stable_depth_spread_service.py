#!/usr/bin/env python3
"""
稳定的深度价差对比分析后台服务
每分钟处理1分钟前的数据，稳定运行
"""

import time
import threading
import logging
import signal
import sys
from datetime import datetime, timedelta
from fixed_depth_spread_analyzer import FixedDepthSpreadAnalyzer
import mysql.connector

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('depth_spread_service.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class StableDepthSpreadService:
    """稳定的深度价差分析后台服务"""
    
    def __init__(self):
        self.analyzer = FixedDepthSpreadAnalyzer()
        self.running = False
        self.symbols = ['BTCUSDT', 'ETHUSDT']
        self.analysis_interval = 60  # 60秒分析一次
        self.last_analysis_time = None
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"📋 收到信号 {signum}，准备停止服务...")
        self.stop_service()
    
    def start_service(self):
        """启动后台服务"""
        logger.info("🚀 启动稳定的深度价差分析后台服务...")
        logger.info("=" * 60)
        logger.info("📋 服务配置:")
        logger.info(f"   📊 分析币种: {', '.join(self.symbols)}")
        logger.info(f"   ⏰ 分析频率: 每{self.analysis_interval}秒")
        logger.info("   📅 数据延迟: 处理1分钟前的数据")
        logger.info("   🔄 服务模式: 持续稳定运行")
        logger.info("   📝 日志文件: depth_spread_service.log")
        logger.info("   🛑 停止方式: Ctrl+C 或 kill 信号")
        logger.info("=" * 60)
        
        self.running = True
        
        # 启动分析线程
        analysis_thread = threading.Thread(target=self._analysis_loop, daemon=True)
        analysis_thread.start()
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        monitor_thread.start()
        
        # 启动健康检查线程
        health_thread = threading.Thread(target=self._health_check_loop, daemon=True)
        health_thread.start()
        
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("📋 收到键盘中断信号...")
            self.stop_service()
    
    def stop_service(self):
        """停止服务"""
        logger.info("🛑 正在停止深度价差分析服务...")
        self.running = False
        time.sleep(2)  # 等待线程结束
        logger.info("✅ 服务已安全停止")
    
    def _analysis_loop(self):
        """分析循环"""
        logger.info("🔍 分析线程启动")
        
        while self.running:
            try:
                current_time = datetime.now()
                
                # 检查是否到了分析时间
                if self._should_run_analysis(current_time):
                    logger.info(f"⏰ 开始分析 {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
                    
                    analysis_success = 0
                    analysis_total = len(self.symbols)
                    
                    for symbol in self.symbols:
                        try:
                            success = self.analyzer.analyze_symbol(symbol)
                            if success:
                                analysis_success += 1
                                logger.info(f"   ✅ {symbol}: 分析成功")
                            else:
                                logger.warning(f"   ⚠️ {symbol}: 分析失败")
                        except Exception as e:
                            logger.error(f"   ❌ {symbol} 分析异常: {e}")
                    
                    self.last_analysis_time = current_time
                    logger.info(f"📋 本轮分析完成: {analysis_success}/{analysis_total} 成功")
                    
                    # 分析完成后等待到下一个分析周期
                    next_analysis = current_time + timedelta(seconds=self.analysis_interval)
                    wait_seconds = (next_analysis - datetime.now()).total_seconds()
                    if wait_seconds > 0:
                        logger.info(f"⏰ 等待 {wait_seconds:.0f} 秒后进行下一轮分析...")
                        
                        # 分段等待，便于响应停止信号
                        while wait_seconds > 0 and self.running:
                            sleep_time = min(5, wait_seconds)
                            time.sleep(sleep_time)
                            wait_seconds -= sleep_time
                else:
                    time.sleep(1)
                    
            except Exception as e:
                logger.error(f"❌ 分析循环异常: {e}")
                time.sleep(10)  # 异常后等待10秒再继续
    
    def _should_run_analysis(self, current_time):
        """判断是否应该运行分析"""
        if self.last_analysis_time is None:
            return True
        
        time_since_last = (current_time - self.last_analysis_time).total_seconds()
        return time_since_last >= self.analysis_interval
    
    def _monitor_loop(self):
        """监控循环"""
        logger.info("📊 监控线程启动")
        
        while self.running:
            try:
                # 每10分钟输出一次状态
                time.sleep(600)
                
                if self.running:
                    logger.info("💓 服务运行正常")
                    
                    # 检查最近的分析结果
                    self._check_recent_results()
                    
            except Exception as e:
                logger.error(f"❌ 监控循环异常: {e}")
    
    def _health_check_loop(self):
        """健康检查循环"""
        logger.info("🏥 健康检查线程启动")
        
        while self.running:
            try:
                # 每5分钟检查一次数据库连接
                time.sleep(300)
                
                if self.running:
                    self._check_database_health()
                    
            except Exception as e:
                logger.error(f"❌ 健康检查异常: {e}")
    
    def _check_database_health(self):
        """检查数据库健康状态"""
        try:
            # 检查源数据库连接
            connection = mysql.connector.connect(**self.analyzer.source_db_config)
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            cursor.close()
            connection.close()
            
            # 检查分析数据库连接
            connection = mysql.connector.connect(**self.analyzer.analysis_db_config)
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            cursor.close()
            connection.close()
            
            logger.info("🏥 数据库连接健康")
            
        except Exception as e:
            logger.error(f"❌ 数据库健康检查失败: {e}")
    
    def _check_recent_results(self):
        """检查最近的分析结果"""
        try:
            connection = mysql.connector.connect(**self.analyzer.analysis_db_config)
            cursor = connection.cursor()
            
            # 检查最近10分钟的数据
            recent_time = datetime.now() - timedelta(minutes=10)
            
            for symbol in self.symbols:
                # 检查深度对比数据
                cursor.execute("""
                    SELECT COUNT(*) FROM depth_comparison 
                    WHERE symbol = %s AND analysis_time >= %s
                """, (symbol, recent_time))
                depth_count = cursor.fetchone()[0]
                
                # 检查价差对比数据
                cursor.execute("""
                    SELECT COUNT(*) FROM spread_comparison 
                    WHERE symbol = %s AND analysis_time >= %s
                """, (symbol, recent_time))
                spread_count = cursor.fetchone()[0]
                
                logger.info(f"   📊 {symbol}: 深度{depth_count}条, 价差{spread_count}条 (最近10分钟)")
            
            cursor.close()
            connection.close()
            
        except Exception as e:
            logger.error(f"❌ 检查结果失败: {e}")
    
    def get_service_status(self):
        """获取服务状态"""
        return {
            'running': self.running,
            'last_analysis_time': self.last_analysis_time,
            'symbols': self.symbols,
            'analysis_interval': self.analysis_interval
        }

def main():
    """主函数"""
    print("🚀 稳定的深度价差对比分析后台服务")
    print("=" * 60)
    print("功能:")
    print("  - 每分钟自动分析BTCUSDT和ETHUSDT")
    print("  - 处理1分钟前的数据避免实时性问题")
    print("  - 计算深度对比和价差对比")
    print("  - 自动保存分析结果到数据库")
    print("  - 稳定运行，支持优雅停止")
    print("  - 健康检查和监控")
    print("  - 详细日志记录")
    print()
    print("控制:")
    print("  - 启动: python3 stable_depth_spread_service.py")
    print("  - 停止: Ctrl+C 或 kill <pid>")
    print("  - 日志: tail -f depth_spread_service.log")
    print()
    
    service = StableDepthSpreadService()
    
    try:
        service.start_service()
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {e}")
    finally:
        logger.info("🔚 服务已完全停止")

if __name__ == "__main__":
    main()
