#!/usr/bin/env python3
"""
创建简化的仪表板
只保留两个深度对比面板
"""

import requests
import json

def create_simplified_dashboard():
    """创建只有两个深度对比面板的仪表板"""
    print("🎨 创建简化的深度对比仪表板...")
    
    session = requests.Session()
    session.auth = ("admin", "admin")
    datasource_uid = "fenikp5swzgu8d"  # 使用工作的数据源
    
    dashboard_config = {
        "dashboard": {
            "id": None,
            "title": "📊 深度对比仪表板 - 1分钟刷新",
            "tags": ["depth-comparison", "simplified"],
            "timezone": "browser",
            "panels": [
                # 1. BTCUSDT深度对比
                {
                    "id": 1,
                    "title": "📊 BTCUSDT 深度对比",
                    "type": "table",
                    "gridPos": {"h": 10, "w": 12, "x": 0, "y": 0},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            SELECT 
                                '买一量' as 项目,
                                (SELECT ROUND(bid_qty_1, 2) FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1) as Bitda,
                                (SELECT ROUND(bid_qty_1, 2) FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1) as Binance,
                                ROUND((SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1) / 
                                      (SELECT bid_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                            UNION ALL
                            SELECT 
                                '卖一量' as 项目,
                                (SELECT ROUND(ask_qty_1, 2) FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1) as Bitda,
                                (SELECT ROUND(ask_qty_1, 2) FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1) as Binance,
                                ROUND((SELECT ask_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1) / 
                                      (SELECT ask_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                            UNION ALL
                            SELECT 
                                '买一量卖一量' as 项目,
                                (SELECT ROUND(bid_qty_1 + ask_qty_1, 2) FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1) as Bitda,
                                (SELECT ROUND(bid_qty_1 + ask_qty_1, 2) FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1) as Binance,
                                ROUND((SELECT bid_qty_1 + ask_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1) / 
                                      (SELECT bid_qty_1 + ask_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                        """,
                        "refId": "A"
                    }]
                },
                
                # 2. ETHUSDT深度对比
                {
                    "id": 2,
                    "title": "📊 ETHUSDT 深度对比",
                    "type": "table",
                    "gridPos": {"h": 10, "w": 12, "x": 12, "y": 0},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            SELECT 
                                '买一量' as 项目,
                                (SELECT ROUND(bid_qty_1, 2) FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY id DESC LIMIT 1) as Bitda,
                                (SELECT ROUND(bid_qty_1, 2) FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1) as Binance,
                                ROUND((SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY id DESC LIMIT 1) / 
                                      (SELECT bid_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                            UNION ALL
                            SELECT 
                                '卖一量' as 项目,
                                (SELECT ROUND(ask_qty_1, 2) FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY id DESC LIMIT 1) as Bitda,
                                (SELECT ROUND(ask_qty_1, 2) FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1) as Binance,
                                ROUND((SELECT ask_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY id DESC LIMIT 1) / 
                                      (SELECT ask_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                            UNION ALL
                            SELECT 
                                '买一量卖一量' as 项目,
                                (SELECT ROUND(bid_qty_1 + ask_qty_1, 2) FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY id DESC LIMIT 1) as Bitda,
                                (SELECT ROUND(bid_qty_1 + ask_qty_1, 2) FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1) as Binance,
                                ROUND((SELECT bid_qty_1 + ask_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY id DESC LIMIT 1) / 
                                      (SELECT bid_qty_1 + ask_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                        """,
                        "refId": "A"
                    }]
                }
            ],
            "time": {"from": "now-1h", "to": "now"},
            "refresh": "1m",  # 1分钟刷新
            "schemaVersion": 30,
            "version": 1
        },
        "overwrite": True
    }
    
    try:
        print("   📡 发送请求到Grafana...")
        response = session.post(
            "http://localhost:3000/api/dashboards/db",
            json=dashboard_config,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"   📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            dashboard_url = f"http://localhost:3000{result['url']}"
            print(f"   ✅ 简化仪表板创建成功")
            print(f"   🌐 地址: {dashboard_url}")
            return dashboard_url
        else:
            print(f"   ❌ 创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return None

def create_depth_stats_dashboard():
    """创建深度统计仪表板"""
    print("🎨 创建深度统计仪表板...")
    
    session = requests.Session()
    session.auth = ("admin", "admin")
    datasource_uid = "fenikp5swzgu8d"  # 使用工作的数据源
    
    dashboard_config = {
        "dashboard": {
            "id": None,
            "title": "📈 深度统计仪表板 - 1分钟刷新",
            "tags": ["depth-stats", "statistics"],
            "timezone": "browser",
            "panels": [
                # 1. BTCUSDT深度统计
                {
                    "id": 1,
                    "title": "📈 BTCUSDT 深度统计",
                    "type": "table",
                    "gridPos": {"h": 10, "w": 12, "x": 0, "y": 0},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            SELECT 
                                '买一量卖一量深度比' as 项目,
                                ROUND(MAX(bid_qty_1 + ask_qty_1), 2) as 最大值,
                                ROUND(MIN(bid_qty_1 + ask_qty_1), 2) as 最小值,
                                ROUND(AVG(bid_qty_1 + ask_qty_1), 2) as 平均值
                            FROM bitda_depth 
                            WHERE symbol = 'BTCUSDT' 
                                AND id >= (SELECT MAX(id) - 100 FROM bitda_depth WHERE symbol = 'BTCUSDT')
                            UNION ALL
                            SELECT 
                                '买卖前两档量深度比' as 项目,
                                ROUND(MAX(bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2), 2) as 最大值,
                                ROUND(MIN(bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2), 2) as 最小值,
                                ROUND(AVG(bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2), 2) as 平均值
                            FROM bitda_depth 
                            WHERE symbol = 'BTCUSDT' 
                                AND id >= (SELECT MAX(id) - 100 FROM bitda_depth WHERE symbol = 'BTCUSDT')
                            UNION ALL
                            SELECT 
                                '买卖前五档量深度比' as 项目,
                                ROUND(MAX(bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5), 2) as 最大值,
                                ROUND(MIN(bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5), 2) as 最小值,
                                ROUND(AVG(bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5), 2) as 平均值
                            FROM bitda_depth 
                            WHERE symbol = 'BTCUSDT' 
                                AND id >= (SELECT MAX(id) - 100 FROM bitda_depth WHERE symbol = 'BTCUSDT')
                        """,
                        "refId": "A"
                    }]
                },
                
                # 2. ETHUSDT深度统计
                {
                    "id": 2,
                    "title": "📈 ETHUSDT 深度统计",
                    "type": "table",
                    "gridPos": {"h": 10, "w": 12, "x": 12, "y": 0},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            SELECT 
                                '买一量卖一量深度比' as 项目,
                                ROUND(MAX(bid_qty_1 + ask_qty_1), 2) as 最大值,
                                ROUND(MIN(bid_qty_1 + ask_qty_1), 2) as 最小值,
                                ROUND(AVG(bid_qty_1 + ask_qty_1), 2) as 平均值
                            FROM bitda_depth 
                            WHERE symbol = 'ETHUSDT' 
                                AND id >= (SELECT MAX(id) - 100 FROM bitda_depth WHERE symbol = 'ETHUSDT')
                            UNION ALL
                            SELECT 
                                '买卖前两档量深度比' as 项目,
                                ROUND(MAX(bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2), 2) as 最大值,
                                ROUND(MIN(bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2), 2) as 最小值,
                                ROUND(AVG(bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2), 2) as 平均值
                            FROM bitda_depth 
                            WHERE symbol = 'ETHUSDT' 
                                AND id >= (SELECT MAX(id) - 100 FROM bitda_depth WHERE symbol = 'ETHUSDT')
                            UNION ALL
                            SELECT 
                                '买卖前五档量深度比' as 项目,
                                ROUND(MAX(bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5), 2) as 最大值,
                                ROUND(MIN(bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5), 2) as 最小值,
                                ROUND(AVG(bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5), 2) as 平均值
                            FROM bitda_depth 
                            WHERE symbol = 'ETHUSDT' 
                                AND id >= (SELECT MAX(id) - 100 FROM bitda_depth WHERE symbol = 'ETHUSDT')
                        """,
                        "refId": "A"
                    }]
                }
            ],
            "time": {"from": "now-1h", "to": "now"},
            "refresh": "1m",  # 1分钟刷新
            "schemaVersion": 30,
            "version": 1
        },
        "overwrite": True
    }
    
    try:
        print("   📡 发送请求到Grafana...")
        response = session.post(
            "http://localhost:3000/api/dashboards/db",
            json=dashboard_config,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"   📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            dashboard_url = f"http://localhost:3000{result['url']}"
            print(f"   ✅ 深度统计仪表板创建成功")
            print(f"   🌐 地址: {dashboard_url}")
            return dashboard_url
        else:
            print(f"   ❌ 创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return None

def main():
    """主函数"""
    print("📊 创建简化和统计仪表板")
    print("=" * 50)
    
    print("1️⃣ 创建简化的深度对比仪表板...")
    comparison_url = create_simplified_dashboard()
    
    print("\n2️⃣ 创建深度统计仪表板...")
    stats_url = create_depth_stats_dashboard()
    
    print("\n" + "=" * 50)
    print("🎉 创建完成！")
    
    if comparison_url:
        print(f"📊 深度对比仪表板: {comparison_url}")
    
    if stats_url:
        print(f"📈 深度统计仪表板: {stats_url}")
    
    print("\n💡 关于手动修改面板名称:")
    print("   ✅ 可以安全修改面板标题")
    print("   ✅ 不会影响数据源连接")
    print("   ✅ 不会影响查询功能")
    print("   ⚠️ 只是显示名称的改变")
    
    # 打开浏览器
    if comparison_url:
        try:
            import webbrowser
            webbrowser.open(comparison_url)
            print(f"\n🌐 已自动打开深度对比仪表板")
        except:
            pass

if __name__ == "__main__":
    main()
