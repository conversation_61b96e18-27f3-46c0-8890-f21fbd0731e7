#!/usr/bin/env python3
"""
监控系统管理脚本
管理Prometheus、Grafana和数据分析指标导出器
"""

import subprocess
import time
import sys
import signal
import os
from pathlib import Path

class MonitoringManager:
    """监控系统管理器"""
    
    def __init__(self):
        self.services = {
            'prometheus': {'port': 9090, 'url': 'http://localhost:9090'},
            'grafana': {'port': 3000, 'url': 'http://localhost:3000'},
            'exporter': {'port': 8000, 'url': 'http://localhost:8000/metrics'}
        }
        self.exporter_process = None
        
    def check_service_status(self, service_name):
        """检查服务状态"""
        try:
            if service_name == 'exporter':
                # 检查我们的指标导出器
                import requests
                response = requests.get(self.services['exporter']['url'], timeout=5)
                return response.status_code == 200
            else:
                # 检查系统服务
                result = subprocess.run(
                    ['sudo', 'systemctl', 'is-active', f'{service_name}-server' if service_name == 'grafana' else service_name],
                    capture_output=True, text=True
                )
                return result.stdout.strip() == 'active'
        except Exception:
            return False
    
    def start_service(self, service_name):
        """启动服务"""
        if service_name == 'exporter':
            return self.start_exporter()
        else:
            try:
                service_full_name = f'{service_name}-server' if service_name == 'grafana' else service_name
                subprocess.run(['sudo', 'systemctl', 'start', service_full_name], check=True)
                return True
            except subprocess.CalledProcessError:
                return False
    
    def stop_service(self, service_name):
        """停止服务"""
        if service_name == 'exporter':
            return self.stop_exporter()
        else:
            try:
                service_full_name = f'{service_name}-server' if service_name == 'grafana' else service_name
                subprocess.run(['sudo', 'systemctl', 'stop', service_full_name], check=True)
                return True
            except subprocess.CalledProcessError:
                return False
    
    def start_exporter(self):
        """启动指标导出器"""
        if self.exporter_process and self.exporter_process.poll() is None:
            print("📊 指标导出器已在运行")
            return True
        
        try:
            self.exporter_process = subprocess.Popen(
                ['python', 'prometheus_exporter.py'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            time.sleep(3)  # 等待启动
            
            if self.exporter_process.poll() is None:
                print("✅ 指标导出器启动成功")
                return True
            else:
                print("❌ 指标导出器启动失败")
                return False
        except Exception as e:
            print(f"❌ 启动指标导出器失败: {e}")
            return False
    
    def stop_exporter(self):
        """停止指标导出器"""
        if self.exporter_process:
            try:
                self.exporter_process.terminate()
                self.exporter_process.wait(timeout=10)
                print("⏹️  指标导出器已停止")
                return True
            except subprocess.TimeoutExpired:
                self.exporter_process.kill()
                print("🔪 强制停止指标导出器")
                return True
            except Exception as e:
                print(f"❌ 停止指标导出器失败: {e}")
                return False
        return True
    
    def show_status(self):
        """显示所有服务状态"""
        print("📊 监控系统状态")
        print("="*50)
        
        for service_name, config in self.services.items():
            status = self.check_service_status(service_name)
            status_icon = "✅" if status else "❌"
            status_text = "运行中" if status else "已停止"
            
            print(f"{status_icon} {service_name.capitalize()}: {status_text}")
            print(f"   端口: {config['port']}")
            print(f"   地址: {config['url']}")
            print()
    
    def start_all(self):
        """启动所有服务"""
        print("🚀 启动所有监控服务...")
        
        success_count = 0
        
        for service_name in ['prometheus', 'grafana', 'exporter']:
            print(f"\n🔧 启动 {service_name}...")
            
            if self.check_service_status(service_name):
                print(f"✅ {service_name} 已在运行")
                success_count += 1
            elif self.start_service(service_name):
                print(f"✅ {service_name} 启动成功")
                success_count += 1
            else:
                print(f"❌ {service_name} 启动失败")
        
        print(f"\n📈 启动完成: {success_count}/3 个服务运行中")
        
        if success_count == 3:
            print("\n🎉 所有服务启动成功！")
            self.show_access_info()
        else:
            print("\n⚠️  部分服务启动失败，请检查日志")
    
    def stop_all(self):
        """停止所有服务"""
        print("⏹️  停止所有监控服务...")
        
        for service_name in ['exporter', 'grafana', 'prometheus']:
            print(f"🔧 停止 {service_name}...")
            if self.stop_service(service_name):
                print(f"✅ {service_name} 已停止")
            else:
                print(f"❌ {service_name} 停止失败")
        
        print("⏹️  所有服务已停止")
    
    def restart_all(self):
        """重启所有服务"""
        print("🔄 重启所有监控服务...")
        self.stop_all()
        time.sleep(2)
        self.start_all()
    
    def show_access_info(self):
        """显示访问信息"""
        print("\n" + "="*50)
        print("🌐 访问地址")
        print("="*50)
        print("📊 Prometheus: http://localhost:9090")
        print("   - 查看指标和目标状态")
        print("   - 执行PromQL查询")
        print()
        print("📈 Grafana: http://localhost:3000")
        print("   - 用户名: admin")
        print("   - 密码: admin")
        print("   - 查看可视化仪表板")
        print()
        print("🔧 指标导出器: http://localhost:8000/metrics")
        print("   - 查看原始指标数据")
        print("   - Prometheus数据源")
        print()
        print("💡 提示:")
        print("   - 首次访问Grafana建议修改默认密码")
        print("   - 仪表板会自动刷新显示最新数据")
        print("   - 指标每5分钟更新一次")
    
    def run_interactive(self):
        """交互式管理"""
        while True:
            print("\n" + "="*50)
            print("🎛️  监控系统管理器")
            print("="*50)
            print("1. 显示状态")
            print("2. 启动所有服务")
            print("3. 停止所有服务")
            print("4. 重启所有服务")
            print("5. 显示访问信息")
            print("0. 退出")
            
            try:
                choice = input("\n请选择操作 (0-5): ").strip()
                
                if choice == '1':
                    self.show_status()
                elif choice == '2':
                    self.start_all()
                elif choice == '3':
                    self.stop_all()
                elif choice == '4':
                    self.restart_all()
                elif choice == '5':
                    self.show_access_info()
                elif choice == '0':
                    print("👋 再见！")
                    break
                else:
                    print("❌ 无效选择，请重试")
                    
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 操作失败: {e}")

def signal_handler(signum, frame):
    """信号处理器"""
    print("\n⏹️  收到停止信号，正在清理...")
    manager = MonitoringManager()
    manager.stop_exporter()
    sys.exit(0)

def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    manager = MonitoringManager()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'start':
            manager.start_all()
        elif command == 'stop':
            manager.stop_all()
        elif command == 'restart':
            manager.restart_all()
        elif command == 'status':
            manager.show_status()
        elif command == 'info':
            manager.show_access_info()
        else:
            print("❌ 未知命令")
            print("用法: python monitoring_manager.py [start|stop|restart|status|info]")
            print("或直接运行进入交互模式")
    else:
        manager.run_interactive()

if __name__ == "__main__":
    main()
