#!/usr/bin/env python3
"""
演示正确逻辑的仪表板 - 使用模拟数据
"""

import requests
import json
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DemoCorrectDashboard:
    """演示正确逻辑的仪表板"""
    
    def __init__(self):
        self.grafana_url = "http://localhost:3000"
        self.admin_user = "admin"
        self.admin_pass = "admin"
        self.session = requests.Session()
        self.session.auth = (self.admin_user, self.admin_pass)
    
    def get_demo_data(self):
        """获取演示数据"""
        return {
            'BTCUSDT': {
                'latest': {
                    'time_diff_ms': 15,  # 最新数据点的时间差
                    'bitda_spread': 0.1000,
                    'binance_spread': 0.1000,
                    'bitda_bid1_qty': 12.45,
                    'bitda_ask1_qty': 8.32,
                    'bitda_bid2_qty': 7.89,
                    'bitda_ask2_qty': 6.54,
                    'bitda_bid5_total': 45.67,
                    'bitda_ask5_total': 38.92,
                    'binance_bid1_qty': 3.21,
                    'binance_ask1_qty': 5.67,
                    'binance_bid2_qty': 2.34,
                    'binance_ask2_qty': 4.56,
                    'binance_bid5_total': 15.78,
                    'binance_ask5_total': 23.45
                },
                'stats': {
                    'bitda_spread_max': 0.5000,
                    'bitda_spread_min': 0.1000,
                    'bitda_spread_avg': 0.2500,
                    'bitda_spread_median': 0.2000,
                    'binance_spread_max': 0.3000,
                    'binance_spread_min': 0.1000,
                    'binance_spread_avg': 0.1500,
                    'binance_spread_median': 0.1200,
                    'bitda_sample_count': 85,
                    'binance_sample_count': 420,
                    'bitda_bid1_avg': 10.25,
                    'bitda_ask1_avg': 9.87,
                    'binance_bid1_avg': 4.12,
                    'binance_ask1_avg': 6.34
                }
            },
            'ETHUSDT': {
                'latest': {
                    'time_diff_ms': 8,  # 最新数据点的时间差
                    'bitda_spread': 0.0100,
                    'binance_spread': 0.0100,
                    'bitda_bid1_qty': 45.67,
                    'bitda_ask1_qty': 52.34,
                    'bitda_bid2_qty': 38.92,
                    'bitda_ask2_qty': 41.23,
                    'bitda_bid5_total': 189.45,
                    'bitda_ask5_total': 203.67,
                    'binance_bid1_qty': 28.34,
                    'binance_ask1_qty': 35.67,
                    'binance_bid2_qty': 15.23,
                    'binance_ask2_qty': 18.45,
                    'binance_bid5_total': 95.67,
                    'binance_ask5_total': 112.34
                },
                'stats': {
                    'bitda_spread_max': 0.0800,
                    'bitda_spread_min': 0.0100,
                    'bitda_spread_avg': 0.0350,
                    'bitda_spread_median': 0.0250,
                    'binance_spread_max': 0.0500,
                    'binance_spread_min': 0.0100,
                    'binance_spread_avg': 0.0200,
                    'binance_spread_median': 0.0150,
                    'bitda_sample_count': 120,
                    'binance_sample_count': 480,
                    'bitda_bid1_avg': 42.15,
                    'bitda_ask1_avg': 48.67,
                    'binance_bid1_avg': 25.34,
                    'binance_ask1_avg': 32.45
                }
            }
        }
    
    def create_demo_dashboard(self):
        """创建演示仪表板"""
        logger.info("🎨 创建演示正确逻辑的仪表板...")
        
        data = self.get_demo_data()
        current_time = datetime.now()
        time_str = current_time.strftime('%Y-%m-%d %H:%M:%S')
        
        dashboard_config = {
            "dashboard": {
                "id": None,
                "title": f"✅ 正确逻辑演示版 BTCUSDT vs ETHUSDT 深度价差分析 - {current_time.strftime('%m-%d %H:%M')}",
                "tags": ["demo", "correct", "logic", "depth", "spread"],
                "timezone": "browser",
                "panels": [
                    # 标题面板
                    {
                        "id": 1,
                        "title": "",
                        "type": "text",
                        "gridPos": {"h": 3, "w": 24, "x": 0, "y": 0},
                        "targets": [],
                        "options": {
                            "mode": "html",
                            "content": f"""
                            <div style="
                                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                                color: white;
                                height: 100%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                border-radius: 8px;
                                font-family: Arial, sans-serif;
                            ">
                                <div style="text-align: center;">
                                    <h1 style="margin: 0; font-size: 24px;">✅ 正确逻辑演示版 BTCUSDT vs ETHUSDT 深度价差分析</h1>
                                    <p style="margin: 8px 0 0 0; font-size: 14px; opacity: 0.9;">
                                        🎯 深度对比(最新+时间差) | 📊 深度统计(无时间差) | 💰 价差对比(无比值+时间差标注) | ⏰ {time_str}
                                    </p>
                                </div>
                            </div>
                            """
                        }
                    }
                ],
                "time": {"from": "now-1h", "to": "now"},
                "timepicker": {},
                "templating": {"list": []},
                "annotations": {"list": []},
                "refresh": "1m",
                "schemaVersion": 30,
                "version": 1,
                "links": []
            },
            "overwrite": True
        }
        
        # 为每个币种创建面板
        panel_id = 2
        y_pos = 3
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            symbol_data = data[symbol]
            latest_data = symbol_data['latest']
            stats_data = symbol_data['stats']
            
            # 1. 深度对比面板 (左侧) - 使用最新数据点 + 时间差
            depth_compare_content = f"""
            <div style="padding: 15px; background: #f8f9fa; border-radius: 8px; font-family: Arial, sans-serif; height: 100%;">
                <h3 style="text-align: center; color: #333; margin-bottom: 15px;">📊 {symbol} 深度对比</h3>
                
                <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 6px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <thead style="background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white;">
                        <tr>
                            <th style="padding: 10px 8px; text-align: left; border: none; font-size: 11px;">项目</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">Bitda</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">Binance</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">比值</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">买一</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #28a745;">{latest_data['bitda_bid1_qty']:.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #28a745;">{latest_data['binance_bid1_qty']:.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #007bff; font-weight: bold;">{(latest_data['bitda_bid1_qty']/latest_data['binance_bid1_qty']):.2f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee; background: #f8f9fa;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">卖一</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #dc3545;">{latest_data['bitda_ask1_qty']:.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #dc3545;">{latest_data['binance_ask1_qty']:.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #007bff; font-weight: bold;">{(latest_data['bitda_ask1_qty']/latest_data['binance_ask1_qty']):.2f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">买一卖一</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #6f42c1;">{(latest_data['bitda_bid1_qty'] + latest_data['bitda_ask1_qty']):.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #6f42c1;">{(latest_data['binance_bid1_qty'] + latest_data['binance_ask1_qty']):.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #007bff; font-weight: bold;">{((latest_data['bitda_bid1_qty'] + latest_data['bitda_ask1_qty'])/(latest_data['binance_bid1_qty'] + latest_data['binance_ask1_qty'])):.2f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee; background: #f8f9fa;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">买二卖二</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #fd7e14;">{(latest_data['bitda_bid1_qty'] + latest_data['bitda_bid2_qty'] + latest_data['bitda_ask1_qty'] + latest_data['bitda_ask2_qty']):.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #fd7e14;">{(latest_data['binance_bid1_qty'] + latest_data['binance_bid2_qty'] + latest_data['binance_ask1_qty'] + latest_data['binance_ask2_qty']):.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #007bff; font-weight: bold;">{((latest_data['bitda_bid1_qty'] + latest_data['bitda_bid2_qty'] + latest_data['bitda_ask1_qty'] + latest_data['bitda_ask2_qty'])/(latest_data['binance_bid1_qty'] + latest_data['binance_bid2_qty'] + latest_data['binance_ask1_qty'] + latest_data['binance_ask2_qty'])):.2f}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">买五卖五</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #20c997;">{(latest_data['bitda_bid5_total'] + latest_data['bitda_ask5_total']):.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #20c997;">{(latest_data['binance_bid5_total'] + latest_data['binance_ask5_total']):.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #007bff; font-weight: bold;">{((latest_data['bitda_bid5_total'] + latest_data['bitda_ask5_total'])/(latest_data['binance_bid5_total'] + latest_data['binance_ask5_total'])):.2f}</td>
                        </tr>
                    </tbody>
                </table>
                
                <div style="margin-top: 10px; padding: 8px; background: #e3f2fd; border-radius: 4px; border-left: 3px solid #2196f3;">
                    <small style="font-size: 10px;"><strong>⏰ 时间差:</strong> {latest_data['time_diff_ms']}ms (最新数据点对比)</small>
                </div>
            </div>
            """
            
            dashboard_config["dashboard"]["panels"].append({
                "id": panel_id,
                "title": "",
                "type": "text",
                "gridPos": {"h": 8, "w": 8, "x": 0, "y": y_pos},
                "targets": [],
                "options": {"mode": "html", "content": depth_compare_content}
            })
            panel_id += 1

            # 2. 深度统计面板 (中间) - 使用统计数据，无时间差
            depth_stats_content = f"""
            <div style="padding: 15px; background: #f8f9fa; border-radius: 8px; font-family: Arial, sans-serif; height: 100%;">
                <h3 style="text-align: center; color: #333; margin-bottom: 15px;">📈 {symbol} 深度统计</h3>

                <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 6px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <thead style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
                        <tr>
                            <th style="padding: 10px 8px; text-align: left; border: none; font-size: 11px;">项目</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">最大值</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">最小值</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">平均值</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">买一卖一</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #dc3545;">{max(stats_data['bitda_bid1_avg'] + stats_data['bitda_ask1_avg'], stats_data['binance_bid1_avg'] + stats_data['binance_ask1_avg']):.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #28a745;">{min(stats_data['bitda_bid1_avg'] + stats_data['bitda_ask1_avg'], stats_data['binance_bid1_avg'] + stats_data['binance_ask1_avg']):.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #007bff;">{(stats_data['bitda_bid1_avg'] + stats_data['bitda_ask1_avg'] + stats_data['binance_bid1_avg'] + stats_data['binance_ask1_avg'])/2:.2f}</td>
                        </tr>
                    </tbody>
                </table>

                <div style="margin-top: 10px; padding: 8px; background: #d4edda; border-radius: 4px; border-left: 3px solid #28a745;">
                    <small style="font-size: 10px;"><strong>📊 统计说明:</strong> 基于最近5分钟数据统计，无时间差</small><br>
                    <small style="font-size: 10px;"><strong>📈 样本数量:</strong> Bitda {stats_data['bitda_sample_count']}条 | Binance {stats_data['binance_sample_count']}条</small>
                </div>
            </div>
            """

            dashboard_config["dashboard"]["panels"].append({
                "id": panel_id,
                "title": "",
                "type": "text",
                "gridPos": {"h": 8, "w": 8, "x": 8, "y": y_pos},
                "targets": [],
                "options": {"mode": "html", "content": depth_stats_content}
            })
            panel_id += 1

            # 3. 价差对比面板 (右侧) - 无比值列，部分项目有时间差
            spread_compare_content = f"""
            <div style="padding: 15px; background: #f8f9fa; border-radius: 8px; font-family: Arial, sans-serif; height: 100%;">
                <h3 style="text-align: center; color: #333; margin-bottom: 15px;">💰 {symbol} 价差对比</h3>

                <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 6px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <thead style="background: linear-gradient(135deg, #ff7f0e 0%, #ff4757 100%); color: white;">
                        <tr>
                            <th style="padding: 10px 8px; text-align: left; border: none; font-size: 11px;">项目</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">Bitda</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">Binance</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">最近价差 ⏰</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #2196f3; font-weight: bold;">{latest_data['bitda_spread']:.4f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #2196f3; font-weight: bold;">{latest_data['binance_spread']:.4f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee; background: #f8f9fa;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">最大价差 ⏰</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #f44336;">{stats_data['bitda_spread_max']:.4f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #f44336;">{stats_data['binance_spread_max']:.4f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">最小价差 ⏰</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #4caf50;">{stats_data['bitda_spread_min']:.4f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #4caf50;">{stats_data['binance_spread_min']:.4f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee; background: #f8f9fa;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">平均价差</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #9c27b0;">{stats_data['bitda_spread_avg']:.4f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #9c27b0;">{stats_data['binance_spread_avg']:.4f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">价差中位数</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #607d8b;">{stats_data['bitda_spread_median']:.4f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #607d8b;">{stats_data['binance_spread_median']:.4f}</td>
                        </tr>
                        <tr style="background: #fff3cd;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">时间差说明</td>
                            <td style="padding: 8px; text-align: center; font-size: 10px; color: #856404;" colspan="2">
                                最近/最大/最小: {latest_data['time_diff_ms']}ms | 平均/中位数: 无时间差(统计值)
                            </td>
                        </tr>
                    </tbody>
                </table>

                <div style="margin-top: 10px; padding: 8px; background: #fff3cd; border-radius: 4px; border-left: 3px solid #ffc107;">
                    <small style="font-size: 10px;"><strong>📊 统计说明:</strong> Bitda {stats_data['bitda_sample_count']}条样本 | Binance {stats_data['binance_sample_count']}条样本</small><br>
                    <small style="font-size: 10px;"><strong>💰 价差说明:</strong> 价差 = 卖一价 - 买一价，数值越小流动性越好</small><br>
                    <small style="font-size: 10px;"><strong>⏰ 时间差:</strong> ⏰标记的项目需要时间差，其他为统计值无需时间差</small>
                </div>
            </div>
            """

            dashboard_config["dashboard"]["panels"].append({
                "id": panel_id,
                "title": "",
                "type": "text",
                "gridPos": {"h": 8, "w": 8, "x": 16, "y": y_pos},
                "targets": [],
                "options": {"mode": "html", "content": spread_compare_content}
            })
            panel_id += 1

            y_pos += 8

        # 创建仪表板
        try:
            response = self.session.post(
                f"{self.grafana_url}/api/dashboards/db",
                json=dashboard_config,
                headers={"Content-Type": "application/json"}
            )

            if response.status_code == 200:
                result = response.json()
                dashboard_url = f"{self.grafana_url}{result['url']}"
                logger.info("✅ 演示仪表板创建成功")
                logger.info(f"🌐 访问地址: {dashboard_url}")
                return dashboard_url
            else:
                logger.error(f"❌ 创建仪表板失败: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"❌ 仪表板创建异常: {e}")
            return None

def main():
    """主函数"""
    print("✅ 正确逻辑演示版深度价差对比分析仪表板")
    print("=" * 60)
    print("🎯 严格按照用户要求实现:")
    print("  1. ❌ 价差比值已取消 (价差对比面板只有3列)")
    print("  2. ✅ 深度对比: 最新数据点 + 时间差")
    print("  3. ✅ 深度统计: 统计数据 + 无时间差")
    print("  4. ✅ 价差对比: 最近/最大/最小有时间差，平均/中位数无时间差")
    print("  5. ✅ 时间差说明行清楚标注哪些项目需要时间差")
    print("  6. 📊 使用模拟数据演示正确界面")
    print()

    creator = DemoCorrectDashboard()
    dashboard_url = creator.create_demo_dashboard()

    if dashboard_url:
        print("🎉 演示仪表板创建成功!")
        print("🎯 核心特点:")
        print("  ✅ 深度对比面板: 最新数据点对比 + 时间差显示")
        print("  ✅ 深度统计面板: 统计数据 + 无时间差")
        print("  ✅ 价差对比面板: 无比值列 + 部分项目有时间差")
        print("  ✅ 时间差逻辑完全正确")
        print("  ✅ 界面清晰，逻辑准确")

        # 自动打开浏览器
        try:
            import webbrowser
            webbrowser.open(dashboard_url)
            print("🌐 浏览器已自动打开")
        except:
            print(f"🌐 请手动访问: {dashboard_url}")
    else:
        print("❌ 仪表板创建失败")

if __name__ == "__main__":
    main()
