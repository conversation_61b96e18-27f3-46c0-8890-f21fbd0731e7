#!/usr/bin/env python3
"""
最终修复 - 删除重建数据源
"""

import requests
import json

def delete_and_recreate_datasource():
    """删除并重新创建数据源"""
    print("🔧 删除并重新创建数据源...")
    
    session = requests.Session()
    session.auth = ("admin", "admin")
    
    # 1. 删除现有数据源
    response = session.get("http://localhost:3000/api/datasources")
    if response.status_code == 200:
        datasources = response.json()
        for ds in datasources:
            if 'DepthDB' in ds['name'] or 'FreshDepthDB' in ds['name']:
                delete_response = session.delete(f"http://localhost:3000/api/datasources/{ds['id']}")
                print(f"   🗑️ 删除数据源: {ds['name']}")
    
    # 2. 创建新数据源，使用secureJsonData存储密码
    datasource_config = {
        "name": "WorkingDepthDB",
        "type": "mysql",
        "url": "localhost:3306",
        "access": "proxy",
        "database": "depth_db",
        "user": "root",
        "basicAuth": False,
        "isDefault": False,
        "jsonData": {
            "maxOpenConns": 10,
            "maxIdleConns": 2,
            "connMaxLifetime": 14400
        },
        "secureJsonData": {
            "password": "Linuxtest"
        }
    }
    
    try:
        response = session.post(
            "http://localhost:3000/api/datasources",
            json=datasource_config,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            datasource_uid = result['datasource']['uid']
            print(f"   ✅ 新数据源创建成功: {datasource_uid}")
            return datasource_uid
        else:
            print(f"   ❌ 创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return None

def create_simple_dashboard(datasource_uid):
    """创建简单仪表板"""
    print("🎨 创建简单仪表板...")
    
    session = requests.Session()
    session.auth = ("admin", "admin")
    
    dashboard_config = {
        "dashboard": {
            "id": None,
            "title": "✅ 工作仪表板",
            "tags": ["working"],
            "timezone": "browser",
            "panels": [
                {
                    "id": 1,
                    "title": "📊 BTCUSDT买一量",
                    "type": "stat",
                    "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
                    "targets": [
                        {
                            "datasource": {
                                "type": "mysql",
                                "uid": datasource_uid
                            },
                            "format": "table",
                            "rawSql": "SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1",
                            "refId": "A"
                        }
                    ],
                    "options": {
                        "reduceOptions": {
                            "values": False,
                            "calcs": ["lastNotNull"]
                        },
                        "textMode": "auto",
                        "colorMode": "background"
                    }
                },
                {
                    "id": 2,
                    "title": "📊 ETHUSDT买一量",
                    "type": "stat",
                    "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
                    "targets": [
                        {
                            "datasource": {
                                "type": "mysql",
                                "uid": datasource_uid
                            },
                            "format": "table",
                            "rawSql": "SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1",
                            "refId": "A"
                        }
                    ],
                    "options": {
                        "reduceOptions": {
                            "values": False,
                            "calcs": ["lastNotNull"]
                        },
                        "textMode": "auto",
                        "colorMode": "background"
                    }
                }
            ],
            "time": {"from": "now-1h", "to": "now"},
            "refresh": "5s",
            "schemaVersion": 30,
            "version": 1
        },
        "overwrite": True
    }
    
    try:
        response = session.post(
            "http://localhost:3000/api/dashboards/db",
            json=dashboard_config,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            dashboard_url = f"http://localhost:3000{result['url']}"
            print(f"   ✅ 仪表板创建成功")
            print(f"   🌐 地址: {dashboard_url}")
            return dashboard_url
        else:
            print(f"   ❌ 创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return None

def main():
    """主函数"""
    print("🔧 最终修复方案")
    print("=" * 40)
    
    # 1. 重新创建数据源
    datasource_uid = delete_and_recreate_datasource()
    if not datasource_uid:
        print("❌ 数据源创建失败")
        return
    
    # 2. 创建简单仪表板
    dashboard_url = create_simple_dashboard(datasource_uid)
    if dashboard_url:
        print(f"\n🎉 修复完成！")
        print(f"🌐 仪表板: {dashboard_url}")
        print(f"⏰ 5秒自动刷新")
        
        # 打开浏览器
        try:
            import webbrowser
            webbrowser.open(dashboard_url)
            print(f"🌐 已自动打开浏览器")
        except:
            pass
        
        print(f"\n💡 如果仍显示'No data'，请等待几秒钟让数据源连接生效")
    else:
        print(f"❌ 仪表板创建失败")

if __name__ == "__main__":
    main()
