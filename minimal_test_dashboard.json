{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "grafana-clickhouse-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "fixed", "fixedColor": "green"}, "custom": {"align": "center", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "grafana-clickhouse-datasource"}, "format": "table", "rawSql": "SELECT 1 as test_value", "refId": "A"}], "title": "🟢 连接测试", "type": "stat"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "grafana-clickhouse-datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "fixed", "fixedColor": "red"}, "custom": {"align": "center", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "grafana-clickhouse-datasource"}, "format": "table", "rawSql": "SELECT COUNT(*) as record_count FROM crypto.binance_bookticker", "refId": "A"}], "title": "🔴 数据计数", "type": "stat"}], "preload": false, "refresh": "30s", "schemaVersion": 41, "tags": ["test", "clickhouse"], "templating": {"list": []}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "ClickHouse最小测试", "uid": "clickhouse-minimal-test", "version": 1, "weekStart": ""}