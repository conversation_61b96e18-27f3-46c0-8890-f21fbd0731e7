#!/usr/bin/env python3
"""
部署工作版本的MySQL深度对比仪表板
"""

import requests
import json

GRAFANA_URL = "http://localhost:3000"
GRAFANA_USER = "admin"
GRAFANA_PASSWORD = "admin"

def test_mysql_query():
    """测试MySQL查询"""
    print("🔍 测试MySQL查询...")
    
    import mysql.connector
    
    try:
        conn = mysql.connector.connect(
            host='localhost',
            user='root',
            password='Linuxtest',
            database='depth_db'
        )
        cursor = conn.cursor()
        
        # 测试查询
        query = """
        SELECT 
          '买一量' as 项目,
          ROUND((SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1), 2) as Bitda,
          ROUND((SELECT bid_qty FROM binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
          ROUND((SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1) / 
                (SELECT bid_qty FROM binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
        """
        
        cursor.execute(query)
        result = cursor.fetchall()
        
        if result:
            print(f"✅ MySQL查询成功: {result[0]}")
            return True
        else:
            print("❌ MySQL查询无结果")
            return False
            
    except Exception as e:
        print(f"❌ MySQL查询失败: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def import_mysql_dashboard():
    """导入MySQL仪表板"""
    print("📊 导入MySQL深度对比仪表板...")
    
    with open('grafana_mysql_depth_dashboard.json', 'r', encoding='utf-8') as f:
        dashboard = json.load(f)
    
    import_config = {
        "dashboard": dashboard,
        "overwrite": True,
        "inputs": []
    }
    
    response = requests.post(
        f"{GRAFANA_URL}/api/dashboards/import",
        json=import_config,
        auth=(GRAFANA_USER, GRAFANA_PASSWORD)
    )
    
    if response.status_code == 200:
        result = response.json()
        dashboard_url = f"{GRAFANA_URL}/d/{result['uid']}"
        print(f"✅ MySQL仪表板导入成功")
        print(f"🔗 仪表板URL: {dashboard_url}")
        return dashboard_url
    else:
        print(f"❌ 导入仪表板失败: {response.status_code} - {response.text}")
        return None

def main():
    """主函数"""
    print("🚀 部署工作版本的深度对比仪表板")
    print("=" * 50)
    
    # 1. 测试MySQL查询
    if not test_mysql_query():
        print("⚠️ MySQL查询测试失败，但继续部署")
    
    # 2. 导入仪表板
    dashboard_url = import_mysql_dashboard()
    
    if dashboard_url:
        print("\n🎉 部署完成!")
        print(f"📊 仪表板地址: {dashboard_url}")
        print(f"👤 用户名: {GRAFANA_USER}")
        print(f"🔑 密码: {GRAFANA_PASSWORD}")
        print("\n📋 仪表板特性:")
        print("  ✅ 基于MySQL数据源 (稳定可靠)")
        print("  ✅ 实时数据更新 (1分钟刷新)")
        print("  ✅ 中文界面显示")
        print("  ✅ 5个深度指标对比")
        print("  ✅ BTCUSDT + ETHUSDT 双交易对")
    else:
        print("❌ 部署失败")

if __name__ == "__main__":
    main()
