#!/usr/bin/env python3
"""
立即修复仪表板 - 使用最简单的查询
"""

import requests
import json
from datetime import datetime

def create_simple_working_dashboard():
    """创建最简单的工作仪表板"""
    print("🔧 创建最简单的工作仪表板...")
    
    grafana_url = "http://localhost:3000"
    session = requests.Session()
    session.auth = ("admin", "admin")
    
    # 使用已存在的数据源UID
    datasource_uid = "benieev71k4cgc"  # DepthDB_Dynamic
    
    dashboard_config = {
        "dashboard": {
            "id": None,
            "title": "✅ 简单工作仪表板",
            "tags": ["simple", "working"],
            "timezone": "browser",
            "panels": [
                # 面板1: 数据计数
                {
                    "id": 1,
                    "title": "📊 BTCUSDT数据条数",
                    "type": "stat",
                    "gridPos": {"h": 6, "w": 8, "x": 0, "y": 0},
                    "targets": [
                        {
                            "datasource": {
                                "type": "mysql",
                                "uid": datasource_uid
                            },
                            "format": "table",
                            "rawSql": "SELECT COUNT(*) as count FROM bitda_depth WHERE symbol = 'BTCUSDT'",
                            "refId": "A"
                        }
                    ],
                    "options": {
                        "reduceOptions": {
                            "values": False,
                            "calcs": ["lastNotNull"]
                        },
                        "textMode": "auto",
                        "colorMode": "background"
                    }
                },
                
                # 面板2: 最新时间
                {
                    "id": 2,
                    "title": "⏰ 最新数据时间",
                    "type": "stat",
                    "gridPos": {"h": 6, "w": 8, "x": 8, "y": 0},
                    "targets": [
                        {
                            "datasource": {
                                "type": "mysql",
                                "uid": datasource_uid
                            },
                            "format": "table",
                            "rawSql": "SELECT FROM_UNIXTIME(MAX(timestamp)/1000) as latest FROM bitda_depth WHERE symbol = 'BTCUSDT'",
                            "refId": "A"
                        }
                    ],
                    "options": {
                        "reduceOptions": {
                            "values": False,
                            "calcs": ["lastNotNull"]
                        },
                        "textMode": "auto",
                        "colorMode": "background"
                    }
                },
                
                # 面板3: 最新买一量
                {
                    "id": 3,
                    "title": "💰 最新买一量",
                    "type": "stat",
                    "gridPos": {"h": 6, "w": 8, "x": 16, "y": 0},
                    "targets": [
                        {
                            "datasource": {
                                "type": "mysql",
                                "uid": datasource_uid
                            },
                            "format": "table",
                            "rawSql": "SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1",
                            "refId": "A"
                        }
                    ],
                    "options": {
                        "reduceOptions": {
                            "values": False,
                            "calcs": ["lastNotNull"]
                        },
                        "textMode": "auto",
                        "colorMode": "background"
                    }
                },
                
                # 面板4: 最新5条数据表格
                {
                    "id": 4,
                    "title": "📋 BTCUSDT最新5条数据",
                    "type": "table",
                    "gridPos": {"h": 10, "w": 24, "x": 0, "y": 6},
                    "targets": [
                        {
                            "datasource": {
                                "type": "mysql",
                                "uid": datasource_uid
                            },
                            "format": "table",
                            "rawSql": """
                                SELECT 
                                    FROM_UNIXTIME(timestamp/1000) as 时间,
                                    ROUND(bid_qty_1, 2) as 买一量,
                                    ROUND(ask_qty_1, 2) as 卖一量,
                                    ROUND(bid_qty_1 + ask_qty_1, 2) as 总量
                                FROM bitda_depth 
                                WHERE symbol = 'BTCUSDT' 
                                ORDER BY timestamp DESC 
                                LIMIT 5
                            """,
                            "refId": "A"
                        }
                    ]
                }
            ],
            "time": {"from": "now-1h", "to": "now"},
            "refresh": "5s",  # 5秒刷新
            "schemaVersion": 30,
            "version": 1
        },
        "overwrite": True
    }
    
    try:
        response = session.post(
            f"{grafana_url}/api/dashboards/db",
            json=dashboard_config,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            dashboard_url = f"{grafana_url}{result['url']}"
            print(f"   ✅ 简单仪表板创建成功")
            print(f"   🌐 访问地址: {dashboard_url}")
            return dashboard_url
        else:
            print(f"   ❌ 创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return None

def main():
    """主函数"""
    print("🔧 立即修复仪表板问题")
    print("=" * 40)
    
    dashboard_url = create_simple_working_dashboard()
    
    if dashboard_url:
        print(f"\n🎉 修复成功！")
        print(f"🌐 新仪表板: {dashboard_url}")
        print(f"⏰ 5秒自动刷新")
        print(f"📊 显示真实数据")
        
        # 打开浏览器
        try:
            import webbrowser
            webbrowser.open(dashboard_url)
            print(f"🌐 已自动打开浏览器")
        except:
            print(f"💡 请手动打开浏览器访问")
    else:
        print(f"\n❌ 修复失败")

if __name__ == "__main__":
    main()
