#!/usr/bin/env python3
"""
修复仪表板更新问题的简单方案
每30秒重新生成一次仪表板，强制更新数据
"""

import time
from datetime import datetime
import logging
from improved_dashboard import ImprovedDepthSpreadDashboard

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_updating_dashboard():
    """创建会自动更新的仪表板"""
    print("🔄 创建真正会更新的仪表板")
    print("=" * 60)
    print("💡 解决方案: 每30秒重新生成仪表板，强制更新数据")
    print()
    
    creator = ImprovedDepthSpreadDashboard()
    
    # 记录第一次创建的URL
    first_url = None
    update_count = 0
    
    try:
        while True:
            update_count += 1
            current_time = datetime.now()
            
            print(f"🔄 第{update_count}次更新 - {current_time.strftime('%H:%M:%S')}")
            
            # 重新创建仪表板
            dashboard_url = creator.create_dashboard()
            
            if dashboard_url:
                if first_url is None:
                    first_url = dashboard_url
                    print(f"   ✅ 仪表板创建成功")
                    print(f"   🌐 访问地址: {dashboard_url}")
                    print(f"   ⏰ 每30秒自动更新数据")
                    print()
                    print(f"💡 请打开浏览器访问上述地址，观察数据变化")
                    print(f"📊 数据会每30秒自动更新")
                else:
                    print(f"   ✅ 数据已更新")
                
                # 等待30秒
                print(f"   ⏰ 等待30秒后进行下次更新...")
                time.sleep(30)
            else:
                print(f"   ❌ 更新失败，10秒后重试...")
                time.sleep(10)
                
    except KeyboardInterrupt:
        print(f"\n🛑 用户中断，停止自动更新")
        print(f"📊 总共更新了{update_count}次")
        if first_url:
            print(f"🌐 最后的仪表板地址: {first_url}")

def verify_data_changes():
    """验证数据是否在变化"""
    print("🔍 验证数据变化")
    print("=" * 40)
    
    creator = ImprovedDepthSpreadDashboard()

    # 获取第一次数据
    print("📊 获取第一次数据...")
    data1 = creator.get_latest_data('BTCUSDT')
    if data1:
        print(f"   BTCUSDT买一量: {data1['bitda_bid1_qty']:.2f}")
        print(f"   BTCUSDT深度比: {data1['bid_ask1_ratio']:.2f}")
        print(f"   时间: {datetime.fromtimestamp(data1['bitda_timestamp']/1000).strftime('%H:%M:%S')}")
    
    print(f"\n⏰ 等待30秒...")
    time.sleep(30)
    
    # 获取第二次数据
    print("📊 获取第二次数据...")
    data2 = creator.get_latest_data('BTCUSDT')
    if data2:
        print(f"   BTCUSDT买一量: {data2['bitda_bid1_qty']:.2f}")
        print(f"   BTCUSDT深度比: {data2['bid_ask1_ratio']:.2f}")
        print(f"   时间: {datetime.fromtimestamp(data2['bitda_timestamp']/1000).strftime('%H:%M:%S')}")
    
    # 对比变化
    if data1 and data2:
        time_changed = data1['bitda_timestamp'] != data2['bitda_timestamp']
        value_changed = abs(data1['bid_ask1_ratio'] - data2['bid_ask1_ratio']) > 0.01
        
        print(f"\n🔍 变化检测:")
        print(f"   时间变化: {'✅是' if time_changed else '❌否'}")
        print(f"   数值变化: {'✅是' if value_changed else '❌否'}")
        
        if time_changed or value_changed:
            print(f"   🎉 数据确实在变化！")
            return True
        else:
            print(f"   ⚠️ 数据没有变化")
            return False
    
    return False

def main():
    """主函数"""
    print("🔄 仪表板更新问题修复器")
    print("=" * 60)
    print("🎯 目标: 让您看到真正更新的数据")
    print()
    
    # 首先验证数据是否在变化
    print("第一步: 验证底层数据是否在变化")
    has_data_changes = verify_data_changes()
    
    if not has_data_changes:
        print(f"\n⚠️ 底层数据没有变化，仪表板也不会更新")
        print(f"💡 建议检查数据采集程序是否正常运行")
        return
    
    print(f"\n✅ 底层数据确实在变化")
    print(f"第二步: 创建会自动更新的仪表板")
    print()
    
    # 询问用户是否要启动自动更新
    print(f"❓ 是否启动自动更新仪表板？(每30秒更新一次)")
    print(f"   输入 'y' 启动，输入 'n' 退出: ", end="")
    
    choice = input().lower().strip()
    
    if choice == 'y':
        print(f"\n🚀 启动自动更新...")
        create_updating_dashboard()
    else:
        print(f"\n💡 您可以手动刷新浏览器页面来查看数据更新")
        
        # 创建一次性仪表板
        creator = ImprovedDepthSpreadDashboard()
        dashboard_url = creator.create_dashboard()
        if dashboard_url:
            print(f"🌐 仪表板地址: {dashboard_url}")
            print(f"🔄 请手动刷新页面查看数据变化")

if __name__ == "__main__":
    main()
