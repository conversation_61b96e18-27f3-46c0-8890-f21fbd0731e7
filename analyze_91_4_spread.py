#!/usr/bin/env python3
"""
分析买五卖五最大价差91.4000的原始数据
"""

import mysql.connector
from datetime import datetime
import json

def analyze_spread_data():
    """分析91.4000价差的原始数据"""
    
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'Linuxtest',
        'database': 'depth_db'
    }
    
    timestamp = 1748654262653
    
    print("🔍 分析买五卖五最大价差91.4000的原始数据")
    print("=" * 60)
    
    # 转换时间戳
    dt = datetime.fromtimestamp(timestamp / 1000)
    print(f"📅 时间戳: {timestamp}")
    print(f"🕐 北京时间: {dt.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
    print()
    
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        # 获取原始数据
        query = """
        SELECT 
            symbol, timestamp, index_price, sign_price, last_price,
            bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
            bid_price_2, ask_price_2, bid_qty_2, ask_qty_2,
            bid_price_3, ask_price_3, bid_qty_3, ask_qty_3,
            bid_price_4, ask_price_4, bid_qty_4, ask_qty_4,
            bid_price_5, ask_price_5, bid_qty_5, ask_qty_5,
            asks, bids
        FROM bitda_depth 
        WHERE timestamp = %s AND symbol = 'BTCUSDT'
        LIMIT 1
        """
        
        cursor.execute(query, (timestamp,))
        result = cursor.fetchone()
        
        if result:
            print("📊 BTCUSDT 原始深度数据:")
            print("-" * 40)
            print(f"交易对: {result[0]}")
            print(f"指数价格: {result[2]}")
            print(f"标记价格: {result[3]}")
            print(f"最新价格: {result[4]}")
            print()
            
            print("📈 买卖盘口数据:")
            print("-" * 40)
            print("档位    买价        买量        卖价        卖量        价差")
            print("-" * 65)
            
            # 计算各档位价差
            for i in range(1, 6):
                bid_price_idx = 4 + (i-1)*4 + 1
                ask_price_idx = 4 + (i-1)*4 + 2
                bid_qty_idx = 4 + (i-1)*4 + 3
                ask_qty_idx = 4 + (i-1)*4 + 4
                
                bid_price = float(result[bid_price_idx])
                ask_price = float(result[ask_price_idx])
                bid_qty = float(result[bid_qty_idx])
                ask_qty = float(result[ask_qty_idx])
                spread = ask_price - bid_price
                
                print(f"买{i}卖{i}  {bid_price:>10.2f}  {bid_qty:>8.4f}  {ask_price:>10.2f}  {ask_qty:>8.4f}  {spread:>8.2f}")
            
            print()
            print("🎯 关键价差计算:")
            print("-" * 30)
            
            # 买一卖一价差
            bid1_ask1_spread = float(result[6]) - float(result[5])
            print(f"买一卖一价差: {float(result[6]):.2f} - {float(result[5]):.2f} = {bid1_ask1_spread:.2f}")
            
            # 买五卖五价差 (这就是91.4的来源)
            bid5_ask5_spread = float(result[22]) - float(result[21])
            print(f"买五卖五价差: {float(result[22]):.2f} - {float(result[21]):.2f} = {bid5_ask5_spread:.2f}")
            print()
            
            print("🔍 详细分析:")
            print("-" * 30)
            print(f"买五价格: {float(result[21]):.2f}")
            print(f"卖五价格: {float(result[22]):.2f}")
            print(f"买五卖五价差: {bid5_ask5_spread:.2f} (这就是91.4000的来源)")
            print()
            
            # 解析完整的asks和bids数据
            asks_data = json.loads(result[25])
            bids_data = json.loads(result[26])
            
            print("📋 完整买卖盘数据 (前10档):")
            print("-" * 50)
            print("买盘                          卖盘")
            print("价格        数量              价格        数量")
            print("-" * 50)
            
            for i in range(min(10, len(bids_data), len(asks_data))):
                bid_price, bid_qty = bids_data[i]
                ask_price, ask_qty = asks_data[i]
                print(f"{float(bid_price):>10.2f}  {float(bid_qty):>8.4f}      {float(ask_price):>10.2f}  {float(ask_qty):>8.4f}")
            
            print()
            print("💡 数据解释:")
            print("-" * 30)
            print("• 买五卖五价差91.4是指买五档价格和卖五档价格之间的差值")
            print("• 这个价差较大说明在第五档位置流动性相对较差")
            print("• 买一卖一价差只有0.40，说明最优价位流动性很好")
            print("• 从买一到买五，价格逐渐降低；从卖一到卖五，价格逐渐升高")
            print("• 这是正常的订单簿结构")
            
        else:
            print("❌ 未找到对应时间戳的数据")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")

if __name__ == "__main__":
    analyze_spread_data()
