#!/usr/bin/env python3
"""
ETHUSDT延时数据处理器
从原始数据库计算延时并存储到延时分析数据库
"""

import mysql.connector
from mysql.connector import Error
import json
import time
from datetime import datetime, timedelta
import logging
from typing import List, Dict, Tuple, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ETHUSDTLatencyProcessor:
    """ETHUSDT延时数据处理器"""

    def __init__(self):
        self.source_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'depth_db'
        }

        self.target_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'ethusdt_latency_db'
        }

        self.min_latency = 10  # 最小有效延时(ms)
        self.max_latency = 2000  # 最大有效延时(ms)

    def get_source_connection(self):
        """获取源数据库连接"""
        return mysql.connector.connect(**self.source_config)

    def get_target_connection(self):
        """获取目标数据库连接"""
        return mysql.connector.connect(**self.target_config)

    def parse_bitda_depth(self, asks_json: str, bids_json: str) -> Tuple[Optional[float], Optional[float], Optional[float], Optional[float]]:
        """
        解析Bitda深度数据

        Returns:
            (ask_price_1, ask_qty_1, bid_price_1, bid_qty_1)
        """
        try:
            if not asks_json or not bids_json:
                return None, None, None, None

            # 解析JSON数据
            asks_data = json.loads(asks_json) if isinstance(asks_json, str) else asks_json
            bids_data = json.loads(bids_json) if isinstance(bids_json, str) else bids_json

            if not asks_data or not bids_data:
                return None, None, None, None

            # 卖一价格 = asks中价格最小的
            ask_prices = [float(item[0]) for item in asks_data if len(item) >= 2]
            if not ask_prices:
                return None, None, None, None

            ask_price_1 = min(ask_prices)
            ask_qty_1 = None

            # 找到卖一对应的数量
            for item in asks_data:
                if len(item) >= 2 and float(item[0]) == ask_price_1:
                    ask_qty_1 = float(item[1])
                    break

            # 买一价格 = bids中价格最大的
            bid_prices = [float(item[0]) for item in bids_data if len(item) >= 2]
            if not bid_prices:
                return ask_price_1, ask_qty_1, None, None

            bid_price_1 = max(bid_prices)
            bid_qty_1 = None

            # 找到买一对应的数量
            for item in bids_data:
                if len(item) >= 2 and float(item[0]) == bid_price_1:
                    bid_qty_1 = float(item[1])
                    break

            return ask_price_1, ask_qty_1, bid_price_1, bid_qty_1

        except Exception as e:
            logger.error(f"解析Bitda深度数据失败: {e}")
            return None, None, None, None

    def find_matching_binance_data(self, source_cursor, bitda_price: float, bitda_timestamp: int, match_type: str) -> Optional[Tuple]:
        """
        查找匹配的Binance数据

        Args:
            source_cursor: 源数据库游标
            bitda_price: Bitda价格
            bitda_timestamp: Bitda时间戳
            match_type: 匹配类型 ('bid' 或 'ask')

        Returns:
            (binance_price, binance_qty, binance_timestamp) 或 None
        """
        try:
            # 根据匹配类型选择价格字段
            price_field = 'bid_price' if match_type == 'bid' else 'ask_price'
            qty_field = 'bid_qty' if match_type == 'bid' else 'ask_qty'

            # 查找价格完全匹配且时间早于Bitda的Binance数据 (使用首次出现时间)
            query = f"""
            SELECT {price_field}, {qty_field}, event_time
            FROM binance_bookticker
            WHERE symbol = 'ETHUSDT'
            AND {price_field} = %s
            AND event_time < %s
            ORDER BY event_time ASC
            LIMIT 1
            """

            source_cursor.execute(query, (bitda_price, bitda_timestamp))
            result = source_cursor.fetchone()

            return result

        except Exception as e:
            logger.error(f"查找匹配Binance数据失败: {e}")
            return None

    def process_latency_batch(self, start_time: datetime, end_time: datetime, batch_size: int = 1000) -> Dict:
        """
        批量处理延时数据

        Args:
            start_time: 开始时间
            end_time: 结束时间
            batch_size: 批处理大小

        Returns:
            处理结果统计
        """
        stats = {
            'processed_records': 0,
            'matched_records': 0,
            'bid_matches': 0,
            'ask_matches': 0,
            'error_count': 0,
            'start_time': start_time,
            'end_time': end_time
        }

        source_conn = None
        target_conn = None

        try:
            # 记录处理开始
            log_id = self.log_processing_start('realtime', start_time, end_time)

            source_conn = self.get_source_connection()
            target_conn = self.get_target_connection()

            source_cursor = source_conn.cursor()
            target_cursor = target_conn.cursor()

            # 查询Bitda深度数据
            bitda_query = """
            SELECT id, timestamp, asks, bids, created_at
            FROM bitda_depth
            WHERE symbol = 'ETHUSDT'
            AND created_at >= %s
            AND created_at <= %s
            AND asks IS NOT NULL
            AND bids IS NOT NULL
            ORDER BY timestamp
            LIMIT %s
            """

            source_cursor.execute(bitda_query, (start_time, end_time, batch_size))
            bitda_records = source_cursor.fetchall()

            logger.info(f"获取到 {len(bitda_records)} 条Bitda记录")

            matches_to_insert = []

            for record in bitda_records:
                try:
                    bitda_id, bitda_timestamp, asks_json, bids_json, created_at = record
                    stats['processed_records'] += 1

                    # 解析Bitda深度数据
                    ask_price_1, ask_qty_1, bid_price_1, bid_qty_1 = self.parse_bitda_depth(asks_json, bids_json)

                    if ask_price_1 is None or bid_price_1 is None:
                        stats['error_count'] += 1
                        continue

                    # 查找买一卖一价格完全匹配 (使用首次出现时间)
                    if bid_price_1 is not None and ask_price_1 is not None and bid_qty_1 is not None and ask_qty_1 is not None:
                        binance_query = """
                        SELECT bid_price, ask_price, bid_qty, ask_qty, event_time
                        FROM binance_bookticker
                        WHERE symbol = 'ETHUSDT'
                        AND bid_price = %s
                        AND ask_price = %s
                        AND event_time < %s
                        ORDER BY event_time ASC
                        LIMIT 1
                        """

                        source_cursor.execute(binance_query, (bid_price_1, ask_price_1, bitda_timestamp))
                        binance_match = source_cursor.fetchone()

                        if binance_match:
                            binance_bid_price, binance_ask_price, binance_bid_qty, binance_ask_qty, binance_timestamp = binance_match
                            latency = bitda_timestamp - binance_timestamp

                            if self.min_latency <= latency <= self.max_latency:
                                # 计算价差
                                price_spread = ask_price_1 - bid_price_1

                                matches_to_insert.append((
                                    bitda_timestamp, binance_timestamp, latency, 'complete',
                                    (bid_price_1 + ask_price_1) / 2,  # 使用中间价作为代表价格
                                    (binance_bid_price + binance_ask_price) / 2,  # Binance中间价
                                    (bid_qty_1 + ask_qty_1) / 2,  # 平均数量
                                    (binance_bid_qty + binance_ask_qty) / 2,  # Binance平均数量
                                    price_spread, 1.0000  # price_spread, match_quality
                                ))
                                stats['matched_records'] += 1
                                stats['bid_matches'] += 1  # 完全匹配算作bid_matches

                except Exception as e:
                    logger.error(f"处理记录失败: {e}")
                    stats['error_count'] += 1
                    continue

            # 批量插入匹配数据
            if matches_to_insert:
                insert_query = """
                INSERT INTO ethusdt_latency_matches
                (bitda_timestamp, binance_timestamp, latency_ms, match_type,
                 bitda_price, binance_price, bitda_qty, binance_qty,
                 price_spread, match_quality)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """

                target_cursor.executemany(insert_query, matches_to_insert)
                target_conn.commit()

                logger.info(f"插入 {len(matches_to_insert)} 条匹配记录")

            # 更新实时状态
            self.update_realtime_status(target_cursor, target_conn)

            # 记录处理完成
            self.log_processing_complete(log_id, stats)

            source_cursor.close()
            target_cursor.close()

        except Exception as e:
            logger.error(f"批量处理失败: {e}")
            stats['error_count'] += 1

            # 记录处理失败
            if 'log_id' in locals():
                self.log_processing_failed(log_id, str(e))

        finally:
            if source_conn:
                source_conn.close()
            if target_conn:
                target_conn.close()

        return stats

    def update_realtime_status(self, cursor, connection):
        """更新实时状态表"""
        try:
            # 计算最近1小时的统计数据
            stats_query = """
            SELECT
                COUNT(*) as total_matches,
                AVG(latency_ms) as avg_latency,
                MIN(latency_ms) as min_latency,
                MAX(latency_ms) as max_latency,
                MAX(created_at) as last_match_time
            FROM ethusdt_latency_matches
            WHERE created_at >= NOW() - INTERVAL 1 HOUR
            """

            cursor.execute(stats_query)
            result = cursor.fetchone()

            if result:
                total_matches, avg_latency, min_latency, max_latency, last_match_time = result

                # 获取最新的延时值
                cursor.execute("""
                    SELECT latency_ms, bitda_price, binance_price
                    FROM ethusdt_latency_matches
                    ORDER BY created_at DESC
                    LIMIT 1
                """)
                latest = cursor.fetchone()

                current_latency = latest[0] if latest else None
                last_bitda_price = latest[1] if latest else None
                last_binance_price = latest[2] if latest else None

                # 计算每分钟匹配率
                match_rate = total_matches / 60 if total_matches else 0

                # 更新实时状态
                update_query = """
                UPDATE ethusdt_realtime_status SET
                    current_latency_ms = %s,
                    avg_latency_1h = %s,
                    max_latency_1h = %s,
                    min_latency_1h = %s,
                    total_matches_1h = %s,
                    last_match_time = %s,
                    last_bitda_price = %s,
                    last_binance_price = %s,
                    match_rate_per_minute = %s,
                    updated_at = NOW()
                WHERE id = 1
                """

                cursor.execute(update_query, (
                    current_latency, avg_latency, max_latency, min_latency,
                    total_matches, last_match_time, last_bitda_price,
                    last_binance_price, match_rate
                ))

                connection.commit()

        except Exception as e:
            logger.error(f"更新实时状态失败: {e}")

    def log_processing_start(self, process_type: str, start_time: datetime, end_time: datetime) -> int:
        """记录处理开始"""
        try:
            conn = self.get_target_connection()
            cursor = conn.cursor()

            insert_query = """
            INSERT INTO latency_processing_log
            (process_type, start_time, data_range_start, data_range_end, status)
            VALUES (%s, %s, %s, %s, 'running')
            """

            cursor.execute(insert_query, (process_type, datetime.now(), start_time, end_time))
            log_id = cursor.lastrowid

            conn.commit()
            cursor.close()
            conn.close()

            return log_id

        except Exception as e:
            logger.error(f"记录处理开始失败: {e}")
            return 0

    def log_processing_complete(self, log_id: int, stats: Dict):
        """记录处理完成"""
        try:
            conn = self.get_target_connection()
            cursor = conn.cursor()

            end_time = datetime.now()

            update_query = """
            UPDATE latency_processing_log SET
                end_time = %s,
                processed_records = %s,
                matched_records = %s,
                error_count = %s,
                status = 'completed',
                processing_duration_ms = TIMESTAMPDIFF(MICROSECOND, start_time, %s) / 1000
            WHERE id = %s
            """

            cursor.execute(update_query, (
                end_time, stats['processed_records'], stats['matched_records'],
                stats['error_count'], end_time, log_id
            ))

            conn.commit()
            cursor.close()
            conn.close()

        except Exception as e:
            logger.error(f"记录处理完成失败: {e}")

    def log_processing_failed(self, log_id: int, error_message: str):
        """记录处理失败"""
        try:
            conn = self.get_target_connection()
            cursor = conn.cursor()

            update_query = """
            UPDATE latency_processing_log SET
                end_time = NOW(),
                status = 'failed',
                error_message = %s
            WHERE id = %s
            """

            cursor.execute(update_query, (error_message, log_id))

            conn.commit()
            cursor.close()
            conn.close()

        except Exception as e:
            logger.error(f"记录处理失败失败: {e}")

def main():
    """主函数 - 处理最近1小时的数据"""
    print("⚡ ETHUSDT延时数据处理器")
    print("=" * 50)

    processor = ETHUSDTLatencyProcessor()

    # 处理最近1小时的数据
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=1)

    print(f"📊 处理时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} ~ {end_time.strftime('%Y-%m-%d %H:%M:%S')}")

    start_process_time = time.time()
    stats = processor.process_latency_batch(start_time, end_time, batch_size=5000)
    process_duration = time.time() - start_process_time

    print(f"\n📈 处理结果:")
    print(f"  - 处理记录数: {stats['processed_records']}")
    print(f"  - 匹配记录数: {stats['matched_records']}")
    print(f"  - 买一匹配: {stats['bid_matches']}")
    print(f"  - 卖一匹配: {stats['ask_matches']}")
    print(f"  - 错误数量: {stats['error_count']}")
    print(f"  - 处理耗时: {process_duration:.2f} 秒")

    if stats['matched_records'] > 0:
        print(f"  - 匹配率: {stats['matched_records']/stats['processed_records']*100:.2f}%")
        print("✅ 延时数据处理完成！")
    else:
        print("⚠️  没有找到匹配的延时数据")

if __name__ == "__main__":
    main()
