#!/usr/bin/env python3
"""
数据分析器演示脚本
展示各种分析功能的使用方法
"""

import asyncio
import json
from datetime import datetime
from visualizer.dashboard import Dashboard
from utils.logging import setup_logger

logger = setup_logger(__name__)

class AnalyzerDemo:
    """分析器演示类"""
    
    def __init__(self):
        self.dashboard = Dashboard()
        
    async def run_demo(self):
        """运行完整演示"""
        print("🎬 数据分析器功能演示")
        print("="*60)
        print(f"⏰ 演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 演示步骤
        demos = [
            ("📊 综合分析演示", self.demo_comprehensive_analysis),
            ("🚀 延时分析演示", self.demo_latency_analysis),
            ("📈 K线分析演示", self.demo_kline_analysis),
            ("📊 深度对比演示", self.demo_depth_analysis),
            ("💰 价格分析演示", self.demo_price_analysis),
            ("📋 资金费率演示", self.demo_funding_analysis),
            ("🎯 可视化演示", self.demo_visualization)
        ]
        
        for demo_name, demo_func in demos:
            print(f"\n{demo_name}")
            print("-" * 40)
            
            try:
                await demo_func()
                print("✅ 演示完成")
            except Exception as e:
                print(f"❌ 演示失败: {e}")
                logger.error(f"{demo_name}失败: {e}")
        
        print("\n🎉 所有演示完成！")
    
    async def demo_comprehensive_analysis(self):
        """演示综合分析"""
        print("正在生成综合分析报告...")
        
        # 生成1小时的综合报告
        report = await self.dashboard.generate_comprehensive_report(hours=1)
        
        # 显示执行摘要
        summary = report.get('executive_summary', {})
        print(f"整体状态: {summary.get('overall_status', 'unknown')}")
        print(f"关键发现: {len(summary.get('key_findings', []))}项")
        print(f"警告信息: {len(summary.get('alerts', []))}项")
        
        # 保存报告
        filename = f"demo_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        await self.dashboard.save_report_to_file(report, filename)
        print(f"报告已保存: {filename}")
    
    async def demo_latency_analysis(self):
        """演示延时分析"""
        print("正在分析ETHUSDT价格延时...")
        
        result = await self.dashboard.latency_analyzer.analyze_price_latency(hours=1)
        eth_data = result.get('results', {}).get('ETHUSDT', {})
        
        if eth_data.get('total_matches', 0) > 0:
            print(f"匹配次数: {eth_data['total_matches']}")
            print(f"平均延时: {eth_data['avg_latency_ms']:.2f} ms")
            print(f"延时范围: {eth_data['min_latency_ms']:.2f} - {eth_data['max_latency_ms']:.2f} ms")
            print(f"平均价格差异: {eth_data['avg_price_diff_pct']:.4f}%")
        else:
            print("暂无价格匹配数据")
    
    async def demo_kline_analysis(self):
        """演示K线分析"""
        print("正在分析连续相同K线（0插针检测）...")
        
        result = await self.dashboard.kline_analyzer.analyze_consecutive_identical_klines(hours=24)
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            symbol_data = result.get('results', {}).get(symbol, {})
            print(f"\n{symbol}:")
            print(f"  连续序列数: {symbol_data.get('total_sequences', 0)}")
            print(f"  相同K线数: {symbol_data.get('total_identical_klines', 0)}")
            print(f"  相同比例: {symbol_data.get('identical_rate_pct', 0):.2f}%")
            
            if symbol_data.get('max_sequence_length', 0) > 0:
                print(f"  最长序列: {symbol_data['max_sequence_length']}根K线")
    
    async def demo_depth_analysis(self):
        """演示深度分析"""
        print("正在分析Bitda vs Binance深度对比...")
        
        result = await self.dashboard.depth_analyzer.analyze_depth_comparison(hours=1)
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            symbol_data = result.get('results', {}).get(symbol, {})
            print(f"\n{symbol}:")
            print(f"  对比次数: {symbol_data.get('total_comparisons', 0)}")
            print(f"  平均深度比值: {symbol_data.get('avg_depth_ratio', 0):.4f}")
            print(f"  Bitda优势比例: {symbol_data.get('bitda_advantage_pct', 0):.2f}%")
            
            # 判断深度优势
            ratio = symbol_data.get('avg_depth_ratio', 0)
            if ratio > 1.1:
                print(f"  💪 Bitda深度明显优势")
            elif ratio < 0.9:
                print(f"  ⚠️  Bitda深度相对劣势")
            else:
                print(f"  ⚖️  深度基本平衡")
    
    async def demo_price_analysis(self):
        """演示价格分析"""
        print("正在分析标记价格与最新价格差值...")
        
        result = await self.dashboard.price_analyzer.analyze_mark_price_difference(hours=1)
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            symbol_data = result.get('results', {}).get(symbol, {})
            print(f"\n{symbol}:")
            print(f"  数据记录数: {symbol_data.get('total_records', 0)}")
            print(f"  平均差值: {symbol_data.get('avg_rel_diff_pct', 0):.4f}%")
            print(f"  最大差值: {symbol_data.get('max_rel_diff_pct', 0):.4f}%")
            print(f"  异常比例: {symbol_data.get('high_diff_rate_pct', 0):.2f}%")
            print(f"  趋势: {symbol_data.get('trend_direction', 'unknown')}")
            
            # 评估价格差值状况
            avg_diff = symbol_data.get('avg_rel_diff_pct', 0)
            if avg_diff < 0.01:
                print(f"  ✅ 价格差值很小，表现良好")
            elif avg_diff < 0.05:
                print(f"  ⚠️  价格差值适中，需要关注")
            else:
                print(f"  ❌ 价格差值较大，需要优化")
    
    async def demo_funding_analysis(self):
        """演示资金费率分析"""
        print("正在分析资金费率...")
        
        result = await self.dashboard.funding_analyzer.analyze_funding_rates(days=1)
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            symbol_data = result.get('results', {}).get(symbol, {})
            print(f"\n{symbol}:")
            print(f"  当前费率: {symbol_data.get('current_funding_rate', 0)*100:.4f}%")
            print(f"  平均费率: {symbol_data.get('avg_funding_rate', 0)*100:.4f}%")
            print(f"  费率波动性: {symbol_data.get('funding_rate_volatility', 0)*100:.4f}%")
            print(f"  趋势方向: {symbol_data.get('trend_direction', 'unknown')}")
            
            # 评估资金费率状况
            current_rate = abs(symbol_data.get('current_funding_rate', 0) * 100)
            if current_rate < 0.01:
                print(f"  ✅ 资金费率很低")
            elif current_rate < 0.05:
                print(f"  ⚠️  资金费率适中")
            else:
                print(f"  ❌ 资金费率较高")
        
        # 比较BTC和ETH费率
        btc_rate = result.get('results', {}).get('BTCUSDT', {}).get('current_funding_rate', 0) * 100
        eth_rate = result.get('results', {}).get('ETHUSDT', {}).get('current_funding_rate', 0) * 100
        rate_diff = abs(btc_rate - eth_rate)
        
        print(f"\n📊 BTC vs ETH 费率对比:")
        print(f"  费率差异: {rate_diff:.4f}%")
        if rate_diff < 0.01:
            print(f"  ✅ 费率差异很小")
        else:
            print(f"  ⚠️  费率差异: {rate_diff:.4f}%")
    
    async def demo_visualization(self):
        """演示可视化功能"""
        print("正在生成可视化图表...")
        
        # 生成所有类型的图表
        chart_files = await self.dashboard.generate_visual_report(hours=1)
        
        if chart_files:
            print("生成的图表文件:")
            for chart_type, filename in chart_files.items():
                if filename:
                    print(f"  📊 {chart_type}: {filename}")
            
            print("\n💡 提示:")
            print("  - 图表文件保存在当前目录")
            print("  - 可以使用图片查看器打开PNG文件")
            print("  - dashboard图表包含所有分析的综合视图")
        else:
            print("⚠️  图表生成失败，请检查matplotlib配置")
    
    def print_analysis_tips(self):
        """打印分析建议"""
        print("\n" + "="*60)
        print("💡 数据分析建议")
        print("="*60)
        
        tips = [
            "🚀 延时分析: 关注平均延时<100ms，价格差异<0.01%",
            "📈 K线分析: 连续相同K线比例应<5%，避免0插针",
            "📊 深度分析: Bitda深度比值>1表示优势，>60%时间优势为佳",
            "💰 价格分析: 标记价格差值应<0.05%，异常比例<10%",
            "📋 资金费率: 绝对值<0.05%为正常，关注趋势变化",
            "🎯 综合监控: 建议每15分钟运行一次分析",
            "📊 可视化: 使用dashboard图表快速了解整体状况"
        ]
        
        for tip in tips:
            print(f"  {tip}")
        
        print("\n🔧 故障排除:")
        print("  - 数据不足: 确保数据收集器正常运行")
        print("  - 图表失败: 检查matplotlib和中文字体")
        print("  - 分析异常: 查看日志文件获取详细信息")

async def main():
    """主函数"""
    demo = AnalyzerDemo()
    
    try:
        await demo.run_demo()
        demo.print_analysis_tips()
        
    except KeyboardInterrupt:
        print("\n⏹️  演示被中断")
    except Exception as e:
        logger.error(f"演示失败: {e}")
        print(f"❌ 演示失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
