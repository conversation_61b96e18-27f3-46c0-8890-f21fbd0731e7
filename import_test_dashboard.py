#!/usr/bin/env python3
"""
导入最小化测试仪表板
"""

import json
import subprocess
import time

def import_dashboard_via_file():
    """通过文件导入仪表板"""
    print("📊 通过文件导入测试仪表板...")
    
    # 读取仪表板JSON
    with open('minimal_test_dashboard.json', 'r', encoding='utf-8') as f:
        dashboard = json.load(f)
    
    # 创建导入配置
    import_config = {
        "dashboard": dashboard,
        "overwrite": True,
        "inputs": []
    }
    
    # 保存导入配置到临时文件
    with open('import_config.json', 'w', encoding='utf-8') as f:
        json.dump(import_config, f, ensure_ascii=False, indent=2)
    
    print("✅ 导入配置文件已创建: import_config.json")
    
    # 使用curl导入
    cmd = [
        'curl', '-s', '-u', 'admin:admin',
        '-X', 'POST',
        '-H', 'Content-Type: application/json',
        '-d', '@import_config.json',
        'http://localhost:3000/api/dashboards/import'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and result.stdout:
            try:
                response = json.loads(result.stdout)
                if 'uid' in response:
                    dashboard_url = f"http://localhost:3000/d/{response['uid']}"
                    print(f"✅ 测试仪表板导入成功!")
                    print(f"🔗 URL: {dashboard_url}")
                    return dashboard_url
                else:
                    print(f"⚠️ 导入响应: {result.stdout}")
            except json.JSONDecodeError:
                print(f"⚠️ 响应不是有效JSON: {result.stdout}")
        else:
            print(f"❌ 导入失败: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("⏰ 导入请求超时")
    except Exception as e:
        print(f"❌ 导入异常: {e}")
    
    return None

def create_dashboard_manually():
    """提供手动创建仪表板的指导"""
    print("\n📋 手动创建仪表板步骤:")
    print("=" * 50)
    print("1. 访问: http://localhost:3000/dashboards")
    print("2. 点击: 'New' -> 'New Dashboard'")
    print("3. 点击: 'Add visualization'")
    print("4. 选择数据源: 'grafana-clickhouse-datasource'")
    print("5. 在查询框中输入: SELECT 1 as test_value")
    print("6. 点击: 'Run query'")
    print("7. 在右侧面板设置:")
    print("   - Title: 🟢 连接测试")
    print("   - Visualization: Stat")
    print("   - Color: Green")
    print("8. 点击: 'Apply'")
    print("9. 重复步骤3-8创建第二个面板:")
    print("   - Query: SELECT COUNT(*) as record_count FROM crypto.binance_bookticker")
    print("   - Title: 🔴 数据计数")
    print("   - Color: Red")
    print("10. 保存仪表板，名称: 'ClickHouse最小测试'")

def main():
    """主函数"""
    print("🚀 导入ClickHouse最小测试仪表板")
    print("=" * 50)
    
    # 尝试自动导入
    dashboard_url = import_dashboard_via_file()
    
    if dashboard_url:
        print(f"\n🎉 自动导入成功!")
        print(f"📊 请访问: {dashboard_url}")
        print("\n📋 验证步骤:")
        print("  1. 左侧绿色面板应显示数字 '1'")
        print("  2. 右侧红色面板应显示数据计数 (约1800万)")
        print("  3. 如果都显示数据，说明ClickHouse连接正常")
        print("  4. 确认后，我将创建完整的BTCUSDT和ETHUSDT面板")
    else:
        print(f"\n⚠️ 自动导入失败，请手动创建:")
        create_dashboard_manually()
        print(f"\n📝 手动创建完成后，请确认测试面板是否显示数据")

if __name__ == "__main__":
    main()
