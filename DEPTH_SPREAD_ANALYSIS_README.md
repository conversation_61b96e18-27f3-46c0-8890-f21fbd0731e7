# 🔥 BTCUSDT vs ETHUSDT 深度价差对比分析系统

## 📋 系统概述

这是一个完整的深度价差对比分析系统，用于比较Bitda和Binance交易所在BTCUSDT和ETHUSDT交易对上的深度和价差差异。

## 🎯 核心功能

### 1. **深度对比分析**
- 根据Bitda时间戳匹配最近的Binance数据
- 计算买一、卖一、买一卖一、买二卖二、买五卖五的数量比值
- 深度比 = Bitda数量 / Binance数量

### 2. **价差对比分析**
- 计算Bitda的价差：卖一价 - 买一价
- 统计Binance在时间窗口内的价差分布
- 包括最近、最大、最小、平均、中位数价差
- 计算价差差值：Bitda价差 - Binance价差

### 3. **时间匹配逻辑**
- 在5分钟时间窗口内查找最接近的Binance数据
- 记录时间差以评估数据匹配质量
- 处理1分钟前的数据避免实时性问题

## 📊 数据表格展示

### 深度对比表格
| 项目 | Bitda | Binance | 深度比 |
|------|-------|---------|--------|
| 买一 | 16.91 | 13.77 | 1.23 |
| 卖一 | 9.88 | 0.001 | 9875.60 |
| 买一卖一 | 26.79 | 13.77 | 1.95 |
| 买二卖二 | 50.72 | 23.67 | 2.14 |
| 买五卖五 | 132.36 | 26.48 | 5.00 |

### 价差对比表格
| 项目 | Bitda | Binance |
|------|-------|---------|
| 买一卖一最近价差 | 0.1000 | 0.1000 |
| 买一卖一最大价差 | - | 0.2000 |
| 买一卖一最小价差 | - | 0.1000 |
| 买一卖一平均价差 | - | 0.1020 |
| 买一卖一价差中位数 | - | 0.1000 |
| 买五卖五最大价差 | - | 2.0000 |
| 买五卖五最小价差 | - | 0.9000 |

## 🗄️ 数据库设计

### 分析结果数据库: `depth_spread_analysis`

#### 1. 深度对比表: `depth_comparison`
```sql
- symbol: 交易对 (BTCUSDT/ETHUSDT)
- analysis_time: 分析时间
- bitda_timestamp: Bitda时间戳
- binance_timestamp: 匹配的Binance时间戳
- time_diff_ms: 时间差(毫秒)
- bitda_bid1_qty, bitda_ask1_qty: Bitda买一卖一数量
- binance_bid1_qty, binance_ask1_qty: Binance买一卖一数量
- bid1_ratio, ask1_ratio: 买一卖一比值
- bid_ask1_ratio, bid_ask2_ratio, bid_ask5_ratio: 各档位总量比值
```

#### 2. 价差对比表: `spread_comparison`
```sql
- symbol: 交易对
- analysis_time: 分析时间
- bitda_spread: Bitda价差
- binance_spread_latest: Binance最近价差
- binance_spread_max/min/avg/median: Binance价差统计
- bid_ask1_spread_diff: 价差差值
- sample_count: 样本数量
```

## 🚀 系统组件

### 1. **核心分析器** (`depth_spread_analyzer.py`)
- 完整的分析逻辑实现
- 数据库初始化和数据处理
- 深度对比和价差对比计算

### 2. **快速测试** (`quick_test_analyzer.py`)
- 验证分析逻辑的正确性
- 展示表格格式的分析结果
- 用于调试和验证

### 3. **快速分析** (`quick_analysis_test.py`)
- 生成样本分析数据
- 只处理最新的1条数据
- 用于演示和测试

### 4. **后台服务** (`depth_spread_service.py`)
- 每分钟自动分析
- 处理1分钟前的数据
- 持续运行的后台服务

### 5. **演示服务** (`demo_service.py`)
- 每30秒运行一次分析
- 自动更新仪表板
- 用于演示完整流程

### 6. **Grafana仪表板** (`depth_spread_dashboard.py`)
- 炫酷的HTML表格展示
- 渐变色设计
- 颜色编码的比值显示
- 实时数据更新

## 🎨 Grafana仪表板特点

### 视觉设计
- 🌈 渐变色背景和表格设计
- 🎯 颜色编码：绿色表示Bitda优势，红色表示劣势
- 📊 清晰的表格布局和数据展示
- 💡 详细的数据说明和解释

### 数据展示
- 📈 BTCUSDT和ETHUSDT并排对比
- 📊 深度对比表格：展示各档位的数量比值
- 💰 价差对比表格：展示价差统计和差值
- ⏰ 时间信息：数据时间戳和时间差
- 📋 样本信息：分析样本数量和时间窗口

## 🔧 使用方法

### 1. 快速验证逻辑
```bash
python3 quick_test_analyzer.py
```

### 2. 生成样本数据
```bash
python3 quick_analysis_test.py
```

### 3. 创建仪表板
```bash
python3 depth_spread_dashboard.py
```

### 4. 启动演示服务
```bash
python3 demo_service.py
```

### 5. 启动生产服务
```bash
python3 depth_spread_service.py
```

## 📊 分析结果示例

### BTCUSDT分析结果
- **买一量比值**: 1.82 (Bitda比Binance多82%)
- **卖一量比值**: 1.14 (Bitda比Binance多14%)
- **价差差值**: 0.1000 (Bitda价差比Binance大0.1)

### ETHUSDT分析结果
- **买一量比值**: 0.92 (Bitda比Binance少8%)
- **卖一量比值**: 3.78 (Bitda比Binance多278%)
- **价差差值**: -0.0000 (价差基本相同)

## 🎯 关键洞察

1. **深度优势**: Bitda在某些档位显示出更好的深度
2. **价差对比**: 两个交易所的价差相对接近
3. **时间匹配**: 数据匹配时间差通常在几毫秒内
4. **数据质量**: 高质量的时间匹配确保分析准确性

## 🔄 系统架构

```
数据源 (depth_db)
    ↓
深度价差分析器
    ↓
分析结果数据库 (depth_spread_analysis)
    ↓
Grafana仪表板展示
```

## 📈 扩展功能

- 支持更多交易对
- 历史趋势分析
- 实时告警功能
- API接口提供
- 更多统计指标

## 🎉 总结

这个系统提供了完整的深度价差对比分析解决方案，从数据处理到可视化展示，帮助用户深入了解不同交易所之间的流动性和价差差异。
