[Unit]
Description=ETHUSDT Latency Processor Service
Documentation=ETHUSDT延时数据实时处理服务
After=network.target mysql.service
Wants=mysql.service

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/home/<USER>/project/WS_DATA_ALL
ExecStart=/usr/bin/python3 /home/<USER>/project/WS_DATA_ALL/ethusdt_realtime_processor.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# 环境变量
Environment=PYTHONPATH=/home/<USER>/project/WS_DATA_ALL
Environment=PYTHONUNBUFFERED=1

# 资源限制
LimitNOFILE=65536
MemoryMax=512M

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/home/<USER>/project/WS_DATA_ALL

[Install]
WantedBy=multi-user.target
