#!/usr/bin/env python3
"""
验证Grafana仪表板数据更新
监控5分钟后数据是否正确更新
"""

import requests
import time
import json
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GrafanaUpdateVerifier:
    """Grafana更新验证器"""
    
    def __init__(self):
        self.grafana_url = "http://localhost:3000"
        self.admin_user = "admin"
        self.admin_pass = "admin"
        self.session = requests.Session()
        self.session.auth = (self.admin_user, self.admin_pass)
        
        # 当前仪表板URL（从之前的运行结果获取）
        self.dashboard_url = "http://localhost:3000/d/60234fe2-b0b3-4726-ae0e-5d055cfe63b4/2147a43"
        
    def capture_current_data(self):
        """捕获当前仪表板数据作为基准"""
        print("📊 捕获当前仪表板数据作为基准...")
        
        current_time = datetime.now()
        
        # 这里我们记录当前时间，作为数据更新的基准点
        baseline_data = {
            'capture_time': current_time,
            'capture_timestamp': current_time.strftime('%Y-%m-%d %H:%M:%S'),
            'expected_update_time': current_time + timedelta(minutes=1),  # 仪表板1分钟刷新
            'dashboard_url': self.dashboard_url,
            'verification_points': [
                '深度对比面板数据',
                '深度统计面板数据', 
                '价差对比面板数据',
                '时间戳更新'
            ]
        }
        
        print(f"   ✅ 基准时间: {baseline_data['capture_timestamp']}")
        print(f"   ⏰ 预期更新时间: {baseline_data['expected_update_time'].strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   🌐 仪表板地址: {baseline_data['dashboard_url']}")
        
        return baseline_data
    
    def create_update_verification_dashboard(self):
        """创建专门用于验证更新的仪表板"""
        print("🎨 创建更新验证仪表板...")
        
        current_time = datetime.now()
        
        dashboard_config = {
            "dashboard": {
                "id": None,
                "title": f"🔄 数据更新验证 - {current_time.strftime('%H:%M:%S')}",
                "tags": ["update", "verification", "monitoring"],
                "timezone": "browser",
                "panels": [
                    # 更新状态面板
                    {
                        "id": 1,
                        "title": "",
                        "type": "text",
                        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0},
                        "targets": [],
                        "options": {
                            "mode": "html",
                            "content": f"""
                            <div style="
                                background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
                                color: white;
                                height: 100%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                border-radius: 8px;
                                font-family: Arial, sans-serif;
                            ">
                                <div style="text-align: center;">
                                    <h1 style="margin: 0; font-size: 28px;">🔄 Grafana数据更新验证</h1>
                                    <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">
                                        📅 基准时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}<br>
                                        ⏰ 刷新频率: 1分钟<br>
                                        🎯 验证目标: 数据自动更新<br>
                                        🌐 主仪表板: <a href="{self.dashboard_url}" style="color: #fff; text-decoration: underline;">点击查看</a>
                                    </p>
                                </div>
                            </div>
                            """
                        }
                    },
                    
                    # 验证指南面板
                    {
                        "id": 2,
                        "title": "",
                        "type": "text",
                        "gridPos": {"h": 12, "w": 12, "x": 0, "y": 8},
                        "targets": [],
                        "options": {
                            "mode": "html",
                            "content": """
                            <div style="padding: 20px; background: #f8f9fa; border-radius: 8px; font-family: Arial, sans-serif; height: 100%;">
                                <h3 style="color: #333; margin-bottom: 20px;">📋 验证步骤指南</h3>
                                
                                <div style="margin-bottom: 15px;">
                                    <h4 style="color: #007bff; margin-bottom: 8px;">🔍 1. 记录当前数据</h4>
                                    <ul style="margin: 0; padding-left: 20px; font-size: 12px;">
                                        <li>记录当前深度对比面板的数值</li>
                                        <li>记录当前深度统计面板的数值</li>
                                        <li>记录当前时间戳</li>
                                    </ul>
                                </div>
                                
                                <div style="margin-bottom: 15px;">
                                    <h4 style="color: #28a745; margin-bottom: 8px;">⏰ 2. 等待更新</h4>
                                    <ul style="margin: 0; padding-left: 20px; font-size: 12px;">
                                        <li>等待1分钟（仪表板刷新频率）</li>
                                        <li>观察页面是否自动刷新</li>
                                        <li>注意浏览器标题栏的刷新指示</li>
                                    </ul>
                                </div>
                                
                                <div style="margin-bottom: 15px;">
                                    <h4 style="color: #ffc107; margin-bottom: 8px;">✅ 3. 验证更新</h4>
                                    <ul style="margin: 0; padding-left: 20px; font-size: 12px;">
                                        <li>检查数值是否发生变化</li>
                                        <li>检查时间戳是否更新</li>
                                        <li>验证数据的合理性</li>
                                    </ul>
                                </div>
                                
                                <div style="background: #d4edda; padding: 10px; border-radius: 4px; border-left: 3px solid #28a745;">
                                    <small style="font-size: 11px;"><strong>💡 提示:</strong> 如果数据没有更新，可能是数据库中没有新数据，或者仪表板配置有问题</small>
                                </div>
                            </div>
                            """
                        }
                    },
                    
                    # 验证清单面板
                    {
                        "id": 3,
                        "title": "",
                        "type": "text",
                        "gridPos": {"h": 12, "w": 12, "x": 12, "y": 8},
                        "targets": [],
                        "options": {
                            "mode": "html",
                            "content": f"""
                            <div style="padding: 20px; background: #f8f9fa; border-radius: 8px; font-family: Arial, sans-serif; height: 100%;">
                                <h3 style="color: #333; margin-bottom: 20px;">📝 验证清单</h3>
                                
                                <div style="margin-bottom: 15px;">
                                    <h4 style="color: #dc3545; margin-bottom: 8px;">🎯 BTCUSDT深度对比</h4>
                                    <div style="font-size: 12px; background: white; padding: 8px; border-radius: 4px;">
                                        <input type="checkbox" id="btc_depth_compare"> 数值是否变化<br>
                                        <input type="checkbox" id="btc_time_diff"> 时间差是否更新<br>
                                        <input type="checkbox" id="btc_ratios"> 深度比是否合理
                                    </div>
                                </div>
                                
                                <div style="margin-bottom: 15px;">
                                    <h4 style="color: #fd7e14; margin-bottom: 8px;">📊 BTCUSDT深度统计</h4>
                                    <div style="font-size: 12px; background: white; padding: 8px; border-radius: 4px;">
                                        <input type="checkbox" id="btc_stats_max"> 最大值是否更新<br>
                                        <input type="checkbox" id="btc_stats_min"> 最小值是否更新<br>
                                        <input type="checkbox" id="btc_stats_avg"> 平均值是否更新
                                    </div>
                                </div>
                                
                                <div style="margin-bottom: 15px;">
                                    <h4 style="color: #6f42c1; margin-bottom: 8px;">🎯 ETHUSDT面板</h4>
                                    <div style="font-size: 12px; background: white; padding: 8px; border-radius: 4px;">
                                        <input type="checkbox" id="eth_depth_compare"> 深度对比更新<br>
                                        <input type="checkbox" id="eth_stats"> 深度统计更新<br>
                                        <input type="checkbox" id="eth_spread"> 价差对比更新
                                    </div>
                                </div>
                                
                                <div style="background: #cce5ff; padding: 10px; border-radius: 4px; border-left: 3px solid #007bff;">
                                    <small style="font-size: 11px;"><strong>⏰ 当前时间:</strong> {current_time.strftime('%H:%M:%S')}</small><br>
                                    <small style="font-size: 11px;"><strong>🔄 下次更新:</strong> {(current_time + timedelta(minutes=1)).strftime('%H:%M:%S')}</small>
                                </div>
                            </div>
                            """
                        }
                    }
                ],
                "time": {"from": "now-1h", "to": "now"},
                "timepicker": {},
                "templating": {"list": []},
                "annotations": {"list": []},
                "refresh": "1m",
                "schemaVersion": 30,
                "version": 1,
                "links": []
            },
            "overwrite": True
        }
        
        try:
            response = self.session.post(
                f"{self.grafana_url}/api/dashboards/db",
                json=dashboard_config,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                verification_url = f"{self.grafana_url}{result['url']}"
                print(f"   ✅ 验证仪表板创建成功")
                print(f"   🌐 验证地址: {verification_url}")
                return verification_url
            else:
                print(f"   ❌ 创建验证仪表板失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"   ❌ 创建验证仪表板异常: {e}")
            return None
    
    def monitor_updates(self, duration_minutes=5):
        """监控指定时间内的更新"""
        print(f"🔄 开始监控{duration_minutes}分钟内的数据更新...")
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        print(f"   📅 监控开始: {start_time.strftime('%H:%M:%S')}")
        print(f"   📅 监控结束: {end_time.strftime('%H:%M:%S')}")
        print(f"   ⏰ 预期更新次数: {duration_minutes}次（每分钟1次）")
        print()
        
        update_count = 0
        
        while datetime.now() < end_time:
            current_time = datetime.now()
            remaining_seconds = (end_time - current_time).total_seconds()
            
            print(f"⏰ {current_time.strftime('%H:%M:%S')} - 剩余 {remaining_seconds:.0f} 秒")
            
            # 检查是否到了更新时间点（每分钟的0秒）
            if current_time.second == 0:
                update_count += 1
                print(f"   🔄 第{update_count}次更新时间点到达")
                print(f"   💡 请检查仪表板是否更新数据")
            
            time.sleep(1)
        
        print(f"\n✅ 监控完成！")
        print(f"   📊 监控时长: {duration_minutes}分钟")
        print(f"   🔄 更新时间点: {update_count}次")
        
        return update_count

def main():
    """主函数"""
    print("🔄 Grafana数据更新验证器")
    print("=" * 60)
    print("🎯 验证目标:")
    print("   1. 确认仪表板按1分钟频率自动刷新")
    print("   2. 验证数据是否正确更新")
    print("   3. 检查时间戳是否更新")
    print("   4. 确保数据的连续性和准确性")
    print()
    
    verifier = GrafanaUpdateVerifier()
    
    # 1. 捕获当前数据基准
    baseline = verifier.capture_current_data()
    
    # 2. 创建验证仪表板
    verification_url = verifier.create_update_verification_dashboard()
    
    if verification_url:
        print(f"\n📋 验证步骤:")
        print(f"   1. 打开主仪表板: {verifier.dashboard_url}")
        print(f"   2. 打开验证仪表板: {verification_url}")
        print(f"   3. 记录当前数据作为基准")
        print(f"   4. 等待1分钟观察更新")
        print(f"   5. 验证数据是否发生变化")
        
        print(f"\n🔍 手动验证要点:")
        print(f"   ✅ 深度对比面板: 数值、时间差是否更新")
        print(f"   ✅ 深度统计面板: 最大值、最小值、平均值是否更新")
        print(f"   ✅ 价差对比面板: 价差数据、时间差是否更新")
        print(f"   ✅ 页面标题: 时间戳是否更新")
        
        # 3. 可选：自动监控
        print(f"\n❓ 是否需要自动监控5分钟？(y/n): ", end="")
        choice = input().lower().strip()
        
        if choice == 'y':
            verifier.monitor_updates(5)
        else:
            print(f"💡 请手动验证仪表板更新")
    
    print(f"\n🎉 验证工具准备完成！")
    print(f"📊 Excel验证数据已导出: depth_verification_20250531_113741.xlsx")
    print(f"🔄 Grafana更新验证仪表板已创建")

if __name__ == "__main__":
    main()
