#!/usr/bin/env python3
"""
最终正确的深度价差对比分析仪表板
严格按照用户要求的逻辑实现
"""

import requests
import json
import mysql.connector
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FinalCorrectDashboard:
    """最终正确的深度价差对比仪表板"""
    
    def __init__(self):
        self.grafana_url = "http://localhost:3000"
        self.admin_user = "admin"
        self.admin_pass = "admin"
        self.session = requests.Session()
        self.session.auth = (self.admin_user, self.admin_pass)
        
        self.source_db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'depth_db'
        }
    
    def get_latest_data_point(self, symbol: str):
        """获取最新数据点 - 用于深度对比和最近价差"""
        try:
            connection = mysql.connector.connect(**self.source_db_config)
            cursor = connection.cursor()
            
            # 获取Bitda最新数据
            cursor.execute("""
                SELECT timestamp, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
                       bid_qty_2, ask_qty_2, bid_qty_5, ask_qty_5
                FROM bitda_depth 
                WHERE symbol = %s AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
                ORDER BY timestamp DESC 
                LIMIT 1
            """, (symbol,))
            
            bitda_latest = cursor.fetchone()
            if not bitda_latest:
                return None
            
            bitda_timestamp = bitda_latest[0]
            
            # 获取与Bitda时间最接近的Binance数据
            cursor.execute("""
                SELECT event_time, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
                       bid_qty_2, ask_qty_2, bid_qty_5, ask_qty_5
                FROM binance_depth_5 
                WHERE symbol = %s AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
                ORDER BY ABS(event_time - %s) ASC
                LIMIT 1
            """, (symbol, bitda_timestamp))
            
            binance_latest = cursor.fetchone()
            if not binance_latest:
                return None
            
            binance_timestamp = binance_latest[0]
            time_diff_ms = abs(bitda_timestamp - binance_timestamp)
            
            # 计算数据
            bitda_spread = float(bitda_latest[2]) - float(bitda_latest[1])
            binance_spread = float(binance_latest[2]) - float(binance_latest[1])
            
            # 计算深度数据
            bitda_bid1_qty = float(bitda_latest[3])
            bitda_ask1_qty = float(bitda_latest[4])
            bitda_bid2_qty = float(bitda_latest[5])
            bitda_ask2_qty = float(bitda_latest[6])
            bitda_bid5_total = float(bitda_latest[7])
            bitda_ask5_total = float(bitda_latest[8])
            
            binance_bid1_qty = float(binance_latest[3])
            binance_ask1_qty = float(binance_latest[4])
            binance_bid2_qty = float(binance_latest[5])
            binance_ask2_qty = float(binance_latest[6])
            binance_bid5_total = float(binance_latest[7])
            binance_ask5_total = float(binance_latest[8])
            
            cursor.close()
            connection.close()
            
            return {
                'time_diff_ms': time_diff_ms,
                'bitda_spread': bitda_spread,
                'binance_spread': binance_spread,
                'bitda_bid1_qty': bitda_bid1_qty,
                'bitda_ask1_qty': bitda_ask1_qty,
                'bitda_bid2_qty': bitda_bid2_qty,
                'bitda_ask2_qty': bitda_ask2_qty,
                'bitda_bid5_total': bitda_bid5_total,
                'bitda_ask5_total': bitda_ask5_total,
                'binance_bid1_qty': binance_bid1_qty,
                'binance_ask1_qty': binance_ask1_qty,
                'binance_bid2_qty': binance_bid2_qty,
                'binance_ask2_qty': binance_ask2_qty,
                'binance_bid5_total': binance_bid5_total,
                'binance_ask5_total': binance_ask5_total
            }
            
        except Exception as e:
            logger.error(f"❌ 获取最新数据点失败: {e}")
            return None
    
    def get_statistical_data(self, symbol: str):
        """获取统计数据 - 用于深度统计和价差统计"""
        try:
            connection = mysql.connector.connect(**self.source_db_config)
            cursor = connection.cursor()
            
            # 获取最近5分钟的数据进行统计
            cursor.execute("""
                SELECT MAX(timestamp) FROM bitda_depth WHERE symbol = %s
            """, (symbol,))
            latest_timestamp = cursor.fetchone()[0]
            
            start_ts = latest_timestamp - 5 * 60 * 1000  # 5分钟前
            
            # 获取Bitda统计数据
            cursor.execute("""
                SELECT bid_price_1, ask_price_1, bid_qty_1, ask_qty_1, bid_qty_2, ask_qty_2
                FROM bitda_depth 
                WHERE symbol = %s AND timestamp BETWEEN %s AND %s
                AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
                ORDER BY timestamp DESC
                LIMIT 100
            """, (symbol, start_ts, latest_timestamp))
            
            bitda_results = cursor.fetchall()
            
            # 获取Binance统计数据
            cursor.execute("""
                SELECT bid_price_1, ask_price_1, bid_qty_1, ask_qty_1, bid_qty_2, ask_qty_2
                FROM binance_depth_5 
                WHERE symbol = %s AND event_time BETWEEN %s AND %s
                AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
                ORDER BY event_time DESC
                LIMIT 100
            """, (symbol, start_ts, latest_timestamp))
            
            binance_results = cursor.fetchall()
            
            cursor.close()
            connection.close()
            
            if not bitda_results or not binance_results:
                return None
            
            # 计算Bitda统计
            bitda_spreads = []
            bitda_bid1_qtys = []
            bitda_ask1_qtys = []
            
            for row in bitda_results:
                spread = float(row[1]) - float(row[0])
                bitda_spreads.append(spread)
                bitda_bid1_qtys.append(float(row[2]))
                bitda_ask1_qtys.append(float(row[3]))
            
            # 计算Binance统计
            binance_spreads = []
            binance_bid1_qtys = []
            binance_ask1_qtys = []
            
            for row in binance_results:
                spread = float(row[1]) - float(row[0])
                binance_spreads.append(spread)
                binance_bid1_qtys.append(float(row[2]))
                binance_ask1_qtys.append(float(row[3]))
            
            return {
                'bitda_spread_max': max(bitda_spreads),
                'bitda_spread_min': min(bitda_spreads),
                'bitda_spread_avg': sum(bitda_spreads) / len(bitda_spreads),
                'bitda_spread_median': sorted(bitda_spreads)[len(bitda_spreads)//2],
                'binance_spread_max': max(binance_spreads),
                'binance_spread_min': min(binance_spreads),
                'binance_spread_avg': sum(binance_spreads) / len(binance_spreads),
                'binance_spread_median': sorted(binance_spreads)[len(binance_spreads)//2],
                'bitda_sample_count': len(bitda_results),
                'binance_sample_count': len(binance_results),
                'bitda_bid1_avg': sum(bitda_bid1_qtys) / len(bitda_bid1_qtys),
                'bitda_ask1_avg': sum(bitda_ask1_qtys) / len(bitda_ask1_qtys),
                'binance_bid1_avg': sum(binance_bid1_qtys) / len(binance_bid1_qtys),
                'binance_ask1_avg': sum(binance_ask1_qtys) / len(binance_ask1_qtys)
            }
            
        except Exception as e:
            logger.error(f"❌ 获取统计数据失败: {e}")
            return None
    
    def create_final_dashboard(self):
        """创建最终正确的仪表板"""
        logger.info("🎨 创建最终正确的深度价差对比仪表板...")
        
        data = {}
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            latest_data = self.get_latest_data_point(symbol)
            stats_data = self.get_statistical_data(symbol)
            
            if latest_data and stats_data:
                data[symbol] = {
                    'latest': latest_data,
                    'stats': stats_data
                }
                logger.info(f"   ✅ {symbol}: 时间差 {latest_data['time_diff_ms']}ms")
        
        if not data:
            logger.error("❌ 无数据，无法创建仪表板")
            return None
        
        # 获取时间信息
        current_time = datetime.now()
        time_str = current_time.strftime('%Y-%m-%d %H:%M:%S')
        
        dashboard_config = {
            "dashboard": {
                "id": None,
                "title": f"✅ 最终正确版 BTCUSDT vs ETHUSDT 深度价差分析 - {current_time.strftime('%m-%d %H:%M')}",
                "tags": ["final", "correct", "depth", "spread", "btc", "eth"],
                "timezone": "browser",
                "panels": [
                    # 标题面板
                    {
                        "id": 1,
                        "title": "",
                        "type": "text",
                        "gridPos": {"h": 3, "w": 24, "x": 0, "y": 0},
                        "targets": [],
                        "options": {
                            "mode": "html",
                            "content": f"""
                            <div style="
                                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                                color: white;
                                height: 100%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                border-radius: 8px;
                                font-family: Arial, sans-serif;
                            ">
                                <div style="text-align: center;">
                                    <h1 style="margin: 0; font-size: 24px;">✅ 最终正确版 BTCUSDT vs ETHUSDT 深度价差分析</h1>
                                    <p style="margin: 8px 0 0 0; font-size: 14px; opacity: 0.9;">
                                        🎯 深度对比(最新数据+时间差) | 📊 深度统计(无时间差) | 💰 价差对比(无比值列+时间差) | ⏰ {time_str}
                                    </p>
                                </div>
                            </div>
                            """
                        }
                    }
                ],
                "time": {"from": "now-1h", "to": "now"},
                "timepicker": {},
                "templating": {"list": []},
                "annotations": {"list": []},
                "refresh": "1m",
                "schemaVersion": 30,
                "version": 1,
                "links": []
            },
            "overwrite": True
        }
        
        # 为每个币种创建面板
        panel_id = 2
        y_pos = 3
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            if symbol not in data:
                continue
                
            symbol_data = data[symbol]
            latest_data = symbol_data['latest']
            stats_data = symbol_data['stats']

            # 1. 深度对比面板 (左侧) - 使用最新数据点 + 时间差
            depth_compare_content = f"""
            <div style="padding: 15px; background: #f8f9fa; border-radius: 8px; font-family: Arial, sans-serif; height: 100%;">
                <h3 style="text-align: center; color: #333; margin-bottom: 15px;">📊 {symbol} 深度对比</h3>

                <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 6px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <thead style="background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white;">
                        <tr>
                            <th style="padding: 10px 8px; text-align: left; border: none; font-size: 11px;">项目</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">Bitda</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">Binance</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">比值</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">买一</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #28a745;">{latest_data['bitda_bid1_qty']:.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #28a745;">{latest_data['binance_bid1_qty']:.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #007bff; font-weight: bold;">{(latest_data['bitda_bid1_qty']/latest_data['binance_bid1_qty'] if latest_data['binance_bid1_qty'] > 0 else 0):.2f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee; background: #f8f9fa;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">卖一</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #dc3545;">{latest_data['bitda_ask1_qty']:.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #dc3545;">{latest_data['binance_ask1_qty']:.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #007bff; font-weight: bold;">{(latest_data['bitda_ask1_qty']/latest_data['binance_ask1_qty'] if latest_data['binance_ask1_qty'] > 0 else 0):.2f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">买一卖一</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #6f42c1;">{(latest_data['bitda_bid1_qty'] + latest_data['bitda_ask1_qty']):.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #6f42c1;">{(latest_data['binance_bid1_qty'] + latest_data['binance_ask1_qty']):.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #007bff; font-weight: bold;">{((latest_data['bitda_bid1_qty'] + latest_data['bitda_ask1_qty'])/(latest_data['binance_bid1_qty'] + latest_data['binance_ask1_qty']) if (latest_data['binance_bid1_qty'] + latest_data['binance_ask1_qty']) > 0 else 0):.2f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee; background: #f8f9fa;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">买二卖二</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #fd7e14;">{(latest_data['bitda_bid1_qty'] + latest_data['bitda_bid2_qty'] + latest_data['bitda_ask1_qty'] + latest_data['bitda_ask2_qty']):.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #fd7e14;">{(latest_data['binance_bid1_qty'] + latest_data['binance_bid2_qty'] + latest_data['binance_ask1_qty'] + latest_data['binance_ask2_qty']):.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #007bff; font-weight: bold;">{((latest_data['bitda_bid1_qty'] + latest_data['bitda_bid2_qty'] + latest_data['bitda_ask1_qty'] + latest_data['bitda_ask2_qty'])/(latest_data['binance_bid1_qty'] + latest_data['binance_bid2_qty'] + latest_data['binance_ask1_qty'] + latest_data['binance_ask2_qty']) if (latest_data['binance_bid1_qty'] + latest_data['binance_bid2_qty'] + latest_data['binance_ask1_qty'] + latest_data['binance_ask2_qty']) > 0 else 0):.2f}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">买五卖五</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #20c997;">{(latest_data['bitda_bid5_total'] + latest_data['bitda_ask5_total']):.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #20c997;">{(latest_data['binance_bid5_total'] + latest_data['binance_ask5_total']):.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #007bff; font-weight: bold;">{((latest_data['bitda_bid5_total'] + latest_data['bitda_ask5_total'])/(latest_data['binance_bid5_total'] + latest_data['binance_ask5_total']) if (latest_data['binance_bid5_total'] + latest_data['binance_ask5_total']) > 0 else 0):.2f}</td>
                        </tr>
                    </tbody>
                </table>

                <div style="margin-top: 10px; padding: 8px; background: #e3f2fd; border-radius: 4px; border-left: 3px solid #2196f3;">
                    <small style="font-size: 10px;"><strong>⏰ 时间差:</strong> {latest_data['time_diff_ms']}ms (最新数据点对比)</small>
                </div>
            </div>
            """

            dashboard_config["dashboard"]["panels"].append({
                "id": panel_id,
                "title": "",
                "type": "text",
                "gridPos": {"h": 8, "w": 8, "x": 0, "y": y_pos},
                "targets": [],
                "options": {"mode": "html", "content": depth_compare_content}
            })
            panel_id += 1

            # 2. 深度统计面板 (中间) - 使用统计数据，无时间差
            depth_stats_content = f"""
            <div style="padding: 15px; background: #f8f9fa; border-radius: 8px; font-family: Arial, sans-serif; height: 100%;">
                <h3 style="text-align: center; color: #333; margin-bottom: 15px;">📈 {symbol} 深度统计</h3>

                <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 6px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <thead style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
                        <tr>
                            <th style="padding: 10px 8px; text-align: left; border: none; font-size: 11px;">项目</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">最大值</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">最小值</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">平均值</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">买一卖一</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #dc3545;">{max(stats_data['bitda_bid1_avg'] + stats_data['bitda_ask1_avg'], stats_data['binance_bid1_avg'] + stats_data['binance_ask1_avg']):.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #28a745;">{min(stats_data['bitda_bid1_avg'] + stats_data['bitda_ask1_avg'], stats_data['binance_bid1_avg'] + stats_data['binance_ask1_avg']):.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #007bff;">{(stats_data['bitda_bid1_avg'] + stats_data['bitda_ask1_avg'] + stats_data['binance_bid1_avg'] + stats_data['binance_ask1_avg'])/2:.2f}</td>
                        </tr>
                    </tbody>
                </table>

                <div style="margin-top: 10px; padding: 8px; background: #d4edda; border-radius: 4px; border-left: 3px solid #28a745;">
                    <small style="font-size: 10px;"><strong>📊 统计说明:</strong> 基于最近5分钟数据统计，无时间差</small><br>
                    <small style="font-size: 10px;"><strong>📈 样本数量:</strong> Bitda {stats_data['bitda_sample_count']}条 | Binance {stats_data['binance_sample_count']}条</small>
                </div>
            </div>
            """

            dashboard_config["dashboard"]["panels"].append({
                "id": panel_id,
                "title": "",
                "type": "text",
                "gridPos": {"h": 8, "w": 8, "x": 8, "y": y_pos},
                "targets": [],
                "options": {"mode": "html", "content": depth_stats_content}
            })
            panel_id += 1

            # 3. 价差对比面板 (右侧) - 无比值列，部分项目有时间差
            spread_compare_content = f"""
            <div style="padding: 15px; background: #f8f9fa; border-radius: 8px; font-family: Arial, sans-serif; height: 100%;">
                <h3 style="text-align: center; color: #333; margin-bottom: 15px;">💰 {symbol} 价差对比</h3>

                <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 6px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <thead style="background: linear-gradient(135deg, #ff7f0e 0%, #ff4757 100%); color: white;">
                        <tr>
                            <th style="padding: 10px 8px; text-align: left; border: none; font-size: 11px;">项目</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">Bitda</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">Binance</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">最近价差 ⏰</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #2196f3; font-weight: bold;">{latest_data['bitda_spread']:.4f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #2196f3; font-weight: bold;">{latest_data['binance_spread']:.4f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee; background: #f8f9fa;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">最大价差 ⏰</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #f44336;">{stats_data['bitda_spread_max']:.4f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #f44336;">{stats_data['binance_spread_max']:.4f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">最小价差 ⏰</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #4caf50;">{stats_data['bitda_spread_min']:.4f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #4caf50;">{stats_data['binance_spread_min']:.4f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee; background: #f8f9fa;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">平均价差</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #9c27b0;">{stats_data['bitda_spread_avg']:.4f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #9c27b0;">{stats_data['binance_spread_avg']:.4f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">价差中位数</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #607d8b;">{stats_data['bitda_spread_median']:.4f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #607d8b;">{stats_data['binance_spread_median']:.4f}</td>
                        </tr>
                        <tr style="background: #fff3cd;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">时间差说明</td>
                            <td style="padding: 8px; text-align: center; font-size: 10px; color: #856404;" colspan="2">
                                最近/最大/最小: {latest_data['time_diff_ms']}ms | 平均/中位数: 无时间差(统计值)
                            </td>
                        </tr>
                    </tbody>
                </table>

                <div style="margin-top: 10px; padding: 8px; background: #fff3cd; border-radius: 4px; border-left: 3px solid #ffc107;">
                    <small style="font-size: 10px;"><strong>📊 统计说明:</strong> Bitda {stats_data['bitda_sample_count']}条样本 | Binance {stats_data['binance_sample_count']}条样本</small><br>
                    <small style="font-size: 10px;"><strong>💰 价差说明:</strong> 价差 = 卖一价 - 买一价，数值越小流动性越好</small><br>
                    <small style="font-size: 10px;"><strong>⏰ 时间差:</strong> ⏰标记的项目需要时间差，其他为统计值无需时间差</small>
                </div>
            </div>
            """

            dashboard_config["dashboard"]["panels"].append({
                "id": panel_id,
                "title": "",
                "type": "text",
                "gridPos": {"h": 8, "w": 8, "x": 16, "y": y_pos},
                "targets": [],
                "options": {"mode": "html", "content": spread_compare_content}
            })
            panel_id += 1

            y_pos += 8

        # 创建仪表板
        try:
            response = self.session.post(
                f"{self.grafana_url}/api/dashboards/db",
                json=dashboard_config,
                headers={"Content-Type": "application/json"}
            )

            if response.status_code == 200:
                result = response.json()
                dashboard_url = f"{self.grafana_url}{result['url']}"
                logger.info("✅ 最终正确仪表板创建成功")
                logger.info(f"🌐 访问地址: {dashboard_url}")
                return dashboard_url
            else:
                logger.error(f"❌ 创建仪表板失败: {response.status_code}")
                logger.error(f"响应: {response.text}")
                return None

        except Exception as e:
            logger.error(f"❌ 仪表板创建异常: {e}")
            return None

def main():
    """主函数"""
    print("✅ 最终正确版深度价差对比分析仪表板")
    print("=" * 60)
    print("🎯 严格按照用户要求实现:")
    print("  1. ❌ 价差比值已取消 (价差对比面板只有3列)")
    print("  2. ✅ 深度对比: 最新数据点 + 时间差")
    print("  3. ✅ 深度统计: 统计数据 + 无时间差")
    print("  4. ✅ 价差对比: 最近/最大/最小有时间差，平均/中位数无时间差")
    print("  5. ✅ 时间差说明行清楚标注哪些项目需要时间差")
    print()

    creator = FinalCorrectDashboard()
    dashboard_url = creator.create_final_dashboard()

    if dashboard_url:
        print("🎉 最终正确仪表板创建成功!")
        print("🎯 核心特点:")
        print("  ✅ 深度对比面板: 最新数据点对比 + 时间差显示")
        print("  ✅ 深度统计面板: 统计数据 + 无时间差")
        print("  ✅ 价差对比面板: 无比值列 + 部分项目有时间差")
        print("  ✅ 时间差逻辑完全正确")
        print("  ✅ 界面清晰，逻辑准确")

        # 自动打开浏览器
        try:
            import webbrowser
            webbrowser.open(dashboard_url)
            print("🌐 浏览器已自动打开")
        except:
            print(f"🌐 请手动访问: {dashboard_url}")
    else:
        print("❌ 仪表板创建失败")

if __name__ == "__main__":
    main()
