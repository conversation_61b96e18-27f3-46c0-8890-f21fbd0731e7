#!/usr/bin/env python3
"""
改进的深度价差对比分析仪表板
添加深度统计和修复价差显示
"""

import requests
import json
import mysql.connector
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImprovedDepthSpreadDashboard:
    """改进的深度价差对比仪表板 - 基于正确的按分钟统计"""

    def __init__(self):
        self.grafana_url = "http://localhost:3000"
        self.admin_user = "admin"
        self.admin_pass = "admin"
        self.session = requests.Session()
        self.session.auth = (self.admin_user, self.admin_pass)

        # 原始数据库配置
        self.source_db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'depth_db'
        }

        # 分析数据库配置 (备用)
        self.analysis_db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'depth_spread_analysis'
        }
    
    def get_latest_minute_stats(self):
        """获取最新数据和统计数据 - 正确区分最新数据点和统计数据"""
        logger.info("📊 获取最新数据和统计数据...")

        try:
            connection = mysql.connector.connect(**self.source_db_config)
            cursor = connection.cursor()

            data = {}

            for symbol in ['BTCUSDT', 'ETHUSDT']:
                # 获取最新数据点 (用于深度对比和最近价差)
                latest_data = self.get_latest_data_points(symbol, cursor)

                # 获取统计数据 (用于深度统计和价差统计)
                stats_data = self.get_statistical_data(symbol, cursor)

                if latest_data and stats_data:
                    data[symbol] = {
                        'latest': latest_data,
                        'stats': stats_data
                    }
                    logger.info(f"   ✅ {symbol}: 最新数据时间差 {latest_data['time_diff_ms']}ms")

            cursor.close()
            connection.close()

            return data

        except Exception as e:
            logger.error(f"❌ 获取数据失败: {e}")
            return {}

    def get_latest_data_points(self, symbol: str, cursor):
        """获取最新数据点 - 用于深度对比和最近价差"""
        try:
            # 获取Bitda最新数据
            cursor.execute("""
                SELECT timestamp, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
                       bid_qty_2, ask_qty_2, bid_qty_3, ask_qty_3, bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5
                FROM bitda_depth
                WHERE symbol = %s AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
                ORDER BY timestamp DESC
                LIMIT 1
            """, (symbol,))

            bitda_latest = cursor.fetchone()
            if not bitda_latest:
                return None

            bitda_timestamp = bitda_latest[0]

            # 获取Bitda时间点之前最近的Binance数据 (正确逻辑)
            cursor.execute("""
                SELECT event_time, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
                       bid_qty_2, ask_qty_2, bid_qty_3, ask_qty_3, bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5
                FROM binance_depth_5
                WHERE symbol = %s AND event_time <= %s
                AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
                ORDER BY event_time DESC
                LIMIT 1
            """, (symbol, bitda_timestamp))

            binance_latest = cursor.fetchone()
            if not binance_latest:
                return None

            binance_timestamp = binance_latest[0]
            time_diff_ms = abs(bitda_timestamp - binance_timestamp)

            # 计算Bitda数据
            bitda_qtys = [float(bitda_latest[i]) if bitda_latest[i] else 0 for i in range(3, 13)]
            bitda_bid_qtys = bitda_qtys[::2]
            bitda_ask_qtys = bitda_qtys[1::2]
            bitda_spread = float(bitda_latest[2]) - float(bitda_latest[1]) if bitda_latest[1] and bitda_latest[2] else 0

            # 计算Binance数据
            binance_qtys = [float(binance_latest[i]) if binance_latest[i] else 0 for i in range(3, 13)]
            binance_bid_qtys = binance_qtys[::2]
            binance_ask_qtys = binance_qtys[1::2]
            binance_spread = float(binance_latest[2]) - float(binance_latest[1]) if binance_latest[1] and binance_latest[2] else 0

            return {
                'bitda_timestamp': bitda_timestamp,
                'binance_timestamp': binance_timestamp,
                'time_diff_ms': time_diff_ms,
                'bitda_bid1_qty': bitda_bid_qtys[0],
                'bitda_ask1_qty': bitda_ask_qtys[0],
                'bitda_bid2_qty': bitda_bid_qtys[1],
                'bitda_ask2_qty': bitda_ask_qtys[1],
                'bitda_bid5_total': sum(bitda_bid_qtys),
                'bitda_ask5_total': sum(bitda_ask_qtys),
                'bitda_spread': bitda_spread,
                'binance_bid1_qty': binance_bid_qtys[0],
                'binance_ask1_qty': binance_ask_qtys[0],
                'binance_bid2_qty': binance_bid_qtys[1],
                'binance_ask2_qty': binance_ask_qtys[1],
                'binance_bid5_total': sum(binance_bid_qtys),
                'binance_ask5_total': sum(binance_ask_qtys),
                'binance_spread': binance_spread
            }

        except Exception as e:
            logger.error(f"❌ 获取最新数据点失败: {e}")
            return None

    def get_statistical_data(self, symbol: str, cursor):
        """获取统计数据 - 用于深度统计和价差统计 (最近5分钟)"""
        from datetime import datetime, timedelta

        try:
            # 获取最新时间戳
            cursor.execute("""
                SELECT MAX(timestamp) FROM bitda_depth
                WHERE symbol = %s
            """, (symbol,))
            latest_timestamp = cursor.fetchone()[0]

            if not latest_timestamp:
                return None

            # 计算统计时间范围 (最近5分钟)
            latest_dt = datetime.fromtimestamp(latest_timestamp / 1000)
            start_dt = latest_dt - timedelta(minutes=5)

            start_ts = int(start_dt.timestamp() * 1000)
            end_ts = latest_timestamp

            # 获取Bitda统计数据
            bitda_query = """
            SELECT timestamp, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
                   bid_qty_2, ask_qty_2, bid_qty_3, ask_qty_3, bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5
            FROM bitda_depth
            WHERE symbol = %s AND timestamp BETWEEN %s AND %s
            AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
            ORDER BY timestamp DESC
            LIMIT 100
            """

            cursor.execute(bitda_query, (symbol, start_ts, end_ts))
            bitda_results = cursor.fetchall()

            # 获取Binance统计数据
            binance_query = """
            SELECT event_time, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
                   bid_qty_2, ask_qty_2, bid_qty_3, ask_qty_3, bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5
            FROM binance_depth_5
            WHERE symbol = %s AND event_time BETWEEN %s AND %s
            AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
            ORDER BY event_time DESC
            LIMIT 100
            """

            cursor.execute(binance_query, (symbol, start_ts, end_ts))
            binance_results = cursor.fetchall()

            if not bitda_results or not binance_results:
                return None

            # 计算统计数据
            bitda_stats = self.calculate_exchange_stats(bitda_results, 'Bitda')
            binance_stats = self.calculate_exchange_stats(binance_results, 'Binance')

            return {
                'bitda_stats': bitda_stats,
                'binance_stats': binance_stats,
                'bitda_sample_count': len(bitda_results),
                'binance_sample_count': len(binance_results)
            }

        except Exception as e:
            logger.error(f"❌ 获取统计数据失败: {e}")
            return None

    def get_minute_data_stats(self, symbol: str, target_minute: datetime, cursor):
        """获取指定分钟内的数据统计"""
        from datetime import timedelta

        # 计算分钟的开始和结束时间戳
        start_time = target_minute
        end_time = start_time + timedelta(minutes=1)

        start_ts = int(start_time.timestamp() * 1000)
        end_ts = int(end_time.timestamp() * 1000)

        # 获取Bitda数据
        bitda_query = """
        SELECT
            timestamp, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
            bid_qty_2, ask_qty_2, bid_qty_3, ask_qty_3, bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5
        FROM bitda_depth
        WHERE symbol = %s AND timestamp BETWEEN %s AND %s
        AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
        ORDER BY timestamp
        """

        cursor.execute(bitda_query, (symbol, start_ts, end_ts))
        bitda_results = cursor.fetchall()

        # 获取Binance数据
        binance_query = """
        SELECT
            event_time, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
            bid_qty_2, ask_qty_2, bid_qty_3, ask_qty_3, bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5
        FROM binance_depth_5
        WHERE symbol = %s AND event_time BETWEEN %s AND %s
        AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
        ORDER BY event_time
        """

        cursor.execute(binance_query, (symbol, start_ts, end_ts))
        binance_results = cursor.fetchall()

        if not bitda_results or not binance_results:
            return None

        # 计算统计数据
        bitda_stats = self.calculate_exchange_stats(bitda_results, 'Bitda')
        binance_stats = self.calculate_exchange_stats(binance_results, 'Binance')

        return {
            'symbol': symbol,
            'minute': target_minute,
            'bitda_sample_count': len(bitda_results),
            'binance_sample_count': len(binance_results),
            'depth': {
                'bid1_ratio': bitda_stats['bid1_qty']['avg'] / binance_stats['bid1_qty']['avg'] if binance_stats['bid1_qty']['avg'] > 0 else 0,
                'ask1_ratio': bitda_stats['ask1_qty']['avg'] / binance_stats['ask1_qty']['avg'] if binance_stats['ask1_qty']['avg'] > 0 else 0,
                'bid_ask1_ratio': (bitda_stats['bid1_qty']['avg'] + bitda_stats['ask1_qty']['avg']) / (binance_stats['bid1_qty']['avg'] + binance_stats['ask1_qty']['avg']) if (binance_stats['bid1_qty']['avg'] + binance_stats['ask1_qty']['avg']) > 0 else 0,
                'bid_ask2_ratio': (bitda_stats['bid1_qty']['avg'] + bitda_stats['bid2_qty']['avg'] + bitda_stats['ask1_qty']['avg'] + bitda_stats['ask2_qty']['avg']) / (binance_stats['bid1_qty']['avg'] + binance_stats['bid2_qty']['avg'] + binance_stats['ask1_qty']['avg'] + binance_stats['ask2_qty']['avg']) if (binance_stats['bid1_qty']['avg'] + binance_stats['bid2_qty']['avg'] + binance_stats['ask1_qty']['avg'] + binance_stats['ask2_qty']['avg']) > 0 else 0,
                'bid_ask5_ratio': (bitda_stats['bid5_total']['avg'] + bitda_stats['ask5_total']['avg']) / (binance_stats['bid5_total']['avg'] + binance_stats['ask5_total']['avg']) if (binance_stats['bid5_total']['avg'] + binance_stats['ask5_total']['avg']) > 0 else 0,
                'bitda_bid1_qty': bitda_stats['bid1_qty']['avg'],
                'bitda_ask1_qty': bitda_stats['ask1_qty']['avg'],
                'bitda_bid2_qty': bitda_stats['bid2_qty']['avg'],
                'bitda_ask2_qty': bitda_stats['ask2_qty']['avg'],
                'bitda_bid5_total': bitda_stats['bid5_total']['avg'],
                'bitda_ask5_total': bitda_stats['ask5_total']['avg'],
                'binance_bid1_qty': binance_stats['bid1_qty']['avg'],
                'binance_ask1_qty': binance_stats['ask1_qty']['avg'],
                'binance_bid2_qty': binance_stats['bid2_qty']['avg'],
                'binance_ask2_qty': binance_stats['ask2_qty']['avg'],
                'binance_bid5_total': binance_stats['bid5_total']['avg'],
                'binance_ask5_total': binance_stats['ask5_total']['avg'],
                'analysis_time': target_minute,
                'time_diff_ms': 0  # 分钟级别统计，时间差为0
            },
            'depth_stats': {
                'bid_ask1_max': max([bitda_stats['bid1_qty']['max'] + bitda_stats['ask1_qty']['max'], binance_stats['bid1_qty']['max'] + binance_stats['ask1_qty']['max']]),
                'bid_ask1_min': min([bitda_stats['bid1_qty']['min'] + bitda_stats['ask1_qty']['min'], binance_stats['bid1_qty']['min'] + binance_stats['ask1_qty']['min']]),
                'bid_ask1_avg': (bitda_stats['bid1_qty']['avg'] + bitda_stats['ask1_qty']['avg'] + binance_stats['bid1_qty']['avg'] + binance_stats['ask1_qty']['avg']) / 2,
                'bid_ask2_max': max([bitda_stats['bid2_qty']['max'] + bitda_stats['ask2_qty']['max'], binance_stats['bid2_qty']['max'] + binance_stats['ask2_qty']['max']]),
                'bid_ask2_min': min([bitda_stats['bid2_qty']['min'] + bitda_stats['ask2_qty']['min'], binance_stats['bid2_qty']['min'] + binance_stats['ask2_qty']['min']]),
                'bid_ask2_avg': (bitda_stats['bid2_qty']['avg'] + bitda_stats['ask2_qty']['avg'] + binance_stats['bid2_qty']['avg'] + binance_stats['ask2_qty']['avg']) / 2,
                'bid_ask5_max': max([bitda_stats['bid5_total']['max'] + bitda_stats['ask5_total']['max'], binance_stats['bid5_total']['max'] + binance_stats['ask5_total']['max']]),
                'bid_ask5_min': min([bitda_stats['bid5_total']['min'] + bitda_stats['ask5_total']['min'], binance_stats['bid5_total']['min'] + binance_stats['ask5_total']['min']]),
                'bid_ask5_avg': (bitda_stats['bid5_total']['avg'] + bitda_stats['ask5_total']['avg'] + binance_stats['bid5_total']['avg'] + binance_stats['ask5_total']['avg']) / 2,
                'sample_count': len(bitda_results) + len(binance_results)
            },
            'spread': {
                'bitda_spread': bitda_stats['bid1_ask1_spread']['latest'],
                'bitda_spread_latest': bitda_stats['bid1_ask1_spread']['latest'],
                'bitda_spread_max': bitda_stats['bid1_ask1_spread']['max'],
                'bitda_spread_min': bitda_stats['bid1_ask1_spread']['min'],
                'bitda_spread_avg': bitda_stats['bid1_ask1_spread']['avg'],
                'bitda_spread_median': bitda_stats['bid1_ask1_spread']['median'],
                'bitda_sample_count': len(bitda_results),
                'binance_spread_latest': binance_stats['bid1_ask1_spread']['latest'],
                'binance_spread_max': binance_stats['bid1_ask1_spread']['max'],
                'binance_spread_min': binance_stats['bid1_ask1_spread']['min'],
                'binance_spread_avg': binance_stats['bid1_ask1_spread']['avg'],
                'binance_spread_median': binance_stats['bid1_ask1_spread']['median'],
                'binance_sample_count': len(binance_results),
                'spread_diff': bitda_stats['bid1_ask1_spread']['latest'] - binance_stats['bid1_ask1_spread']['latest'],
                'analysis_time': target_minute
            }
        }

    def calculate_exchange_stats(self, results, exchange_name):
        """计算交易所统计数据"""
        if not results:
            return {}

        # 计算价差统计 (只保留买一卖一价差)
        bid1_ask1_spreads = []

        # 计算深度统计
        bid1_qtys = []
        ask1_qtys = []
        bid2_qtys = []
        ask2_qtys = []
        bid5_totals = []
        ask5_totals = []

        for row in results:
            # 价格数据
            bid1_price = float(row[1]) if row[1] else 0
            ask1_price = float(row[2]) if row[2] else 0

            # 数量数据
            qtys = [float(row[i]) if row[i] else 0 for i in range(3, 13)]
            bid_qtys = qtys[::2]  # 买盘数量 [买1, 买2, 买3, 买4, 买5]
            ask_qtys = qtys[1::2]  # 卖盘数量 [卖1, 卖2, 卖3, 卖4, 卖5]

            # 价差计算 (只计算买一卖一价差)
            if bid1_price > 0 and ask1_price > 0:
                bid1_ask1_spreads.append(ask1_price - bid1_price)

            # 深度统计
            bid1_qtys.append(bid_qtys[0])
            ask1_qtys.append(ask_qtys[0])
            bid2_qtys.append(bid_qtys[1])
            ask2_qtys.append(ask_qtys[1])
            bid5_totals.append(sum(bid_qtys))
            ask5_totals.append(sum(ask_qtys))

        # 计算统计值
        def calc_stats(data_list):
            if not data_list:
                return {'min': 0, 'max': 0, 'avg': 0, 'median': 0, 'latest': 0}

            sorted_data = sorted(data_list)
            n = len(sorted_data)

            return {
                'min': min(sorted_data),
                'max': max(sorted_data),
                'avg': sum(sorted_data) / n,
                'median': sorted_data[n//2],
                'latest': data_list[-1]  # 最新的数据
            }

        return {
            'bid1_ask1_spread': calc_stats(bid1_ask1_spreads),
            'bid1_qty': calc_stats(bid1_qtys),
            'ask1_qty': calc_stats(ask1_qtys),
            'bid2_qty': calc_stats(bid2_qtys),
            'ask2_qty': calc_stats(ask2_qtys),
            'bid5_total': calc_stats(bid5_totals),
            'ask5_total': calc_stats(ask5_totals),
            'sample_count': len(results)
        }

    def calculate_depth_ratio_stats(self, symbol: str, stats_data):
        """
        计算深度比值统计 - 正确逻辑
        1. 获取前一分钟内所有Bitda数据（比如152条）
        2. 为每条Bitda数据找到对应的Binance数据
        3. 计算152个深度对比数据
        4. 对这152个深度比值进行统计
        """
        try:
            logger.info(f"📊 计算{symbol}深度比值统计（正确逻辑）...")

            # 数据库配置
            db_config = {
                'host': 'localhost',
                'user': 'root',
                'password': 'Linuxtest',
                'database': 'depth_db'
            }
            connection = mysql.connector.connect(**db_config)
            cursor = connection.cursor()

            # 计算前一分钟的时间戳范围
            from datetime import datetime, timedelta
            now = datetime.now()
            one_minute_ago = now - timedelta(minutes=1)
            start_timestamp = int(one_minute_ago.timestamp() * 1000)
            end_timestamp = int(now.timestamp() * 1000)

            logger.info(f"   时间范围: {one_minute_ago.strftime('%H:%M:%S')} - {now.strftime('%H:%M:%S')}")

            # 第一步：获取前一分钟内所有Bitda数据
            cursor.execute("""
                SELECT timestamp, bid_qty_1, ask_qty_1, bid_qty_2, ask_qty_2,
                       bid_qty_3, ask_qty_3, bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5
                FROM bitda_depth
                WHERE symbol = %s AND timestamp BETWEEN %s AND %s
                AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
                ORDER BY timestamp ASC
            """, (symbol, start_timestamp, end_timestamp))

            bitda_records = cursor.fetchall()
            logger.info(f"   📊 获取{symbol} Bitda数据: {len(bitda_records)}条")

            if not bitda_records:
                logger.warning(f"   ❌ 前一分钟无{symbol} Bitda数据")
                cursor.close()
                connection.close()
                return None

            # 第二步：为每条Bitda数据计算深度对比
            depth_comparisons = []  # 存储所有深度对比结果
            successful_matches = 0

            for i, bitda_record in enumerate(bitda_records):
                bitda_timestamp = bitda_record[0]

                # 为这条Bitda数据找到对应的Binance数据（该时间点之前最近的）
                cursor.execute("""
                    SELECT bid_qty_1, ask_qty_1, bid_qty_2, ask_qty_2,
                           bid_qty_3, ask_qty_3, bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5
                    FROM binance_depth_5
                    WHERE symbol = %s AND event_time <= %s
                    AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
                    ORDER BY event_time DESC
                    LIMIT 1
                """, (symbol, bitda_timestamp))

                binance_record = cursor.fetchone()
                if not binance_record:
                    continue

                # 解析Bitda数据
                bitda_bid1 = float(bitda_record[1]) if bitda_record[1] else 0
                bitda_ask1 = float(bitda_record[2]) if bitda_record[2] else 0
                bitda_bid2 = float(bitda_record[3]) if bitda_record[3] else 0
                bitda_ask2 = float(bitda_record[4]) if bitda_record[4] else 0
                bitda_bid3 = float(bitda_record[5]) if bitda_record[5] else 0
                bitda_ask3 = float(bitda_record[6]) if bitda_record[6] else 0
                bitda_bid4 = float(bitda_record[7]) if bitda_record[7] else 0
                bitda_ask4 = float(bitda_record[8]) if bitda_record[8] else 0
                bitda_bid5 = float(bitda_record[9]) if bitda_record[9] else 0
                bitda_ask5 = float(bitda_record[10]) if bitda_record[10] else 0

                # 解析Binance数据
                binance_bid1 = float(binance_record[0]) if binance_record[0] else 0
                binance_ask1 = float(binance_record[1]) if binance_record[1] else 0
                binance_bid2 = float(binance_record[2]) if binance_record[2] else 0
                binance_ask2 = float(binance_record[3]) if binance_record[3] else 0
                binance_bid3 = float(binance_record[4]) if binance_record[4] else 0
                binance_ask3 = float(binance_record[5]) if binance_record[5] else 0
                binance_bid4 = float(binance_record[6]) if binance_record[6] else 0
                binance_ask4 = float(binance_record[7]) if binance_record[7] else 0
                binance_bid5 = float(binance_record[8]) if binance_record[8] else 0
                binance_ask5 = float(binance_record[9]) if binance_record[9] else 0

                # 计算这一条记录的深度对比（就像深度对比面板一样）
                comparison = {}

                # 1. 买一量卖一量深度比
                bitda_bid_ask1 = bitda_bid1 + bitda_ask1
                binance_bid_ask1 = binance_bid1 + binance_ask1
                if binance_bid_ask1 > 0:
                    comparison['bid_ask1_ratio'] = bitda_bid_ask1 / binance_bid_ask1
                else:
                    comparison['bid_ask1_ratio'] = 0

                # 2. 买卖前两档量深度比
                bitda_bid_ask2 = bitda_bid1 + bitda_bid2 + bitda_ask1 + bitda_ask2
                binance_bid_ask2 = binance_bid1 + binance_bid2 + binance_ask1 + binance_ask2
                if binance_bid_ask2 > 0:
                    comparison['bid_ask2_ratio'] = bitda_bid_ask2 / binance_bid_ask2
                else:
                    comparison['bid_ask2_ratio'] = 0

                # 3. 买卖前五档量深度比
                bitda_bid_ask5 = (bitda_bid1 + bitda_bid2 + bitda_bid3 + bitda_bid4 + bitda_bid5 +
                                 bitda_ask1 + bitda_ask2 + bitda_ask3 + bitda_ask4 + bitda_ask5)
                binance_bid_ask5 = (binance_bid1 + binance_bid2 + binance_bid3 + binance_bid4 + binance_bid5 +
                                   binance_ask1 + binance_ask2 + binance_ask3 + binance_ask4 + binance_ask5)
                if binance_bid_ask5 > 0:
                    comparison['bid_ask5_ratio'] = bitda_bid_ask5 / binance_bid_ask5
                else:
                    comparison['bid_ask5_ratio'] = 0

                depth_comparisons.append(comparison)
                successful_matches += 1

            logger.info(f"   ✅ 成功匹配{successful_matches}条深度对比数据")

            if not depth_comparisons:
                logger.warning(f"   ❌ 无有效的{symbol}深度对比数据")
                cursor.close()
                connection.close()
                return None

            # 第三步：对所有深度比值进行统计
            bid_ask1_ratios = [comp['bid_ask1_ratio'] for comp in depth_comparisons if comp['bid_ask1_ratio'] > 0]
            bid_ask2_ratios = [comp['bid_ask2_ratio'] for comp in depth_comparisons if comp['bid_ask2_ratio'] > 0]
            bid_ask5_ratios = [comp['bid_ask5_ratio'] for comp in depth_comparisons if comp['bid_ask5_ratio'] > 0]

            def calc_stats(ratios):
                if not ratios:
                    return {'max': 0, 'min': 0, 'avg': 0}
                return {
                    'max': max(ratios),
                    'min': min(ratios),
                    'avg': sum(ratios) / len(ratios)
                }

            result = {
                'bid_ask1_max': calc_stats(bid_ask1_ratios)['max'],
                'bid_ask1_min': calc_stats(bid_ask1_ratios)['min'],
                'bid_ask1_avg': calc_stats(bid_ask1_ratios)['avg'],
                'bid_ask2_max': calc_stats(bid_ask2_ratios)['max'],
                'bid_ask2_min': calc_stats(bid_ask2_ratios)['min'],
                'bid_ask2_avg': calc_stats(bid_ask2_ratios)['avg'],
                'bid_ask5_max': calc_stats(bid_ask5_ratios)['max'],
                'bid_ask5_min': calc_stats(bid_ask5_ratios)['min'],
                'bid_ask5_avg': calc_stats(bid_ask5_ratios)['avg'],
                'sample_count': len(depth_comparisons)
            }

            logger.info(f"   🎯 {symbol}深度统计结果:")
            logger.info(f"      基于{len(bitda_records)}条Bitda数据，成功匹配{successful_matches}条")
            logger.info(f"      买一量卖一量深度比: 最大{result['bid_ask1_max']:.2f}, 最小{result['bid_ask1_min']:.2f}, 平均{result['bid_ask1_avg']:.2f}")
            logger.info(f"      买卖前两档量深度比: 最大{result['bid_ask2_max']:.2f}, 最小{result['bid_ask2_min']:.2f}, 平均{result['bid_ask2_avg']:.2f}")
            logger.info(f"      买卖前五档量深度比: 最大{result['bid_ask5_max']:.2f}, 最小{result['bid_ask5_min']:.2f}, 平均{result['bid_ask5_avg']:.2f}")

            cursor.close()
            connection.close()
            return result

        except Exception as e:
            logger.error(f"❌ 计算{symbol}深度比值统计失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def create_improved_dashboard(self):
        """创建改进的仪表板"""
        logger.info("🎨 创建改进的深度价差对比仪表板...")
        
        # 获取最新分钟统计数据
        data = self.get_latest_minute_stats()
        if not data:
            logger.error("❌ 无分钟统计数据，无法创建仪表板")
            return None
        
        # 获取时间信息
        latest_time = None
        for symbol_data in data.values():
            if symbol_data['latest']['bitda_timestamp']:
                from datetime import datetime
                latest_time = datetime.fromtimestamp(symbol_data['latest']['bitda_timestamp'] / 1000)
                break

        time_str = latest_time.strftime('%Y-%m-%d %H:%M:%S') if latest_time else "未知"
        
        dashboard_config = {
            "dashboard": {
                "id": None,
                "title": f"🔥 BTCUSDT vs ETHUSDT 深度价差对比分析 - {datetime.now().strftime('%m-%d %H:%M')}",
                "tags": ["depth", "spread", "comparison", "btc", "eth"],
                "timezone": "browser",
                "panels": [
                    # 标题面板
                    {
                        "id": 1,
                        "title": "",
                        "type": "text",
                        "gridPos": {"h": 3, "w": 24, "x": 0, "y": 0},
                        "targets": [],
                        "options": {
                            "mode": "html",
                            "content": f"""
                            <div style="
                                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                color: white;
                                height: 100%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                border-radius: 8px;
                                font-family: Arial, sans-serif;
                            ">
                                <div style="text-align: center;">
                                    <h1 style="margin: 0; font-size: 24px;">🔥 BTCUSDT vs ETHUSDT 深度价差对比分析</h1>
                                    <p style="margin: 8px 0 0 0; font-size: 14px; opacity: 0.9;">
                                        📊 实时深度对比 | 💰 价差分析 | ⏰ 数据时间: {time_str}
                                    </p>
                                </div>
                            </div>
                            """
                        }
                    }
                ],
                "time": {"from": "now-1h", "to": "now"},
                "timepicker": {},
                "templating": {"list": []},
                "annotations": {"list": []},
                "refresh": "1m",
                "schemaVersion": 30,
                "version": 1,
                "links": []
            },
            "overwrite": True
        }
        
        # 为每个币种创建面板
        panel_id = 2
        y_pos = 3
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            if symbol not in data:
                continue

            symbol_data = data[symbol]
            latest_data = symbol_data['latest']
            stats_data = symbol_data['stats']

            # 构建深度对比数据 (使用最新数据点)
            depth_data = {
                'bid1_ratio': latest_data['bitda_bid1_qty'] / latest_data['binance_bid1_qty'] if latest_data['binance_bid1_qty'] > 0 else 0,
                'ask1_ratio': latest_data['bitda_ask1_qty'] / latest_data['binance_ask1_qty'] if latest_data['binance_ask1_qty'] > 0 else 0,
                'bid_ask1_ratio': (latest_data['bitda_bid1_qty'] + latest_data['bitda_ask1_qty']) / (latest_data['binance_bid1_qty'] + latest_data['binance_ask1_qty']) if (latest_data['binance_bid1_qty'] + latest_data['binance_ask1_qty']) > 0 else 0,
                'bid_ask2_ratio': (latest_data['bitda_bid1_qty'] + latest_data['bitda_bid2_qty'] + latest_data['bitda_ask1_qty'] + latest_data['bitda_ask2_qty']) / (latest_data['binance_bid1_qty'] + latest_data['binance_bid2_qty'] + latest_data['binance_ask1_qty'] + latest_data['binance_ask2_qty']) if (latest_data['binance_bid1_qty'] + latest_data['binance_bid2_qty'] + latest_data['binance_ask1_qty'] + latest_data['binance_ask2_qty']) > 0 else 0,
                'bid_ask5_ratio': (latest_data['bitda_bid5_total'] + latest_data['bitda_ask5_total']) / (latest_data['binance_bid5_total'] + latest_data['binance_ask5_total']) if (latest_data['binance_bid5_total'] + latest_data['binance_ask5_total']) > 0 else 0,
                'bitda_bid1_qty': latest_data['bitda_bid1_qty'],
                'bitda_ask1_qty': latest_data['bitda_ask1_qty'],
                'bitda_bid2_qty': latest_data['bitda_bid2_qty'],
                'bitda_ask2_qty': latest_data['bitda_ask2_qty'],
                'bitda_bid5_total': latest_data['bitda_bid5_total'],
                'bitda_ask5_total': latest_data['bitda_ask5_total'],
                'binance_bid1_qty': latest_data['binance_bid1_qty'],
                'binance_ask1_qty': latest_data['binance_ask1_qty'],
                'binance_bid2_qty': latest_data['binance_bid2_qty'],
                'binance_ask2_qty': latest_data['binance_ask2_qty'],
                'binance_bid5_total': latest_data['binance_bid5_total'],
                'binance_ask5_total': latest_data['binance_ask5_total'],
                'time_diff_ms': latest_data['time_diff_ms']
            }

            # 构建深度统计数据 (使用真实的5分钟统计数据)
            bitda_stats = stats_data['bitda_stats']
            binance_stats = stats_data['binance_stats']

            # 计算真实的深度比值统计 (基于5分钟内的数据)
            # 这里需要从原始数据计算深度比值的统计，而不是重复单一比值

            # 获取5分钟内的深度比值统计
            depth_stats = self.calculate_depth_ratio_stats(symbol, stats_data)

            if not depth_stats:
                # 如果无法获取统计数据，使用当前比值作为备用
                depth_stats = {
                    'bid_ask1_max': depth_data['bid_ask1_ratio'],
                    'bid_ask1_min': depth_data['bid_ask1_ratio'],
                    'bid_ask1_avg': depth_data['bid_ask1_ratio'],
                    'bid_ask2_max': depth_data['bid_ask2_ratio'],
                    'bid_ask2_min': depth_data['bid_ask2_ratio'],
                    'bid_ask2_avg': depth_data['bid_ask2_ratio'],
                    'bid_ask5_max': depth_data['bid_ask5_ratio'],
                    'bid_ask5_min': depth_data['bid_ask5_ratio'],
                    'bid_ask5_avg': depth_data['bid_ask5_ratio'],
                    'sample_count': 1
                }

            # 构建价差对比数据
            spread_data = {
                'bitda_spread_latest': latest_data['bitda_spread'],
                'binance_spread_latest': latest_data['binance_spread'],
                'bitda_spread_max': bitda_stats['bid1_ask1_spread']['max'],
                'bitda_spread_min': bitda_stats['bid1_ask1_spread']['min'],
                'bitda_spread_avg': bitda_stats['bid1_ask1_spread']['avg'],
                'bitda_spread_median': bitda_stats['bid1_ask1_spread']['median'],
                'binance_spread_max': binance_stats['bid1_ask1_spread']['max'],
                'binance_spread_min': binance_stats['bid1_ask1_spread']['min'],
                'binance_spread_avg': binance_stats['bid1_ask1_spread']['avg'],
                'binance_spread_median': binance_stats['bid1_ask1_spread']['median'],
                'spread_diff': latest_data['bitda_spread'] - latest_data['binance_spread'],
                'bitda_sample_count': stats_data['bitda_sample_count'],
                'binance_sample_count': stats_data['binance_sample_count'],
                'latest_time_diff_ms': latest_data['time_diff_ms'],  # 最近价差的时间差
                'max_time_diff_ms': latest_data['time_diff_ms'],     # 最大价差的时间差 (简化处理)
                'min_time_diff_ms': latest_data['time_diff_ms']      # 最小价差的时间差 (简化处理)
            }
            
            # 深度对比表格面板 (左上)
            depth_table_content = f"""
            <div style="padding: 15px; background: #f8f9fa; border-radius: 8px; font-family: Arial, sans-serif; height: 100%;">
                <h3 style="text-align: center; color: #333; margin-bottom: 15px;">📊 {symbol} 深度对比</h3>
                
                <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 6px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1); font-size: 12px;">
                    <thead style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                        <tr>
                            <th style="padding: 10px 8px; text-align: left; border: none; font-size: 11px;">项目</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">Bitda</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">Binance</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">深度比</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">买一量</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px;">{depth_data['bitda_bid1_qty']:.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px;">{depth_data['binance_bid1_qty']:.2f}</td>
                            <td style="padding: 8px; text-align: right; color: {'#28a745' if depth_data['bid1_ratio'] > 1 else '#dc3545'}; font-weight: bold; font-size: 11px;">{depth_data['bid1_ratio']:.2f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee; background: #f8f9fa;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">卖一量</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px;">{depth_data['bitda_ask1_qty']:.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px;">{depth_data['binance_ask1_qty']:.2f}</td>
                            <td style="padding: 8px; text-align: right; color: {'#28a745' if depth_data['ask1_ratio'] > 1 else '#dc3545'}; font-weight: bold; font-size: 11px;">{depth_data['ask1_ratio']:.2f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">买一量卖一量</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px;">{depth_data['bitda_bid1_qty'] + depth_data['bitda_ask1_qty']:.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px;">{depth_data['binance_bid1_qty'] + depth_data['binance_ask1_qty']:.2f}</td>
                            <td style="padding: 8px; text-align: right; color: {'#28a745' if depth_data['bid_ask1_ratio'] > 1 else '#dc3545'}; font-weight: bold; font-size: 11px;">{depth_data['bid_ask1_ratio']:.2f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee; background: #f8f9fa;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">买卖前两档量</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px;">{depth_data['bitda_bid1_qty'] + depth_data['bitda_bid2_qty'] + depth_data['bitda_ask1_qty'] + depth_data['bitda_ask2_qty']:.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px;">{depth_data['binance_bid1_qty'] + depth_data['binance_bid2_qty'] + depth_data['binance_ask1_qty'] + depth_data['binance_ask2_qty']:.2f}</td>
                            <td style="padding: 8px; text-align: right; color: {'#28a745' if depth_data['bid_ask2_ratio'] > 1 else '#dc3545'}; font-weight: bold; font-size: 11px;">{depth_data['bid_ask2_ratio']:.2f}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">买卖前五档量</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px;">{depth_data['bitda_bid5_total'] + depth_data['bitda_ask5_total']:.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px;">{depth_data['binance_bid5_total'] + depth_data['binance_ask5_total']:.2f}</td>
                            <td style="padding: 8px; text-align: right; color: {'#28a745' if depth_data['bid_ask5_ratio'] > 1 else '#dc3545'}; font-weight: bold; font-size: 11px;">{depth_data['bid_ask5_ratio']:.2f}</td>
                        </tr>
                    </tbody>
                </table>
                
                <div style="margin-top: 10px; padding: 8px; background: #e3f2fd; border-radius: 4px; border-left: 3px solid #2196f3;">
                    <small style="font-size: 10px;"><strong>⏰ 时间差:</strong> {depth_data['time_diff_ms']}ms</small>
                </div>
            </div>
            """
            
            dashboard_config["dashboard"]["panels"].append({
                "id": panel_id,
                "title": "",
                "type": "text",
                "gridPos": {"h": 10, "w": 8, "x": 0, "y": y_pos},
                "targets": [],
                "options": {
                    "mode": "html",
                    "content": depth_table_content
                }
            })
            
            panel_id += 1

            # 深度统计面板 (右上)
            depth_stats_content = f"""
            <div style="padding: 15px; background: #f8f9fa; border-radius: 8px; font-family: Arial, sans-serif; height: 100%;">
                <h3 style="text-align: center; color: #333; margin-bottom: 15px;">📈 {symbol} 深度统计</h3>

                <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 6px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1); font-size: 12px;">
                    <thead style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
                        <tr>
                            <th style="padding: 10px 8px; text-align: left; border: none; font-size: 11px;">项目</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">最大值</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">最小值</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">平均值</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">买一量卖一量深度比</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #28a745;">{depth_stats['bid_ask1_max']:.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #dc3545;">{depth_stats['bid_ask1_min']:.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #6c757d;">{depth_stats['bid_ask1_avg']:.2f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee; background: #f8f9fa;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">买卖前两档量深度比</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #28a745;">{depth_stats['bid_ask2_max']:.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #dc3545;">{depth_stats['bid_ask2_min']:.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #6c757d;">{depth_stats['bid_ask2_avg']:.2f}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">买卖前五档量深度比</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #28a745;">{depth_stats['bid_ask5_max']:.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #dc3545;">{depth_stats['bid_ask5_min']:.2f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #6c757d;">{depth_stats['bid_ask5_avg']:.2f}</td>
                        </tr>
                    </tbody>
                </table>

                <div style="margin-top: 10px; padding: 8px; background: #d4edda; border-radius: 4px; border-left: 3px solid #28a745;">
                    <small style="font-size: 10px;"><strong>📊 统计说明:</strong> 基于最近5分钟内所有记录的深度比值统计</small><br>
                    <small style="font-size: 10px;"><strong>🎯 深度比:</strong> Bitda数量 / Binance数量，>1表示Bitda深度更好</small>
                </div>
            </div>
            """

            dashboard_config["dashboard"]["panels"].append({
                "id": panel_id,
                "title": "",
                "type": "text",
                "gridPos": {"h": 10, "w": 8, "x": 8, "y": y_pos},
                "targets": [],
                "options": {
                    "mode": "html",
                    "content": depth_stats_content
                }
            })

            panel_id += 1

            # 价差对比表格面板 (右侧) - 恢复双列对比显示
            spread_table_content = f"""
            <div style="padding: 15px; background: #f8f9fa; border-radius: 8px; font-family: Arial, sans-serif; height: 100%;">
                <h3 style="text-align: center; color: #333; margin-bottom: 15px;">💰 {symbol} 价差对比</h3>

                <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 6px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1); font-size: 12px;">
                    <thead style="background: linear-gradient(135deg, #ff7f0e 0%, #ff4757 100%); color: white;">
                        <tr>
                            <th style="padding: 10px 8px; text-align: left; border: none; font-size: 11px;">项目</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">Bitda</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">Binance</th>
                            <th style="padding: 10px 8px; text-align: right; border: none; font-size: 11px;">时间差</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">最近价差</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #2196f3; font-weight: bold;">{spread_data['bitda_spread_latest']:.4f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #2196f3; font-weight: bold;">{spread_data['binance_spread_latest']:.4f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #856404; font-weight: bold;">{spread_data['latest_time_diff_ms']}ms</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee; background: #f8f9fa;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">最大价差</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #f44336;">{spread_data['bitda_spread_max']:.4f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #f44336;">{spread_data['binance_spread_max']:.4f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #856404; font-weight: bold;">{spread_data['max_time_diff_ms']}ms</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">最小价差</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #4caf50;">{spread_data['bitda_spread_min']:.4f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #4caf50;">{spread_data['binance_spread_min']:.4f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #856404; font-weight: bold;">{spread_data['min_time_diff_ms']}ms</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee; background: #f8f9fa;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">平均价差</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #9c27b0;">{spread_data['bitda_spread_avg']:.4f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #9c27b0;">{spread_data['binance_spread_avg']:.4f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #6c757d;">/</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 8px; font-weight: bold; font-size: 11px;">价差中位数</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #607d8b;">{spread_data['bitda_spread_median']:.4f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #607d8b;">{spread_data['binance_spread_median']:.4f}</td>
                            <td style="padding: 8px; text-align: right; font-size: 11px; color: #6c757d;">/</td>
                        </tr>
                    </tbody>
                </table>

                <!-- 价差对比结果 -->
                <div style="margin-top: 15px; padding: 10px; background: {'#d4edda' if spread_data['spread_diff'] <= 0 else '#f8d7da'}; border-radius: 6px; border-left: 4px solid {'#28a745' if spread_data['spread_diff'] <= 0 else '#dc3545'};">
                    <div style="text-align: center;">
                        <strong style="font-size: 11px;">💰 价差差值 (Bitda - Binance)</strong><br>
                        <span style="font-size: 16px; font-weight: bold; color: {'#28a745' if spread_data['spread_diff'] <= 0 else '#dc3545'};">{spread_data['spread_diff']:.4f}</span><br>
                        <small style="font-size: 9px; opacity: 0.8;">{'Bitda价差更小，流动性更好' if spread_data['spread_diff'] <= 0 else 'Binance价差更小，流动性更好'}</small>
                    </div>
                </div>

                <div style="margin-top: 10px; padding: 8px; background: #e3f2fd; border-radius: 4px; border-left: 3px solid #2196f3;">
                    <small style="font-size: 10px;"><strong>📊 统计说明:</strong> Bitda {spread_data['bitda_sample_count']}条样本 | Binance {spread_data['binance_sample_count']}条样本</small><br>
                    <small style="font-size: 10px;"><strong>📈 更新频率:</strong> Binance是Bitda的 {(spread_data['binance_sample_count']/spread_data['bitda_sample_count'] if spread_data['bitda_sample_count'] > 0 else 0):.1f} 倍</small>
                </div>
            </div>
            """

            dashboard_config["dashboard"]["panels"].append({
                "id": panel_id,
                "title": "",
                "type": "text",
                "gridPos": {"h": 10, "w": 8, "x": 16, "y": y_pos},
                "targets": [],
                "options": {
                    "mode": "html",
                    "content": spread_table_content
                }
            })

            panel_id += 1
            y_pos += 10

        # 创建仪表板
        try:
            response = self.session.post(
                f"{self.grafana_url}/api/dashboards/db",
                json=dashboard_config,
                headers={'Content-Type': 'application/json'}
            )

            if response.status_code == 200:
                result = response.json()
                dashboard_url = f"{self.grafana_url}{result.get('url', '')}"
                logger.info(f"✅ 仪表板创建成功")
                logger.info(f"🌐 访问地址: {dashboard_url}")
                return dashboard_url
            else:
                logger.error(f"❌ 仪表板创建失败: {response.status_code} - {response.text}")

        except Exception as e:
            logger.error(f"❌ 仪表板创建异常: {e}")

        return None

def main():
    """主函数"""
    print("🎨 改进的深度价差对比分析仪表板 (优化版)")
    print("=" * 60)
    print("重大改进:")
    print("  ❌ 原逻辑: 随机取5条最新数据")
    print("  ✅ 新逻辑: 按分钟统计该分钟内所有数据")
    print("  ❌ 原逻辑: 包含买五卖五价差对比")
    print("  ✅ 新逻辑: 取消买五卖五价差，专注有意义指标")
    print("  ✅ 新功能: 价差对比表格添加比值列")
    print("  ✅ 新功能: 显示数据更新频率统计")
    print("  ✅ 新功能: 基于真实分钟级数据统计")
    print()

    creator = ImprovedDepthSpreadDashboard()
    dashboard_url = creator.create_improved_dashboard()

    if dashboard_url:
        print("✅ 优化仪表板创建成功!")
        print("🎯 核心改进:")
        print("  ✅ 正确的按分钟统计逻辑")
        print("  ✅ 取消无意义的买五卖五价差对比")
        print("  ✅ 价差比值列 (Bitda/Binance)")
        print("  ✅ 数据更新频率对比")
        print("  ✅ 基于真实数据样本的统计")
        print("  ✅ 更紧凑的四列布局")

        # 自动打开浏览器
        try:
            import webbrowser
            webbrowser.open(dashboard_url)
            print("🌐 浏览器已自动打开")
        except:
            print(f"🌐 请手动访问: {dashboard_url}")
    else:
        print("❌ 仪表板创建失败")

if __name__ == "__main__":
    main()
