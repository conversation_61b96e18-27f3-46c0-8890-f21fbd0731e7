#!/usr/bin/env python3
"""
数据分析器主程序
整合所有分析功能，提供命令行界面
"""

import asyncio
import argparse
import sys
from datetime import datetime
from visualizer.dashboard import Dashboard
from utils.logging import setup_logger

logger = setup_logger(__name__)

class AnalyzerMain:
    """分析器主程序"""
    
    def __init__(self):
        self.dashboard = Dashboard()
        
    async def run_single_analysis(self, hours: int = 1, generate_charts: bool = True):
        """
        运行单次分析
        
        Args:
            hours: 分析时间范围（小时）
            generate_charts: 是否生成图表
        """
        logger.info(f"开始执行{hours}小时数据分析...")
        
        try:
            # 生成综合报告
            report = await self.dashboard.generate_comprehensive_report(hours)
            
            # 保存报告
            report_file = await self.dashboard.save_report_to_file(report)
            
            # 打印执行摘要
            self._print_executive_summary(report.get('executive_summary', {}))
            
            # 生成图表
            if generate_charts:
                logger.info("正在生成可视化图表...")
                chart_files = await self.dashboard.generate_visual_report(hours)
                
                if chart_files:
                    print("\n📊 生成的图表文件:")
                    for chart_type, filename in chart_files.items():
                        if filename:
                            print(f"  - {chart_type}: {filename}")
                else:
                    print("⚠️  图表生成失败")
            
            print(f"\n📄 详细报告已保存: {report_file}")
            
        except Exception as e:
            logger.error(f"分析执行失败: {e}")
            print(f"❌ 分析失败: {e}")
    
    def _print_executive_summary(self, summary: dict):
        """打印执行摘要"""
        print("\n" + "="*60)
        print("📋 执行摘要")
        print("="*60)
        
        # 整体状态
        status = summary.get('overall_status', 'unknown')
        status_emoji = {
            'normal': '✅',
            'warning': '⚠️',
            'error': '❌',
            'unknown': '❓'
        }
        
        print(f"整体状态: {status_emoji.get(status, '❓')} {status.upper()}")
        
        # 关键发现
        key_findings = summary.get('key_findings', [])
        if key_findings:
            print(f"\n🔍 关键发现 ({len(key_findings)}项):")
            for i, finding in enumerate(key_findings, 1):
                print(f"  {i}. {finding}")
        
        # 警告信息
        alerts = summary.get('alerts', [])
        if alerts:
            print(f"\n⚠️  警告信息 ({len(alerts)}项):")
            for i, alert in enumerate(alerts, 1):
                print(f"  {i}. {alert}")
        
        # 建议
        recommendations = summary.get('recommendations', [])
        if recommendations:
            print(f"\n💡 建议 ({len(recommendations)}项):")
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec}")
        
        print("="*60)
    
    async def run_monitoring(self, interval: int = 15):
        """
        运行持续监控
        
        Args:
            interval: 监控间隔（分钟）
        """
        print(f"🔄 开始持续监控，间隔: {interval}分钟")
        print("按 Ctrl+C 停止监控")
        
        try:
            await self.dashboard.run_realtime_monitoring(interval)
        except KeyboardInterrupt:
            print("\n⏹️  监控已停止")
        except Exception as e:
            logger.error(f"监控失败: {e}")
            print(f"❌ 监控失败: {e}")
    
    async def run_specific_analysis(self, analysis_type: str, hours: int = 1):
        """
        运行特定类型的分析
        
        Args:
            analysis_type: 分析类型
            hours: 分析时间范围
        """
        logger.info(f"开始执行{analysis_type}分析...")
        
        try:
            if analysis_type == 'latency':
                result = await self.dashboard.latency_analyzer.analyze_price_latency(hours)
                self._print_latency_result(result)
                
            elif analysis_type == 'kline':
                result = await self.dashboard.kline_analyzer.analyze_consecutive_identical_klines(hours)
                self._print_kline_result(result)
                
            elif analysis_type == 'depth':
                result = await self.dashboard.depth_analyzer.analyze_depth_comparison(hours)
                self._print_depth_result(result)
                
            elif analysis_type == 'price':
                result = await self.dashboard.price_analyzer.analyze_mark_price_difference(hours)
                self._print_price_result(result)
                
            elif analysis_type == 'funding':
                result = await self.dashboard.funding_analyzer.analyze_funding_rates(days=1)
                self._print_funding_result(result)
                
            else:
                print(f"❌ 不支持的分析类型: {analysis_type}")
                print("支持的类型: latency, kline, depth, price, funding")
                
        except Exception as e:
            logger.error(f"{analysis_type}分析失败: {e}")
            print(f"❌ {analysis_type}分析失败: {e}")
    
    def _print_latency_result(self, result: dict):
        """打印延时分析结果"""
        print("\n🚀 ETHUSDT 延时分析结果:")
        eth_data = result.get('results', {}).get('ETHUSDT', {})
        
        if eth_data.get('total_matches', 0) > 0:
            print(f"  匹配次数: {eth_data['total_matches']}")
            print(f"  平均延时: {eth_data['avg_latency_ms']:.2f} ms")
            print(f"  最小延时: {eth_data['min_latency_ms']:.2f} ms")
            print(f"  最大延时: {eth_data['max_latency_ms']:.2f} ms")
            print(f"  平均价格差异: {eth_data['avg_price_diff_pct']:.4f}%")
        else:
            print("  暂无匹配数据")
    
    def _print_kline_result(self, result: dict):
        """打印K线分析结果"""
        print("\n📈 K线连续相同分析结果:")
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            symbol_data = result.get('results', {}).get(symbol, {})
            print(f"\n  {symbol}:")
            print(f"    连续序列数: {symbol_data.get('total_sequences', 0)}")
            print(f"    相同K线数: {symbol_data.get('total_identical_klines', 0)}")
            print(f"    相同比例: {symbol_data.get('identical_rate_pct', 0):.2f}%")
            print(f"    最大序列长度: {symbol_data.get('max_sequence_length', 0)}")
    
    def _print_depth_result(self, result: dict):
        """打印深度分析结果"""
        print("\n📊 深度对比分析结果:")
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            symbol_data = result.get('results', {}).get(symbol, {})
            print(f"\n  {symbol}:")
            print(f"    对比次数: {symbol_data.get('total_comparisons', 0)}")
            print(f"    平均深度比值: {symbol_data.get('avg_depth_ratio', 0):.4f}")
            print(f"    Bitda优势比例: {symbol_data.get('bitda_advantage_pct', 0):.2f}%")
            print(f"    平均时间差: {symbol_data.get('avg_time_diff_ms', 0):.2f} ms")
    
    def _print_price_result(self, result: dict):
        """打印价格分析结果"""
        print("\n💰 标记价格差值分析结果:")
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            symbol_data = result.get('results', {}).get(symbol, {})
            print(f"\n  {symbol}:")
            print(f"    数据记录数: {symbol_data.get('total_records', 0)}")
            print(f"    平均相对差值: {symbol_data.get('avg_rel_diff_pct', 0):.4f}%")
            print(f"    最大相对差值: {symbol_data.get('max_rel_diff_pct', 0):.4f}%")
            print(f"    异常差值比例: {symbol_data.get('high_diff_rate_pct', 0):.2f}%")
            print(f"    趋势方向: {symbol_data.get('trend_direction', 'unknown')}")
    
    def _print_funding_result(self, result: dict):
        """打印资金费率分析结果"""
        print("\n📋 资金费率分析结果:")
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            symbol_data = result.get('results', {}).get(symbol, {})
            print(f"\n  {symbol}:")
            print(f"    当前费率: {symbol_data.get('current_funding_rate', 0)*100:.4f}%")
            print(f"    平均费率: {symbol_data.get('avg_funding_rate', 0)*100:.4f}%")
            print(f"    费率波动性: {symbol_data.get('funding_rate_volatility', 0)*100:.4f}%")
            print(f"    趋势方向: {symbol_data.get('trend_direction', 'unknown')}")
            print(f"    趋势强度: {symbol_data.get('trend_strength_pct', 0):.2f}%")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='加密货币数据分析器')
    parser.add_argument('--mode', choices=['single', 'monitor', 'specific'], 
                       default='single', help='运行模式')
    parser.add_argument('--hours', type=int, default=1, 
                       help='分析时间范围（小时）')
    parser.add_argument('--interval', type=int, default=15, 
                       help='监控间隔（分钟）')
    parser.add_argument('--type', choices=['latency', 'kline', 'depth', 'price', 'funding'],
                       help='特定分析类型')
    parser.add_argument('--no-charts', action='store_true', 
                       help='不生成图表')
    
    args = parser.parse_args()
    
    analyzer = AnalyzerMain()
    
    try:
        if args.mode == 'single':
            asyncio.run(analyzer.run_single_analysis(
                hours=args.hours, 
                generate_charts=not args.no_charts
            ))
            
        elif args.mode == 'monitor':
            asyncio.run(analyzer.run_monitoring(args.interval))
            
        elif args.mode == 'specific':
            if not args.type:
                print("❌ 特定分析模式需要指定 --type 参数")
                sys.exit(1)
            asyncio.run(analyzer.run_specific_analysis(args.type, args.hours))
            
    except KeyboardInterrupt:
        print("\n⏹️  程序已停止")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    main()
