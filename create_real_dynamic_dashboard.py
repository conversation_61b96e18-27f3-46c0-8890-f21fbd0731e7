#!/usr/bin/env python3
"""
创建真正动态的Grafana仪表板
使用MySQL数据源和SQL查询实现真正的自动更新
"""

import requests
import json
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealDynamicDashboard:
    """真正动态的仪表板创建器"""
    
    def __init__(self):
        self.grafana_url = "http://localhost:3000"
        self.admin_user = "admin"
        self.admin_pass = "admin"
        self.session = requests.Session()
        self.session.auth = (self.admin_user, self.admin_pass)
        self.datasource_uid = None
    
    def create_mysql_datasource(self):
        """创建MySQL数据源"""
        print("📊 创建MySQL数据源...")
        
        datasource_config = {
            "name": "DepthDB_Dynamic",
            "type": "mysql",
            "url": "localhost:3306",
            "access": "proxy",
            "database": "depth_db",
            "user": "root",
            "password": "Linuxtest",
            "basicAuth": False,
            "isDefault": False,
            "jsonData": {
                "maxOpenConns": 100,
                "maxIdleConns": 10,
                "connMaxLifetime": 14400
            }
        }
        
        try:
            # 先删除可能存在的同名数据源
            try:
                response = self.session.get(f"{self.grafana_url}/api/datasources/name/DepthDB_Dynamic")
                if response.status_code == 200:
                    existing_ds = response.json()
                    delete_response = self.session.delete(f"{self.grafana_url}/api/datasources/{existing_ds['id']}")
                    print("   🗑️ 删除已存在的数据源")
            except:
                pass
            
            # 创建新数据源
            response = self.session.post(
                f"{self.grafana_url}/api/datasources",
                json=datasource_config,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                self.datasource_uid = result.get('uid') or result.get('datasource', {}).get('uid')
                print(f"   ✅ MySQL数据源创建成功，UID: {self.datasource_uid}")
                print(f"   📊 完整响应: {result}")
                return True
            else:
                print(f"   ❌ 创建数据源失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return False
                
        except Exception as e:
            print(f"   ❌ 创建数据源异常: {e}")
            return False
    
    def create_dynamic_dashboard(self):
        """创建真正动态的仪表板"""
        print("🎨 创建真正动态的仪表板...")
        
        if not self.datasource_uid:
            print("   ❌ 数据源UID未设置")
            return None
        
        current_time = datetime.now()
        
        dashboard_config = {
            "dashboard": {
                "id": None,
                "title": f"🔄 真正动态深度分析 - {current_time.strftime('%H:%M:%S')}",
                "tags": ["dynamic", "real-time", "depth"],
                "timezone": "browser",
                "panels": [
                    # BTCUSDT深度对比
                    {
                        "id": 1,
                        "title": "📊 BTCUSDT 深度对比",
                        "type": "table",
                        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
                        "targets": [
                            {
                                "datasource": {
                                    "type": "mysql",
                                    "uid": self.datasource_uid
                                },
                                "format": "table",
                                "rawSql": """
SELECT 
    '买一量' as 项目,
    ROUND(b.bid_qty_1, 2) as Bitda,
    ROUND(bn.bid_qty_1, 2) as Binance,
    ROUND(b.bid_qty_1 / bn.bid_qty_1, 2) as 深度比
FROM bitda_depth b
JOIN (
    SELECT timestamp as bt FROM bitda_depth 
    WHERE symbol = 'BTCUSDT' AND bid_price_1 IS NOT NULL 
    ORDER BY timestamp DESC LIMIT 1
) latest ON b.timestamp = latest.bt
JOIN binance_depth_5 bn ON bn.symbol = 'BTCUSDT' 
    AND bn.event_time <= b.timestamp
    AND bn.bid_price_1 IS NOT NULL
WHERE b.symbol = 'BTCUSDT'
ORDER BY bn.event_time DESC
LIMIT 1

UNION ALL

SELECT 
    '卖一量' as 项目,
    ROUND(b.ask_qty_1, 2) as Bitda,
    ROUND(bn.ask_qty_1, 2) as Binance,
    ROUND(b.ask_qty_1 / bn.ask_qty_1, 2) as 深度比
FROM bitda_depth b
JOIN (
    SELECT timestamp as bt FROM bitda_depth 
    WHERE symbol = 'BTCUSDT' AND bid_price_1 IS NOT NULL 
    ORDER BY timestamp DESC LIMIT 1
) latest ON b.timestamp = latest.bt
JOIN binance_depth_5 bn ON bn.symbol = 'BTCUSDT' 
    AND bn.event_time <= b.timestamp
    AND bn.bid_price_1 IS NOT NULL
WHERE b.symbol = 'BTCUSDT'
ORDER BY bn.event_time DESC
LIMIT 1

UNION ALL

SELECT 
    '买一量+卖一量' as 项目,
    ROUND(b.bid_qty_1 + b.ask_qty_1, 2) as Bitda,
    ROUND(bn.bid_qty_1 + bn.ask_qty_1, 2) as Binance,
    ROUND((b.bid_qty_1 + b.ask_qty_1) / (bn.bid_qty_1 + bn.ask_qty_1), 2) as 深度比
FROM bitda_depth b
JOIN (
    SELECT timestamp as bt FROM bitda_depth 
    WHERE symbol = 'BTCUSDT' AND bid_price_1 IS NOT NULL 
    ORDER BY timestamp DESC LIMIT 1
) latest ON b.timestamp = latest.bt
JOIN binance_depth_5 bn ON bn.symbol = 'BTCUSDT' 
    AND bn.event_time <= b.timestamp
    AND bn.bid_price_1 IS NOT NULL
WHERE b.symbol = 'BTCUSDT'
ORDER BY bn.event_time DESC
LIMIT 1
                                """,
                                "refId": "A"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "custom": {
                                    "align": "center",
                                    "displayMode": "basic"
                                }
                            }
                        }
                    },
                    
                    # ETHUSDT深度对比
                    {
                        "id": 2,
                        "title": "📊 ETHUSDT 深度对比",
                        "type": "table",
                        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
                        "targets": [
                            {
                                "datasource": {
                                    "type": "mysql",
                                    "uid": self.datasource_uid
                                },
                                "format": "table",
                                "rawSql": """
SELECT 
    '买一量' as 项目,
    ROUND(b.bid_qty_1, 2) as Bitda,
    ROUND(bn.bid_qty_1, 2) as Binance,
    ROUND(b.bid_qty_1 / bn.bid_qty_1, 2) as 深度比
FROM bitda_depth b
JOIN (
    SELECT timestamp as bt FROM bitda_depth 
    WHERE symbol = 'ETHUSDT' AND bid_price_1 IS NOT NULL 
    ORDER BY timestamp DESC LIMIT 1
) latest ON b.timestamp = latest.bt
JOIN binance_depth_5 bn ON bn.symbol = 'ETHUSDT' 
    AND bn.event_time <= b.timestamp
    AND bn.bid_price_1 IS NOT NULL
WHERE b.symbol = 'ETHUSDT'
ORDER BY bn.event_time DESC
LIMIT 1

UNION ALL

SELECT 
    '卖一量' as 项目,
    ROUND(b.ask_qty_1, 2) as Bitda,
    ROUND(bn.ask_qty_1, 2) as Binance,
    ROUND(b.ask_qty_1 / bn.ask_qty_1, 2) as 深度比
FROM bitda_depth b
JOIN (
    SELECT timestamp as bt FROM bitda_depth 
    WHERE symbol = 'ETHUSDT' AND bid_price_1 IS NOT NULL 
    ORDER BY timestamp DESC LIMIT 1
) latest ON b.timestamp = latest.bt
JOIN binance_depth_5 bn ON bn.symbol = 'ETHUSDT' 
    AND bn.event_time <= b.timestamp
    AND bn.bid_price_1 IS NOT NULL
WHERE b.symbol = 'ETHUSDT'
ORDER BY bn.event_time DESC
LIMIT 1

UNION ALL

SELECT 
    '买一量+卖一量' as 项目,
    ROUND(b.bid_qty_1 + b.ask_qty_1, 2) as Bitda,
    ROUND(bn.bid_qty_1 + bn.ask_qty_1, 2) as Binance,
    ROUND((b.bid_qty_1 + b.ask_qty_1) / (bn.bid_qty_1 + bn.ask_qty_1), 2) as 深度比
FROM bitda_depth b
JOIN (
    SELECT timestamp as bt FROM bitda_depth 
    WHERE symbol = 'ETHUSDT' AND bid_price_1 IS NOT NULL 
    ORDER BY timestamp DESC LIMIT 1
) latest ON b.timestamp = latest.bt
JOIN binance_depth_5 bn ON bn.symbol = 'ETHUSDT' 
    AND bn.event_time <= b.timestamp
    AND bn.bid_price_1 IS NOT NULL
WHERE b.symbol = 'ETHUSDT'
ORDER BY bn.event_time DESC
LIMIT 1
                                """,
                                "refId": "A"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "custom": {
                                    "align": "center",
                                    "displayMode": "basic"
                                }
                            }
                        }
                    },
                    
                    # 数据更新时间
                    {
                        "id": 3,
                        "title": "⏰ 最新数据时间",
                        "type": "stat",
                        "gridPos": {"h": 4, "w": 24, "x": 0, "y": 8},
                        "targets": [
                            {
                                "datasource": {
                                    "type": "mysql",
                                    "uid": self.datasource_uid
                                },
                                "format": "table",
                                "rawSql": """
SELECT 
    FROM_UNIXTIME(MAX(timestamp)/1000, '%Y-%m-%d %H:%i:%s') as 最新数据时间
FROM bitda_depth 
WHERE symbol IN ('BTCUSDT', 'ETHUSDT')
                                """,
                                "refId": "A"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {
                                    "mode": "thresholds"
                                },
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None}
                                    ]
                                }
                            }
                        },
                        "options": {
                            "reduceOptions": {
                                "values": False,
                                "calcs": ["lastNotNull"],
                                "fields": ""
                            },
                            "orientation": "auto",
                            "textMode": "auto",
                            "colorMode": "background",
                            "graphMode": "none",
                            "justifyMode": "center"
                        }
                    }
                ],
                "time": {"from": "now-1h", "to": "now"},
                "timepicker": {},
                "templating": {"list": []},
                "annotations": {"list": []},
                "refresh": "10s",  # 10秒自动刷新
                "schemaVersion": 30,
                "version": 1,
                "links": []
            },
            "overwrite": True
        }
        
        try:
            response = self.session.post(
                f"{self.grafana_url}/api/dashboards/db",
                json=dashboard_config,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                dashboard_url = f"{self.grafana_url}{result['url']}"
                print(f"   ✅ 动态仪表板创建成功")
                print(f"   🌐 访问地址: {dashboard_url}")
                return dashboard_url
            else:
                print(f"   ❌ 创建仪表板失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return None
                
        except Exception as e:
            print(f"   ❌ 创建仪表板异常: {e}")
            return None

def main():
    """主函数"""
    print("🔄 创建真正动态的Grafana仪表板")
    print("=" * 60)
    print("🎯 特性:")
    print("   ✅ 使用MySQL数据源")
    print("   ✅ SQL查询获取实时数据")
    print("   ✅ 10秒自动刷新")
    print("   ✅ 真正的动态更新")
    print()
    
    dashboard = RealDynamicDashboard()
    
    # 1. 创建数据源
    if not dashboard.create_mysql_datasource():
        print("❌ 数据源创建失败，无法继续")
        return
    
    # 2. 创建动态仪表板
    dashboard_url = dashboard.create_dynamic_dashboard()
    
    if dashboard_url:
        print(f"\n🎉 真正动态仪表板创建成功！")
        print(f"🌐 访问地址: {dashboard_url}")
        print(f"⏰ 自动刷新: 每10秒")
        print(f"📊 数据源: MySQL直接查询")
        print(f"🔄 真正动态: 数据会自动更新")
        
        print(f"\n💡 验证方法:")
        print(f"   1. 打开仪表板页面")
        print(f"   2. 观察右上角的刷新指示器")
        print(f"   3. 每10秒数据会自动更新")
        print(f"   4. 观察数值和时间的变化")
        
        # 打开浏览器
        try:
            import webbrowser
            webbrowser.open(dashboard_url)
            print(f"\n🌐 已自动打开浏览器")
        except:
            print(f"\n💡 请手动打开浏览器访问上述地址")
    else:
        print(f"\n❌ 动态仪表板创建失败")

if __name__ == "__main__":
    main()
