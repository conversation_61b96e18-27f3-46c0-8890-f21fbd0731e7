#!/usr/bin/env python3
"""
深度价差对比分析演示服务
每30秒运行一次分析 (演示用)
"""

import time
import threading
import logging
from datetime import datetime
import subprocess

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DemoService:
    """演示服务"""
    
    def __init__(self):
        self.running = False
        
    def start_service(self):
        """启动演示服务"""
        logger.info("🚀 启动深度价差对比分析演示服务...")
        logger.info("📋 演示配置:")
        logger.info("   📊 分析币种: BTCUSDT, ETHUSDT")
        logger.info("   ⏰ 分析频率: 每30秒")
        logger.info("   🎯 演示目的: 展示分析和仪表板更新")
        logger.info("   🔄 按Ctrl+C停止服务")
        
        self.running = True
        
        try:
            while self.running:
                logger.info("🔍 开始新一轮分析...")
                
                # 运行快速分析
                try:
                    result = subprocess.run(
                        ['python3', 'quick_analysis_test.py'],
                        capture_output=True,
                        text=True,
                        timeout=20
                    )
                    
                    if result.returncode == 0:
                        logger.info("   ✅ 分析完成")
                        
                        # 更新仪表板
                        logger.info("🎨 更新仪表板...")
                        dashboard_result = subprocess.run(
                            ['python3', 'depth_spread_dashboard.py'],
                            capture_output=True,
                            text=True,
                            timeout=15
                        )
                        
                        if dashboard_result.returncode == 0:
                            logger.info("   ✅ 仪表板更新完成")
                        else:
                            logger.warning("   ⚠️ 仪表板更新失败")
                    else:
                        logger.error("   ❌ 分析失败")
                        
                except subprocess.TimeoutExpired:
                    logger.error("   ❌ 分析超时")
                except Exception as e:
                    logger.error(f"   ❌ 分析异常: {e}")
                
                # 等待30秒
                logger.info("⏰ 等待30秒后进行下一轮分析...")
                for i in range(30):
                    if not self.running:
                        break
                    time.sleep(1)
                    
        except KeyboardInterrupt:
            logger.info("📋 收到停止信号...")
            self.stop_service()
    
    def stop_service(self):
        """停止服务"""
        logger.info("🛑 停止演示服务...")
        self.running = False

def main():
    """主函数"""
    print("🚀 深度价差对比分析演示服务")
    print("=" * 50)
    print("功能:")
    print("  - 每30秒自动分析BTCUSDT和ETHUSDT")
    print("  - 自动更新Grafana仪表板")
    print("  - 展示深度对比和价差对比")
    print("  - 演示完整的分析流程")
    print("  - 按Ctrl+C停止服务")
    print()
    
    service = DemoService()
    
    try:
        service.start_service()
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {e}")
    finally:
        logger.info("🔚 演示服务已停止")

if __name__ == "__main__":
    main()
