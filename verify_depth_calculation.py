#!/usr/bin/env python3
"""
验证BTCUSDT深度统计计算过程
专门验证最大值和最小值的原始数据、时间点和计算方式
"""

import mysql.connector
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def verify_btcusdt_depth_calculation():
    """验证BTCUSDT深度统计的计算过程"""
    print("🔍 验证BTCUSDT深度统计计算过程")
    print("=" * 80)
    print("🎯 验证目标:")
    print("   1. 找到买一量卖一量深度比的最大值(28.98)和最小值(0.32)的原始数据")
    print("   2. 显示具体的时间点和数据库表值")
    print("   3. 验证计算公式的正确性")
    print("   4. 完整重现计算过程")
    print()
    
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'Linuxtest',
        'database': 'depth_db'
    }
    
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        # 计算前一分钟的时间戳范围（与仪表板逻辑一致）
        now = datetime.now()
        one_minute_ago = now - timedelta(minutes=1)
        start_timestamp = int(one_minute_ago.timestamp() * 1000)
        end_timestamp = int(now.timestamp() * 1000)
        
        print(f"📅 时间范围: {one_minute_ago.strftime('%Y-%m-%d %H:%M:%S')} - {now.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📅 时间戳范围: {start_timestamp} - {end_timestamp}")
        print()
        
        # 第一步：获取前一分钟内所有BTCUSDT Bitda数据
        print("📊 第一步：获取前一分钟内所有BTCUSDT Bitda数据...")
        cursor.execute("""
            SELECT timestamp, bid_qty_1, ask_qty_1, bid_qty_2, ask_qty_2,
                   bid_qty_3, ask_qty_3, bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5
            FROM bitda_depth
            WHERE symbol = 'BTCUSDT' AND timestamp BETWEEN %s AND %s
            AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
            ORDER BY timestamp ASC
        """, (start_timestamp, end_timestamp))
        
        bitda_records = cursor.fetchall()
        print(f"   ✅ 获取到{len(bitda_records)}条Bitda数据")
        print()
        
        if not bitda_records:
            print("❌ 无Bitda数据，无法验证")
            return
        
        # 第二步：为每条Bitda数据计算深度对比
        print("📊 第二步：为每条Bitda数据计算深度对比...")
        depth_comparisons = []
        
        for i, bitda_record in enumerate(bitda_records):
            bitda_timestamp = bitda_record[0]
            bitda_time = datetime.fromtimestamp(bitda_timestamp / 1000)
            
            # 获取该Bitda时间点之前最近的Binance数据
            cursor.execute("""
                SELECT event_time, bid_qty_1, ask_qty_1, bid_qty_2, ask_qty_2,
                       bid_qty_3, ask_qty_3, bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5
                FROM binance_depth_5
                WHERE symbol = 'BTCUSDT' AND event_time <= %s
                AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
                ORDER BY event_time DESC
                LIMIT 1
            """, (bitda_timestamp,))
            
            binance_record = cursor.fetchone()
            if not binance_record:
                continue
            
            binance_timestamp = binance_record[0]
            binance_time = datetime.fromtimestamp(binance_timestamp / 1000)
            time_diff = bitda_timestamp - binance_timestamp
            
            # 解析Bitda数据
            bitda_bid1 = float(bitda_record[1]) if bitda_record[1] else 0
            bitda_ask1 = float(bitda_record[2]) if bitda_record[2] else 0
            bitda_bid2 = float(bitda_record[3]) if bitda_record[3] else 0
            bitda_ask2 = float(bitda_record[4]) if bitda_record[4] else 0
            bitda_bid3 = float(bitda_record[5]) if bitda_record[5] else 0
            bitda_ask3 = float(bitda_record[6]) if bitda_record[6] else 0
            bitda_bid4 = float(bitda_record[7]) if bitda_record[7] else 0
            bitda_ask4 = float(bitda_record[8]) if bitda_record[8] else 0
            bitda_bid5 = float(bitda_record[9]) if bitda_record[9] else 0
            bitda_ask5 = float(bitda_record[10]) if bitda_record[10] else 0
            
            # 解析Binance数据
            binance_bid1 = float(binance_record[1]) if binance_record[1] else 0
            binance_ask1 = float(binance_record[2]) if binance_record[2] else 0
            binance_bid2 = float(binance_record[3]) if binance_record[3] else 0
            binance_ask2 = float(binance_record[4]) if binance_record[4] else 0
            binance_bid3 = float(binance_record[5]) if binance_record[5] else 0
            binance_ask3 = float(binance_record[6]) if binance_record[6] else 0
            binance_bid4 = float(binance_record[7]) if binance_record[7] else 0
            binance_ask4 = float(binance_record[8]) if binance_record[8] else 0
            binance_bid5 = float(binance_record[9]) if binance_record[9] else 0
            binance_ask5 = float(binance_record[10]) if binance_record[10] else 0
            
            # 计算买一量卖一量深度比
            bitda_bid_ask1 = bitda_bid1 + bitda_ask1
            binance_bid_ask1 = binance_bid1 + binance_ask1
            
            if binance_bid_ask1 > 0:
                bid_ask1_ratio = bitda_bid_ask1 / binance_bid_ask1
            else:
                bid_ask1_ratio = 0
            
            # 计算买卖前两档量深度比
            bitda_bid_ask2 = bitda_bid1 + bitda_bid2 + bitda_ask1 + bitda_ask2
            binance_bid_ask2 = binance_bid1 + binance_bid2 + binance_ask1 + binance_ask2
            
            if binance_bid_ask2 > 0:
                bid_ask2_ratio = bitda_bid_ask2 / binance_bid_ask2
            else:
                bid_ask2_ratio = 0
            
            # 计算买卖前五档量深度比
            bitda_bid_ask5 = (bitda_bid1 + bitda_bid2 + bitda_bid3 + bitda_bid4 + bitda_bid5 +
                             bitda_ask1 + bitda_ask2 + bitda_ask3 + bitda_ask4 + bitda_ask5)
            binance_bid_ask5 = (binance_bid1 + binance_bid2 + binance_bid3 + binance_bid4 + binance_bid5 +
                               binance_ask1 + binance_ask2 + binance_ask3 + binance_ask4 + binance_ask5)
            
            if binance_bid_ask5 > 0:
                bid_ask5_ratio = bitda_bid_ask5 / binance_bid_ask5
            else:
                bid_ask5_ratio = 0
            
            # 存储完整的对比数据
            comparison = {
                'index': i + 1,
                'bitda_time': bitda_time,
                'binance_time': binance_time,
                'time_diff_ms': time_diff,
                'bitda_bid1': bitda_bid1,
                'bitda_ask1': bitda_ask1,
                'bitda_bid_ask1': bitda_bid_ask1,
                'binance_bid1': binance_bid1,
                'binance_ask1': binance_ask1,
                'binance_bid_ask1': binance_bid_ask1,
                'bid_ask1_ratio': bid_ask1_ratio,
                'bitda_bid_ask2': bitda_bid_ask2,
                'binance_bid_ask2': binance_bid_ask2,
                'bid_ask2_ratio': bid_ask2_ratio,
                'bitda_bid_ask5': bitda_bid_ask5,
                'binance_bid_ask5': binance_bid_ask5,
                'bid_ask5_ratio': bid_ask5_ratio
            }
            
            depth_comparisons.append(comparison)
        
        print(f"   ✅ 成功计算{len(depth_comparisons)}条深度对比数据")
        print()
        
        # 第三步：找到最大值和最小值
        print("📊 第三步：找到买一量卖一量深度比的最大值和最小值...")
        
        valid_ratios = [comp for comp in depth_comparisons if comp['bid_ask1_ratio'] > 0]
        
        if not valid_ratios:
            print("❌ 无有效的深度比值")
            return
        
        # 找到最大值和最小值
        max_comparison = max(valid_ratios, key=lambda x: x['bid_ask1_ratio'])
        min_comparison = min(valid_ratios, key=lambda x: x['bid_ask1_ratio'])
        
        print(f"   📈 最大值: {max_comparison['bid_ask1_ratio']:.2f}")
        print(f"   📉 最小值: {min_comparison['bid_ask1_ratio']:.2f}")
        print()
        
        # 第四步：详细验证最大值
        print("🔍 第四步：详细验证最大值计算过程")
        print("=" * 80)
        max_comp = max_comparison
        print(f"📅 时间点: {max_comp['bitda_time'].strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
        print(f"⏰ 时间差: {max_comp['time_diff_ms']}ms")
        print()
        print("📊 原始数据库表值:")
        print(f"   Bitda买一量: {max_comp['bitda_bid1']:.4f}")
        print(f"   Bitda卖一量: {max_comp['bitda_ask1']:.4f}")
        print(f"   Binance买一量: {max_comp['binance_bid1']:.4f}")
        print(f"   Binance卖一量: {max_comp['binance_ask1']:.4f}")
        print()
        print("🧮 计算过程:")
        print(f"   Bitda买一量卖一量 = {max_comp['bitda_bid1']:.4f} + {max_comp['bitda_ask1']:.4f} = {max_comp['bitda_bid_ask1']:.4f}")
        print(f"   Binance买一量卖一量 = {max_comp['binance_bid1']:.4f} + {max_comp['binance_ask1']:.4f} = {max_comp['binance_bid_ask1']:.4f}")
        print(f"   深度比 = {max_comp['bitda_bid_ask1']:.4f} ÷ {max_comp['binance_bid_ask1']:.4f} = {max_comp['bid_ask1_ratio']:.6f}")
        print(f"   ✅ 最大值验证: {max_comp['bid_ask1_ratio']:.2f}")
        print()
        
        # 第五步：详细验证最小值
        print("🔍 第五步：详细验证最小值计算过程")
        print("=" * 80)
        min_comp = min_comparison
        print(f"📅 时间点: {min_comp['bitda_time'].strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
        print(f"⏰ 时间差: {min_comp['time_diff_ms']}ms")
        print()
        print("📊 原始数据库表值:")
        print(f"   Bitda买一量: {min_comp['bitda_bid1']:.4f}")
        print(f"   Bitda卖一量: {min_comp['bitda_ask1']:.4f}")
        print(f"   Binance买一量: {min_comp['binance_bid1']:.4f}")
        print(f"   Binance卖一量: {min_comp['binance_ask1']:.4f}")
        print()
        print("🧮 计算过程:")
        print(f"   Bitda买一量卖一量 = {min_comp['bitda_bid1']:.4f} + {min_comp['bitda_ask1']:.4f} = {min_comp['bitda_bid_ask1']:.4f}")
        print(f"   Binance买一量卖一量 = {min_comp['binance_bid1']:.4f} + {min_comp['binance_ask1']:.4f} = {min_comp['binance_bid_ask1']:.4f}")
        print(f"   深度比 = {min_comp['bitda_bid_ask1']:.4f} ÷ {min_comp['binance_bid_ask1']:.4f} = {min_comp['bid_ask1_ratio']:.6f}")
        print(f"   ✅ 最小值验证: {min_comp['bid_ask1_ratio']:.2f}")
        print()
        
        # 第六步：统计验证
        print("📊 第六步：统计验证")
        print("=" * 80)
        all_ratios = [comp['bid_ask1_ratio'] for comp in valid_ratios]
        max_ratio = max(all_ratios)
        min_ratio = min(all_ratios)
        avg_ratio = sum(all_ratios) / len(all_ratios)
        
        print(f"   样本数量: {len(all_ratios)}条")
        print(f"   最大值: {max_ratio:.2f}")
        print(f"   最小值: {min_ratio:.2f}")
        print(f"   平均值: {avg_ratio:.2f}")
        print()
        print("✅ 与仪表板显示对比:")
        print(f"   仪表板最大值: 28.98 vs 计算结果: {max_ratio:.2f}")
        print(f"   仪表板最小值: 0.32 vs 计算结果: {min_ratio:.2f}")
        print(f"   仪表板平均值: 2.60 vs 计算结果: {avg_ratio:.2f}")
        
        # 验证是否匹配
        if abs(max_ratio - 28.98) < 0.01 and abs(min_ratio - 0.32) < 0.01:
            print("🎉 验证成功！计算结果与仪表板完全匹配！")
        else:
            print("⚠️ 验证发现差异，可能是时间点不同导致的")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔍 BTCUSDT深度统计计算验证器")
    print("=" * 80)
    print("🎯 验证内容:")
    print("   1. 原始数据库表值")
    print("   2. 具体时间点")
    print("   3. 完整计算过程")
    print("   4. 最大值和最小值的详细验证")
    print("   5. 与仪表板结果对比")
    print()
    
    verify_btcusdt_depth_calculation()

if __name__ == "__main__":
    main()
