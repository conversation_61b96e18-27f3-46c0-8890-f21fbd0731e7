# ClickHouse + Grafana 深度对比仪表板

## 概述

本项目提供了基于ClickHouse数据库的Grafana深度对比仪表板，用于显示BTCUSDT和ETHUSDT的深度数据对比分析。

## 功能特性

- 📊 **深度对比分析**: 显示Bitda vs Binance的深度数据对比
- 🔄 **实时更新**: 每1分钟自动刷新数据
- 📈 **多维度指标**: 包含5个关键深度指标
- 🎯 **精确计算**: 基于ClickHouse高性能查询

## 深度指标

仪表板显示以下5个关键指标：

| 项目 | 说明 | 计算方式 |
|------|------|----------|
| 卖一量 | 卖一档位数量 | ask_qty_1 |
| 买一量 | 买一档位数量 | bid_qty_1 |
| 买一量卖一量 | 买一卖一总量 | bid_qty_1 + ask_qty_1 |
| 买卖前两档量 | 前两档总量 | bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 |
| 买卖前五档量 | 前五档总量 | sum(bid_qty_1~5) + sum(ask_qty_1~5) |

## 文件说明

### 核心文件

- `grafana_clickhouse_depth_dashboard.json` - Grafana仪表板配置文件
- `setup_grafana_clickhouse.py` - 自动配置脚本
- `start_grafana_clickhouse.sh` - 一键启动脚本
- `.env` - 环境配置文件

### 配置文件

```bash
# ClickHouse配置
CH_HOST=localhost
CH_USER=default
CH_PASSWORD=Linuxtest
CH_DATABASE=crypto
CH_PORT=9000

# Grafana配置
GRAFANA_HOST=localhost
GRAFANA_PORT=3000
GRAFANA_USER=admin
GRAFANA_PASSWORD=admin
GRAFANA_URL=http://localhost:3000
```

## 快速开始

### 方法一：一键启动（推荐）

```bash
# 1. 运行启动脚本
./start_grafana_clickhouse.sh

# 2. 访问Grafana
# 浏览器打开: http://localhost:3000
# 用户名: admin
# 密码: admin
```

### 方法二：手动配置

```bash
# 1. 启动ClickHouse
sudo systemctl start clickhouse-server

# 2. 启动Grafana
sudo systemctl start grafana-server

# 3. 安装ClickHouse插件
sudo grafana-cli plugins install grafana-clickhouse-datasource
sudo systemctl restart grafana-server

# 4. 运行配置脚本
python3 setup_grafana_clickhouse.py
```

## 数据库表结构

### bitda_depth 表

```sql
CREATE TABLE crypto.bitda_depth (
    id UInt64,
    symbol String,
    timestamp UInt64,
    bid_price_1 Decimal(15,2),
    ask_price_1 Decimal(15,2),
    bid_qty_1 Decimal(20,4),
    ask_qty_1 Decimal(20,4),
    bid_price_2~5 Decimal(15,2),
    ask_price_2~5 Decimal(15,2),
    bid_qty_2~5 Decimal(20,4),
    ask_qty_2~5 Decimal(20,4),
    created_at DateTime
) ENGINE = MergeTree()
ORDER BY (symbol, timestamp)
```

### binance_depth_5 表

```sql
CREATE TABLE crypto.binance_depth_5 (
    id UInt64,
    symbol String,
    event_time UInt64,
    bid_price_1 Decimal(15,2),
    ask_price_1 Decimal(15,2),
    bid_qty_1 Decimal(20,4),
    ask_qty_1 Decimal(20,4),
    bid_price_2~5 Decimal(15,2),
    ask_price_2~5 Decimal(15,2),
    bid_qty_2~5 Decimal(20,4),
    ask_qty_2~5 Decimal(20,4),
    created_at DateTime
) ENGINE = MergeTree()
ORDER BY (symbol, event_time)
```

## SQL查询示例

### BTCUSDT深度对比查询

```sql
SELECT 
  '卖一量' as 项目,
  round((SELECT ask_qty_1 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
  round((SELECT ask_qty_1 FROM crypto.binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
  round(Bitda / Binance, 2) as 深度比
UNION ALL
SELECT 
  '买一量' as 项目,
  round((SELECT bid_qty_1 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
  round((SELECT bid_qty_1 FROM crypto.binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
  round(Bitda / Binance, 2) as 深度比
-- ... 其他指标
```

## 故障排除

### 常见问题

1. **ClickHouse连接失败**
   ```bash
   # 检查ClickHouse状态
   sudo systemctl status clickhouse-server
   
   # 检查端口
   nc -z localhost 9000
   ```

2. **Grafana插件未安装**
   ```bash
   # 安装ClickHouse插件
   sudo grafana-cli plugins install grafana-clickhouse-datasource
   sudo systemctl restart grafana-server
   ```

3. **数据不显示**
   ```bash
   # 检查数据
   clickhouse-client --query "SELECT COUNT(*) FROM crypto.bitda_depth"
   clickhouse-client --query "SELECT COUNT(*) FROM crypto.binance_depth_5"
   ```

### 日志查看

```bash
# ClickHouse日志
sudo tail -f /var/log/clickhouse-server/clickhouse-server.log

# Grafana日志
sudo tail -f /var/log/grafana/grafana.log
```

## 性能优化

### ClickHouse优化

1. **索引优化**: 表已按 `(symbol, timestamp)` 排序
2. **分区策略**: 可考虑按月分区大表
3. **压缩设置**: 使用LZ4压缩算法

### Grafana优化

1. **查询缓存**: 启用查询结果缓存
2. **刷新间隔**: 根据需要调整刷新频率
3. **面板优化**: 避免过于复杂的查询

## 扩展功能

### 添加新交易对

1. 修改SQL查询中的symbol过滤条件
2. 复制面板并更新交易对名称
3. 调整面板布局

### 添加新指标

1. 在SQL查询中添加新的UNION ALL子句
2. 定义新的计算逻辑
3. 更新面板标题和说明

## 技术支持

如有问题，请检查：

1. ✅ ClickHouse服务状态
2. ✅ Grafana服务状态  
3. ✅ ClickHouse插件安装
4. ✅ 数据源配置
5. ✅ 数据完整性

---

**版本**: 1.0  
**更新时间**: 2024年  
**兼容性**: ClickHouse 22.x+, Grafana 9.x+
