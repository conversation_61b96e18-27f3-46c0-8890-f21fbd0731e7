#!/usr/bin/env python3
"""
数据库精度调整脚本
根据交易所和交易对的精度要求调整数据库表结构

精度要求：
Bitda:
- BTCUSDT: 价格精度1位，数量精度4位
- ETHUSDT: 价格精度2位，数量精度3位

Binance:
- BTCUSDT: 价格精度2位，数量精度3位  
- ETHUSDT: 价格精度2位，数量精度3位

统一精度方案：
- 价格字段：DECIMAL(15,2) - 支持最高精度需求
- 数量字段：DECIMAL(20,4) - 支持最高精度需求
"""

import mysql.connector
from utils.config import DB_CONFIG
from utils.logging import setup_logger

logger = setup_logger(__name__)

def update_table_precision():
    """更新数据库表精度"""
    
    # 表结构修改SQL
    alter_statements = [
        # Bitda深度表
        "ALTER TABLE bitda_depth MODIFY COLUMN index_price DECIMAL(15,2) DEFAULT NULL COMMENT '指数价格'",
        "ALTER TABLE bitda_depth MODIFY COLUMN sign_price DECIMAL(15,2) DEFAULT NULL COMMENT '标记价格'", 
        "ALTER TABLE bitda_depth MODIFY COLUMN last_price DECIMAL(15,2) DEFAULT NULL COMMENT '最新价格'",
        
        # Bitda K线表
        "ALTER TABLE bitda_kline MODIFY COLUMN open_price DECIMAL(15,2) NOT NULL COMMENT '开盘价'",
        "ALTER TABLE bitda_kline MODIFY COLUMN high_price DECIMAL(15,2) NOT NULL COMMENT '最高价'",
        "ALTER TABLE bitda_kline MODIFY COLUMN low_price DECIMAL(15,2) NOT NULL COMMENT '最低价'",
        "ALTER TABLE bitda_kline MODIFY COLUMN close_price DECIMAL(15,2) NOT NULL COMMENT '收盘价'",
        "ALTER TABLE bitda_kline MODIFY COLUMN volume DECIMAL(20,4) NOT NULL COMMENT '成交数量'",
        "ALTER TABLE bitda_kline MODIFY COLUMN amount DECIMAL(25,2) NOT NULL COMMENT '成交金额'",
        
        # Bitda成交表
        "ALTER TABLE bitda_trades MODIFY COLUMN price DECIMAL(15,2) NOT NULL COMMENT '成交价格'",
        "ALTER TABLE bitda_trades MODIFY COLUMN amount DECIMAL(20,4) NOT NULL COMMENT '成交数量'",
        
        # Bitda行情表
        "ALTER TABLE bitda_ticker MODIFY COLUMN open_price DECIMAL(15,2) NOT NULL COMMENT '开盘价'",
        "ALTER TABLE bitda_ticker MODIFY COLUMN high_price DECIMAL(15,2) NOT NULL COMMENT '最高价'",
        "ALTER TABLE bitda_ticker MODIFY COLUMN low_price DECIMAL(15,2) NOT NULL COMMENT '最低价'",
        "ALTER TABLE bitda_ticker MODIFY COLUMN last_price DECIMAL(15,2) NOT NULL COMMENT '最新价'",
        "ALTER TABLE bitda_ticker MODIFY COLUMN volume DECIMAL(25,4) NOT NULL COMMENT '成交量'",
        "ALTER TABLE bitda_ticker MODIFY COLUMN amount DECIMAL(25,2) NOT NULL COMMENT '成交金额'",
        "ALTER TABLE bitda_ticker MODIFY COLUMN change_rate DECIMAL(10,4) NOT NULL COMMENT '涨跌幅'",
        "ALTER TABLE bitda_ticker MODIFY COLUMN position_amount DECIMAL(25,4) DEFAULT NULL COMMENT '持仓量'",
        "ALTER TABLE bitda_ticker MODIFY COLUMN funding_rate_last DECIMAL(10,6) DEFAULT NULL COMMENT '当前资金费率'",
        "ALTER TABLE bitda_ticker MODIFY COLUMN funding_rate_next DECIMAL(10,6) DEFAULT NULL COMMENT '下一个资金费率'",
        "ALTER TABLE bitda_ticker MODIFY COLUMN funding_rate_predict DECIMAL(10,6) DEFAULT NULL COMMENT '预测资金费率'",
        "ALTER TABLE bitda_ticker MODIFY COLUMN insurance DECIMAL(25,2) DEFAULT NULL COMMENT '保险基金'",
        "ALTER TABLE bitda_ticker MODIFY COLUMN sign_price DECIMAL(15,2) DEFAULT NULL COMMENT '标记价格'",
        "ALTER TABLE bitda_ticker MODIFY COLUMN index_price DECIMAL(15,2) DEFAULT NULL COMMENT '指数价格'",
        "ALTER TABLE bitda_ticker MODIFY COLUMN sell_total DECIMAL(25,4) DEFAULT NULL COMMENT '卖盘总量'",
        "ALTER TABLE bitda_ticker MODIFY COLUMN buy_total DECIMAL(25,4) DEFAULT NULL COMMENT '买盘总量'",
        
        # Binance BookTicker表
        "ALTER TABLE binance_bookticker MODIFY COLUMN bid_price DECIMAL(15,2) NOT NULL COMMENT '最优买单价'",
        "ALTER TABLE binance_bookticker MODIFY COLUMN bid_qty DECIMAL(20,4) NOT NULL COMMENT '最优买单量'",
        "ALTER TABLE binance_bookticker MODIFY COLUMN ask_price DECIMAL(15,2) NOT NULL COMMENT '最优卖单价'",
        "ALTER TABLE binance_bookticker MODIFY COLUMN ask_qty DECIMAL(20,4) NOT NULL COMMENT '最优卖单量'",
        
        # Binance 5档深度表 - 这个表的数据在JSON字段中，不需要修改
    ]
    
    conn = None
    cursor = None
    
    try:
        # 连接数据库
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        logger.info("开始更新数据库表精度...")
        
        # 执行所有修改语句
        for i, statement in enumerate(alter_statements, 1):
            try:
                logger.info(f"执行修改 {i}/{len(alter_statements)}: {statement[:50]}...")
                cursor.execute(statement)
                logger.info(f"✓ 修改 {i} 完成")
            except Exception as e:
                logger.error(f"✗ 修改 {i} 失败: {e}")
                logger.error(f"SQL: {statement}")
        
        # 提交更改
        conn.commit()
        logger.info("所有表精度更新完成！")
        
        return True
        
    except Exception as e:
        logger.error(f"更新表精度失败: {e}")
        if conn:
            conn.rollback()
        return False
        
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def verify_precision():
    """验证精度修改结果"""
    conn = None
    cursor = None
    
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 检查关键表的字段精度
        tables_to_check = [
            'bitda_depth',
            'bitda_kline', 
            'bitda_trades',
            'bitda_ticker',
            'binance_bookticker'
        ]
        
        logger.info("验证表精度修改结果...")
        
        for table in tables_to_check:
            cursor.execute(f"DESCRIBE {table}")
            columns = cursor.fetchall()
            
            logger.info(f"\n表 {table} 的字段信息:")
            for col in columns:
                field_name, field_type, null, key, default, extra = col
                if 'decimal' in field_type.lower():
                    logger.info(f"  {field_name}: {field_type}")
        
        return True
        
    except Exception as e:
        logger.error(f"验证精度失败: {e}")
        return False
        
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    logger.info("开始数据库精度调整...")
    
    # 更新精度
    if update_table_precision():
        logger.info("精度更新成功")
        
        # 验证结果
        if verify_precision():
            logger.info("精度验证通过")
        else:
            logger.error("精度验证失败")
    else:
        logger.error("精度更新失败")
