#!/usr/bin/env python3
"""
数据清理工具
用于清理数据库中的测试数据和无效数据
"""

import sys
from datetime import datetime
from utils.db import db_manager
from utils.logging import setup_logger

logger = setup_logger(__name__)

class DataCleaner:
    """数据清理器"""
    
    def __init__(self):
        self.tables = [
            'bitda_ticker',
            'bitda_depth', 
            'bitda_kline',
            'bitda_trades',
            'binance_bookticker',
            'binance_depth_5',
            'depth_matches',
            'exchange_raw_data',
            'binance_raw_data'
        ]
    
    def clean_test_data(self, confirm: bool = False) -> bool:
        """
        清理测试数据
        
        Args:
            confirm: 是否确认清理
            
        Returns:
            清理是否成功
        """
        if not confirm:
            print("⚠️  这将删除数据库中的测试数据，请确认操作")
            response = input("输入 'yes' 确认清理测试数据: ")
            if response.lower() != 'yes':
                print("取消清理操作")
                return False
        
        try:
            logger.info("开始清理测试数据...")
            
            # 清理明显的测试数据
            test_data_queries = [
                # 清理bitda_ticker表中的测试数据（ID为1,2的记录）
                "DELETE FROM bitda_ticker WHERE id IN (1, 2)",
                
                # 清理价格为测试值的数据
                "DELETE FROM bitda_ticker WHERE last_price IN (50000.00, 1.0000, 123456789)",
                
                # 清理depth_matches表中早期的测试数据（ID小于100）
                "DELETE FROM depth_matches WHERE id < 100",
                
                # 清理异常的延时数据（延时超过10秒或为负数）
                "DELETE FROM depth_matches WHERE message_latency_ms > 10000 OR message_latency_ms < 0",
                
                # 清理异常的深度比值数据
                "DELETE FROM depth_matches WHERE depth_ratio > 100 OR depth_ratio < 0.001",
            ]
            
            total_deleted = 0
            
            for query in test_data_queries:
                try:
                    result = db_manager.execute_query(query, fetch=False)
                    if result:
                        deleted_count = result
                        total_deleted += deleted_count
                        logger.info(f"执行清理查询，删除 {deleted_count} 条记录")
                except Exception as e:
                    logger.warning(f"清理查询执行失败: {e}")
                    continue
            
            logger.info(f"测试数据清理完成，总共删除 {total_deleted} 条记录")
            print(f"✅ 测试数据清理完成，删除了 {total_deleted} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"清理测试数据失败: {e}")
            print(f"❌ 清理失败: {e}")
            return False
    
    def clean_old_data(self, days: int = 7, confirm: bool = False) -> bool:
        """
        清理旧数据
        
        Args:
            days: 保留天数
            confirm: 是否确认清理
            
        Returns:
            清理是否成功
        """
        if not confirm:
            print(f"⚠️  这将删除 {days} 天前的所有数据，请确认操作")
            response = input("输入 'yes' 确认清理旧数据: ")
            if response.lower() != 'yes':
                print("取消清理操作")
                return False
        
        try:
            logger.info(f"开始清理 {days} 天前的旧数据...")
            
            from datetime import datetime, timedelta
            cutoff_date = datetime.now() - timedelta(days=days)
            
            total_deleted = 0
            
            for table in self.tables:
                try:
                    # 检查表是否存在created_at字段
                    check_query = f"SHOW COLUMNS FROM {table} LIKE 'created_at'"
                    columns = db_manager.execute_query(check_query, fetch=True)
                    
                    if not columns:
                        logger.info(f"表 {table} 没有created_at字段，跳过")
                        continue
                    
                    # 删除旧数据
                    delete_query = f"DELETE FROM {table} WHERE created_at < %s"
                    result = db_manager.execute_query(delete_query, (cutoff_date,), fetch=False)
                    
                    if result:
                        deleted_count = result
                        total_deleted += deleted_count
                        logger.info(f"表 {table} 删除 {deleted_count} 条旧记录")
                    
                except Exception as e:
                    logger.warning(f"清理表 {table} 失败: {e}")
                    continue
            
            logger.info(f"旧数据清理完成，总共删除 {total_deleted} 条记录")
            print(f"✅ 旧数据清理完成，删除了 {total_deleted} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"清理旧数据失败: {e}")
            print(f"❌ 清理失败: {e}")
            return False
    
    def clean_invalid_data(self, confirm: bool = False) -> bool:
        """
        清理无效数据
        
        Args:
            confirm: 是否确认清理
            
        Returns:
            清理是否成功
        """
        if not confirm:
            print("⚠️  这将删除数据库中的无效数据，请确认操作")
            response = input("输入 'yes' 确认清理无效数据: ")
            if response.lower() != 'yes':
                print("取消清理操作")
                return False
        
        try:
            logger.info("开始清理无效数据...")
            
            # 清理无效数据的查询
            invalid_data_queries = [
                # 清理价格为0或NULL的数据
                "DELETE FROM bitda_ticker WHERE last_price IS NULL OR last_price <= 0",
                "DELETE FROM binance_bookticker WHERE bid_price IS NULL OR bid_price <= 0 OR ask_price IS NULL OR ask_price <= 0",
                
                # 清理数量为负数的数据
                "DELETE FROM binance_bookticker WHERE bid_qty < 0 OR ask_qty < 0",
                "DELETE FROM depth_matches WHERE binance_bid_qty < 0 OR binance_ask_qty < 0 OR exchange_bid_qty < 0 OR exchange_ask_qty < 0",
                
                # 清理时间戳异常的数据（未来时间或过于久远的时间）
                f"DELETE FROM bitda_ticker WHERE created_at > NOW() + INTERVAL 1 HOUR",
                f"DELETE FROM bitda_ticker WHERE created_at < '2020-01-01'",
                
                # 清理重复的数据（保留最新的）
                """
                DELETE t1 FROM bitda_ticker t1
                INNER JOIN bitda_ticker t2 
                WHERE t1.id < t2.id 
                AND t1.symbol = t2.symbol 
                AND t1.last_price = t2.last_price 
                AND ABS(TIMESTAMPDIFF(SECOND, t1.created_at, t2.created_at)) < 1
                """
            ]
            
            total_deleted = 0
            
            for query in invalid_data_queries:
                try:
                    result = db_manager.execute_query(query, fetch=False)
                    if result:
                        deleted_count = result
                        total_deleted += deleted_count
                        logger.info(f"清理无效数据，删除 {deleted_count} 条记录")
                except Exception as e:
                    logger.warning(f"清理查询执行失败: {e}")
                    continue
            
            logger.info(f"无效数据清理完成，总共删除 {total_deleted} 条记录")
            print(f"✅ 无效数据清理完成，删除了 {total_deleted} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"清理无效数据失败: {e}")
            print(f"❌ 清理失败: {e}")
            return False
    
    def get_table_stats(self) -> dict:
        """获取表统计信息"""
        try:
            stats = {}
            
            for table in self.tables:
                try:
                    # 获取记录总数
                    count_query = f"SELECT COUNT(*) FROM {table}"
                    count_result = db_manager.execute_query(count_query, fetch=True)
                    total_count = count_result[0][0] if count_result else 0
                    
                    # 获取最新记录时间
                    latest_query = f"SELECT MAX(created_at) FROM {table}"
                    latest_result = db_manager.execute_query(latest_query, fetch=True)
                    latest_time = latest_result[0][0] if latest_result and latest_result[0][0] else None
                    
                    stats[table] = {
                        'total_records': total_count,
                        'latest_record': latest_time
                    }
                    
                except Exception as e:
                    logger.warning(f"获取表 {table} 统计信息失败: {e}")
                    stats[table] = {'total_records': 0, 'latest_record': None}
            
            return stats
            
        except Exception as e:
            logger.error(f"获取表统计信息失败: {e}")
            return {}

def main():
    """主函数"""
    cleaner = DataCleaner()
    
    if len(sys.argv) < 2:
        print("数据清理工具")
        print("使用方法:")
        print("  python clean_test_data.py test      # 清理测试数据")
        print("  python clean_test_data.py old [天数] # 清理旧数据")
        print("  python clean_test_data.py invalid   # 清理无效数据")
        print("  python clean_test_data.py stats     # 显示表统计信息")
        return
    
    command = sys.argv[1].lower()
    
    if command == 'test':
        cleaner.clean_test_data()
    elif command == 'old':
        days = int(sys.argv[2]) if len(sys.argv) > 2 else 7
        cleaner.clean_old_data(days)
    elif command == 'invalid':
        cleaner.clean_invalid_data()
    elif command == 'stats':
        stats = cleaner.get_table_stats()
        print("\n📊 数据库表统计信息:")
        print("=" * 60)
        for table, info in stats.items():
            print(f"{table:20} | {info['total_records']:>10} 条记录 | 最新: {info['latest_record']}")
    else:
        print(f"❌ 未知命令: {command}")

if __name__ == "__main__":
    main()
