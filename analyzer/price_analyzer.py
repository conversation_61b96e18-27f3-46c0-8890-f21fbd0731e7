#!/usr/bin/env python3
"""
价格分析器
分析Bitda标记价格与最新价格的差值
"""

from datetime import datetime, timedelta
from typing import Dict, List
import pandas as pd
from utils.db import db_manager
from utils.logging import setup_logger

logger = setup_logger(__name__)

class PriceAnalyzer:
    """价格分析器"""

    def __init__(self):
        self.symbols = ['BTCUSDT', 'ETHUSDT']

    def analyze_mark_price_deviation(self, hours: int = 1) -> Dict:
        """
        分析标记价格偏差（同步版本）

        Args:
            hours: 分析时间范围（小时）

        Returns:
            标记价格偏差分析结果
        """
        try:
            from datetime import datetime, timedelta

            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)

            results = {}

            for symbol in self.symbols:
                # 查询bitda_ticker表中的标记价格和最新价格数据
                query = """
                SELECT
                    last_price,
                    sign_price,
                    created_at
                FROM bitda_ticker
                WHERE symbol = %s
                AND created_at >= %s
                AND created_at <= %s
                AND sign_price IS NOT NULL
                AND last_price IS NOT NULL
                ORDER BY created_at DESC
                """

                data = db_manager.execute_query(
                    query, (symbol, start_time, end_time), fetch=True
                )

                if not data:
                    logger.warning(f"没有找到{symbol}的标记价格数据")
                    results[symbol] = self._empty_price_result()
                    continue

                # 计算偏差
                deviations = []
                absolute_deviations = []

                for row in data:
                    last_price = float(row[0])
                    sign_price = float(row[1])

                    if last_price > 0:
                        # 计算偏差 (标记价格 - 最新价格) / 最新价格
                        deviation = (sign_price - last_price) / last_price
                        deviations.append(deviation)
                        absolute_deviations.append(abs(deviation))

                if not deviations:
                    results[symbol] = self._empty_price_result()
                    continue

                # 计算统计数据
                avg_deviation = sum(deviations) / len(deviations)
                max_deviation = max(deviations)
                min_deviation = min(deviations)

                # 计算中位数
                sorted_deviations = sorted(deviations)
                n = len(sorted_deviations)
                median_deviation = (sorted_deviations[n//2] + sorted_deviations[(n-1)//2]) / 2

                # 计算绝对偏差统计
                avg_abs_deviation = sum(absolute_deviations) / len(absolute_deviations)
                max_abs_deviation = max(absolute_deviations)

                # 偏差分布统计
                distribution = {
                    '0-0.01%': len([d for d in absolute_deviations if 0 <= d < 0.0001]),
                    '0.01-0.05%': len([d for d in absolute_deviations if 0.0001 <= d < 0.0005]),
                    '0.05-0.1%': len([d for d in absolute_deviations if 0.0005 <= d < 0.001]),
                    '0.1-0.5%': len([d for d in absolute_deviations if 0.001 <= d < 0.005]),
                    '0.5-1%': len([d for d in absolute_deviations if 0.005 <= d < 0.01]),
                    '1%+': len([d for d in absolute_deviations if d >= 0.01])
                }

                results[symbol] = {
                    'sample_count': len(data),
                    'max_deviation': round(max_deviation * 100, 4),  # 转换为百分比
                    'min_deviation': round(min_deviation * 100, 4),
                    'avg_deviation': round(avg_deviation * 100, 4),
                    'median_deviation': round(median_deviation * 100, 4),
                    'max_abs_deviation': round(max_abs_deviation * 100, 4),
                    'avg_abs_deviation': round(avg_abs_deviation * 100, 4),
                    'deviation_distribution': distribution,
                    'analysis_time': datetime.now().isoformat()
                }

                logger.info(f"{symbol}标记价格偏差分析完成，样本数: {len(data)}")

            return {
                'results': results,
                'time_range': f"{start_time.strftime('%Y-%m-%d %H:%M')} - {end_time.strftime('%Y-%m-%d %H:%M')}",
                'analysis_time': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"标记价格偏差分析失败: {e}")
            return {
                'results': {symbol: self._empty_price_result() for symbol in self.symbols},
                'error': str(e),
                'analysis_time': datetime.now().isoformat()
            }

    def _empty_price_result(self) -> Dict:
        """返回空的价格分析结果"""
        return {
            'sample_count': 0,
            'max_deviation': 0,
            'min_deviation': 0,
            'avg_deviation': 0,
            'median_deviation': 0,
            'max_abs_deviation': 0,
            'avg_abs_deviation': 0,
            'deviation_distribution': {},
            'analysis_time': datetime.now().isoformat()
        }

    def get_latest_price_deviation(self) -> Dict:
        """获取最新的价格偏差数据"""
        try:
            results = {}

            for symbol in self.symbols:
                # 查询最新的价格数据
                query = """
                SELECT
                    last_price,
                    sign_price,
                    created_at
                FROM bitda_ticker
                WHERE symbol = %s
                AND sign_price IS NOT NULL
                AND last_price IS NOT NULL
                ORDER BY created_at DESC
                LIMIT 1
                """

                data = db_manager.execute_query(query, (symbol,), fetch=True)

                if data:
                    row = data[0]
                    last_price = float(row[0])
                    sign_price = float(row[1])

                    if last_price > 0:
                        deviation = (sign_price - last_price) / last_price
                        abs_deviation = abs(deviation)

                        results[symbol] = {
                            'last_price': round(last_price, 2),
                            'sign_price': round(sign_price, 2),
                            'deviation_pct': round(deviation * 100, 4),
                            'abs_deviation_pct': round(abs_deviation * 100, 4),
                            'timestamp': row[2]
                        }
                    else:
                        results[symbol] = {
                            'last_price': 0,
                            'sign_price': 0,
                            'deviation_pct': 0,
                            'abs_deviation_pct': 0,
                            'timestamp': None
                        }
                else:
                    results[symbol] = {
                        'last_price': 0,
                        'sign_price': 0,
                        'deviation_pct': 0,
                        'abs_deviation_pct': 0,
                        'timestamp': None
                    }

            return results

        except Exception as e:
            logger.error(f"获取最新价格偏差数据失败: {e}")
            return {symbol: {} for symbol in self.symbols}

    async def analyze_mark_price_difference(self, hours: int = 1) -> Dict:
        """
        分析标记价格与最新价格的差值

        Args:
            hours: 分析时间范围（小时）

        Returns:
            价格差值分析结果
        """
        try:
            results = {}

            for symbol in self.symbols:
                logger.info(f"分析{symbol}的价格差值...")

                # 获取价格数据
                price_data = await self._get_price_data(symbol, hours)

                if not price_data:
                    logger.warning(f"没有找到{symbol}的价格数据")
                    results[symbol] = self._empty_price_result()
                    continue

                # 计算价格差值统计
                stats = self._calculate_price_stats(price_data)
                results[symbol] = stats

                logger.info(f"{symbol}价格分析完成，数据点: {len(price_data)}")

            return {
                'analysis_time': datetime.now().isoformat(),
                'hours_analyzed': hours,
                'results': results
            }

        except Exception as e:
            logger.error(f"价格分析失败: {e}")
            return self._empty_analysis_result()

    async def _get_price_data(self, symbol: str, hours: int) -> List[Dict]:
        """
        获取价格数据

        Args:
            symbol: 交易对
            hours: 时间范围

        Returns:
            价格数据列表
        """
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)

        query = """
        SELECT sign_price, last_price, index_price, created_at
        FROM bitda_ticker
        WHERE symbol = %s AND created_at >= %s AND created_at <= %s
        AND sign_price IS NOT NULL AND last_price IS NOT NULL
        ORDER BY created_at
        """

        data = db_manager.execute_query(
            query, (symbol, start_time, end_time), fetch=True
        )

        if not data:
            return []

        price_data = []
        for row in data:
            sign_price, last_price, index_price, created_at = row

            if sign_price and last_price:
                # 计算绝对差值和相对差值
                abs_diff = abs(float(sign_price) - float(last_price))
                rel_diff_pct = (abs_diff / float(last_price)) * 100 if last_price != 0 else 0

                price_data.append({
                    'timestamp': created_at,
                    'sign_price': float(sign_price),
                    'last_price': float(last_price),
                    'index_price': float(index_price) if index_price else None,
                    'abs_diff': abs_diff,
                    'rel_diff_pct': rel_diff_pct
                })

        return price_data

    def _calculate_price_stats(self, price_data: List[Dict]) -> Dict:
        """
        计算价格统计数据

        Args:
            price_data: 价格数据列表

        Returns:
            统计结果
        """
        if not price_data:
            return self._empty_price_result()

        # 提取差值数据
        abs_diffs = [data['abs_diff'] for data in price_data]
        rel_diffs = [data['rel_diff_pct'] for data in price_data]

        # 计算统计指标
        avg_abs_diff = sum(abs_diffs) / len(abs_diffs)
        max_abs_diff = max(abs_diffs)
        min_abs_diff = min(abs_diffs)

        avg_rel_diff = sum(rel_diffs) / len(rel_diffs)
        max_rel_diff = max(rel_diffs)
        min_rel_diff = min(rel_diffs)

        # 计算异常情况（差值超过阈值的次数）
        high_diff_threshold = 0.1  # 0.1%
        high_diff_count = sum(1 for diff in rel_diffs if diff > high_diff_threshold)

        # 计算趋势（最近vs早期）
        mid_point = len(price_data) // 2
        early_avg = sum(rel_diffs[:mid_point]) / mid_point if mid_point > 0 else 0
        recent_avg = sum(rel_diffs[mid_point:]) / (len(rel_diffs) - mid_point) if len(rel_diffs) > mid_point else 0

        return {
            'total_records': len(price_data),
            'avg_abs_diff': avg_abs_diff,
            'max_abs_diff': max_abs_diff,
            'min_abs_diff': min_abs_diff,
            'avg_rel_diff_pct': avg_rel_diff,
            'max_rel_diff_pct': max_rel_diff,
            'min_rel_diff_pct': min_rel_diff,
            'high_diff_count': high_diff_count,
            'high_diff_rate_pct': (high_diff_count / len(price_data) * 100) if price_data else 0,
            'early_period_avg_pct': early_avg,
            'recent_period_avg_pct': recent_avg,
            'trend_direction': 'improving' if recent_avg < early_avg else 'worsening' if recent_avg > early_avg else 'stable',
            'samples': price_data[-5:]  # 返回最近5个样本
        }

    def _empty_price_result(self) -> Dict:
        """返回空的价格结果"""
        return {
            'total_records': 0,
            'avg_abs_diff': 0,
            'max_abs_diff': 0,
            'min_abs_diff': 0,
            'avg_rel_diff_pct': 0,
            'max_rel_diff_pct': 0,
            'min_rel_diff_pct': 0,
            'high_diff_count': 0,
            'high_diff_rate_pct': 0,
            'early_period_avg_pct': 0,
            'recent_period_avg_pct': 0,
            'trend_direction': 'unknown',
            'samples': []
        }

    def _empty_analysis_result(self) -> Dict:
        """返回空的分析结果"""
        return {
            'analysis_time': datetime.now().isoformat(),
            'hours_analyzed': 0,
            'results': {symbol: self._empty_price_result() for symbol in self.symbols}
        }

    async def get_realtime_price_analysis(self) -> Dict:
        """获取实时价格分析（最近1小时）"""
        return await self.analyze_mark_price_difference(hours=1)

    async def get_daily_price_summary(self) -> Dict:
        """获取日价格汇总（最近24小时）"""
        return await self.analyze_mark_price_difference(hours=24)

    async def analyze_price_volatility(self, hours: int = 24) -> Dict:
        """
        分析价格波动性

        Args:
            hours: 分析时间范围

        Returns:
            波动性分析结果
        """
        try:
            results = {}

            for symbol in self.symbols:
                price_data = await self._get_price_data(symbol, hours)

                if not price_data:
                    results[symbol] = {'volatility': 0, 'price_range': 0}
                    continue

                # 计算价格波动性
                prices = [data['last_price'] for data in price_data]
                if len(prices) > 1:
                    price_changes = [abs(prices[i] - prices[i-1]) / prices[i-1] * 100
                                   for i in range(1, len(prices))]
                    avg_volatility = sum(price_changes) / len(price_changes)
                    price_range = (max(prices) - min(prices)) / min(prices) * 100
                else:
                    avg_volatility = 0
                    price_range = 0

                results[symbol] = {
                    'avg_volatility_pct': avg_volatility,
                    'price_range_pct': price_range,
                    'max_price': max(prices) if prices else 0,
                    'min_price': min(prices) if prices else 0
                }

            return {
                'analysis_time': datetime.now().isoformat(),
                'hours_analyzed': hours,
                'volatility_results': results
            }

        except Exception as e:
            logger.error(f"波动性分析失败: {e}")
            return {'volatility_results': {}}
