#!/usr/bin/env python3
"""
深度分析器
比较Bitda与Binance的深度数据，分析买一卖一数量之和的比值
"""

import json
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import pandas as pd
from utils.db import db_manager
from utils.logging import setup_logger

logger = setup_logger(__name__)

class DepthAnalyzer:
    """深度分析器"""

    def __init__(self):
        self.symbols = ['BTCUSDT', 'ETHUSDT']

    def analyze_depth_comparison(self, hours: int = 1) -> Dict:
        """
        分析深度对比（同步版本）

        Args:
            hours: 分析时间范围（小时）

        Returns:
            深度对比分析结果
        """
        try:
            from datetime import datetime, timedelta

            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)

            results = {}

            for symbol in self.symbols:
                # 查询最近的深度匹配数据
                query = """
                SELECT
                    binance_bid_qty,
                    binance_ask_qty,
                    exchange_bid_qty,
                    exchange_ask_qty,
                    binance_depth_sum,
                    exchange_depth_sum,
                    depth_ratio,
                    timestamp
                FROM depth_matches
                WHERE symbol = %s
                AND timestamp >= %s
                AND timestamp <= %s
                ORDER BY timestamp DESC
                LIMIT 1000
                """

                data = db_manager.execute_query(
                    query, (symbol, start_time, end_time), fetch=True
                )

                if not data:
                    logger.warning(f"没有找到{symbol}的深度匹配数据")
                    results[symbol] = self._empty_depth_result()
                    continue

                # 计算各种深度比值
                bid_ask_ratios = []  # 买一卖一量比值
                bid_ratios = []     # 买一量比值
                ask_ratios = []     # 卖一量比值
                depth_ratios = []   # 总深度比值

                for row in data:
                    binance_bid_qty = float(row[0])
                    binance_ask_qty = float(row[1])
                    exchange_bid_qty = float(row[2])
                    exchange_ask_qty = float(row[3])

                    # 计算比值（避免除零）
                    if binance_bid_qty > 0:
                        bid_ratios.append(exchange_bid_qty / binance_bid_qty)
                    if binance_ask_qty > 0:
                        ask_ratios.append(exchange_ask_qty / binance_ask_qty)

                    # 买一卖一量总和比值
                    binance_total = binance_bid_qty + binance_ask_qty
                    exchange_total = exchange_bid_qty + exchange_ask_qty
                    if binance_total > 0:
                        bid_ask_ratios.append(exchange_total / binance_total)

                    # 深度比值
                    depth_ratios.append(float(row[6]))

                # 计算统计数据
                results[symbol] = {
                    'bid_ask_ratio': {
                        'avg': round(sum(bid_ask_ratios) / len(bid_ask_ratios), 2) if bid_ask_ratios else 0,
                        'min': round(min(bid_ask_ratios), 2) if bid_ask_ratios else 0,
                        'max': round(max(bid_ask_ratios), 2) if bid_ask_ratios else 0,
                        'latest': round(bid_ask_ratios[0], 2) if bid_ask_ratios else 0
                    },
                    'bid_ratio': {
                        'avg': round(sum(bid_ratios) / len(bid_ratios), 2) if bid_ratios else 0,
                        'min': round(min(bid_ratios), 2) if bid_ratios else 0,
                        'max': round(max(bid_ratios), 2) if bid_ratios else 0,
                        'latest': round(bid_ratios[0], 2) if bid_ratios else 0
                    },
                    'ask_ratio': {
                        'avg': round(sum(ask_ratios) / len(ask_ratios), 2) if ask_ratios else 0,
                        'min': round(min(ask_ratios), 2) if ask_ratios else 0,
                        'max': round(max(ask_ratios), 2) if ask_ratios else 0,
                        'latest': round(ask_ratios[0], 2) if ask_ratios else 0
                    },
                    'depth_ratio': {
                        'avg': round(sum(depth_ratios) / len(depth_ratios), 4) if depth_ratios else 0,
                        'min': round(min(depth_ratios), 4) if depth_ratios else 0,
                        'max': round(max(depth_ratios), 4) if depth_ratios else 0,
                        'latest': round(depth_ratios[0], 4) if depth_ratios else 0
                    },
                    'sample_count': len(data),
                    'analysis_time': datetime.now().isoformat()
                }

                logger.info(f"{symbol}深度对比分析完成，样本数: {len(data)}")

            return {
                'results': results,
                'time_range': f"{start_time.strftime('%Y-%m-%d %H:%M')} - {end_time.strftime('%Y-%m-%d %H:%M')}",
                'analysis_time': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"深度对比分析失败: {e}")
            return {
                'results': {symbol: self._empty_depth_result() for symbol in self.symbols},
                'error': str(e),
                'analysis_time': datetime.now().isoformat()
            }

    def _empty_depth_result(self) -> Dict:
        """返回空的深度分析结果"""
        return {
            'bid_ask_ratio': {'avg': 0, 'min': 0, 'max': 0, 'latest': 0},
            'bid_ratio': {'avg': 0, 'min': 0, 'max': 0, 'latest': 0},
            'ask_ratio': {'avg': 0, 'min': 0, 'max': 0, 'latest': 0},
            'depth_ratio': {'avg': 0, 'min': 0, 'max': 0, 'latest': 0},
            'sample_count': 0,
            'analysis_time': datetime.now().isoformat()
        }

    def get_latest_depth_comparison(self) -> Dict:
        """获取最新的深度对比数据"""
        try:
            results = {}

            for symbol in self.symbols:
                # 查询最新的深度匹配数据
                query = """
                SELECT
                    binance_bid_qty,
                    binance_ask_qty,
                    exchange_bid_qty,
                    exchange_ask_qty,
                    depth_ratio,
                    timestamp
                FROM depth_matches
                WHERE symbol = %s
                ORDER BY timestamp DESC
                LIMIT 1
                """

                data = db_manager.execute_query(query, (symbol,), fetch=True)

                if data:
                    row = data[0]
                    binance_bid_qty = float(row[0])
                    binance_ask_qty = float(row[1])
                    exchange_bid_qty = float(row[2])
                    exchange_ask_qty = float(row[3])

                    # 计算各种比值
                    binance_total = binance_bid_qty + binance_ask_qty
                    exchange_total = exchange_bid_qty + exchange_ask_qty

                    results[symbol] = {
                        'bitda_bid_ask_sum': round(exchange_total, 4),
                        'binance_bid_ask_sum': round(binance_total, 4),
                        'bid_ask_ratio': round(exchange_total / binance_total, 2) if binance_total > 0 else 0,
                        'bitda_bid_qty': round(exchange_bid_qty, 4),
                        'binance_bid_qty': round(binance_bid_qty, 4),
                        'bid_ratio': round(exchange_bid_qty / binance_bid_qty, 2) if binance_bid_qty > 0 else 0,
                        'bitda_ask_qty': round(exchange_ask_qty, 4),
                        'binance_ask_qty': round(binance_ask_qty, 4),
                        'ask_ratio': round(exchange_ask_qty / binance_ask_qty, 2) if binance_ask_qty > 0 else 0,
                        'timestamp': row[5]
                    }
                else:
                    results[symbol] = {
                        'bitda_bid_ask_sum': 0,
                        'binance_bid_ask_sum': 0,
                        'bid_ask_ratio': 0,
                        'bitda_bid_qty': 0,
                        'binance_bid_qty': 0,
                        'bid_ratio': 0,
                        'bitda_ask_qty': 0,
                        'binance_ask_qty': 0,
                        'ask_ratio': 0,
                        'timestamp': None
                    }

            return results

        except Exception as e:
            logger.error(f"获取最新深度对比数据失败: {e}")
            return {symbol: {} for symbol in self.symbols}




