#!/usr/bin/env python3
"""
延时分析器
分析ETHUSDT价格相同的延时和统计成功次数
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import pandas as pd
from utils.db import db_manager
from utils.logging import setup_logger

logger = setup_logger(__name__)

class LatencyAnalyzer:
    """延时分析器"""

    def __init__(self):
        self.symbol = 'ETHUSDT'

    async def analyze_price_latency(self, hours: int = 1) -> Dict:
        """
        分析价格延时

        Args:
            hours: 分析时间范围（小时）

        Returns:
            延时分析结果
        """
        try:
            # 获取指定时间范围内的数据
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)

            # 查询Bitda数据
            bitda_query = """
            SELECT last_price, created_at
            FROM bitda_ticker
            WHERE symbol = %s AND created_at >= %s AND created_at <= %s
            ORDER BY created_at
            """

            # 查询Binance数据
            binance_query = """
            SELECT (bid_price + ask_price) / 2 as mid_price, created_at
            FROM binance_bookticker
            WHERE symbol = %s AND created_at >= %s AND created_at <= %s
            ORDER BY created_at
            """

            bitda_data = db_manager.execute_query(
                bitda_query, (self.symbol, start_time, end_time), fetch=True
            )

            binance_data = db_manager.execute_query(
                binance_query, (self.symbol, start_time, end_time), fetch=True
            )

            if not bitda_data or not binance_data:
                logger.warning(f"没有找到{self.symbol}的数据")
                return self._empty_result()

            # 转换为DataFrame进行分析
            bitda_df = pd.DataFrame(bitda_data, columns=['price', 'timestamp'])
            binance_df = pd.DataFrame(binance_data, columns=['price', 'timestamp'])

            # 分析价格匹配和延时
            matches = self._find_price_matches(bitda_df, binance_df)

            # 计算统计数据
            stats = self._calculate_latency_stats(matches)

            logger.info(f"延时分析完成，匹配次数: {len(matches)}")
            return stats

        except Exception as e:
            logger.error(f"延时分析失败: {e}")
            return self._empty_result()

    def _find_price_matches(self, bitda_df: pd.DataFrame, binance_df: pd.DataFrame,
                           tolerance: float = 0.01) -> List[Dict]:
        """
        查找价格匹配

        Args:
            bitda_df: Bitda价格数据
            binance_df: Binance价格数据
            tolerance: 价格容差（百分比）

        Returns:
            匹配结果列表
        """
        matches = []

        for _, bitda_row in bitda_df.iterrows():
            bitda_price = float(bitda_row['price'])
            bitda_time = bitda_row['timestamp']

            # 在Binance数据中查找相近价格
            for _, binance_row in binance_df.iterrows():
                binance_price = float(binance_row['price'])
                binance_time = binance_row['timestamp']

                # 计算价格差异百分比
                price_diff = abs(bitda_price - binance_price) / binance_price * 100

                if price_diff <= tolerance:
                    # 计算时间延迟（毫秒）
                    time_diff = abs((bitda_time - binance_time).total_seconds() * 1000)

                    matches.append({
                        'bitda_price': bitda_price,
                        'binance_price': binance_price,
                        'price_diff_pct': price_diff,
                        'latency_ms': time_diff,
                        'bitda_time': bitda_time,
                        'binance_time': binance_time
                    })
                    break  # 找到匹配后跳出内层循环

        return matches

    def _calculate_latency_stats(self, matches: List[Dict]) -> Dict:
        """计算延时统计数据"""
        if not matches:
            return self._empty_result()

        latencies = [match['latency_ms'] for match in matches]
        price_diffs = [match['price_diff_pct'] for match in matches]

        return {
            'total_matches': len(matches),
            'avg_latency_ms': sum(latencies) / len(latencies),
            'min_latency_ms': min(latencies),
            'max_latency_ms': max(latencies),
            'avg_price_diff_pct': sum(price_diffs) / len(price_diffs),
            'success_rate': len(matches),  # 成功匹配次数
            'analysis_time': datetime.now().isoformat(),
            'matches': matches[:10]  # 返回前10个匹配示例
        }

    def _empty_result(self) -> Dict:
        """返回空结果"""
        return {
            'total_matches': 0,
            'avg_latency_ms': 0,
            'min_latency_ms': 0,
            'max_latency_ms': 0,
            'avg_price_diff_pct': 0,
            'success_rate': 0,
            'analysis_time': datetime.now().isoformat(),
            'matches': []
        }

    async def get_realtime_latency(self) -> Dict:
        """获取实时延时数据"""
        return await self.analyze_price_latency(hours=1)

    async def get_daily_latency_summary(self) -> Dict:
        """获取日延时汇总"""
        return await self.analyze_price_latency(hours=24)

    def analyze_ethusdt_latency(self, hours: int = 1) -> Dict:
        """
        分析ETHUSDT延时（同步版本）

        Args:
            hours: 分析时间范围（小时）

        Returns:
            延时分析结果
        """
        try:
            # 使用depth_matches表中的数据进行分析
            from datetime import datetime, timedelta

            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)

            # 查询depth_matches表中的ETHUSDT延时数据
            query = """
            SELECT
                message_latency_ms,
                engine_latency_ms,
                depth_ratio,
                timestamp,
                binance_bid_price,
                binance_ask_price,
                exchange_bid_qty,
                exchange_ask_qty
            FROM depth_matches
            WHERE symbol = 'ETHUSDT'
            AND timestamp >= %s
            AND timestamp <= %s
            AND message_latency_ms BETWEEN 10 AND 2000
            ORDER BY timestamp DESC
            """

            data = db_manager.execute_query(
                query, (start_time, end_time), fetch=True
            )

            if not data:
                logger.warning("没有找到ETHUSDT的延时匹配数据")
                return {
                    'match_count': 0,
                    'avg_latency': 0,
                    'min_latency': 0,
                    'max_latency': 0,
                    'median_latency': 0,
                    'latency_distribution': {},
                    'analysis_time': datetime.now().isoformat()
                }

            # 提取延时数据
            latencies = [row[0] for row in data]  # message_latency_ms

            # 计算统计数据
            avg_latency = sum(latencies) / len(latencies)
            min_latency = min(latencies)
            max_latency = max(latencies)

            # 计算中位数
            sorted_latencies = sorted(latencies)
            n = len(sorted_latencies)
            median_latency = (sorted_latencies[n//2] + sorted_latencies[(n-1)//2]) / 2

            # 延时分布统计
            distribution = {
                '10-50ms': len([l for l in latencies if 10 <= l < 50]),
                '50-100ms': len([l for l in latencies if 50 <= l < 100]),
                '100-200ms': len([l for l in latencies if 100 <= l < 200]),
                '200-500ms': len([l for l in latencies if 200 <= l < 500]),
                '500-1000ms': len([l for l in latencies if 500 <= l < 1000]),
                '1000ms+': len([l for l in latencies if l >= 1000])
            }

            result = {
                'match_count': len(data),
                'avg_latency': round(avg_latency, 2),
                'min_latency': min_latency,
                'max_latency': max_latency,
                'median_latency': round(median_latency, 2),
                'latency_distribution': distribution,
                'analysis_time': datetime.now().isoformat(),
                'time_range': f"{start_time.strftime('%Y-%m-%d %H:%M')} - {end_time.strftime('%Y-%m-%d %H:%M')}"
            }

            logger.info(f"ETHUSDT延时分析完成，匹配记录数: {len(data)}, 平均延时: {avg_latency:.2f}ms")
            return result

        except Exception as e:
            logger.error(f"ETHUSDT延时分析失败: {e}")
            return {
                'match_count': 0,
                'avg_latency': 0,
                'min_latency': 0,
                'max_latency': 0,
                'median_latency': 0,
                'latency_distribution': {},
                'analysis_time': datetime.now().isoformat(),
                'error': str(e)
            }
