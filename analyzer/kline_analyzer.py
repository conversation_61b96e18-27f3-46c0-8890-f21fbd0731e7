#!/usr/bin/env python3
"""
K线分析器
分析Bitda的1分钟K线，检测连续相同K线（0插针）
"""

from datetime import datetime, timedelta
from typing import Dict, List, Tuple
import pandas as pd
from utils.db import db_manager
from utils.logging import setup_logger

logger = setup_logger(__name__)

class KlineAnalyzer:
    """K线分析器"""
    
    def __init__(self):
        self.symbols = ['BTCUSDT', 'ETHUSDT']
        self.period = '1min'
        
    async def analyze_consecutive_identical_klines(self, hours: int = 24) -> Dict:
        """
        分析连续相同K线
        
        Args:
            hours: 分析时间范围（小时）
            
        Returns:
            分析结果
        """
        try:
            results = {}
            
            for symbol in self.symbols:
                logger.info(f"分析{symbol}的K线数据...")
                
                # 获取K线数据
                kline_data = await self._get_kline_data(symbol, hours)
                
                if not kline_data:
                    logger.warning(f"没有找到{symbol}的K线数据")
                    results[symbol] = self._empty_kline_result()
                    continue
                
                # 检测连续相同K线
                identical_sequences = self._find_identical_sequences(kline_data)
                
                # 计算统计数据
                stats = self._calculate_kline_stats(identical_sequences, len(kline_data))
                
                results[symbol] = stats
                logger.info(f"{symbol}分析完成，发现{len(identical_sequences)}个连续相同K线序列")
            
            return {
                'analysis_time': datetime.now().isoformat(),
                'period': self.period,
                'hours_analyzed': hours,
                'results': results
            }
            
        except Exception as e:
            logger.error(f"K线分析失败: {e}")
            return self._empty_analysis_result()
    
    async def _get_kline_data(self, symbol: str, hours: int) -> List[Dict]:
        """获取K线数据"""
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)
        
        query = """
        SELECT timestamp, open_price, high_price, low_price, close_price, volume
        FROM bitda_kline 
        WHERE symbol = %s AND period = %s 
        AND created_at >= %s AND created_at <= %s
        ORDER BY timestamp
        """
        
        data = db_manager.execute_query(
            query, (symbol, self.period, start_time, end_time), fetch=True
        )
        
        if not data:
            return []
            
        return [
            {
                'timestamp': row[0],
                'open': float(row[1]),
                'high': float(row[2]),
                'low': float(row[3]),
                'close': float(row[4]),
                'volume': float(row[5])
            }
            for row in data
        ]
    
    def _find_identical_sequences(self, kline_data: List[Dict]) -> List[Dict]:
        """
        查找连续相同的K线序列
        
        Args:
            kline_data: K线数据列表
            
        Returns:
            连续相同K线序列列表
        """
        if len(kline_data) < 2:
            return []
            
        identical_sequences = []
        current_sequence = []
        
        for i in range(len(kline_data) - 1):
            current_kline = kline_data[i]
            next_kline = kline_data[i + 1]
            
            # 检查是否完全相同（OHLC）
            if self._are_klines_identical(current_kline, next_kline):
                if not current_sequence:
                    current_sequence = [current_kline]
                current_sequence.append(next_kline)
            else:
                # 序列结束
                if len(current_sequence) >= 2:
                    identical_sequences.append({
                        'start_time': current_sequence[0]['timestamp'],
                        'end_time': current_sequence[-1]['timestamp'],
                        'count': len(current_sequence),
                        'ohlc': {
                            'open': current_sequence[0]['open'],
                            'high': current_sequence[0]['high'],
                            'low': current_sequence[0]['low'],
                            'close': current_sequence[0]['close']
                        },
                        'total_volume': sum(k['volume'] for k in current_sequence)
                    })
                current_sequence = []
        
        # 处理最后一个序列
        if len(current_sequence) >= 2:
            identical_sequences.append({
                'start_time': current_sequence[0]['timestamp'],
                'end_time': current_sequence[-1]['timestamp'],
                'count': len(current_sequence),
                'ohlc': {
                    'open': current_sequence[0]['open'],
                    'high': current_sequence[0]['high'],
                    'low': current_sequence[0]['low'],
                    'close': current_sequence[0]['close']
                },
                'total_volume': sum(k['volume'] for k in current_sequence)
            })
        
        return identical_sequences
    
    def _are_klines_identical(self, kline1: Dict, kline2: Dict, tolerance: float = 1e-8) -> bool:
        """
        检查两根K线是否完全相同
        
        Args:
            kline1: 第一根K线
            kline2: 第二根K线
            tolerance: 价格容差
            
        Returns:
            是否相同
        """
        return (
            abs(kline1['open'] - kline2['open']) < tolerance and
            abs(kline1['high'] - kline2['high']) < tolerance and
            abs(kline1['low'] - kline2['low']) < tolerance and
            abs(kline1['close'] - kline2['close']) < tolerance
        )
    
    def _calculate_kline_stats(self, identical_sequences: List[Dict], total_klines: int) -> Dict:
        """计算K线统计数据"""
        if not identical_sequences:
            return self._empty_kline_result()
        
        # 计算统计数据
        sequence_lengths = [seq['count'] for seq in identical_sequences]
        total_identical_klines = sum(sequence_lengths)
        
        return {
            'total_sequences': len(identical_sequences),
            'total_identical_klines': total_identical_klines,
            'total_klines_analyzed': total_klines,
            'identical_rate_pct': (total_identical_klines / total_klines * 100) if total_klines > 0 else 0,
            'avg_sequence_length': sum(sequence_lengths) / len(sequence_lengths),
            'max_sequence_length': max(sequence_lengths),
            'min_sequence_length': min(sequence_lengths),
            'sequences': identical_sequences[:5]  # 返回前5个序列作为示例
        }
    
    def _empty_kline_result(self) -> Dict:
        """返回空的K线结果"""
        return {
            'total_sequences': 0,
            'total_identical_klines': 0,
            'total_klines_analyzed': 0,
            'identical_rate_pct': 0,
            'avg_sequence_length': 0,
            'max_sequence_length': 0,
            'min_sequence_length': 0,
            'sequences': []
        }
    
    def _empty_analysis_result(self) -> Dict:
        """返回空的分析结果"""
        return {
            'analysis_time': datetime.now().isoformat(),
            'period': self.period,
            'hours_analyzed': 0,
            'results': {symbol: self._empty_kline_result() for symbol in self.symbols}
        }
    
    async def get_realtime_kline_analysis(self) -> Dict:
        """获取实时K线分析（最近1小时）"""
        return await self.analyze_consecutive_identical_klines(hours=1)
    
    async def get_daily_kline_summary(self) -> Dict:
        """获取日K线汇总（最近24小时）"""
        return await self.analyze_consecutive_identical_klines(hours=24)
