#!/usr/bin/env python3
"""
资金费率分析器
分析和展示BTC/ETH的资金费率数据
"""

from datetime import datetime, timedelta
from typing import Dict, List, Tuple
import pandas as pd
from utils.db import db_manager
from utils.logging import setup_logger

logger = setup_logger(__name__)

class FundingAnalyzer:
    """资金费率分析器"""

    def __init__(self):
        self.symbols = ['BTCUSDT', 'ETHUSDT']
        # 资金费率收取时间（UTC）：00:00, 08:00, 16:00
        self.funding_times = ['00:00', '08:00', '16:00']

    async def analyze_funding_rates(self, days: int = 1) -> Dict:
        """
        分析资金费率

        Args:
            days: 分析天数

        Returns:
            资金费率分析结果
        """
        try:
            results = {}

            for symbol in self.symbols:
                logger.info(f"分析{symbol}的资金费率...")

                # 获取资金费率数据
                funding_data = await self._get_funding_data(symbol, days)

                if not funding_data:
                    logger.warning(f"没有找到{symbol}的资金费率数据")
                    results[symbol] = self._empty_funding_result()
                    continue

                # 计算资金费率统计
                stats = self._calculate_funding_stats(funding_data)
                results[symbol] = stats

                logger.info(f"{symbol}资金费率分析完成，数据点: {len(funding_data)}")

            return {
                'analysis_time': datetime.now().isoformat(),
                'days_analyzed': days,
                'funding_times': self.funding_times,
                'results': results
            }

        except Exception as e:
            logger.error(f"资金费率分析失败: {e}")
            return self._empty_analysis_result()

    async def _get_funding_data(self, symbol: str, days: int) -> List[Dict]:
        """
        获取资金费率数据

        Args:
            symbol: 交易对
            days: 天数

        Returns:
            资金费率数据列表
        """
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)

        query = """
        SELECT funding_rate_last, funding_rate_next, funding_rate_predict,
               funding_time, created_at
        FROM bitda_ticker
        WHERE symbol = %s AND created_at >= %s AND created_at <= %s
        AND funding_rate_last IS NOT NULL
        ORDER BY created_at
        """

        data = db_manager.execute_query(
            query, (symbol, start_time, end_time), fetch=True
        )

        if not data:
            return []

        funding_data = []
        for row in data:
            funding_last, funding_next, funding_predict, funding_time, created_at = row

            funding_data.append({
                'timestamp': created_at,
                'funding_rate_last': float(funding_last) if funding_last else 0,
                'funding_rate_next': float(funding_next) if funding_next else 0,
                'funding_rate_predict': float(funding_predict) if funding_predict else 0,
                'funding_time': funding_time,
                'hour': created_at.hour
            })

        return funding_data

    def _calculate_funding_stats(self, funding_data: List[Dict]) -> Dict:
        """
        计算资金费率统计数据

        Args:
            funding_data: 资金费率数据列表

        Returns:
            统计结果
        """
        if not funding_data:
            return self._empty_funding_result()

        # 提取费率数据
        last_rates = [data['funding_rate_last'] for data in funding_data]
        next_rates = [data['funding_rate_next'] for data in funding_data if data['funding_rate_next'] != 0]
        predict_rates = [data['funding_rate_predict'] for data in funding_data if data['funding_rate_predict'] != 0]

        # 计算基本统计
        stats = {
            'total_records': len(funding_data),
            'current_funding_rate': last_rates[-1] if last_rates else 0,
            'avg_funding_rate': sum(last_rates) / len(last_rates) if last_rates else 0,
            'max_funding_rate': max(last_rates) if last_rates else 0,
            'min_funding_rate': min(last_rates) if last_rates else 0,
            'funding_rate_volatility': self._calculate_volatility(last_rates),
        }

        # 按时间段分析
        time_period_stats = self._analyze_by_time_periods(funding_data)
        stats.update(time_period_stats)

        # 趋势分析
        trend_analysis = self._analyze_funding_trend(funding_data)
        stats.update(trend_analysis)

        # 费率差值分析
        if next_rates:
            rate_differences = [abs(funding_data[i]['funding_rate_last'] - funding_data[i]['funding_rate_next'])
                              for i in range(len(funding_data)) if funding_data[i]['funding_rate_next'] != 0]
            stats['avg_rate_difference'] = sum(rate_differences) / len(rate_differences) if rate_differences else 0
            stats['max_rate_difference'] = max(rate_differences) if rate_differences else 0
        else:
            stats['avg_rate_difference'] = 0
            stats['max_rate_difference'] = 0

        # 最近的样本数据
        stats['recent_samples'] = funding_data[-5:] if len(funding_data) >= 5 else funding_data

        return stats

    def _calculate_volatility(self, rates: List[float]) -> float:
        """计算费率波动性"""
        if len(rates) < 2:
            return 0

        mean_rate = sum(rates) / len(rates)
        variance = sum((rate - mean_rate) ** 2 for rate in rates) / len(rates)
        return variance ** 0.5

    def _analyze_by_time_periods(self, funding_data: List[Dict]) -> Dict:
        """按时间段分析资金费率"""
        # 按小时分组
        hourly_rates = {}
        for data in funding_data:
            hour = data['hour']
            if hour not in hourly_rates:
                hourly_rates[hour] = []
            hourly_rates[hour].append(data['funding_rate_last'])

        # 计算各时间段的平均费率
        period_stats = {}
        for hour, rates in hourly_rates.items():
            if rates:
                period_stats[f'hour_{hour:02d}_avg'] = sum(rates) / len(rates)
                period_stats[f'hour_{hour:02d}_count'] = len(rates)

        # 识别资金费率收取时间的特殊情况
        funding_hour_stats = {}
        for time_str in self.funding_times:
            hour = int(time_str.split(':')[0])
            if hour in hourly_rates and hourly_rates[hour]:
                funding_hour_stats[f'funding_{time_str}_avg'] = sum(hourly_rates[hour]) / len(hourly_rates[hour])
                funding_hour_stats[f'funding_{time_str}_count'] = len(hourly_rates[hour])

        return {
            'hourly_distribution': period_stats,
            'funding_periods': funding_hour_stats
        }

    def _analyze_funding_trend(self, funding_data: List[Dict]) -> Dict:
        """分析资金费率趋势"""
        if len(funding_data) < 10:
            return {'trend_direction': 'insufficient_data', 'trend_strength': 0}

        # 计算移动平均
        window_size = min(10, len(funding_data) // 3)
        recent_avg = sum(data['funding_rate_last'] for data in funding_data[-window_size:]) / window_size
        early_avg = sum(data['funding_rate_last'] for data in funding_data[:window_size]) / window_size

        # 判断趋势方向
        trend_diff = recent_avg - early_avg
        trend_strength = abs(trend_diff) / early_avg * 100 if early_avg != 0 else 0

        if trend_diff > 0.0001:  # 0.01%
            trend_direction = 'increasing'
        elif trend_diff < -0.0001:
            trend_direction = 'decreasing'
        else:
            trend_direction = 'stable'

        return {
            'trend_direction': trend_direction,
            'trend_strength_pct': trend_strength,
            'recent_avg_rate': recent_avg,
            'early_avg_rate': early_avg,
            'trend_difference': trend_diff
        }

    def _empty_funding_result(self) -> Dict:
        """返回空的资金费率结果"""
        return {
            'total_records': 0,
            'current_funding_rate': 0,
            'avg_funding_rate': 0,
            'max_funding_rate': 0,
            'min_funding_rate': 0,
            'funding_rate_volatility': 0,
            'avg_rate_difference': 0,
            'max_rate_difference': 0,
            'hourly_distribution': {},
            'funding_periods': {},
            'trend_direction': 'unknown',
            'trend_strength_pct': 0,
            'recent_samples': []
        }

    def _empty_analysis_result(self) -> Dict:
        """返回空的分析结果"""
        return {
            'analysis_time': datetime.now().isoformat(),
            'days_analyzed': 0,
            'funding_times': self.funding_times,
            'results': {symbol: self._empty_funding_result() for symbol in self.symbols}
        }

    async def get_current_funding_rates(self) -> Dict:
        """获取当前资金费率"""
        return await self.analyze_funding_rates(days=1)

    async def get_weekly_funding_summary(self) -> Dict:
        """获取周资金费率汇总"""
        return await self.analyze_funding_rates(days=7)

    async def compare_funding_rates(self) -> Dict:
        """比较BTC和ETH的资金费率差异"""
        try:
            btc_data = await self.analyze_funding_rates(days=1)

            btc_rate = btc_data['results']['BTCUSDT']['current_funding_rate']
            eth_rate = btc_data['results']['ETHUSDT']['current_funding_rate']

            rate_diff = abs(btc_rate - eth_rate)
            rate_diff_pct = (rate_diff / max(abs(btc_rate), abs(eth_rate)) * 100) if max(abs(btc_rate), abs(eth_rate)) > 0 else 0

            return {
                'btc_funding_rate': btc_rate,
                'eth_funding_rate': eth_rate,
                'rate_difference': rate_diff,
                'rate_difference_pct': rate_diff_pct,
                'higher_rate_symbol': 'BTCUSDT' if btc_rate > eth_rate else 'ETHUSDT',
                'analysis_time': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"资金费率对比失败: {e}")
            return {}

    def get_latest_funding_rates(self) -> Dict:
        """
        获取最新的资金费率数据（同步版本）

        Returns:
            最新资金费率数据
        """
        try:
            results = {}

            for symbol in self.symbols:
                # 查询最新的资金费率数据
                query = """
                SELECT
                    funding_rate_last,
                    funding_rate_next,
                    funding_rate_predict,
                    funding_time,
                    created_at
                FROM bitda_ticker
                WHERE symbol = %s
                AND funding_rate_last IS NOT NULL
                ORDER BY created_at DESC
                LIMIT 1
                """

                data = db_manager.execute_query(query, (symbol,), fetch=True)

                if data:
                    row = data[0]
                    results[symbol] = {
                        'current_rate': float(row[0]) if row[0] else 0,
                        'next_rate': float(row[1]) if row[1] else 0,
                        'predict_rate': float(row[2]) if row[2] else 0,
                        'funding_time': row[3],
                        'timestamp': row[4]
                    }
                else:
                    results[symbol] = {
                        'current_rate': 0,
                        'next_rate': 0,
                        'predict_rate': 0,
                        'funding_time': None,
                        'timestamp': None
                    }

            return results

        except Exception as e:
            logger.error(f"获取最新资金费率数据失败: {e}")
            return {symbol: {} for symbol in self.symbols}

    def get_funding_rate_table_data(self) -> Dict:
        """
        获取资金费率表格数据，用于Grafana展示

        Returns:
            格式化的资金费率表格数据
        """
        try:
            # 获取最新的资金费率数据
            latest_rates = self.get_latest_funding_rates()

            # 构建表格数据
            table_data = {
                'headers': ['交易对', 'Bitda当前费率', 'Binance费率', '费率差值'],
                'rows': []
            }

            for symbol in self.symbols:
                if symbol in latest_rates:
                    bitda_rate = latest_rates[symbol]['current_rate'] * 100  # 转换为百分比

                    # 这里可以添加Binance费率的查询逻辑
                    # 目前先使用模拟数据
                    binance_rate = 0.01  # 模拟Binance费率

                    rate_diff = bitda_rate - binance_rate

                    table_data['rows'].append({
                        'symbol': symbol,
                        'bitda_rate': f"{bitda_rate:.5f}%",
                        'binance_rate': f"{binance_rate:.5f}%",
                        'rate_diff': f"{rate_diff:.5f}%"
                    })

            return table_data

        except Exception as e:
            logger.error(f"获取资金费率表格数据失败: {e}")
            return {'headers': [], 'rows': []}
