#!/usr/bin/env python3
"""
真实数据详细报告生成器
"""

import requests
import json
from datetime import datetime
import os

class RealDataReporter:
    """真实数据报告生成器"""
    
    def __init__(self):
        self.api_base = "http://localhost:8001"
        
    def get_all_real_data(self):
        """获取所有真实数据"""
        try:
            response = requests.get(f"{self.api_base}/api/real-all-data", timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"API返回错误: {response.status_code}"}
        except Exception as e:
            return {"error": f"无法连接到数据API: {e}"}
    
    def generate_markdown_report(self, data):
        """生成Markdown格式的详细报告"""
        
        if "error" in data:
            return f"""
# ❌ 真实数据获取失败

**错误信息**: {data['error']}
**时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

请检查数据导出器是否正在运行: `python real_data_exporter.py`
"""
        
        # 提取数据
        funding_data = data.get('funding_rates', {}).get('data', {})
        latency_data = data.get('latency_analysis', {})
        depth_data = data.get('depth_comparison', {}).get('data', {})
        price_data = data.get('price_deviation', {}).get('data', {})
        metadata = data.get('metadata', {})
        
        report = f"""
# 🎯 真实加密货币数据分析报告

**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**数据源**: {metadata.get('data_source', 'MySQL Database')}  
**数据表**: {', '.join(metadata.get('tables', []))}  
**交易所**: {', '.join(metadata.get('exchanges', []))}  
**交易对**: {', '.join(metadata.get('symbols', []))}  

---

## 💰 资金费率数据 (真实数据)

### 📊 当前费率
"""
        
        if funding_data:
            for symbol, info in funding_data.items():
                current_rate = info.get('current_rate', 0)
                next_rate = info.get('next_rate', 0)
                predict_rate = info.get('predict_rate', 0)
                data_time = info.get('data_time', 'Unknown')
                
                # 判断费率状态
                if current_rate > 0:
                    status = "🔴 多头支付空头"
                    color = "red"
                elif current_rate < 0:
                    status = "🟢 空头支付多头"
                    color = "green"
                else:
                    status = "⚪ 费率为零"
                    color = "gray"
                
                report += f"""
#### {symbol} (Bitda交易所)
- **当前费率**: `{current_rate*100:.5f}%` {status}
- **下期费率**: `{next_rate*100:.5f}%`
- **预测费率**: `{predict_rate*100:.5f}%`
- **数据时间**: {data_time}
- **费率状态**: <span style="color:{color}">**{status}**</span>
"""
        else:
            report += "\n⚠️ 暂无资金费率数据\n"
        
        # 延时分析
        report += f"""
---

## ⚡ ETHUSDT延时分析 (真实数据)
"""
        
        if latency_data and 'statistics' in latency_data:
            stats = latency_data['statistics']
            latest_records = latency_data.get('latest_records', [])
            
            report += f"""
### 📈 延时统计 (最近1小时)
- **总匹配次数**: {stats.get('total_matches', 0)}
- **平均延时**: {stats.get('avg_latency_ms', 0):.2f} ms
- **最小延时**: {stats.get('min_latency_ms', 0)} ms
- **最大延时**: {stats.get('max_latency_ms', 0)} ms
- **中位数延时**: {stats.get('median_latency_ms', 0):.2f} ms

### 🔍 最新匹配记录
| 时间 | 消息延时(ms) | 引擎延时(ms) | 深度比值 |
|------|-------------|-------------|----------|
"""
            
            for record in latest_records[:5]:  # 只显示前5条
                timestamp = record.get('timestamp', '')[:19]  # 只取日期时间部分
                msg_latency = record.get('message_latency_ms', 0)
                engine_latency = record.get('engine_latency_ms', 0)
                depth_ratio = record.get('depth_ratio', 0)
                
                report += f"| {timestamp} | {msg_latency} | {engine_latency} | {depth_ratio:.4f} |\n"
        else:
            if 'error' in latency_data:
                report += f"\n⚠️ {latency_data['error']}\n"
            else:
                report += "\n⚠️ 暂无延时分析数据\n"
        
        # 深度对比
        report += f"""
---

## 📊 深度对比分析 (真实数据)
"""
        
        if depth_data:
            report += """
### 🏢 Bitda vs Binance 深度对比

| 交易对 | Bitda买一量 | Bitda卖一量 | Binance买一量 | Binance卖一量 | 买一比值 | 卖一比值 | 总量比值 |
|--------|-------------|-------------|---------------|---------------|----------|----------|----------|
"""
            
            for symbol, info in depth_data.items():
                bitda_bid = info.get('bitda_bid_qty', 0)
                bitda_ask = info.get('bitda_ask_qty', 0)
                binance_bid = info.get('binance_bid_qty', 0)
                binance_ask = info.get('binance_ask_qty', 0)
                bid_ratio = info.get('bid_ratio', 0)
                ask_ratio = info.get('ask_ratio', 0)
                total_ratio = info.get('total_ratio', 0)
                data_time = info.get('data_time', '')
                
                report += f"| {symbol} | {bitda_bid:.2f} | {bitda_ask:.2f} | {binance_bid:.2f} | {binance_ask:.2f} | {bid_ratio:.2f} | {ask_ratio:.2f} | {total_ratio:.2f} |\n"
            
            report += f"\n**数据时间**: {depth_data.get(list(depth_data.keys())[0], {}).get('data_time', 'Unknown')}\n"
        else:
            report += "\n⚠️ 暂无深度对比数据\n"
        
        # 价格偏差
        report += f"""
---

## 📈 标记价格偏差分析 (真实数据)
"""
        
        if price_data:
            report += """
### 💲 标记价格 vs 最新价格偏差

| 交易对 | 最新价格 | 标记价格 | 偏差百分比 | 样本数量 | 平均偏差 | 最大偏差 | 最小偏差 |
|--------|----------|----------|------------|----------|----------|----------|----------|
"""
            
            for symbol, info in price_data.items():
                latest_last = info.get('latest_last_price', 0)
                latest_sign = info.get('latest_sign_price', 0)
                latest_dev = info.get('latest_deviation_percent', '0%')
                stats = info.get('statistics', {})
                sample_count = stats.get('sample_count', 0)
                avg_dev = stats.get('avg_deviation_percent', '0%')
                max_dev = stats.get('max_deviation_percent', '0%')
                min_dev = stats.get('min_deviation_percent', '0%')
                data_time = info.get('data_time', '')
                
                report += f"| {symbol} | ${latest_last:.2f} | ${latest_sign:.2f} | {latest_dev} | {sample_count} | {avg_dev} | {max_dev} | {min_dev} |\n"
            
            report += f"\n**数据时间**: {price_data.get(list(price_data.keys())[0], {}).get('data_time', 'Unknown')}\n"
        else:
            report += "\n⚠️ 暂无价格偏差数据\n"
        
        # 总结
        report += f"""
---

## 📋 数据总结

### ✅ 数据完整性检查
- **资金费率数据**: {'✅ 正常' if funding_data else '❌ 缺失'}
- **延时分析数据**: {'✅ 正常' if latency_data and 'statistics' in latency_data else '❌ 缺失'}
- **深度对比数据**: {'✅ 正常' if depth_data else '❌ 缺失'}
- **价格偏差数据**: {'✅ 正常' if price_data else '❌ 缺失'}

### 🔗 相关链接
- **真实数据API**: http://localhost:8001/api/real-all-data
- **Grafana仪表板**: http://localhost:3000
- **资金费率API**: http://localhost:8001/api/real-funding-rates
- **延时分析API**: http://localhost:8001/api/real-latency
- **深度对比API**: http://localhost:8001/api/real-depth-comparison
- **价格偏差API**: http://localhost:8001/api/real-price-deviation

### 📝 说明
- 所有数据均来自您的MySQL数据库 (depth_db)
- 数据包含具体的时间戳和交易所信息
- 资金费率来自Bitda交易所的真实数据
- 延时分析基于Bitda与Binance的实际匹配记录
- 深度对比显示两个交易所的真实买卖盘数据
- 价格偏差分析基于Bitda的标记价格与最新价格

---

**报告生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**数据来源**: 真实交易数据 (非模拟)
"""
        
        return report
    
    def save_report(self, report, filename="real_data_report.md"):
        """保存报告到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"✅ 报告已保存到: {filename}")
            return True
        except Exception as e:
            print(f"❌ 保存报告失败: {e}")
            return False
    
    def generate_and_save_report(self):
        """生成并保存完整报告"""
        print("🎯 开始生成真实数据报告...")
        
        # 获取数据
        data = self.get_all_real_data()
        
        # 生成报告
        report = self.generate_markdown_report(data)
        
        # 保存报告
        filename = f"real_data_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        if self.save_report(report, filename):
            print(f"📊 真实数据报告生成完成: {filename}")
            
            # 同时保存一个最新版本
            self.save_report(report, "latest_real_data_report.md")
            
            return filename
        
        return None

def main():
    """主函数"""
    reporter = RealDataReporter()
    
    print("🎯 真实数据报告生成器")
    print("=" * 50)
    
    filename = reporter.generate_and_save_report()
    
    if filename:
        print("\n✅ 报告生成成功！")
        print(f"📄 文件名: {filename}")
        print("📄 最新版本: latest_real_data_report.md")
        print("\n📊 报告包含:")
        print("  - 真实资金费率数据 (Bitda)")
        print("  - ETHUSDT延时分析 (真实匹配记录)")
        print("  - 深度对比分析 (Bitda vs Binance)")
        print("  - 标记价格偏差分析")
        print("  - 完整的数据源信息和时间戳")
        
        # 显示文件内容预览
        try:
            with open("latest_real_data_report.md", 'r', encoding='utf-8') as f:
                content = f.read()
                print("\n" + "=" * 50)
                print("📋 报告预览 (前500字符):")
                print("=" * 50)
                print(content[:500] + "..." if len(content) > 500 else content)
        except:
            pass
    else:
        print("\n❌ 报告生成失败")

if __name__ == "__main__":
    main()
