#!/bin/bash

# WebSocket数据收集器服务卸载脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 卸载服务
uninstall_service() {
    log_step "卸载WebSocket数据收集器服务..."
    
    # 停止服务
    if sudo systemctl is-active --quiet ws-data-collector.service; then
        log_info "停止服务..."
        sudo systemctl stop ws-data-collector.service
    fi
    
    # 禁用服务
    if sudo systemctl is-enabled --quiet ws-data-collector.service; then
        log_info "禁用开机自启动..."
        sudo systemctl disable ws-data-collector.service
    fi
    
    # 删除服务文件
    if [[ -f /etc/systemd/system/ws-data-collector.service ]]; then
        log_info "删除服务文件..."
        sudo rm /etc/systemd/system/ws-data-collector.service
    fi
    
    # 重新加载systemd
    sudo systemctl daemon-reload
    sudo systemctl reset-failed
    
    log_info "服务卸载完成"
}

# 清理进程
cleanup_processes() {
    log_step "清理相关进程..."
    
    # 杀死可能还在运行的进程
    pkill -f ws_data_collector.py || true
    
    log_info "进程清理完成"
}

# 主函数
main() {
    echo "=== WebSocket数据收集器服务卸载程序 ==="
    echo ""
    
    read -p "确定要卸载WebSocket数据收集器服务吗？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "取消卸载"
        exit 0
    fi
    
    uninstall_service
    cleanup_processes
    
    echo ""
    log_info "🎉 服务卸载完成！"
    log_warn "注意: 数据库数据和日志文件未被删除"
}

# 运行主函数
main "$@"
