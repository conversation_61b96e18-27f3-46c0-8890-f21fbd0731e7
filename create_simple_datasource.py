#!/usr/bin/env python3
"""
创建简化的ClickHouse数据源配置
"""

import requests
import json
import time

GRAFANA_URL = "http://localhost:3000"
GRAFANA_USER = "admin"
GRAFANA_PASSWORD = "admin"

def delete_all_datasources():
    """删除所有数据源"""
    print("🗑️ 删除所有现有数据源...")
    
    response = requests.get(
        f"{GRAFANA_URL}/api/datasources",
        auth=(GRAFANA_USER, GRAFANA_PASSWORD)
    )
    
    if response.status_code == 200:
        datasources = response.json()
        for ds in datasources:
            if ds.get('name') != 'TestData DB':  # 保留测试数据源
                delete_response = requests.delete(
                    f"{GRAFANA_URL}/api/datasources/{ds['id']}",
                    auth=(GRAFANA_USER, GRAFANA_PASSWORD)
                )
                print(f"   删除: {ds['name']} - {delete_response.status_code}")

def create_simple_clickhouse_datasource():
    """创建最简化的ClickHouse数据源"""
    print("📊 创建简化ClickHouse数据源...")
    
    # 最简配置
    datasource_config = {
        "name": "ClickHouse-Simple",
        "type": "grafana-clickhouse-datasource",
        "url": "http://localhost:8123",
        "access": "proxy",
        "basicAuth": False,
        "isDefault": True,
        "jsonData": {
            "defaultDatabase": "crypto",
            "username": "default",
            "server": "localhost",
            "port": 8123,
            "protocol": "http"
        },
        "secureJsonData": {
            "password": "Linuxtest"
        }
    }
    
    response = requests.post(
        f"{GRAFANA_URL}/api/datasources",
        json=datasource_config,
        auth=(GRAFANA_USER, GRAFANA_PASSWORD)
    )
    
    print(f"创建响应: {response.status_code}")
    print(f"响应内容: {response.text}")
    if response.status_code == 200:
        result = response.json()
        uid = result.get('uid')
        print(f"✅ 数据源创建成功，UID: {uid}")
        print(f"完整响应: {result}")
        return uid
    else:
        print(f"❌ 创建失败: {response.text}")
        return None

def test_datasource_with_curl(uid):
    """使用curl测试数据源"""
    print("🔍 使用curl测试数据源...")
    
    # 直接测试ClickHouse HTTP接口
    import subprocess
    
    # 测试1: 基本查询
    result1 = subprocess.run([
        'curl', '-s', 
        '***************************************/?query=SELECT%201%20as%20test'
    ], capture_output=True, text=True)
    
    print(f"   基本查询结果: {result1.stdout.strip()}")
    
    # 测试2: 数据库查询
    result2 = subprocess.run([
        'curl', '-s', 
        '***************************************/?query=SELECT%20COUNT(*)%20FROM%20crypto.binance_bookticker%20LIMIT%201'
    ], capture_output=True, text=True)
    
    print(f"   数据查询结果: {result2.stdout.strip()}")
    
    return True

def create_minimal_test_dashboard(uid):
    """创建最小测试仪表板"""
    print("📊 创建最小测试仪表板...")
    
    dashboard = {
        "dashboard": {
            "title": "ClickHouse最小测试",
            "tags": ["test"],
            "timezone": "browser",
            "panels": [
                {
                    "id": 1,
                    "title": "基本测试",
                    "type": "stat",
                    "targets": [
                        {
                            "datasource": {
                                "type": "grafana-clickhouse-datasource",
                                "uid": uid
                            },
                            "rawSql": "SELECT 1 as value",
                            "refId": "A"
                        }
                    ],
                    "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
                }
            ],
            "time": {"from": "now-5m", "to": "now"},
            "refresh": "30s"
        },
        "overwrite": True
    }
    
    response = requests.post(
        f"{GRAFANA_URL}/api/dashboards/db",
        json=dashboard,
        auth=(GRAFANA_USER, GRAFANA_PASSWORD)
    )
    
    if response.status_code == 200:
        result = response.json()
        dashboard_url = f"{GRAFANA_URL}/d/{result['uid']}"
        print(f"✅ 测试仪表板创建成功")
        print(f"🔗 URL: {dashboard_url}")
        return dashboard_url
    else:
        print(f"❌ 仪表板创建失败: {response.status_code} - {response.text}")
        return None

def main():
    """主函数"""
    print("🚀 创建简化ClickHouse配置")
    print("=" * 50)
    
    # 1. 删除现有数据源
    delete_all_datasources()
    time.sleep(2)
    
    # 2. 创建简化数据源
    uid = create_simple_clickhouse_datasource()
    if not uid:
        print("❌ 数据源创建失败")
        return
    
    # 3. 测试连接
    test_datasource_with_curl(uid)
    
    # 4. 创建测试仪表板
    dashboard_url = create_minimal_test_dashboard(uid)
    
    if dashboard_url:
        print(f"\n🎉 配置完成!")
        print(f"📊 测试仪表板: {dashboard_url}")
        print(f"🔧 数据源UID: {uid}")
        print("\n📋 下一步:")
        print("  1. 访问测试仪表板")
        print("  2. 检查是否显示数字 '1'")
        print("  3. 如果成功，确认继续创建完整面板")
    else:
        print("❌ 配置失败")

if __name__ == "__main__":
    main()
