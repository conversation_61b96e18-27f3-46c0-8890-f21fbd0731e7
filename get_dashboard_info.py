#!/usr/bin/env python3
"""
获取Grafana仪表板信息
"""

import requests
import json
from datetime import datetime

def get_dashboard_list():
    """获取仪表板列表"""
    print("📊 Grafana仪表板信息")
    print("=" * 50)
    
    grafana_url = "http://localhost:3000"
    auth = ("admin", "admin")
    
    try:
        # 获取仪表板列表
        response = requests.get(
            f"{grafana_url}/api/search?type=dash-db",
            auth=auth,
            timeout=5
        )
        
        if response.status_code == 200:
            dashboards = response.json()
            
            print(f"🎨 找到 {len(dashboards)} 个仪表板:")
            print()
            
            for i, dashboard in enumerate(dashboards, 1):
                title = dashboard.get('title', '未知标题')
                uid = dashboard.get('uid', '未知UID')
                url = dashboard.get('url', '未知URL')
                
                print(f"{i}. 📈 {title}")
                print(f"   🔗 URL: {grafana_url}{url}")
                print(f"   🆔 UID: {uid}")
                
                # 检查是否是深度价差相关的仪表板
                if any(keyword in title.lower() for keyword in ['btc', 'eth', '深度', '价差', 'depth', 'spread']):
                    print(f"   ⭐ 这是深度价差分析仪表板!")
                    
                    # 直接打开这个仪表板
                    full_url = f"{grafana_url}{url}"
                    print(f"   🌐 完整访问地址: {full_url}")
                    
                    try:
                        import webbrowser
                        webbrowser.open(full_url)
                        print(f"   ✅ 浏览器已自动打开")
                    except:
                        print(f"   ℹ️  请手动复制上面的URL到浏览器")
                
                print()
            
            # 如果没有找到相关仪表板，显示通用访问方式
            if not any(any(keyword in d.get('title', '').lower() for keyword in ['btc', 'eth', '深度', '价差', 'depth', 'spread']) for d in dashboards):
                print("🔍 未找到深度价差分析仪表板")
                print("📝 通用访问方式:")
                print(f"   🌐 Grafana主页: {grafana_url}")
                print(f"   👤 用户名: admin")
                print(f"   🔑 密码: admin")
                
        else:
            print(f"❌ 获取仪表板列表失败: {response.status_code}")
            print("📝 手动访问方式:")
            print(f"   🌐 Grafana主页: {grafana_url}")
            print(f"   👤 用户名: admin")
            print(f"   🔑 密码: admin")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 连接Grafana失败: {e}")
        print()
        print("🔧 可能的解决方案:")
        print("   1. 检查Grafana是否正在运行")
        print("   2. 检查端口3000是否可访问")
        print("   3. 手动访问 http://localhost:3000")
        
    except Exception as e:
        print(f"❌ 获取信息失败: {e}")

def show_manual_access():
    """显示手动访问方式"""
    print("\n📋 手动访问指南:")
    print("-" * 30)
    print("1. 🌐 打开浏览器")
    print("2. 🔗 访问: http://localhost:3000")
    print("3. 👤 用户名: admin")
    print("4. 🔑 密码: admin")
    print("5. 📊 在左侧菜单选择 'Dashboards'")
    print("6. 🔍 查找包含 'BTCUSDT'、'ETHUSDT'、'深度'、'价差' 的仪表板")
    print()
    print("🎯 仪表板特征:")
    print("   - 标题包含: BTCUSDT vs ETHUSDT")
    print("   - 包含: 深度价差对比分析")
    print("   - 三个面板: 深度对比、深度统计、价差对比")

def main():
    """主函数"""
    print("🔍 获取Grafana仪表板路径")
    print("=" * 50)
    
    # 尝试获取仪表板列表
    get_dashboard_list()
    
    # 显示手动访问方式
    show_manual_access()
    
    print("\n💡 提示:")
    print("   如果自动打开失败，请复制上面的URL手动访问")
    print("   仪表板会显示最新的深度和价差对比数据")

if __name__ == "__main__":
    main()
