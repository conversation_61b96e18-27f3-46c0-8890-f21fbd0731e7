#!/usr/bin/env python3
"""
分析价格匹配逻辑和原始数据
详细检查延时处理器的价格匹配机制
"""

import mysql.connector
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PriceMatchingAnalyzer:
    """价格匹配逻辑分析器"""
    
    def __init__(self):
        self.source_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'depth_db'
        }
        
        self.latency_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'ethusdt_latency_db'
        }
    
    def analyze_specific_match(self, target_latency_ms=1667):
        """分析特定延时记录的价格匹配逻辑"""
        logger.info(f"🔍 分析 {target_latency_ms}ms 延时记录的价格匹配逻辑...")
        logger.info("=" * 80)
        
        try:
            # 获取延时记录
            latency_conn = mysql.connector.connect(**self.latency_config)
            latency_cursor = latency_conn.cursor()
            
            latency_cursor.execute("""
                SELECT 
                    bitda_timestamp,
                    binance_timestamp,
                    latency_ms,
                    bitda_price,
                    binance_price
                FROM ethusdt_latency_matches 
                WHERE latency_ms = %s
                LIMIT 1
            """, (target_latency_ms,))
            
            record = latency_cursor.fetchone()
            if not record:
                logger.error(f"❌ 未找到延时为 {target_latency_ms}ms 的记录")
                return False
            
            bitda_ts, binance_ts, latency_ms, bitda_price, binance_price = record
            
            logger.info("📋 延时记录中的价格信息:")
            logger.info(f"   Bitda时间戳: {bitda_ts}")
            logger.info(f"   Binance时间戳: {binance_ts}")
            logger.info(f"   延时: {latency_ms}ms")
            logger.info(f"   Bitda价格: {bitda_price}")
            logger.info(f"   Binance价格: {binance_price}")
            
            # 查找原始Bitda数据
            logger.info("\n📊 步骤1: 查找原始Bitda数据")
            source_conn = mysql.connector.connect(**self.source_config)
            source_cursor = source_conn.cursor()
            
            source_cursor.execute("""
                SELECT timestamp, bid_price_1, ask_price_1, created_at
                FROM bitda_depth
                WHERE symbol = 'ETHUSDT'
                AND timestamp = %s
                LIMIT 1
            """, (bitda_ts,))
            
            bitda_record = source_cursor.fetchone()
            if bitda_record:
                bitda_timestamp, bitda_bid, bitda_ask, bitda_created = bitda_record
                logger.info("   ✅ 找到原始Bitda记录:")
                logger.info(f"      时间戳: {bitda_timestamp}")
                logger.info(f"      买一价(bid_price_1): {bitda_bid}")
                logger.info(f"      卖一价(ask_price_1): {bitda_ask}")
                logger.info(f"      创建时间: {bitda_created}")
                
                # 分析价格匹配
                logger.info("\n💰 价格匹配分析:")
                if bitda_price == bitda_bid:
                    logger.info(f"   ✅ 延时记录中的bitda_price({bitda_price}) = 原始数据的买一价({bitda_bid})")
                elif bitda_price == bitda_ask:
                    logger.info(f"   ✅ 延时记录中的bitda_price({bitda_price}) = 原始数据的卖一价({bitda_ask})")
                else:
                    logger.warning(f"   ⚠️  延时记录中的bitda_price({bitda_price}) 不匹配原始数据 买一:{bitda_bid} 卖一:{bitda_ask}")
            else:
                logger.warning("   ⚠️  未找到对应的原始Bitda记录")
                return False
            
            # 查找原始Binance数据
            logger.info("\n📊 步骤2: 查找原始Binance数据")
            source_cursor.execute("""
                SELECT bid_price, ask_price, event_time, created_at
                FROM binance_bookticker
                WHERE symbol = 'ETHUSDT'
                AND event_time = %s
                LIMIT 1
            """, (binance_ts,))
            
            binance_record = source_cursor.fetchone()
            if binance_record:
                binance_bid, binance_ask, binance_event_time, binance_created = binance_record
                logger.info("   ✅ 找到原始Binance记录:")
                logger.info(f"      买一价(bid_price): {binance_bid}")
                logger.info(f"      卖一价(ask_price): {binance_ask}")
                logger.info(f"      事件时间: {binance_event_time}")
                logger.info(f"      创建时间: {binance_created}")
                
                # 分析价格匹配
                logger.info("\n💰 价格匹配分析:")
                if binance_price == binance_bid:
                    logger.info(f"   ✅ 延时记录中的binance_price({binance_price}) = 原始数据的买一价({binance_bid})")
                elif binance_price == binance_ask:
                    logger.info(f"   ✅ 延时记录中的binance_price({binance_price}) = 原始数据的卖一价({binance_ask})")
                else:
                    logger.warning(f"   ⚠️  延时记录中的binance_price({binance_price}) 不匹配原始数据 买一:{binance_bid} 卖一:{binance_ask}")
            else:
                logger.warning("   ⚠️  未找到对应的原始Binance记录")
                return False
            
            # 验证匹配逻辑
            logger.info("\n🔍 步骤3: 验证匹配逻辑")
            logger.info("   根据代码逻辑，匹配条件应该是:")
            logger.info(f"   Binance.bid_price = Bitda.bid_price_1 AND Binance.ask_price = Bitda.ask_price_1")
            logger.info(f"   即: {binance_bid} = {bitda_bid} AND {binance_ask} = {bitda_ask}")
            
            bid_match = abs(float(binance_bid) - float(bitda_bid)) < 0.01
            ask_match = abs(float(binance_ask) - float(bitda_ask)) < 0.01
            
            if bid_match and ask_match:
                logger.info("   ✅ 买一价和卖一价都完全匹配")
            elif bid_match:
                logger.info("   ⚠️  只有买一价匹配")
            elif ask_match:
                logger.info("   ⚠️  只有卖一价匹配")
            else:
                logger.warning("   ❌ 买一价和卖一价都不匹配")
            
            # 分析代码逻辑问题
            logger.info("\n🐛 步骤4: 代码逻辑分析")
            logger.info("   从代码分析发现的问题:")
            logger.info("   1. 查询条件: bid_price = bid_price_1 AND ask_price = ask_price_1 (完全匹配)")
            logger.info("   2. 保存数据: bitda_price = bid_price_1, binance_price = binance_bid (只保存买价)")
            logger.info("   3. 这意味着虽然是完全匹配，但只记录了买一价格的延时")
            
            logger.info("\n📋 结论:")
            logger.info("   ✅ 匹配逻辑: 要求Bitda和Binance的买一价和卖一价都相同")
            logger.info("   ✅ 延时计算: 基于完全匹配的价格对")
            logger.info("   ⚠️  记录内容: 只保存了买一价格，但延时是基于完全匹配计算的")
            logger.info("   🎯 延时含义: Bitda出现特定买一卖一价格对比Binance晚了多少毫秒")
            
            # 关闭连接
            source_cursor.close()
            source_conn.close()
            latency_cursor.close()
            latency_conn.close()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 分析过程出错: {e}")
            return False
    
    def check_recent_matches(self, count=3):
        """检查最近的匹配记录"""
        logger.info(f"\n🔍 检查最近 {count} 条匹配记录的价格逻辑...")
        
        try:
            latency_conn = mysql.connector.connect(**self.latency_config)
            latency_cursor = latency_conn.cursor()
            
            latency_cursor.execute("""
                SELECT 
                    bitda_timestamp,
                    binance_timestamp,
                    latency_ms,
                    bitda_price,
                    binance_price
                FROM ethusdt_latency_matches 
                ORDER BY created_at DESC
                LIMIT %s
            """, (count,))
            
            records = latency_cursor.fetchall()
            
            source_conn = mysql.connector.connect(**self.source_config)
            source_cursor = source_conn.cursor()
            
            logger.info("📊 最近匹配记录的价格验证:")
            logger.info("   序号 | 延时(ms) | Bitda买一 | Bitda卖一 | Binance买一 | Binance卖一 | 匹配状态")
            logger.info("   " + "-" * 85)
            
            for i, (bitda_ts, binance_ts, latency_ms, bitda_price, binance_price) in enumerate(records):
                # 获取Bitda原始数据
                source_cursor.execute("""
                    SELECT bid_price_1, ask_price_1
                    FROM bitda_depth
                    WHERE symbol = 'ETHUSDT' AND timestamp = %s
                    LIMIT 1
                """, (bitda_ts,))
                bitda_data = source_cursor.fetchone()
                
                # 获取Binance原始数据
                source_cursor.execute("""
                    SELECT bid_price, ask_price
                    FROM binance_bookticker
                    WHERE symbol = 'ETHUSDT' AND event_time = %s
                    LIMIT 1
                """, (binance_ts,))
                binance_data = source_cursor.fetchone()
                
                if bitda_data and binance_data:
                    bitda_bid, bitda_ask = bitda_data
                    binance_bid, binance_ask = binance_data
                    
                    # 检查匹配状态
                    bid_match = abs(float(binance_bid) - float(bitda_bid)) < 0.01
                    ask_match = abs(float(binance_ask) - float(bitda_ask)) < 0.01
                    
                    if bid_match and ask_match:
                        status = "✅完全匹配"
                    elif bid_match:
                        status = "⚠️买价匹配"
                    elif ask_match:
                        status = "⚠️卖价匹配"
                    else:
                        status = "❌不匹配"
                    
                    logger.info(f"   {i+1:2d}   | {latency_ms:4d}    | {bitda_bid:8.2f} | {bitda_ask:8.2f} | {binance_bid:9.2f} | {binance_ask:9.2f} | {status}")
                else:
                    logger.info(f"   {i+1:2d}   | {latency_ms:4d}    | 数据缺失")
            
            source_cursor.close()
            source_conn.close()
            latency_cursor.close()
            latency_conn.close()
            
        except Exception as e:
            logger.error(f"❌ 检查最近匹配记录出错: {e}")

def main():
    """主函数"""
    print("🔍 价格匹配逻辑分析工具")
    print("=" * 50)
    print("功能:")
    print("  - 分析延时处理器的价格匹配逻辑")
    print("  - 检查原始数据的买一卖一价格")
    print("  - 验证匹配条件和保存逻辑")
    print("  - 确认延时计算的准确含义")
    print()
    
    analyzer = PriceMatchingAnalyzer()
    
    # 分析1667ms的记录
    success1 = analyzer.analyze_specific_match(1667)
    
    # 检查最近的匹配记录
    analyzer.check_recent_matches(3)
    
    if success1:
        print("\n🎉 价格匹配逻辑分析完成！")
        print("📋 关键发现:")
        print("   ✅ 延时基于完全匹配计算（买一价和卖一价都相同）")
        print("   ✅ 延时含义：Bitda出现特定价格对比Binance晚多少毫秒")
        print("   ⚠️  数据库只保存买一价格，但匹配是基于完全价格对")
    else:
        print("\n⚠️  分析发现问题，请检查详细日志")

if __name__ == "__main__":
    main()
