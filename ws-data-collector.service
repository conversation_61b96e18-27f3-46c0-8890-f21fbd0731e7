[Unit]
Description=WebSocket Data Collector Service
Documentation=https://github.com/your-repo/ws-data-collector
After=network.target
Wants=network.target

[Service]
Type=simple
User=code
Group=code
WorkingDirectory=/home/<USER>/project/WS_DATA_Collector
Environment=PATH=/home/<USER>/anaconda3/bin:/usr/local/bin:/usr/bin:/bin
Environment=PYTHONPATH=/home/<USER>/project/WS_DATA_Collector
Environment=CONDA_DEFAULT_ENV=base
Environment=CONDA_PREFIX=/home/<USER>/anaconda3
ExecStart=/home/<USER>/anaconda3/bin/python /home/<USER>/project/WS_DATA_Collector/ws_data_collector.py
ExecReload=/bin/kill -HUP $MAINPID
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=ws-data-collector

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=false
ReadWritePaths=/home/<USER>/project/WS_DATA_Collector /tmp /var/log

[Install]
WantedBy=multi-user.target
