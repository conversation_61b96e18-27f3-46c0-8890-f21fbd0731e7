#!/bin/bash

# 进程状态检查脚本
# 用于检查当前运行的数据采集和分析进程

echo "🔍 检查数据采集和分析进程状态..."
echo "========================================"

# 检查主要进程
echo "📊 主要数据采集进程:"
ps aux | grep -E "(main\.py|ws_data_collector\.py)" | grep -v grep | while read line; do
    echo "  ✅ $line"
done

echo ""
echo "📈 Prometheus导出进程:"
ps aux | grep "prometheus_exporter\.py" | grep -v grep | while read line; do
    echo "  ✅ $line"
done

echo ""
echo "🔧 其他相关进程:"
ps aux | grep -E "(analyzer|grafana|prometheus)" | grep -v grep | while read line; do
    echo "  ✅ $line"
done

# 检查进程数量
main_count=$(ps aux | grep -E "(main\.py|ws_data_collector\.py)" | grep -v grep | wc -l)
prometheus_count=$(ps aux | grep "prometheus_exporter\.py" | grep -v grep | wc -l)

echo ""
echo "📋 进程统计:"
echo "  数据采集进程: $main_count 个"
echo "  Prometheus导出: $prometheus_count 个"

# 检查端口占用
echo ""
echo "🌐 端口占用情况:"
netstat -tlnp 2>/dev/null | grep -E ":3000|:9090|:8000" | while read line; do
    echo "  📡 $line"
done

# 检查数据库连接
echo ""
echo "🗄️ 数据库连接测试:"
mysql -u root -pLinuxtest -e "SELECT 'Database connection OK' as status;" depth_db 2>/dev/null && echo "  ✅ 数据库连接正常" || echo "  ❌ 数据库连接失败"

# 检查最新数据
echo ""
echo "📊 最新数据统计:"
mysql -u root -pLinuxtest -e "
SELECT 
    'bitda_ticker' as table_name, 
    COUNT(*) as total_records,
    MAX(created_at) as latest_record
FROM bitda_ticker 
WHERE symbol IN ('BTCUSDT', 'ETHUSDT')
UNION ALL
SELECT 
    'depth_matches' as table_name, 
    COUNT(*) as total_records,
    MAX(timestamp) as latest_record
FROM depth_matches 
WHERE symbol IN ('BTCUSDT', 'ETHUSDT');
" depth_db 2>/dev/null

echo ""
echo "🎯 快捷操作提示:"
echo "  停止数据采集: pkill -f 'main.py'"
echo "  停止Prometheus: pkill -f 'prometheus_exporter.py'"
echo "  查看实时日志: tail -f crypto_collector.log"
echo "  运行数据分析: ./run_analysis.sh"
echo "  启动Grafana: ./run_grafana.sh"
