#!/usr/bin/env python3
"""
验证延时计算的准确性
"""

import json

def verify_calculation():
    """验证具体记录的延时计算"""

    print("🔍 验证延时计算准确性")
    print("=" * 50)

    # Bitda原始数据 (正确的记录)
    bitda_timestamp = 1748583551097
    bitda_created_at = "2025-05-30 13:39:16"

    # <PERSON><PERSON> asks数据 (JSON格式) - 正确的记录
    asks_json = '[["2644.22", "171.034"], ["2644.23", "122.070"], ["2644.24", "31.642"], ["2644.26", "219.701"], ["2644.27", "99.558"], ["2644.29", "25.140"], ["2644.30", "96.229"], ["2644.31", "67.792"], ["2644.32", "69.753"], ["2644.33", "76.643"], ["2644.35", "27.669"], ["2644.36", "91.819"], ["2644.37", "80.343"], ["2644.38", "112.580"], ["2644.39", "123.701"], ["2644.40", "153.707"], ["2644.42", "28.990"], ["2644.43", "129.671"], ["2644.49", "29.276"], ["2644.55", "21.255"], ["2644.62", "24.016"], ["2644.68", "30.836"], ["2644.75", "22.669"], ["2644.82", "27.466"], ["2644.88", "20.918"], ["2644.95", "23.464"], ["2645.01", "26.476"], ["2645.08", "22.116"], ["2645.15", "24.760"], ["2645.17", "59.806"], ["2645.23", "51.017"], ["2645.30", "60.530"], ["2645.37", "52.141"], ["2645.43", "59.373"], ["2645.50", "69.299"], ["2645.57", "55.897"], ["2645.63", "52.911"], ["2645.69", "113.387"], ["2650.10", "88.871"], ["2658.50", "69.711"], ["2663.88", "74.323"], ["2669.74", "142.769"], ["2674.25", "103.476"], ["2674.29", "194.476"], ["2674.44", "166.794"], ["2674.56", "66.309"], ["2681.59", "446.341"], ["2681.80", "148.319"], ["2681.97", "239.022"], ["2686.89", "6.189"]]'

    # Bitda bids数据 (JSON格式) - 正确的记录
    bids_json = '[["2644.21", "159.279"], ["2644.19", "115.912"], ["2644.17", "5.255"], ["2644.16", "83.750"], ["2644.15", "98.687"], ["2644.14", "77.935"], ["2644.13", "74.303"], ["2644.12", "86.929"], ["2644.11", "70.999"], ["2644.10", "97.762"], ["2644.09", "77.184"], ["2644.08", "74.087"], ["2644.07", "78.477"], ["2644.06", "90.147"], ["2644.05", "91.936"], ["2644.04", "72.684"], ["2644.03", "71.744"], ["2644.02", "90.295"], ["2643.95", "29.474"], ["2643.93", "85.466"], ["2643.92", "100.709"], ["2643.90", "75.825"], ["2643.89", "88.709"], ["2643.88", "72.454"], ["2643.86", "78.765"], ["2643.85", "75.605"], ["2643.84", "80.085"], ["2643.82", "93.819"], ["2643.81", "74.172"], ["2643.80", "73.213"], ["2643.79", "92.145"], ["2643.74", "74.500"], ["2643.73", "71.028"], ["2643.72", "83.097"], ["2643.71", "67.870"], ["2631.70", "83.224"], ["2622.50", "66.155"], ["2615.25", "65.595"], ["2614.31", "77.013"], ["2611.71", "165.095"], ["2603.13", "108.123"], ["2594.56", "55.073"], ["2594.32", "66.611"], ["2587.11", "75.706"], ["2586.99", "102.523"], ["2586.95", "17.724"], ["2586.40", "100.280"], ["2579.67", "43.441"], ["2579.23", "73.824"], ["2573.12", "56.415"]]'

    # Binance数据
    binance_bid_price = 2644.21
    binance_ask_price = 2644.22
    binance_bid_qty = 5.9350
    binance_ask_qty = 180.1430
    binance_timestamp = 1748583551056  # 首次出现时间 (按您的要求)

    print("📊 Bitda原始数据:")
    print(f"  时间戳: {bitda_timestamp}")
    print(f"  入库时间: {bitda_created_at}")
    print(f"  asks数据长度: {len(json.loads(asks_json))} 档")
    print(f"  bids数据长度: {len(json.loads(bids_json))} 档")

    # 解析Bitda数据
    asks_data = json.loads(asks_json)
    bids_data = json.loads(bids_json)

    # 计算买一卖一价格
    ask_prices = [float(item[0]) for item in asks_data]
    bid_prices = [float(item[0]) for item in bids_data]

    bitda_ask_price = min(ask_prices)  # 卖一价格
    bitda_bid_price = max(bid_prices)  # 买一价格

    # 找到对应的数量
    bitda_ask_qty = float([item[1] for item in asks_data if float(item[0]) == bitda_ask_price][0])
    bitda_bid_qty = float([item[1] for item in bids_data if float(item[0]) == bitda_bid_price][0])

    print(f"\n📈 Bitda解析结果:")
    print(f"  买一价格: {bitda_bid_price}")
    print(f"  买一数量: {bitda_bid_qty}")
    print(f"  卖一价格: {bitda_ask_price}")
    print(f"  卖一数量: {bitda_ask_qty}")

    print(f"\n📊 Binance数据:")
    print(f"  时间戳: {binance_timestamp}")
    print(f"  买一价格: {binance_bid_price}")
    print(f"  买一数量: {binance_bid_qty}")
    print(f"  卖一价格: {binance_ask_price}")
    print(f"  卖一数量: {binance_ask_qty}")

    # 验证匹配
    bid_match = (bitda_bid_price == binance_bid_price)
    ask_match = (bitda_ask_price == binance_ask_price)
    complete_match = bid_match and ask_match

    print(f"\n🎯 匹配验证:")
    print(f"  买一价格匹配: {'✅' if bid_match else '❌'} ({bitda_bid_price} vs {binance_bid_price})")
    print(f"  卖一价格匹配: {'✅' if ask_match else '❌'} ({bitda_ask_price} vs {binance_ask_price})")
    print(f"  完全匹配: {'✅' if complete_match else '❌'}")

    # 计算延时
    if complete_match:
        latency = bitda_timestamp - binance_timestamp
        print(f"\n⏱️  延时计算:")
        print(f"  Bitda时间戳: {bitda_timestamp}")
        print(f"  Binance时间戳: {binance_timestamp}")
        print(f"  延时: {latency}ms")
        print(f"  延时(秒): {latency/1000:.3f}s")

        # 验证延时范围
        valid_latency = 10 <= latency <= 2000
        print(f"  有效延时: {'✅' if valid_latency else '❌'} (10-2000ms范围)")

        # 计算价差
        price_spread = bitda_ask_price - bitda_bid_price
        print(f"  价差: {price_spread:.2f} USDT")

        return {
            'bitda_bid': bitda_bid_price,
            'bitda_ask': bitda_ask_price,
            'binance_bid': binance_bid_price,
            'binance_ask': binance_ask_price,
            'latency': latency,
            'valid': valid_latency,
            'price_spread': price_spread
        }
    else:
        print(f"\n❌ 价格不匹配，无法计算延时")
        return None

def main():
    """主函数"""
    print("🔍 ETHUSDT延时计算验证")
    print("验证记录: 2025-05-30 13:39:16 的延时=20ms记录")
    print("=" * 60)

    result = verify_calculation()

    if result:
        print(f"\n✅ 验证完成!")
        print(f"计算的延时: {result['latency']}ms")
        print(f"是否有效: {'是' if result['valid'] else '否'}")
        print(f"价差: {result['price_spread']:.2f} USDT")

        if result['latency'] == 20:
            print("🎉 延时计算完全正确!")
        else:
            print(f"⚠️  延时计算有差异，期望20ms，实际{result['latency']}ms")
    else:
        print("❌ 验证失败")

if __name__ == "__main__":
    main()
