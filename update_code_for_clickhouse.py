#!/usr/bin/env python3
"""
更新代码以使用ClickHouse
替换MySQL连接为ClickHouse连接
"""

import os
import shutil
from datetime import datetime

def log(message):
    """日志输出"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def backup_file(filepath):
    """备份文件"""
    if os.path.exists(filepath):
        backup_path = f"{filepath}.mysql_backup"
        shutil.copy2(filepath, backup_path)
        log(f"✅ 备份文件: {filepath} -> {backup_path}")
        return True
    return False

def update_utils_config():
    """更新utils/config.py"""
    log("🔧 更新utils/config.py...")
    
    config_path = "utils/config.py"
    if not os.path.exists(config_path):
        log(f"❌ 文件不存在: {config_path}")
        return False
    
    backup_file(config_path)
    
    # 读取原文件
    with open(config_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换数据库配置
    new_content = '''"""
配置管理模块 - ClickHouse版本
"""
import os
from typing import Dict, List

# 加载.env文件
from dotenv import load_dotenv
load_dotenv()

# ClickHouse数据库配置
CLICKHOUSE_CONFIG = {
    'host': os.getenv('CH_HOST', 'localhost'),
    'user': os.getenv('CH_USER', 'default'),
    'password': os.getenv('CH_PASSWORD', 'Linuxtest'),
    'database': os.getenv('CH_DATABASE', 'crypto'),
    'port': int(os.getenv('CH_PORT', '9000')),
}

# HTTP接口配置
CLICKHOUSE_HTTP_URL = f"http://{CLICKHOUSE_CONFIG['user']}:{CLICKHOUSE_CONFIG['password']}@{CLICKHOUSE_CONFIG['host']}:8123/"

# 保留原MySQL配置作为备份
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', 'Linuxtest'),
    'database': os.getenv('DB_NAME', 'depth_db'),
    'port': int(os.getenv('DB_PORT', '3306')),
    'charset': 'utf8mb4',
    'use_unicode': True,
}

# 交易对配置
SYMBOLS = ['BTCUSDT', 'ETHUSDT']

# WebSocket配置
BITDA_WS_URL = 'wss://ws.bitda.com/wsf'
BINANCE_WS_URL = 'wss://stream.binance.com:9443/ws'

# 数据保留配置
DATA_RETENTION_DAYS = 3
DISK_SPACE_WARNING_GB = 50

# 日志配置
LOG_LEVEL = 'INFO'
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
'''
    
    # 写入新文件
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    log("✅ utils/config.py 更新完成")
    return True

def update_utils_db():
    """更新utils/db.py"""
    log("🔧 更新utils/db.py...")
    
    db_path = "utils/db.py"
    if not os.path.exists(db_path):
        log(f"❌ 文件不存在: {db_path}")
        return False
    
    backup_file(db_path)
    
    new_content = '''"""
数据库连接管理模块 - ClickHouse版本
"""
import requests
import json
from typing import Optional, List, Any
from utils.config import CLICKHOUSE_HTTP_URL
from utils.logging import setup_logger

logger = setup_logger(__name__)

class ClickHouseManager:
    """ClickHouse数据库管理器"""
    
    def __init__(self):
        self.http_url = CLICKHOUSE_HTTP_URL
        logger.info("ClickHouse管理器初始化成功")
    
    def execute_query(self, query: str, params: tuple = None, fetch: bool = False):
        """执行ClickHouse查询"""
        try:
            # 处理参数化查询
            if params:
                # 简单的参数替换（生产环境建议使用更安全的方法）
                for param in params:
                    if isinstance(param, str):
                        query = query.replace('%s', f"'{param}'", 1)
                    else:
                        query = query.replace('%s', str(param), 1)
            
            response = requests.post(self.http_url, data=query, timeout=30)
            
            if response.status_code == 200:
                if fetch:
                    # 返回查询结果
                    result_text = response.text.strip()
                    if result_text:
                        lines = result_text.split('\\n')
                        return [line.split('\\t') for line in lines]
                    return []
                else:
                    # 返回影响行数（对于INSERT/UPDATE/DELETE）
                    return 1
            else:
                logger.error(f"ClickHouse查询失败: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"执行ClickHouse查询失败: {e}")
            return None
    
    def execute_many(self, query: str, params_list: list):
        """批量执行SQL"""
        try:
            # 构建批量插入语句
            if 'INSERT INTO' in query.upper():
                # 提取表名和字段
                parts = query.split('VALUES')
                if len(parts) == 2:
                    insert_part = parts[0].strip()
                    
                    # 构建VALUES部分
                    values_list = []
                    for params in params_list:
                        value_str = "("
                        for i, param in enumerate(params):
                            if i > 0:
                                value_str += ", "
                            if isinstance(param, str):
                                value_str += f"'{param.replace(chr(39), chr(39)+chr(39))}'"
                            elif param is None:
                                value_str += "NULL"
                            else:
                                value_str += str(param)
                        value_str += ")"
                        values_list.append(value_str)
                    
                    full_query = f"{insert_part} VALUES {', '.join(values_list)}"
                    return self.execute_query(full_query)
            
            return None
            
        except Exception as e:
            logger.error(f"批量执行ClickHouse查询失败: {e}")
            return None

# 全局ClickHouse管理器实例
ch_manager = ClickHouseManager()

# 为了兼容性，保留db_manager别名
db_manager = ch_manager
'''
    
    with open(db_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    log("✅ utils/db.py 更新完成")
    return True

def update_storage():
    """更新storage.py"""
    log("🔧 更新storage.py...")
    
    storage_path = "storage.py"
    if not os.path.exists(storage_path):
        log(f"❌ 文件不存在: {storage_path}")
        return False
    
    backup_file(storage_path)
    
    # 读取原文件
    with open(storage_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换表创建逻辑
    new_content = content.replace(
        'from utils.db import db_manager',
        'from utils.db import ch_manager as db_manager'
    )
    
    # 替换表结构为ClickHouse格式
    new_content = new_content.replace(
        'ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci',
        'ENGINE = MergeTree() ORDER BY (symbol, timestamp)'
    )
    
    # 替换AUTO_INCREMENT
    new_content = new_content.replace('AUTO_INCREMENT PRIMARY KEY', '')
    new_content = new_content.replace('BIGINT AUTO_INCREMENT PRIMARY KEY', 'UInt64')
    
    # 替换TIMESTAMP
    new_content = new_content.replace('TIMESTAMP DEFAULT CURRENT_TIMESTAMP', 'DateTime DEFAULT now()')
    
    with open(storage_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    log("✅ storage.py 更新完成")
    return True

def create_env_file():
    """创建.env文件"""
    log("📝 创建.env文件...")
    
    env_content = '''# ClickHouse配置
CH_HOST=localhost
CH_USER=default
CH_PASSWORD=Linuxtest
CH_DATABASE=crypto
CH_PORT=9000

# MySQL备份配置（保留）
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=Linuxtest
DB_NAME=depth_db
DB_PORT=3306
'''
    
    with open('.env', 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    log("✅ .env文件创建完成")
    return True

def test_clickhouse_connection():
    """测试ClickHouse连接"""
    log("🔍 测试ClickHouse连接...")
    
    try:
        import requests
        
        url = "***************************************/"
        response = requests.post(url, data="SELECT 1 as test", timeout=10)
        
        if response.status_code == 200:
            log("✅ ClickHouse连接测试成功")
            
            # 测试查询
            response = requests.post(url, data="SELECT COUNT(*) FROM crypto.bitda_depth", timeout=10)
            if response.status_code == 200:
                count = response.text.strip()
                log(f"✅ bitda_depth表记录数: {count}")
                return True
            else:
                log(f"❌ 查询测试失败: {response.text}")
                return False
        else:
            log(f"❌ ClickHouse连接失败: {response.text}")
            return False
            
    except Exception as e:
        log(f"❌ ClickHouse连接异常: {e}")
        return False

def main():
    """主函数"""
    log("🚀 开始更新代码以使用ClickHouse")
    log("=" * 50)
    
    # 1. 创建.env文件
    if not create_env_file():
        log("❌ 创建.env文件失败")
        return
    
    # 2. 更新配置文件
    if not update_utils_config():
        log("❌ 更新配置文件失败")
        return
    
    # 3. 更新数据库管理器
    if not update_utils_db():
        log("❌ 更新数据库管理器失败")
        return
    
    # 4. 更新存储模块
    if not update_storage():
        log("❌ 更新存储模块失败")
        return
    
    # 5. 测试连接
    if not test_clickhouse_connection():
        log("❌ ClickHouse连接测试失败")
        return
    
    log("=" * 50)
    log("🎉 代码更新完成！")
    log("📋 下一步:")
    log("  1. 重启数据收集程序")
    log("  2. 配置Grafana ClickHouse数据源")
    log("  3. 验证系统功能")
    log("")
    log("🔧 ClickHouse信息:")
    log("  - HTTP接口: http://localhost:8123")
    log("  - 数据库: crypto")
    log("  - 用户: default")
    log("  - 密码: Linuxtest")

if __name__ == "__main__":
    main()
