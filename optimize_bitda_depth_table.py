#!/usr/bin/env python3
"""
优化Bitda深度表 - 提取买一卖一价格到独立字段
解决JSON查询性能问题
"""

import mysql.connector
import json
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BitdaDepthOptimizer:
    """Bitda深度表优化器"""
    
    def __init__(self):
        self.config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'depth_db'
        }
    
    def add_price_columns(self):
        """添加买一卖一价格字段"""
        try:
            connection = mysql.connector.connect(**self.config)
            cursor = connection.cursor()
            
            logger.info("添加买一卖一价格字段...")
            
            # 检查字段是否已存在
            cursor.execute("DESCRIBE bitda_depth")
            columns = [col[0] for col in cursor.fetchall()]
            
            if 'bid_price_1' not in columns:
                alter_sql = """
                ALTER TABLE bitda_depth 
                ADD COLUMN bid_price_1 DECIMAL(15,2) COMMENT '买一价格',
                ADD COLUMN ask_price_1 DECIMAL(15,2) COMMENT '卖一价格',
                ADD COLUMN bid_qty_1 DECIMAL(20,4) COMMENT '买一数量',
                ADD COLUMN ask_qty_1 DECIMAL(20,4) COMMENT '卖一数量'
                """
                cursor.execute(alter_sql)
                logger.info("✅ 字段添加成功")
            else:
                logger.info("✅ 字段已存在，跳过添加")
            
            cursor.close()
            connection.close()
            
        except Exception as e:
            logger.error(f"添加字段失败: {e}")
            raise
    
    def extract_prices_batch(self, batch_size=1000):
        """批量提取价格数据"""
        try:
            connection = mysql.connector.connect(**self.config)
            cursor = connection.cursor()
            
            # 获取需要处理的记录数
            cursor.execute("""
                SELECT COUNT(*) FROM bitda_depth 
                WHERE symbol = 'ETHUSDT' 
                AND bid_price_1 IS NULL 
                AND asks IS NOT NULL 
                AND bids IS NOT NULL
            """)
            total_records = cursor.fetchone()[0]
            
            if total_records == 0:
                logger.info("✅ 所有记录已处理完成")
                return
            
            logger.info(f"📊 需要处理 {total_records} 条记录")
            
            processed = 0
            
            while processed < total_records:
                # 获取一批数据
                cursor.execute("""
                    SELECT id, asks, bids FROM bitda_depth 
                    WHERE symbol = 'ETHUSDT' 
                    AND bid_price_1 IS NULL 
                    AND asks IS NOT NULL 
                    AND bids IS NOT NULL
                    ORDER BY id
                    LIMIT %s
                """, (batch_size,))
                
                records = cursor.fetchall()
                if not records:
                    break
                
                # 处理这批数据
                updates = []
                for record_id, asks_json, bids_json in records:
                    try:
                        # 解析JSON
                        asks = json.loads(asks_json)
                        bids = json.loads(bids_json)
                        
                        if asks and bids:
                            # 买一价格 = bids中的最高价格
                            bid_price_1 = max(float(bid[0]) for bid in bids)
                            bid_qty_1 = next(float(bid[1]) for bid in bids if float(bid[0]) == bid_price_1)
                            
                            # 卖一价格 = asks中的最低价格
                            ask_price_1 = min(float(ask[0]) for ask in asks)
                            ask_qty_1 = next(float(ask[1]) for ask in asks if float(ask[0]) == ask_price_1)
                            
                            updates.append((bid_price_1, ask_price_1, bid_qty_1, ask_qty_1, record_id))
                    
                    except Exception as e:
                        logger.warning(f"解析记录 {record_id} 失败: {e}")
                        continue
                
                # 批量更新
                if updates:
                    update_sql = """
                    UPDATE bitda_depth SET 
                        bid_price_1 = %s, 
                        ask_price_1 = %s, 
                        bid_qty_1 = %s, 
                        ask_qty_1 = %s 
                    WHERE id = %s
                    """
                    cursor.executemany(update_sql, updates)
                    connection.commit()
                    
                    processed += len(updates)
                    progress = (processed / total_records) * 100
                    logger.info(f"📈 处理进度: {processed}/{total_records} ({progress:.1f}%)")
            
            cursor.close()
            connection.close()
            
            logger.info(f"✅ 价格提取完成，共处理 {processed} 条记录")
            
        except Exception as e:
            logger.error(f"批量提取价格失败: {e}")
            raise
    
    def create_indexes(self):
        """创建优化索引"""
        try:
            connection = mysql.connector.connect(**self.config)
            cursor = connection.cursor()
            
            logger.info("创建价格匹配索引...")
            
            # 检查索引是否已存在
            cursor.execute("SHOW INDEX FROM bitda_depth WHERE Key_name = 'idx_ethusdt_price_match'")
            if cursor.fetchone():
                logger.info("✅ 索引已存在，跳过创建")
            else:
                # 创建价格匹配索引
                index_sql = """
                CREATE INDEX idx_ethusdt_price_match 
                ON bitda_depth (symbol, bid_price_1, ask_price_1, timestamp)
                """
                cursor.execute(index_sql)
                logger.info("✅ 价格匹配索引创建成功")
            
            # 检查Binance表的索引
            cursor.execute("SHOW INDEX FROM binance_bookticker WHERE Key_name = 'idx_price_match'")
            if not cursor.fetchone():
                logger.info("创建Binance价格匹配索引...")
                binance_index_sql = """
                CREATE INDEX idx_price_match 
                ON binance_bookticker (symbol, bid_price, ask_price, event_time)
                """
                cursor.execute(binance_index_sql)
                logger.info("✅ Binance价格匹配索引创建成功")
            
            cursor.close()
            connection.close()
            
        except Exception as e:
            logger.error(f"创建索引失败: {e}")
            raise
    
    def verify_optimization(self):
        """验证优化效果"""
        try:
            connection = mysql.connector.connect(**self.config)
            cursor = connection.cursor()
            
            logger.info("验证优化效果...")
            
            # 检查提取的价格数据
            cursor.execute("""
                SELECT COUNT(*) as total,
                       COUNT(bid_price_1) as with_bid_price,
                       COUNT(ask_price_1) as with_ask_price
                FROM bitda_depth 
                WHERE symbol = 'ETHUSDT'
            """)
            
            total, with_bid, with_ask = cursor.fetchone()
            logger.info(f"📊 数据统计:")
            logger.info(f"   总记录数: {total}")
            logger.info(f"   有买一价格: {with_bid} ({with_bid/total*100:.1f}%)")
            logger.info(f"   有卖一价格: {with_ask} ({with_ask/total*100:.1f}%)")
            
            # 测试查询性能
            logger.info("测试查询性能...")
            start_time = datetime.now()
            
            cursor.execute("""
                SELECT COUNT(*) FROM bitda_depth 
                WHERE symbol = 'ETHUSDT' 
                AND bid_price_1 = 2612.11 
                AND ask_price_1 = 2612.12
            """)
            
            result = cursor.fetchone()[0]
            end_time = datetime.now()
            query_time = (end_time - start_time).total_seconds()
            
            logger.info(f"📈 查询结果: 找到 {result} 条匹配记录")
            logger.info(f"⏱️  查询耗时: {query_time:.3f} 秒")
            
            if query_time < 1.0:
                logger.info("✅ 查询性能优秀 (<1秒)")
            elif query_time < 5.0:
                logger.info("✅ 查询性能良好 (<5秒)")
            else:
                logger.warning("⚠️  查询性能需要进一步优化")
            
            cursor.close()
            connection.close()
            
        except Exception as e:
            logger.error(f"验证失败: {e}")
    
    def optimize_all(self):
        """执行完整优化流程"""
        logger.info("🚀 开始Bitda深度表优化")
        logger.info("=" * 50)
        
        try:
            # 1. 添加字段
            self.add_price_columns()
            
            # 2. 提取价格数据
            self.extract_prices_batch()
            
            # 3. 创建索引
            self.create_indexes()
            
            # 4. 验证效果
            self.verify_optimization()
            
            logger.info("🎉 优化完成！")
            logger.info("现在可以使用优化后的查询:")
            logger.info("SELECT * FROM bitda_depth WHERE symbol='ETHUSDT' AND bid_price_1=? AND ask_price_1=?")
            
        except Exception as e:
            logger.error(f"❌ 优化失败: {e}")
            raise

def main():
    """主函数"""
    print("🔧 Bitda深度表性能优化工具")
    print("=" * 50)
    print("功能:")
    print("  - 提取JSON中的买一卖一价格到独立字段")
    print("  - 创建高效的价格匹配索引")
    print("  - 大幅提升延时分析查询性能")
    print()
    
    optimizer = BitdaDepthOptimizer()
    optimizer.optimize_all()

if __name__ == "__main__":
    main()
