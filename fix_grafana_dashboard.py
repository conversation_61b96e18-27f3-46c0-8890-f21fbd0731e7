#!/usr/bin/env python3
"""
修复Grafana仪表板的"No data"错误并优化延时图表
"""

import requests
import json
import mysql.connector
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GrafanaDashboardFixer:
    """Grafana仪表板修复器"""
    
    def __init__(self):
        self.grafana_url = "http://localhost:3000"
        self.admin_user = "admin"
        self.admin_pass = "admin"
        self.session = requests.Session()
        self.session.auth = (self.admin_user, self.admin_pass)
        
        self.latency_db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'ethusdt_latency_db'
        }
    
    def test_database_connection(self):
        """测试数据库连接"""
        logger.info("🔍 测试数据库连接...")
        
        try:
            connection = mysql.connector.connect(**self.latency_db_config)
            cursor = connection.cursor()
            
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    MAX(created_at) as latest,
                    AVG(latency_ms) as avg_latency
                FROM ethusdt_latency_matches 
                WHERE created_at >= NOW() - INTERVAL 1 HOUR
            """)
            
            result = cursor.fetchone()
            total, latest, avg_latency = result
            
            logger.info(f"   ✅ 数据库连接正常")
            logger.info(f"   📊 最近1小时数据: {total}条")
            logger.info(f"   ⏰ 最新记录: {latest}")
            logger.info(f"   📈 平均延时: {avg_latency:.1f}ms")
            
            cursor.close()
            connection.close()
            
            return True, {
                'total': total,
                'latest': latest,
                'avg_latency': avg_latency
            }
            
        except Exception as e:
            logger.error(f"   ❌ 数据库连接失败: {e}")
            return False, None
    
    def recreate_datasource(self):
        """重新创建数据源"""
        logger.info("🔧 重新创建MySQL数据源...")
        
        # 删除所有旧的数据源
        try:
            response = self.session.get(f"{self.grafana_url}/api/datasources")
            if response.status_code == 200:
                datasources = response.json()
                for ds in datasources:
                    if 'mysql' in ds.get('type', '').lower() or 'latency' in ds.get('name', '').lower():
                        self.session.delete(f"{self.grafana_url}/api/datasources/{ds['id']}")
                        logger.info(f"   🗑️  删除旧数据源: {ds.get('name')}")
        except:
            pass
        
        # 创建新的MySQL数据源
        datasource_config = {
            "name": "ETHUSDT_Latency_MySQL",
            "type": "mysql",
            "access": "proxy",
            "url": "localhost:3306",
            "database": "ethusdt_latency_db",
            "user": "root",
            "secureJsonData": {
                "password": "Linuxtest"
            },
            "jsonData": {
                "maxOpenConns": 100,
                "maxIdleConns": 10,
                "connMaxLifetime": 14400,
                "timezone": "UTC"
            }
        }
        
        try:
            response = self.session.post(
                f"{self.grafana_url}/api/datasources",
                json=datasource_config,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                datasource_uid = result.get('datasource', {}).get('uid')
                logger.info(f"   ✅ MySQL数据源创建成功，UID: {datasource_uid}")
                return datasource_uid
            else:
                logger.error(f"   ❌ 数据源创建失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"   ❌ 数据源创建异常: {e}")
            return None
    
    def create_optimized_dashboard(self, datasource_uid, data_stats):
        """创建优化的延时分析仪表板"""
        logger.info("📋 创建优化的延时分析仪表板...")
        
        # 使用真实统计数据
        if data_stats:
            avg_latency = data_stats['avg_latency']
            total_matches = data_stats['total']
        else:
            avg_latency = 0
            total_matches = 0
        
        dashboard_config = {
            "dashboard": {
                "id": None,
                "title": f"⚡ ETHUSDT延时分析 (修复版) - {datetime.now().strftime('%H:%M')}",
                "tags": ["ethusdt", "latency", "fixed"],
                "timezone": "browser",
                "panels": [
                    {
                        "id": 1,
                        "title": f"📊 平均延时: {avg_latency:.1f}ms",
                        "type": "stat",
                        "gridPos": {"h": 6, "w": 6, "x": 0, "y": 0},
                        "targets": [
                            {
                                "datasource": {"type": "mysql", "uid": datasource_uid},
                                "refId": "A",
                                "rawSql": """
                                SELECT 
                                    UNIX_TIMESTAMP(NOW()) as time_sec,
                                    AVG(latency_ms) as value
                                FROM ethusdt_latency_matches 
                                WHERE created_at >= NOW() - INTERVAL 1 HOUR
                                """,
                                "format": "time_series"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None},
                                        {"color": "yellow", "value": 300},
                                        {"color": "red", "value": 600}
                                    ]
                                },
                                "unit": "ms",
                                "decimals": 1
                            }
                        },
                        "options": {
                            "colorMode": "background",
                            "graphMode": "area",
                            "justifyMode": "center",
                            "orientation": "auto",
                            "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": False},
                            "textMode": "auto"
                        }
                    },
                    {
                        "id": 2,
                        "title": f"📈 匹配数量: {total_matches}",
                        "type": "stat",
                        "gridPos": {"h": 6, "w": 6, "x": 6, "y": 0},
                        "targets": [
                            {
                                "datasource": {"type": "mysql", "uid": datasource_uid},
                                "refId": "A",
                                "rawSql": """
                                SELECT 
                                    UNIX_TIMESTAMP(NOW()) as time_sec,
                                    COUNT(*) as value
                                FROM ethusdt_latency_matches 
                                WHERE created_at >= NOW() - INTERVAL 1 HOUR
                                """,
                                "format": "time_series"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "red", "value": None},
                                        {"color": "yellow", "value": 50},
                                        {"color": "green", "value": 100}
                                    ]
                                },
                                "unit": "short",
                                "decimals": 0
                            }
                        },
                        "options": {
                            "colorMode": "background",
                            "graphMode": "area",
                            "justifyMode": "center",
                            "orientation": "auto",
                            "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": False},
                            "textMode": "auto"
                        }
                    },
                    {
                        "id": 3,
                        "title": "📊 延时范围",
                        "type": "stat",
                        "gridPos": {"h": 6, "w": 6, "x": 12, "y": 0},
                        "targets": [
                            {
                                "datasource": {"type": "mysql", "uid": datasource_uid},
                                "refId": "A",
                                "rawSql": """
                                SELECT 
                                    UNIX_TIMESTAMP(NOW()) as time_sec,
                                    MIN(latency_ms) as min_value,
                                    MAX(latency_ms) as max_value
                                FROM ethusdt_latency_matches 
                                WHERE created_at >= NOW() - INTERVAL 1 HOUR
                                """,
                                "format": "table"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None}
                                    ]
                                },
                                "unit": "ms",
                                "decimals": 0
                            }
                        },
                        "options": {
                            "colorMode": "background",
                            "graphMode": "none",
                            "justifyMode": "center",
                            "orientation": "auto",
                            "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": False},
                            "textMode": "auto"
                        }
                    },
                    {
                        "id": 4,
                        "title": "⏰ 最新更新",
                        "type": "stat",
                        "gridPos": {"h": 6, "w": 6, "x": 18, "y": 0},
                        "targets": [
                            {
                                "datasource": {"type": "mysql", "uid": datasource_uid},
                                "refId": "A",
                                "rawSql": """
                                SELECT 
                                    UNIX_TIMESTAMP(MAX(created_at)) as time_sec,
                                    TIMESTAMPDIFF(SECOND, MAX(created_at), NOW()) as seconds_ago
                                FROM ethusdt_latency_matches 
                                WHERE created_at >= NOW() - INTERVAL 1 HOUR
                                """,
                                "format": "time_series"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None},
                                        {"color": "yellow", "value": 300},
                                        {"color": "red", "value": 600}
                                    ]
                                },
                                "unit": "s",
                                "decimals": 0
                            }
                        },
                        "options": {
                            "colorMode": "background",
                            "graphMode": "none",
                            "justifyMode": "center",
                            "orientation": "auto",
                            "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": False},
                            "textMode": "auto"
                        }
                    },
                    {
                        "id": 5,
                        "title": "📈 延时趋势图 (平滑曲线)",
                        "type": "timeseries",
                        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6},
                        "targets": [
                            {
                                "datasource": {"type": "mysql", "uid": datasource_uid},
                                "refId": "A",
                                "rawSql": """
                                SELECT 
                                    UNIX_TIMESTAMP(
                                        DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:00')
                                    ) as time_sec,
                                    AVG(latency_ms) as '平均延时(ms)'
                                FROM ethusdt_latency_matches 
                                WHERE created_at >= $__timeFrom() 
                                AND created_at <= $__timeTo()
                                GROUP BY DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:00')
                                ORDER BY time_sec
                                """,
                                "format": "time_series"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "palette-classic"},
                                "custom": {
                                    "axisLabel": "延时 (毫秒)",
                                    "axisPlacement": "auto",
                                    "drawStyle": "line",
                                    "fillOpacity": 30,
                                    "lineWidth": 3,
                                    "pointSize": 5,
                                    "showPoints": "never",
                                    "lineInterpolation": "smooth"
                                },
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None},
                                        {"color": "yellow", "value": 300},
                                        {"color": "red", "value": 600}
                                    ]
                                },
                                "unit": "ms"
                            }
                        },
                        "options": {
                            "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"},
                            "tooltip": {"mode": "multi", "sort": "none"}
                        }
                    },
                    {
                        "id": 6,
                        "title": "📊 延时分布直方图",
                        "type": "histogram",
                        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6},
                        "targets": [
                            {
                                "datasource": {"type": "mysql", "uid": datasource_uid},
                                "refId": "A",
                                "rawSql": """
                                SELECT 
                                    UNIX_TIMESTAMP(created_at) as time_sec,
                                    latency_ms as value
                                FROM ethusdt_latency_matches 
                                WHERE created_at >= $__timeFrom() 
                                AND created_at <= $__timeTo()
                                ORDER BY created_at
                                """,
                                "format": "time_series"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "palette-classic"},
                                "custom": {
                                    "hideFrom": {"legend": False, "tooltip": False, "vis": False}
                                },
                                "mappings": [],
                                "unit": "ms"
                            }
                        },
                        "options": {
                            "bucketSize": 50,
                            "bucketOffset": 0
                        }
                    }
                ],
                "time": {"from": "now-2h", "to": "now"},
                "timepicker": {},
                "templating": {"list": []},
                "annotations": {"list": []},
                "refresh": "30s",
                "schemaVersion": 30,
                "version": 1,
                "links": []
            },
            "overwrite": True
        }
        
        try:
            response = self.session.post(
                f"{self.grafana_url}/api/dashboards/db",
                json=dashboard_config,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                dashboard_url = f"{self.grafana_url}{result.get('url', '')}"
                logger.info(f"   ✅ 优化仪表板创建成功")
                logger.info(f"   🌐 访问地址: {dashboard_url}")
                return dashboard_url
            else:
                logger.error(f"   ❌ 仪表板创建失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            logger.error(f"   ❌ 仪表板创建异常: {e}")
        
        return None
    
    def fix_dashboard(self):
        """修复仪表板"""
        logger.info("🔧 开始修复Grafana仪表板...")
        logger.info("=" * 60)
        
        # 1. 测试数据库连接
        db_ok, data_stats = self.test_database_connection()
        if not db_ok:
            logger.error("❌ 数据库连接失败，无法继续")
            return False
        
        # 2. 重新创建数据源
        datasource_uid = self.recreate_datasource()
        if not datasource_uid:
            logger.error("❌ 数据源创建失败，无法继续")
            return False
        
        # 3. 创建优化的仪表板
        dashboard_url = self.create_optimized_dashboard(datasource_uid, data_stats)
        if not dashboard_url:
            logger.error("❌ 仪表板创建失败")
            return False
        
        logger.info("\n" + "=" * 60)
        logger.info("🎉 Grafana仪表板修复完成！")
        logger.info("📋 修复内容:")
        logger.info("   ✅ 重新创建MySQL数据源")
        logger.info("   ✅ 优化SQL查询语句")
        logger.info("   ✅ 添加平滑曲线图")
        logger.info("   ✅ 添加延时分布直方图")
        logger.info("   ✅ 修复数据显示问题")
        logger.info(f"🌐 新仪表板地址: {dashboard_url}")
        logger.info("🔄 数据每30秒自动刷新")
        logger.info("=" * 60)
        
        return dashboard_url

def main():
    """主函数"""
    print("🔧 Grafana仪表板修复工具")
    print("=" * 50)
    print("功能:")
    print("  - 修复'No data'错误")
    print("  - 重新创建数据源")
    print("  - 优化延时图表显示")
    print("  - 添加平滑曲线和直方图")
    print()
    
    fixer = GrafanaDashboardFixer()
    dashboard_url = fixer.fix_dashboard()
    
    if dashboard_url:
        print("\n✅ 修复成功！")
        print("📊 现在您可以看到正常的延时分析数据")
        print("🎨 图表更加美观和平滑")
        
        # 自动打开浏览器
        try:
            import webbrowser
            webbrowser.open(dashboard_url)
            print("🌐 浏览器已自动打开")
        except:
            print(f"🌐 请手动访问: {dashboard_url}")
    else:
        print("\n❌ 修复失败，请检查服务状态")

if __name__ == "__main__":
    main()
