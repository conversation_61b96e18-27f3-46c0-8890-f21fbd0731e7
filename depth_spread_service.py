#!/usr/bin/env python3
"""
深度价差对比分析后台服务
每分钟处理1分钟前的数据
"""

import time
import threading
import logging
from datetime import datetime, timedelta
from depth_spread_analyzer import DepthSpreadAnalyzer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('depth_spread_service.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DepthSpreadService:
    """深度价差分析后台服务"""
    
    def __init__(self):
        self.analyzer = DepthSpreadAnalyzer()
        self.running = False
        self.symbols = ['BTCUSDT', 'ETHUSDT']
        
    def start_service(self):
        """启动后台服务"""
        logger.info("🚀 启动深度价差分析后台服务...")
        logger.info("📋 服务配置:")
        logger.info(f"   📊 分析币种: {', '.join(self.symbols)}")
        logger.info("   ⏰ 分析频率: 每分钟")
        logger.info("   📅 数据延迟: 处理1分钟前的数据")
        logger.info("   🔄 服务模式: 持续运行")
        
        self.running = True
        
        # 启动分析线程
        analysis_thread = threading.Thread(target=self._analysis_loop, daemon=True)
        analysis_thread.start()
        
        # 启动状态监控线程
        monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        monitor_thread.start()
        
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("📋 收到停止信号...")
            self.stop_service()
    
    def stop_service(self):
        """停止服务"""
        logger.info("🛑 停止深度价差分析服务...")
        self.running = False
    
    def _analysis_loop(self):
        """分析循环"""
        logger.info("🔍 分析线程启动")
        
        while self.running:
            try:
                # 计算分析时间 (1分钟前)
                analysis_time = datetime.now() - timedelta(minutes=1)
                
                # 只在每分钟的开始执行分析
                current_second = datetime.now().second
                if current_second < 10:  # 前10秒执行
                    logger.info(f"⏰ 开始分析 {analysis_time.strftime('%Y-%m-%d %H:%M')} 的数据")
                    
                    for symbol in self.symbols:
                        try:
                            success = self.analyzer.analyze_and_save_data(symbol, analysis_time)
                            status = "✅ 成功" if success else "❌ 失败"
                            logger.info(f"   📊 {symbol}: {status}")
                        except Exception as e:
                            logger.error(f"   ❌ {symbol} 分析失败: {e}")
                    
                    logger.info("📋 本轮分析完成")
                    
                    # 等待到下一分钟
                    time.sleep(50)
                else:
                    time.sleep(1)
                    
            except Exception as e:
                logger.error(f"❌ 分析循环异常: {e}")
                time.sleep(10)
    
    def _monitor_loop(self):
        """监控循环"""
        logger.info("📊 监控线程启动")
        
        while self.running:
            try:
                # 每10分钟输出一次状态
                time.sleep(600)
                
                if self.running:
                    logger.info("💓 服务运行正常")
                    
                    # 检查最近的分析结果
                    self._check_recent_results()
                    
            except Exception as e:
                logger.error(f"❌ 监控循环异常: {e}")
    
    def _check_recent_results(self):
        """检查最近的分析结果"""
        try:
            import mysql.connector
            
            connection = mysql.connector.connect(**self.analyzer.analysis_db_config)
            cursor = connection.cursor()
            
            # 检查最近10分钟的数据
            recent_time = datetime.now() - timedelta(minutes=10)
            
            for symbol in self.symbols:
                # 检查深度对比数据
                cursor.execute("""
                    SELECT COUNT(*) FROM depth_comparison 
                    WHERE symbol = %s AND analysis_time >= %s
                """, (symbol, recent_time))
                depth_count = cursor.fetchone()[0]
                
                # 检查价差对比数据
                cursor.execute("""
                    SELECT COUNT(*) FROM spread_comparison 
                    WHERE symbol = %s AND analysis_time >= %s
                """, (symbol, recent_time))
                spread_count = cursor.fetchone()[0]
                
                logger.info(f"   📊 {symbol}: 深度{depth_count}条, 价差{spread_count}条")
            
            cursor.close()
            connection.close()
            
        except Exception as e:
            logger.error(f"❌ 检查结果失败: {e}")

def main():
    """主函数"""
    print("🚀 深度价差对比分析后台服务")
    print("=" * 50)
    print("功能:")
    print("  - 每分钟自动分析BTCUSDT和ETHUSDT")
    print("  - 处理1分钟前的数据避免实时性问题")
    print("  - 计算深度对比和价差对比")
    print("  - 自动保存分析结果到数据库")
    print("  - 持续运行，支持Ctrl+C停止")
    print()
    
    service = DepthSpreadService()
    
    try:
        service.start_service()
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {e}")
    finally:
        logger.info("🔚 服务已停止")

if __name__ == "__main__":
    main()
