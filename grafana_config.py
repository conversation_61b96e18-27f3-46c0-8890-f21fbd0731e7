#!/usr/bin/env python3
"""
Grafana仪表板配置生成器
用于生成加密货币数据分析的Grafana仪表板配置
"""

import json
from datetime import datetime
from typing import Dict, List, Any

class GrafanaDashboardGenerator:
    """Grafana仪表板生成器"""
    
    def __init__(self):
        self.dashboard_title = "加密货币数据分析仪表板"
        self.dashboard_tags = ["crypto", "trading", "analysis"]
        
    def generate_funding_rate_panel(self) -> Dict[str, Any]:
        """生成资金费率面板"""
        return {
            "id": 1,
            "title": "资金费率对比",
            "type": "table",
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
            "targets": [
                {
                    "expr": "funding_rate_bitda",
                    "legendFormat": "Bitda",
                    "refId": "A"
                },
                {
                    "expr": "funding_rate_binance", 
                    "legendFormat": "Binance",
                    "refId": "B"
                }
            ],
            "fieldConfig": {
                "defaults": {
                    "custom": {
                        "displayMode": "table",
                        "filterable": True
                    },
                    "mappings": [],
                    "thresholds": {
                        "steps": [
                            {"color": "green", "value": None},
                            {"color": "red", "value": 0.01}
                        ]
                    },
                    "unit": "percent"
                }
            },
            "options": {
                "showHeader": True,
                "sortBy": [{"desc": False, "displayName": "Symbol"}]
            },
            "transformations": [
                {
                    "id": "organize",
                    "options": {
                        "excludeByName": {},
                        "indexByName": {},
                        "renameByName": {
                            "symbol": "交易对",
                            "bitda_rate": "Bitda费率",
                            "binance_rate": "Binance费率",
                            "rate_diff": "费率差值"
                        }
                    }
                }
            ]
        }
    
    def generate_latency_panel(self) -> Dict[str, Any]:
        """生成延时分析面板"""
        return {
            "id": 2,
            "title": "ETHUSDT延时分析",
            "type": "timeseries",
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
            "targets": [
                {
                    "expr": "ethusdt_latency_avg",
                    "legendFormat": "平均延时",
                    "refId": "A"
                },
                {
                    "expr": "ethusdt_latency_min",
                    "legendFormat": "最小延时", 
                    "refId": "B"
                },
                {
                    "expr": "ethusdt_latency_max",
                    "legendFormat": "最大延时",
                    "refId": "C"
                }
            ],
            "fieldConfig": {
                "defaults": {
                    "color": {"mode": "palette-classic"},
                    "custom": {
                        "axisLabel": "",
                        "axisPlacement": "auto",
                        "barAlignment": 0,
                        "drawStyle": "line",
                        "fillOpacity": 10,
                        "gradientMode": "none",
                        "hideFrom": {"legend": False, "tooltip": False, "vis": False},
                        "lineInterpolation": "linear",
                        "lineWidth": 1,
                        "pointSize": 5,
                        "scaleDistribution": {"type": "linear"},
                        "showPoints": "never",
                        "spanNulls": False,
                        "stacking": {"group": "A", "mode": "none"},
                        "thresholdsStyle": {"mode": "off"}
                    },
                    "mappings": [],
                    "thresholds": {
                        "mode": "absolute",
                        "steps": [
                            {"color": "green", "value": None},
                            {"color": "yellow", "value": 100},
                            {"color": "red", "value": 500}
                        ]
                    },
                    "unit": "ms"
                }
            },
            "options": {
                "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"},
                "tooltip": {"mode": "single", "sort": "none"}
            }
        }
    
    def generate_depth_comparison_panel(self) -> Dict[str, Any]:
        """生成深度对比面板"""
        return {
            "id": 3,
            "title": "深度对比",
            "type": "table",
            "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8},
            "targets": [
                {
                    "expr": "depth_comparison_data",
                    "refId": "A"
                }
            ],
            "fieldConfig": {
                "defaults": {
                    "custom": {
                        "displayMode": "table",
                        "filterable": True
                    },
                    "mappings": [],
                    "thresholds": {
                        "steps": [
                            {"color": "green", "value": None},
                            {"color": "yellow", "value": 2},
                            {"color": "red", "value": 5}
                        ]
                    },
                    "unit": "short"
                }
            },
            "options": {
                "showHeader": True,
                "sortBy": [{"desc": False, "displayName": "Symbol"}]
            },
            "transformations": [
                {
                    "id": "organize",
                    "options": {
                        "renameByName": {
                            "symbol": "交易对",
                            "bitda_bid_ask_sum": "Bitda买一卖一量",
                            "binance_bid_ask_sum": "Binance买一卖一量", 
                            "bid_ask_ratio": "买一卖一量比值",
                            "bitda_bid_qty": "Bitda买一量",
                            "binance_bid_qty": "Binance买一量",
                            "bid_ratio": "买一量比值",
                            "bitda_ask_qty": "Bitda卖一量",
                            "binance_ask_qty": "Binance卖一量",
                            "ask_ratio": "卖一量比值"
                        }
                    }
                }
            ]
        }
    
    def generate_price_deviation_panel(self) -> Dict[str, Any]:
        """生成标记价格偏差面板"""
        return {
            "id": 4,
            "title": "标记价格偏差分析",
            "type": "stat",
            "gridPos": {"h": 6, "w": 12, "x": 0, "y": 16},
            "targets": [
                {
                    "expr": "mark_price_deviation_max",
                    "legendFormat": "最大偏差",
                    "refId": "A"
                },
                {
                    "expr": "mark_price_deviation_avg",
                    "legendFormat": "平均偏差",
                    "refId": "B"
                }
            ],
            "fieldConfig": {
                "defaults": {
                    "color": {"mode": "thresholds"},
                    "mappings": [],
                    "thresholds": {
                        "steps": [
                            {"color": "green", "value": None},
                            {"color": "yellow", "value": 0.1},
                            {"color": "red", "value": 0.5}
                        ]
                    },
                    "unit": "percent"
                }
            },
            "options": {
                "colorMode": "background",
                "graphMode": "area",
                "justifyMode": "auto",
                "orientation": "auto",
                "reduceOptions": {
                    "calcs": ["lastNotNull"],
                    "fields": "",
                    "values": False
                },
                "textMode": "auto"
            }
        }
    
    def generate_dashboard_config(self) -> Dict[str, Any]:
        """生成完整的仪表板配置"""
        dashboard = {
            "dashboard": {
                "id": None,
                "title": self.dashboard_title,
                "tags": self.dashboard_tags,
                "timezone": "browser",
                "panels": [
                    self.generate_funding_rate_panel(),
                    self.generate_latency_panel(),
                    self.generate_depth_comparison_panel(),
                    self.generate_price_deviation_panel()
                ],
                "time": {
                    "from": "now-1h",
                    "to": "now"
                },
                "timepicker": {},
                "templating": {"list": []},
                "annotations": {"list": []},
                "refresh": "30s",
                "schemaVersion": 30,
                "version": 1,
                "links": []
            },
            "overwrite": True
        }
        
        return dashboard
    
    def save_dashboard_config(self, filename: str = "crypto_dashboard.json") -> str:
        """保存仪表板配置到文件"""
        try:
            config = self.generate_dashboard_config()
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Grafana仪表板配置已保存到: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ 保存仪表板配置失败: {e}")
            return ""
    
    def generate_prometheus_config(self) -> Dict[str, Any]:
        """生成Prometheus配置"""
        return {
            "global": {
                "scrape_interval": "15s",
                "evaluation_interval": "15s"
            },
            "scrape_configs": [
                {
                    "job_name": "crypto-data-exporter",
                    "static_configs": [
                        {
                            "targets": ["localhost:8000"]
                        }
                    ],
                    "scrape_interval": "30s",
                    "metrics_path": "/metrics"
                }
            ]
        }
    
    def save_prometheus_config(self, filename: str = "prometheus_crypto.yml") -> str:
        """保存Prometheus配置到文件"""
        try:
            import yaml
            config = self.generate_prometheus_config()
            
            with open(filename, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False)
            
            print(f"✅ Prometheus配置已保存到: {filename}")
            return filename
            
        except ImportError:
            print("❌ 需要安装PyYAML: pip install PyYAML")
            return ""
        except Exception as e:
            print(f"❌ 保存Prometheus配置失败: {e}")
            return ""

def main():
    """主函数"""
    generator = GrafanaDashboardGenerator()
    
    print("🚀 生成Grafana仪表板配置...")
    
    # 生成并保存仪表板配置
    dashboard_file = generator.save_dashboard_config()
    
    # 生成并保存Prometheus配置
    prometheus_file = generator.save_prometheus_config()
    
    if dashboard_file:
        print(f"\n📊 仪表板配置文件: {dashboard_file}")
        print("导入方法:")
        print("1. 登录Grafana (http://localhost:3000)")
        print("2. 点击 '+' -> Import")
        print("3. 上传JSON文件或粘贴配置内容")
        
    if prometheus_file:
        print(f"\n📈 Prometheus配置文件: {prometheus_file}")
        print("使用方法:")
        print("1. 将配置文件复制到Prometheus配置目录")
        print("2. 重启Prometheus服务")

if __name__ == "__main__":
    main()
