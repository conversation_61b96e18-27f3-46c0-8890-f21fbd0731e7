#!/usr/bin/env python3
"""
快速计算指定时间段的ETHUSDT延时数据
优化版本：使用批量查询
"""

import mysql.connector
import json
from datetime import datetime, timedelta

def calculate_fast_latency():
    """快速计算延时数据"""
    
    # 数据库配置
    source_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'Linuxtest',
        'database': 'depth_db'
    }
    
    target_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'Linuxtest',
        'database': 'ethusdt_latency_db'
    }
    
    # 时间段设置 (UTC+8: 12:00-13:00 = UTC: 04:00-05:00)
    start_time = datetime(2025, 5, 30, 4, 0, 0)  # UTC时间
    end_time = datetime(2025, 5, 30, 5, 0, 0)    # UTC时间
    
    print("⚡ 快速延时计算器")
    print("=" * 40)
    print(f"时间段 (UTC): {start_time} ~ {end_time}")
    print(f"时间段 (UTC+8): {start_time + timedelta(hours=8)} ~ {end_time + timedelta(hours=8)}")
    
    try:
        # 连接数据库
        source_conn = mysql.connector.connect(**source_config)
        target_conn = mysql.connector.connect(**target_config)
        
        source_cursor = source_conn.cursor()
        target_cursor = target_conn.cursor()
        
        # 清理旧数据
        print("🗑️  清理旧数据...")
        target_cursor.execute("DELETE FROM ethusdt_latency_matches")
        target_conn.commit()
        
        # 查询Bitda数据 (限制数量以加快处理)
        print("📊 查询Bitda数据...")
        bitda_query = """
        SELECT timestamp, asks, bids, created_at
        FROM bitda_depth 
        WHERE symbol = 'ETHUSDT'
        AND created_at >= %s 
        AND created_at < %s
        AND asks IS NOT NULL 
        AND bids IS NOT NULL
        ORDER BY timestamp
        LIMIT 500
        """
        
        source_cursor.execute(bitda_query, (start_time, end_time))
        bitda_records = source_cursor.fetchall()
        
        print(f"📋 获取到 {len(bitda_records)} 条Bitda记录")
        
        matches_found = 0
        latency_list = []
        
        for i, record in enumerate(bitda_records):
            bitda_timestamp, asks_json, bids_json, created_at = record
            
            if i % 50 == 0:
                print(f"  处理进度: {i+1}/{len(bitda_records)}")
            
            try:
                # 解析JSON数据
                asks_data = json.loads(asks_json)
                bids_data = json.loads(bids_json)
                
                # 计算买一卖一价格
                ask_prices = [float(item[0]) for item in asks_data]
                bid_prices = [float(item[0]) for item in bids_data]
                
                ask_price_1 = min(ask_prices)
                bid_price_1 = max(bid_prices)
                
                ask_qty_1 = float([item[1] for item in asks_data if float(item[0]) == ask_price_1][0])
                bid_qty_1 = float([item[1] for item in bids_data if float(item[0]) == bid_price_1][0])
                
                # 查找匹配的Binance数据
                binance_query = """
                SELECT bid_price, ask_price, bid_qty, ask_qty, event_time
                FROM binance_bookticker 
                WHERE symbol = 'ETHUSDT'
                AND bid_price = %s
                AND ask_price = %s
                AND event_time < %s
                ORDER BY event_time ASC
                LIMIT 1
                """
                
                source_cursor.execute(binance_query, (bid_price_1, ask_price_1, bitda_timestamp))
                binance_match = source_cursor.fetchone()
                
                if binance_match:
                    binance_bid_price, binance_ask_price, binance_bid_qty, binance_ask_qty, binance_timestamp = binance_match
                    latency = bitda_timestamp - binance_timestamp
                    
                    if 10 <= latency <= 2000:
                        # 插入匹配记录
                        insert_query = """
                        INSERT INTO ethusdt_latency_matches 
                        (bitda_timestamp, binance_timestamp, latency_ms, match_type, 
                         bitda_price, binance_price, bitda_qty, binance_qty, 
                         price_spread, match_quality, created_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """
                        
                        price_spread = ask_price_1 - bid_price_1
                        
                        target_cursor.execute(insert_query, (
                            bitda_timestamp, binance_timestamp, latency, 'complete',
                            (bid_price_1 + ask_price_1) / 2,
                            (binance_bid_price + binance_ask_price) / 2,
                            (bid_qty_1 + ask_qty_1) / 2,
                            (binance_bid_qty + binance_ask_qty) / 2,
                            price_spread, 1.0000, created_at
                        ))
                        
                        matches_found += 1
                        latency_list.append(latency)
                        
                        if matches_found <= 10:
                            utc8_time = created_at + timedelta(hours=8)
                            print(f"  ✅ 匹配 {matches_found}: {utc8_time.strftime('%H:%M:%S')} 延时={latency}ms")
                
            except Exception as e:
                print(f"  ❌ 处理记录失败: {e}")
                continue
        
        # 提交事务
        target_conn.commit()
        
        # 显示结果
        print(f"\n📈 处理完成:")
        print(f"  - 处理记录: {len(bitda_records)}")
        print(f"  - 完全匹配: {matches_found}")
        
        if latency_list:
            print(f"  - 平均延时: {sum(latency_list)/len(latency_list):.2f}ms")
            print(f"  - 最小延时: {min(latency_list)}ms")
            print(f"  - 最大延时: {max(latency_list)}ms")
            
            # 延时分布
            ranges = [(10, 50), (51, 100), (101, 200), (201, 500), (501, 1000), (1001, 2000)]
            print(f"\n📊 延时分布:")
            for min_val, max_val in ranges:
                count = len([l for l in latency_list if min_val <= l <= max_val])
                if count > 0:
                    percentage = count / len(latency_list) * 100
                    print(f"  - {min_val}-{max_val}ms: {count}条 ({percentage:.1f}%)")
        
        # 查询最终结果
        target_cursor.execute("""
            SELECT COUNT(*) as total, AVG(latency_ms) as avg_lat, 
                   MIN(latency_ms) as min_lat, MAX(latency_ms) as max_lat
            FROM ethusdt_latency_matches
        """)
        
        final_result = target_cursor.fetchone()
        if final_result and final_result[0] > 0:
            total, avg_lat, min_lat, max_lat = final_result
            print(f"\n📋 最终统计:")
            print(f"  - 总匹配数: {total}")
            print(f"  - 平均延时: {avg_lat:.2f}ms")
            print(f"  - 延时范围: {min_lat}ms ~ {max_lat}ms")
        
        source_cursor.close()
        target_cursor.close()
        source_conn.close()
        target_conn.close()
        
        return matches_found > 0
        
    except Exception as e:
        print(f"❌ 计算失败: {e}")
        return False

def main():
    """主函数"""
    if calculate_fast_latency():
        print("\n✅ 延时数据计算完成！")
        print("📊 数据已保存到数据库")
        print("🌐 可在Grafana查看: http://localhost:3000")
    else:
        print("\n❌ 计算失败")

if __name__ == "__main__":
    main()
