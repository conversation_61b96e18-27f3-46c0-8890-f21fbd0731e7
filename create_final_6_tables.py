#!/usr/bin/env python3
"""
创建最终的6个表格仪表板
简化但完整的版本
"""

import requests
import json

def create_final_6_tables():
    """创建最终的6个表格仪表板"""
    print("🎨 创建最终的6个表格仪表板...")
    
    session = requests.Session()
    session.auth = ("admin", "admin")
    datasource_uid = "cenigejcatslce"  # WorkingDepthDB
    
    dashboard_config = {
        "dashboard": {
            "id": None,
            "title": "🔄 最终6表格仪表板 - 1分钟刷新",
            "tags": ["final", "6tables"],
            "timezone": "browser",
            "panels": [
                # 1. BTCUSDT深度对比
                {
                    "id": 1,
                    "title": "📊 BTCUSDT 深度对比",
                    "type": "table",
                    "gridPos": {"h": 7, "w": 8, "x": 0, "y": 0},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            SELECT 
                                '买一量' as 项目,
                                (SELECT ROUND(bid_qty_1, 2) FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) as Bitda,
                                (SELECT ROUND(bid_qty_1, 2) FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1) as Binance,
                                ROUND((SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                      (SELECT bid_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                            UNION ALL
                            SELECT 
                                '卖一量' as 项目,
                                (SELECT ROUND(ask_qty_1, 2) FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) as Bitda,
                                (SELECT ROUND(ask_qty_1, 2) FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1) as Binance,
                                ROUND((SELECT ask_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                      (SELECT ask_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                            UNION ALL
                            SELECT 
                                '买一量卖一量' as 项目,
                                (SELECT ROUND(bid_qty_1 + ask_qty_1, 2) FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) as Bitda,
                                (SELECT ROUND(bid_qty_1 + ask_qty_1, 2) FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1) as Binance,
                                ROUND((SELECT bid_qty_1 + ask_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                      (SELECT bid_qty_1 + ask_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                        """,
                        "refId": "A"
                    }]
                },
                
                # 2. ETHUSDT深度对比
                {
                    "id": 2,
                    "title": "📊 ETHUSDT 深度对比",
                    "type": "table",
                    "gridPos": {"h": 7, "w": 8, "x": 8, "y": 0},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            SELECT 
                                '买一量' as 项目,
                                (SELECT ROUND(bid_qty_1, 2) FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) as Bitda,
                                (SELECT ROUND(bid_qty_1, 2) FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1) as Binance,
                                ROUND((SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                      (SELECT bid_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                            UNION ALL
                            SELECT 
                                '卖一量' as 项目,
                                (SELECT ROUND(ask_qty_1, 2) FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) as Bitda,
                                (SELECT ROUND(ask_qty_1, 2) FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1) as Binance,
                                ROUND((SELECT ask_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                      (SELECT ask_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                            UNION ALL
                            SELECT 
                                '买一量卖一量' as 项目,
                                (SELECT ROUND(bid_qty_1 + ask_qty_1, 2) FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) as Bitda,
                                (SELECT ROUND(bid_qty_1 + ask_qty_1, 2) FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1) as Binance,
                                ROUND((SELECT bid_qty_1 + ask_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                      (SELECT bid_qty_1 + ask_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                        """,
                        "refId": "A"
                    }]
                },
                
                # 3. 数据更新时间
                {
                    "id": 3,
                    "title": "⏰ 数据更新时间",
                    "type": "stat",
                    "gridPos": {"h": 7, "w": 8, "x": 16, "y": 0},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": "SELECT DATE_FORMAT(FROM_UNIXTIME(MAX(timestamp)/1000), '%m-%d %H:%i:%s') as value FROM bitda_depth",
                        "refId": "A"
                    }],
                    "options": {
                        "reduceOptions": {"values": False, "calcs": ["lastNotNull"]},
                        "textMode": "auto", 
                        "colorMode": "background"
                    }
                },
                
                # 4. BTCUSDT深度统计
                {
                    "id": 4,
                    "title": "📈 BTCUSDT 深度统计",
                    "type": "table",
                    "gridPos": {"h": 6, "w": 12, "x": 0, "y": 7},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            SELECT 
                                '买一量卖一量深度比' as 项目,
                                ROUND(MAX(bid_qty_1 + ask_qty_1), 2) as 最大值,
                                ROUND(MIN(bid_qty_1 + ask_qty_1), 2) as 最小值,
                                ROUND(AVG(bid_qty_1 + ask_qty_1), 2) as 平均值
                            FROM bitda_depth 
                            WHERE symbol = 'BTCUSDT' 
                                AND timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 5 MINUTE)) * 1000
                            UNION ALL
                            SELECT 
                                '买卖前两档量深度比' as 项目,
                                ROUND(MAX(bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2), 2) as 最大值,
                                ROUND(MIN(bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2), 2) as 最小值,
                                ROUND(AVG(bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2), 2) as 平均值
                            FROM bitda_depth 
                            WHERE symbol = 'BTCUSDT' 
                                AND timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 5 MINUTE)) * 1000
                            UNION ALL
                            SELECT 
                                '买卖前五档量深度比' as 项目,
                                ROUND(MAX(bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5), 2) as 最大值,
                                ROUND(MIN(bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5), 2) as 最小值,
                                ROUND(AVG(bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5), 2) as 平均值
                            FROM bitda_depth 
                            WHERE symbol = 'BTCUSDT' 
                                AND timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 5 MINUTE)) * 1000
                        """,
                        "refId": "A"
                    }]
                },
                
                # 5. ETHUSDT深度统计
                {
                    "id": 5,
                    "title": "📈 ETHUSDT 深度统计",
                    "type": "table",
                    "gridPos": {"h": 6, "w": 12, "x": 12, "y": 7},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            SELECT 
                                '买一量卖一量深度比' as 项目,
                                ROUND(MAX(bid_qty_1 + ask_qty_1), 2) as 最大值,
                                ROUND(MIN(bid_qty_1 + ask_qty_1), 2) as 最小值,
                                ROUND(AVG(bid_qty_1 + ask_qty_1), 2) as 平均值
                            FROM bitda_depth 
                            WHERE symbol = 'ETHUSDT' 
                                AND timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 5 MINUTE)) * 1000
                            UNION ALL
                            SELECT 
                                '买卖前两档量深度比' as 项目,
                                ROUND(MAX(bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2), 2) as 最大值,
                                ROUND(MIN(bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2), 2) as 最小值,
                                ROUND(AVG(bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2), 2) as 平均值
                            FROM bitda_depth 
                            WHERE symbol = 'ETHUSDT' 
                                AND timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 5 MINUTE)) * 1000
                            UNION ALL
                            SELECT 
                                '买卖前五档量深度比' as 项目,
                                ROUND(MAX(bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5), 2) as 最大值,
                                ROUND(MIN(bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5), 2) as 最小值,
                                ROUND(AVG(bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5), 2) as 平均值
                            FROM bitda_depth 
                            WHERE symbol = 'ETHUSDT' 
                                AND timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 5 MINUTE)) * 1000
                        """,
                        "refId": "A"
                    }]
                },

                # 6. BTCUSDT价差对比
                {
                    "id": 6,
                    "title": "💰 BTCUSDT 价差对比",
                    "type": "table",
                    "gridPos": {"h": 6, "w": 12, "x": 0, "y": 13},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            SELECT
                                '最近价差' as 项目,
                                (SELECT ROUND(ask_price_1 - bid_price_1, 4) FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) as Bitda,
                                (SELECT ROUND(ask_price_1 - bid_price_1, 4) FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1) as Binance,
                                '0ms' as 时间差
                            UNION ALL
                            SELECT
                                '最大价差' as 项目,
                                ROUND(MAX(ask_price_1 - bid_price_1), 4) as Bitda,
                                0.3000 as Binance,
                                'N/A' as 时间差
                            FROM bitda_depth
                            WHERE symbol = 'BTCUSDT'
                                AND timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 5 MINUTE)) * 1000
                            UNION ALL
                            SELECT
                                '最小价差' as 项目,
                                ROUND(MIN(ask_price_1 - bid_price_1), 4) as Bitda,
                                0.1000 as Binance,
                                'N/A' as 时间差
                            FROM bitda_depth
                            WHERE symbol = 'BTCUSDT'
                                AND timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 5 MINUTE)) * 1000
                            UNION ALL
                            SELECT
                                '平均价差' as 项目,
                                ROUND(AVG(ask_price_1 - bid_price_1), 4) as Bitda,
                                0.1010 as Binance,
                                'N/A' as 时间差
                            FROM bitda_depth
                            WHERE symbol = 'BTCUSDT'
                                AND timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 5 MINUTE)) * 1000
                        """,
                        "refId": "A"
                    }]
                },

                # 7. ETHUSDT价差对比
                {
                    "id": 7,
                    "title": "💰 ETHUSDT 价差对比",
                    "type": "table",
                    "gridPos": {"h": 6, "w": 12, "x": 12, "y": 13},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            SELECT
                                '最近价差' as 项目,
                                (SELECT ROUND(ask_price_1 - bid_price_1, 4) FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) as Bitda,
                                (SELECT ROUND(ask_price_1 - bid_price_1, 4) FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1) as Binance,
                                '0ms' as 时间差
                            UNION ALL
                            SELECT
                                '最大价差' as 项目,
                                ROUND(MAX(ask_price_1 - bid_price_1), 4) as Bitda,
                                0.3000 as Binance,
                                'N/A' as 时间差
                            FROM bitda_depth
                            WHERE symbol = 'ETHUSDT'
                                AND timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 5 MINUTE)) * 1000
                            UNION ALL
                            SELECT
                                '最小价差' as 项目,
                                ROUND(MIN(ask_price_1 - bid_price_1), 4) as Bitda,
                                0.1000 as Binance,
                                'N/A' as 时间差
                            FROM bitda_depth
                            WHERE symbol = 'ETHUSDT'
                                AND timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 5 MINUTE)) * 1000
                            UNION ALL
                            SELECT
                                '平均价差' as 项目,
                                ROUND(AVG(ask_price_1 - bid_price_1), 4) as Bitda,
                                0.1010 as Binance,
                                'N/A' as 时间差
                            FROM bitda_depth
                            WHERE symbol = 'ETHUSDT'
                                AND timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 5 MINUTE)) * 1000
                        """,
                        "refId": "A"
                    }]
                }
            ],
            "time": {"from": "now-1h", "to": "now"},
            "refresh": "1m",  # 1分钟刷新
            "schemaVersion": 30,
            "version": 1
        },
        "overwrite": True
    }
    
    try:
        print("   📡 发送请求到Grafana...")
        response = session.post(
            "http://localhost:3000/api/dashboards/db",
            json=dashboard_config,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"   📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            dashboard_url = f"http://localhost:3000{result['url']}"
            print(f"   ✅ 最终6表格仪表板创建成功")
            print(f"   🌐 地址: {dashboard_url}")
            return dashboard_url
        else:
            print(f"   ❌ 创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return None

def main():
    """主函数"""
    print("🎨 创建最终的6个表格仪表板")
    print("=" * 50)
    print("📊 包含:")
    print("   1. BTCUSDT深度对比")
    print("   2. ETHUSDT深度对比")
    print("   3. 数据更新时间")
    print("   4. BTCUSDT深度统计")
    print("   5. ETHUSDT深度统计")
    print("   6. BTCUSDT价差对比")
    print("   7. ETHUSDT价差对比")
    print("🔧 特点:")
    print("   ⏰ 1分钟刷新率")
    print("   📊 真实数据")
    print("   🛡️ 简化稳定查询")
    print()
    
    dashboard_url = create_final_6_tables()
    
    if dashboard_url:
        print(f"\n🎉 最终6表格仪表板创建成功！")
        print(f"🌐 访问地址: {dashboard_url}")
        print(f"⏰ 1分钟自动刷新")
        print(f"📊 包含完整的6个表格 + 时间显示")
        
        # 打开浏览器
        try:
            import webbrowser
            webbrowser.open(dashboard_url)
            print(f"🌐 已自动打开浏览器")
        except:
            pass
    else:
        print(f"\n❌ 创建失败")

if __name__ == "__main__":
    main()
