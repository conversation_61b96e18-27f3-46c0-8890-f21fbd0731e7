#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WebSocket数据收集器
用于从Binance和交易所（生产环境和测试环境）收集WebSocket数据，
计算延迟和深度比较指标，并将数据存储到MySQL数据库中。
"""

import os
import json
import time
import logging
import asyncio
import random
import websockets
import mysql.connector
from mysql.connector import pooling
from datetime import datetime
import threading
import queue
import signal
import sys
import psutil  # 用于监控CPU使用率
from dotenv import load_dotenv
import socks
import socket

# 配置测试打印日志
test_logger = logging.getLogger("test_print")
# 使用/tmp目录存放日志文件，确保有写入权限
test_handler = logging.FileHandler("/tmp/testprint.log")
test_handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))
test_logger.setLevel(logging.INFO)
test_logger.addHandler(test_handler)

# 标记是否已经打印过测试数据
binance_test_printed = False
production_test_printed = False

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("/tmp/ws_data_collector.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("ws_data_collector")

# 加载环境变量
load_dotenv()

# 配置系统代理 - 已禁用，不使用代理服务器
def setup_proxy():
    """设置系统代理"""
    # 不使用代理服务器，直接连接
    logger.info("不使用代理服务器，使用直连")
    # try:
    #     proxy_host = os.getenv('PROXY_HOST')
    #     proxy_port = os.getenv('PROXY_PORT')
    #
    #     if proxy_host and proxy_port:
    #         # 设置SOCKS代理
    #         socks.set_default_proxy(socks.SOCKS5, proxy_host, int(proxy_port))
    #         socket.socket = socks.socksocket
    #         logger.info(f"已配置SOCKS5代理: {proxy_host}:{proxy_port}")
    #     else:
    #         logger.info("未配置代理，使用直连")
    # except Exception as e:
    #     logger.warning(f"配置代理失败: {e}")

# 配置常量
SYMBOLS = ["BTCUSDT", "ETHUSDT"]  # 收集两个交易对的数据
COMPARE_SYMBOLS = ["ETHUSDT"]  # 只比较ETHUSDT
ENVIRONMENTS = ["binance", "production"]  # 已移除测试环境
QUEUE_SIZE = 1000
BATCH_SIZE = 50
MAX_RETRIES = 3
RETRY_DELAY = 5

# 币安WebSocket连接配置
BINANCE_CONNECTION_TIMEOUT = 23 * 60 * 60  # 23小时后主动重连，避免24小时限制
BINANCE_PING_INTERVAL = 30  # 每30秒发送一次ping保持连接活跃

# 是否启用Binance连接（由于地区限制，可能需要禁用）
ENABLE_BINANCE = os.getenv('ENABLE_BINANCE', 'true').lower() == 'true'

# CPU监控配置
CPU_CHECK_INTERVAL = 60  # 每60秒检查一次CPU使用率
CPU_THRESHOLD = 80  # CPU使用率阈值，超过此值将重启进程
SHUTDOWN_FLAG = False  # 全局关闭标志

# WebSocket URLs
BINANCE_WS_URL = "wss://fstream.binance.com/stream?streams="
PRODUCTION_WS_URL = "wss://ws.bitda.com/wsf"
# TESTING_WS_URL = "wss://ws.bitdatech.com/wsf"  # 已注释掉测试环境URL

# 数据库配置
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', 'Linuxtest'),
    'database': os.getenv('DB_NAME', 'depth_db'),
    'port': int(os.getenv('DB_PORT', '3306')),
    'charset': 'utf8mb4',
    'use_unicode': True,
    'get_warnings': True,
    'autocommit': True,
    'raise_on_warnings': True,
}

# 创建数据库连接池
try:
    connection_pool = pooling.MySQLConnectionPool(
        pool_name="ws_data_collector_pool",
        pool_size=5,
        **DB_CONFIG
    )
    logger.info("数据库连接池创建成功")
except Exception as e:
    logger.error(f"创建数据库连接池失败: {e}")
    connection_pool = None

# 数据队列
data_queue = queue.Queue(maxsize=QUEUE_SIZE)

# 主函数
async def main():
    """主函数，启动所有WebSocket客户端和数据处理线程"""
    global SHUTDOWN_FLAG

    logger.info("启动WebSocket数据收集器")

    # 设置系统代理
    setup_proxy()

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    logger.info("已注册信号处理器")

    # 启动数据库写入线程
    db_thread = threading.Thread(target=db_writer)
    db_thread.daemon = True
    db_thread.start()

    # 创建任务列表
    tasks = []

    # 添加Binance WebSocket任务（如果启用）
    if ENABLE_BINANCE:
        for symbol in SYMBOLS:
            tasks.append(asyncio.create_task(connect_binance(symbol)))
        logger.info("Binance WebSocket连接已启用")
    else:
        logger.warning("Binance WebSocket连接已禁用（由于地区限制）")

    # 添加生产环境WebSocket任务
    tasks.append(asyncio.create_task(connect_production_env()))

    # 测试环境WebSocket任务已注释掉
    # tasks.append(asyncio.create_task(connect_testing_env()))

    # 添加CPU监控任务
    tasks.append(asyncio.create_task(cpu_monitor_task()))

    # 等待所有任务完成或者收到关闭信号
    try:
        await asyncio.gather(*tasks)
    except asyncio.CancelledError:
        logger.info("任务被取消")

    # 如果收到关闭信号，取消所有任务
    if SHUTDOWN_FLAG:
        logger.info("正在关闭所有任务...")
        for task in tasks:
            if not task.done():
                task.cancel()

        # 等待所有任务完成取消
        await asyncio.gather(*tasks, return_exceptions=True)
        logger.info("所有任务已关闭")

# 数据库写入线程
def db_writer():
    """数据库写入线程，从队列中获取数据并批量写入数据库"""
    logger.info("启动数据库写入线程")

    buffer = []
    last_write_time = time.time()

    while not SHUTDOWN_FLAG:
        try:
            # 从队列获取数据，最多等待1秒
            try:
                data = data_queue.get(timeout=1)
                buffer.append(data)
            except queue.Empty:
                pass

            # 如果缓冲区达到批处理大小或者距离上次写入已经过去了10秒，则写入数据库
            current_time = time.time()
            if len(buffer) >= BATCH_SIZE or (buffer and current_time - last_write_time >= 10):
                if buffer:
                    save_to_database(buffer)
                    buffer = []
                    last_write_time = current_time

        except Exception as e:
            logger.error(f"数据库写入线程出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            time.sleep(5)  # 出错后等待一段时间再继续

    # 在关闭前确保所有数据都被写入
    if buffer:
        try:
            logger.info(f"关闭前写入剩余的 {len(buffer)} 条数据")
            save_to_database(buffer)
        except Exception as e:
            logger.error(f"关闭前写入数据失败: {e}")

    logger.info("数据库写入线程已关闭")

# 保存数据到数据库
def save_to_database(data_list):
    """将数据保存到数据库"""
    if not data_list:
        return

    # 按数据类型分组
    matches_data = []
    binance_raw_data = []
    exchange_raw_data = []

    for data in data_list:
        if data["type"] == "depth_match":
            matches_data.append(data)
        elif data["type"] == "binance_raw":
            binance_raw_data.append(data)
        elif data["type"] == "exchange_raw":
            exchange_raw_data.append(data)

    # 保存深度匹配数据
    if matches_data:
        save_depth_matches(matches_data)

    # 保存Binance原始数据
    if binance_raw_data:
        save_binance_raw_data(binance_raw_data)

    # 保存交易所原始数据
    if exchange_raw_data:
        save_exchange_raw_data(exchange_raw_data)

    logger.info(f"成功保存 {len(data_list)} 条数据到数据库")

# 保存深度匹配数据
def save_depth_matches(matches_data):
    """保存深度匹配数据到数据库"""
    if not matches_data:
        return

    conn = None
    cursor = None

    try:
        conn = connection_pool.get_connection()
        cursor = conn.cursor()

        query = """
        INSERT INTO depth_matches (
            symbol, environment, timestamp,
            binance_bid_price, binance_ask_price,
            binance_bid_qty, binance_ask_qty,
            exchange_bid_qty, exchange_ask_qty,
            binance_depth_sum, exchange_depth_sum,
            binance_message_time, exchange_time,
            message_latency_ms, engine_latency_ms,
            depth_ratio
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
        """

        values = []
        for data in matches_data:
            values.append((
                data["symbol"],
                data["environment"],
                data["timestamp"],
                data["binance_bid_price"],
                data["binance_ask_price"],
                data["binance_bid_qty"],
                data["binance_ask_qty"],
                data["exchange_bid_qty"],
                data["exchange_ask_qty"],
                data["binance_depth_sum"],
                data["exchange_depth_sum"],
                data["binance_message_time"],
                data["exchange_time"],
                data["message_latency_ms"],
                data["engine_latency_ms"],
                data["depth_ratio"]
            ))

        cursor.executemany(query, values)
        conn.commit()

        logger.info(f"成功保存 {len(values)} 条深度匹配数据")

    except Exception as e:
        logger.error(f"保存深度匹配数据失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# 保存Binance原始数据
def save_binance_raw_data(binance_data_list):
    """保存Binance原始数据到数据库"""
    if not binance_data_list:
        return

    conn = None
    cursor = None

    try:
        conn = connection_pool.get_connection()
        cursor = conn.cursor()

        query = """
        INSERT INTO binance_raw_data (
            symbol, trade_time, event_time,
            bid_price, bid_qty, ask_price, ask_qty
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s
        )
        """

        values = []
        for data in binance_data_list:
            values.append((
                data["symbol"],
                data["trade_time"],
                data["event_time"],
                data["bid_price"],
                data["bid_qty"],
                data["ask_price"],
                data["ask_qty"]
            ))

        cursor.executemany(query, values)
        conn.commit()

        logger.info(f"成功保存 {len(values)} 条Binance原始数据")

    except Exception as e:
        logger.error(f"保存Binance原始数据失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# 保存交易所原始数据
def save_exchange_raw_data(exchange_data_list):
    """保存交易所原始数据到数据库"""
    if not exchange_data_list:
        return

    conn = None
    cursor = None

    try:
        conn = connection_pool.get_connection()
        cursor = conn.cursor()

        query = """
        INSERT INTO exchange_raw_data (
            symbol, environment, time,
            index_price, sign_price, last_price,
            asks, bids
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s
        )
        """

        values = []
        for data in exchange_data_list:
            values.append((
                data["symbol"],
                data["environment"],
                data["time"],
                data["index_price"],
                data["sign_price"],
                data["last_price"],
                json.dumps(data["asks"]),
                json.dumps(data["bids"])
            ))

        cursor.executemany(query, values)
        conn.commit()

        logger.info(f"成功保存 {len(values)} 条交易所原始数据")

    except Exception as e:
        logger.error(f"保存交易所原始数据失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# 心跳函数
async def heartbeat(ws, interval, exchange_name):
    """定期发送心跳消息"""
    while not SHUTDOWN_FLAG:
        try:
            # 发送ping消息
            ping_obj = {"method": "ping"}
            await ws.send(json.dumps(ping_obj))
            logger.info(f"向{exchange_name}发送ping消息")

            # 等待指定的时间间隔，但每秒检查一次关闭标志
            for _ in range(interval):
                if SHUTDOWN_FLAG:
                    break
                await asyncio.sleep(1)
        except Exception as e:
            logger.error(f"{exchange_name}心跳任务出错: {e}")
            break

    logger.info(f"{exchange_name}心跳任务已停止")

# 订阅深度数据
async def subscribe_depth(ws, symbol, exchange_name):
    """订阅深度数据"""
    try:
        # 构造订阅消息
        subscribe_obj = {
            "method": "subscribe.depth",
            "params": {
                "market": symbol,
                "merge": "0"
            },
            "id": 0
        }

        # 发送订阅消息
        await ws.send(json.dumps(subscribe_obj))
        logger.info(f"向{exchange_name}订阅{symbol}深度数据")
    except Exception as e:
        logger.error(f"订阅{exchange_name} {symbol}深度数据出错: {e}")

# 比较数据
def compare_data(symbol, environment):
    """比较Binance和交易所数据"""
    try:
        # 获取Binance数据
        binance_data_key = f"binance_{symbol}_data"
        exchange_data_key = f"{environment}_{symbol}_data"

        if binance_data_key not in globals() or exchange_data_key not in globals():
            return

        binance_data = globals()[binance_data_key]
        exchange_data = globals()[exchange_data_key]

        # 检查买一卖一价格是否完全相等（不允许任何误差）
        # 直接使用float比较，保持原始精度
        prices_equal = (binance_data['bid_price'] == exchange_data['bid_price'] and
                        binance_data['ask_price'] == exchange_data['ask_price'])

        # 记录价格比较结果
        if not prices_equal:
            logger.info(f"价格不匹配 - Binance: 买一={binance_data['bid_price']}, 卖一={binance_data['ask_price']} | {environment}: 买一={exchange_data['bid_price']}, 卖一={exchange_data['ask_price']}")

        if prices_equal:

            # 计算延迟
            binance_time = binance_data["event_time"]
            exchange_time = exchange_data["exchange_time"]

            # 确保Binance时间早于交易所时间（延迟不能为负）
            if binance_time < exchange_time:
                message_latency_ms = exchange_time - binance_time

                # 计算深度比值
                binance_depth_sum = binance_data["bid_qty"] + binance_data["ask_qty"]
                exchange_depth_sum = exchange_data["bid_qty"] + exchange_data["ask_qty"]
                depth_ratio = exchange_depth_sum / binance_depth_sum if binance_depth_sum > 0 else 0

                # 创建匹配数据
                match_data = {
                    "type": "depth_match",
                    "symbol": symbol,
                    "environment": environment,
                    # 使用当前时间，格式为YYYY-MM-DD HH:MM:SS.mmm
                    # 确保使用系统的本地时间（UTC+8）
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3],
                    "binance_bid_price": binance_data["bid_price"],
                    "binance_ask_price": binance_data["ask_price"],
                    "binance_bid_qty": binance_data["bid_qty"],
                    "binance_ask_qty": binance_data["ask_qty"],
                    "exchange_bid_qty": exchange_data["bid_qty"],
                    "exchange_ask_qty": exchange_data["ask_qty"],
                    "binance_depth_sum": binance_depth_sum,
                    "exchange_depth_sum": exchange_depth_sum,
                    "binance_message_time": binance_time,
                    "exchange_time": exchange_time,
                    "message_latency_ms": message_latency_ms,
                    "engine_latency_ms": message_latency_ms,  # 简化处理，使用相同的延迟
                    "depth_ratio": depth_ratio
                }

                # 将匹配数据放入队列
                data_queue.put(match_data)

                # 打印匹配信息
                logger.info(f"找到匹配! {symbol} {environment} 延迟: {message_latency_ms}ms, 深度比值: {depth_ratio:.4f}")

    except Exception as e:
        logger.error(f"比较数据出错: {e}")
        import traceback
        logger.error(traceback.format_exc())

# 更新统计数据
def update_stats():
    """更新统计数据"""
    conn = None
    cursor = None

    try:
        conn = connection_pool.get_connection()
        cursor = conn.cursor()

        # 对于每个比较的交易对和环境，计算最近5分钟的统计数据
        for symbol in COMPARE_SYMBOLS:
            # 只处理生产环境，测试环境已注释掉
            for env in ["production"]:
                # 首先检查是否有匹配数据
                check_query = """
                SELECT COUNT(*) FROM depth_matches
                WHERE symbol = %s
                AND environment = %s
                AND timestamp >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
                """

                cursor.execute(check_query, (symbol, env))
                count = cursor.fetchone()[0]

                # 只有在有匹配数据时才插入统计数据
                if count > 0:
                    query = """
                    INSERT INTO depth_stats (
                        symbol, environment, timestamp,
                        avg_message_latency, min_message_latency, max_message_latency,
                        avg_engine_latency, min_engine_latency, max_engine_latency,
                        avg_depth_ratio, min_depth_ratio, max_depth_ratio,
                        match_count
                    )
                    SELECT
                        %s, %s, NOW(),
                        AVG(message_latency_ms), MIN(message_latency_ms), MAX(message_latency_ms),
                        AVG(engine_latency_ms), MIN(engine_latency_ms), MAX(engine_latency_ms),
                        AVG(depth_ratio), MIN(depth_ratio), MAX(depth_ratio),
                        COUNT(*)
                    FROM depth_matches
                    WHERE symbol = %s
                    AND environment = %s
                    AND timestamp >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
                    """

                    cursor.execute(query, (symbol, env, symbol, env))
                    logger.info(f"已更新 {symbol} {env} 的统计数据")

        conn.commit()
        logger.info("统计数据更新成功")

    except Exception as e:
        logger.error(f"更新统计数据失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# Binance WebSocket ping任务
async def binance_ping_task(ws, symbol):
    """定期向Binance WebSocket发送ping消息保持连接活跃"""
    while not SHUTDOWN_FLAG:
        try:
            await asyncio.sleep(BINANCE_PING_INTERVAL)
            if not SHUTDOWN_FLAG:
                await ws.ping()
                logger.debug(f"向Binance {symbol} 发送ping")
        except Exception as e:
            logger.warning(f"Binance {symbol} ping任务出错: {e}")
            break

# 连接Binance WebSocket
async def connect_binance(symbol):
    """连接到Binance WebSocket，支持主动重连避免24小时限制"""
    symbol_lower = symbol.lower()
    url = f"{BINANCE_WS_URL}{symbol_lower}@bookTicker"

    logger.info(f"开始连接Binance WebSocket: {url}")

    # 外层循环，确保断开后重连
    while not SHUTDOWN_FLAG:
        connection_start_time = time.time()
        ping_task = None

        try:
            # 连接WebSocket
            async with websockets.connect(url, ping_interval=None) as ws:
                logger.info(f"已连接到Binance {symbol} WebSocket")

                # 启动ping任务保持连接活跃
                ping_task = asyncio.create_task(binance_ping_task(ws, symbol))

                # 记录消息接收开始时间
                start_time = time.time()
                message_count = 0

                # 接收并处理消息
                while not SHUTDOWN_FLAG:
                    try:
                        # 检查连接时间，如果接近24小时则主动重连
                        current_time = time.time()
                        connection_duration = current_time - connection_start_time

                        if connection_duration >= BINANCE_CONNECTION_TIMEOUT:
                            logger.info(f"Binance {symbol} 连接已运行 {connection_duration/3600:.1f} 小时，主动重连避免24小时限制")
                            break

                        # 设置接收超时，避免长时间阻塞
                        message = await asyncio.wait_for(ws.recv(), timeout=60)
                        message_count += 1

                        # 每100条消息记录一次消息率
                        if message_count % 100 == 0:
                            elapsed = time.time() - start_time
                            rate = message_count / elapsed if elapsed > 0 else 0
                            logger.info(f"Binance {symbol} 消息率: {rate:.2f} 消息/秒，连接时长: {connection_duration/3600:.1f}小时")

                        # 解析JSON数据
                        try:
                            data = json.loads(message)

                            # 处理bookTicker数据
                            if 'data' in data:
                                ticker_data = data['data']

                                # 获取价格和数量
                                bid_price = float(ticker_data.get('b', 0))
                                bid_qty = float(ticker_data.get('B', 0))
                                ask_price = float(ticker_data.get('a', 0))
                                ask_qty = float(ticker_data.get('A', 0))

                                # 获取Binance事件时间和交易时间
                                event_time = ticker_data.get('E', int(time.time() * 1000))
                                trade_time = ticker_data.get('T', event_time)

                                # 记录当前本地时间
                                local_time = int(time.time() * 1000)

                                # 打印价格信息
                                logger.info(f"Binance {symbol} 报价: 买一 {bid_price}x{bid_qty}, 卖一 {ask_price}x{ask_qty}")

                                # 存储Binance数据到全局变量（后续会用于比较）
                                binance_data = {
                                    "symbol": symbol,
                                    "bid_price": bid_price,
                                    "bid_qty": bid_qty,
                                    "ask_price": ask_price,
                                    "ask_qty": ask_qty,
                                    "event_time": event_time,
                                    "local_time": local_time
                                }

                                # 将数据存储到全局字典中，以便其他函数访问
                                # 这里使用symbol作为键，确保每个交易对的数据不会混淆
                                globals()[f"binance_{symbol}_data"] = binance_data

                                # 创建Binance原始数据记录
                                binance_raw_data = {
                                    "type": "binance_raw",
                                    "symbol": symbol,
                                    "trade_time": trade_time,
                                    "event_time": event_time,
                                    "bid_price": bid_price,
                                    "bid_qty": bid_qty,
                                    "ask_price": ask_price,
                                    "ask_qty": ask_qty
                                }

                                # 将Binance原始数据放入队列
                                data_queue.put(binance_raw_data)

                        except json.JSONDecodeError:
                            logger.error(f"解析Binance {symbol} JSON数据失败: {message}")
                        except Exception as e:
                            logger.error(f"处理Binance {symbol} 数据出错: {e}")

                    except asyncio.TimeoutError:
                        # 接收超时，检查连接状态
                        logger.debug(f"Binance {symbol} 接收超时，继续等待...")
                        continue
                    except websockets.exceptions.ConnectionClosed:
                        logger.warning(f"Binance {symbol} WebSocket连接已关闭")
                        break
                    except Exception as e:
                        logger.error(f"Binance {symbol} 接收消息出错: {e}")
                        break

        except Exception as e:
            logger.error(f"连接Binance {symbol} WebSocket出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
        finally:
            # 取消ping任务
            if ping_task and not ping_task.done():
                ping_task.cancel()
                try:
                    await ping_task
                except asyncio.CancelledError:
                    pass

        # 等待一段时间后重连
        wait_time = 5 + (hash(symbol) % 5)  # 使用不同的交易对有稍微不同的重连时间，避免同时重连
        logger.info(f"Binance {symbol} 连接断开，将在 {wait_time:.2f} 秒后尝试重连...")
        await asyncio.sleep(wait_time)

# 连接生产环境WebSocket
async def connect_production_env():
    """连接到生产环境WebSocket"""
    url = PRODUCTION_WS_URL
    environment = "production"

    logger.info(f"开始连接生产环境WebSocket: {url}")

    # 外层循环，确保断开后重连
    while not SHUTDOWN_FLAG:
        try:
            # 连接WebSocket
            async with websockets.connect(url) as ws:
                logger.info(f"已连接到生产环境WebSocket")

                # 订阅深度数据
                for symbol in SYMBOLS:
                    await subscribe_depth(ws, symbol, environment)
                    await asyncio.sleep(0.5)  # 避免请求过于频繁

                # 启动心跳任务
                heartbeat_task = asyncio.create_task(heartbeat(ws, 25, environment))

                # 记录消息接收开始时间
                start_time = time.time()
                message_count = 0

                # 接收并处理消息
                while not SHUTDOWN_FLAG:
                    try:
                        message = await ws.recv()
                        message_count += 1

                        # 每100条消息记录一次消息率
                        if message_count % 100 == 0:
                            elapsed = time.time() - start_time
                            rate = message_count / elapsed if elapsed > 0 else 0
                            logger.info(f"生产环境消息率: {rate:.2f} 消息/秒")

                        # 打印原始消息（调试用）
                        logger.info(f"生产环境原始消息: {message[:200]}...")

                        # 解析JSON数据
                        try:
                            data = json.loads(message)

                            # 处理深度数据
                            if 'method' in data and data['method'] == 'update.depth':
                                result = data.get('result', {})

                                # 根据价格判断是BTCUSDT还是ETHUSDT
                                # 获取第一个买单和卖单的价格用于判断
                                bids = result.get('bids', [])
                                asks = result.get('asks', [])

                                symbol = None
                                if bids and asks:
                                    first_bid_price = float(bids[0][0])
                                    # 根据价格范围判断symbol
                                    # BTCUSDT的价格通常在数万美元，而ETHUSDT的价格通常在数千美元
                                    if first_bid_price > 10000:  # 如果价格大于10000，很可能是BTCUSDT
                                        symbol = "BTCUSDT"
                                    else:  # 否则可能是ETHUSDT
                                        symbol = "ETHUSDT"

                                    logger.info(f"根据价格 {first_bid_price} 判断symbol为 {symbol}")

                                if symbol in SYMBOLS:
                                    # 获取买一卖一价格和数量
                                    bids = result.get('bids', [])
                                    asks = result.get('asks', [])

                                    if bids and asks:
                                        bid_price = float(bids[0][0])
                                        bid_qty = float(bids[0][1])
                                        ask_price = float(asks[0][0])
                                        ask_qty = float(asks[0][1])

                                        # 获取交易所时间
                                        exchange_time = int(time.time() * 1000)

                                        # 获取其他数据
                                        index_price = float(result.get('index_price', 0))
                                        sign_price = float(result.get('sign_price', 0))
                                        last_price = float(result.get('last', 0))
                                        market_time = int(result.get('time', exchange_time))

                                        # 打印价格信息
                                        logger.info(f"生产环境 {symbol} 报价: 买一 {bid_price}x{bid_qty}, 卖一 {ask_price}x{ask_qty}")

                                        # 存储交易所数据到全局变量（后续会用于比较）
                                        exchange_data = {
                                            "symbol": symbol,
                                            "environment": environment,
                                            "bid_price": bid_price,
                                            "bid_qty": bid_qty,
                                            "ask_price": ask_price,
                                            "ask_qty": ask_qty,
                                            "exchange_time": exchange_time
                                        }

                                        # 将数据存储到全局字典中，以便其他函数访问
                                        globals()[f"{environment}_{symbol}_data"] = exchange_data

                                        # 创建交易所原始数据记录
                                        exchange_raw_data = {
                                            "type": "exchange_raw",
                                            "symbol": symbol,
                                            "environment": environment,
                                            "time": market_time,
                                            "index_price": index_price,
                                            "sign_price": sign_price,
                                            "last_price": last_price,
                                            "asks": asks,
                                            "bids": bids
                                        }

                                        # 将交易所原始数据放入队列
                                        data_queue.put(exchange_raw_data)

                                        # 只对ETHUSDT进行比较
                                        if symbol in COMPARE_SYMBOLS:
                                            compare_data(symbol, environment)

                            # 处理ping响应
                            elif 'method' in data and data['method'] == 'pong':
                                logger.info(f"收到生产环境pong响应")

                        except json.JSONDecodeError:
                            logger.error(f"解析生产环境JSON数据失败: {message}")
                        except Exception as e:
                            logger.error(f"处理生产环境数据出错: {e}")
                            import traceback
                            logger.error(traceback.format_exc())

                    except websockets.exceptions.ConnectionClosed as e:
                        logger.warning(f"生产环境WebSocket连接已关闭: {e}")
                        break

                # 取消心跳任务
                if heartbeat_task and not heartbeat_task.done():
                    heartbeat_task.cancel()

        except Exception as e:
            logger.error(f"连接生产环境WebSocket出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

        # 等待一段时间后重连
        wait_time = 5 + random.uniform(0, 5)  # 5-10秒的随机等待时间
        logger.info(f"生产环境连接断开，将在 {wait_time:.2f} 秒后尝试重连...")
        await asyncio.sleep(wait_time)

# 连接测试环境WebSocket - 已注释掉
# async def connect_testing_env():
#     """连接到测试环境WebSocket"""
#     # 测试环境已被注释掉，不再使用
#     pass

# 定时更新统计数据的任务
async def stats_updater_task():
    """定时更新统计数据的任务"""
    while True:
        if SHUTDOWN_FLAG:
            break

        try:
            update_stats()
        except Exception as e:
            logger.error(f"更新统计数据出错: {e}")

        # 每5分钟更新一次统计数据
        await asyncio.sleep(300)

# CPU监控任务
async def cpu_monitor_task():
    """监控CPU使用率，如果超过阈值则重启进程"""
    global SHUTDOWN_FLAG

    logger.info(f"启动CPU监控任务，阈值: {CPU_THRESHOLD}%，检查间隔: {CPU_CHECK_INTERVAL}秒")

    while True:
        if SHUTDOWN_FLAG:
            break

        try:
            # 获取当前CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)

            # 获取当前进程的CPU使用率
            process = psutil.Process(os.getpid())
            process_cpu_percent = process.cpu_percent(interval=1)

            # 获取内存使用情况
            memory_info = process.memory_info()
            memory_percent = process.memory_percent()

            logger.info(f"系统CPU使用率: {cpu_percent}%, 进程CPU使用率: {process_cpu_percent}%, "
                       f"内存使用: {memory_info.rss / (1024 * 1024):.2f} MB ({memory_percent:.2f}%)")

            # 如果CPU使用率超过阈值，触发重启
            if cpu_percent > CPU_THRESHOLD:
                logger.warning(f"系统CPU使用率 ({cpu_percent}%) 超过阈值 ({CPU_THRESHOLD}%)，准备重启进程")

                # 设置关闭标志
                SHUTDOWN_FLAG = True

                # 等待一段时间以便其他任务可以优雅地关闭
                await asyncio.sleep(5)

                # 重启进程
                logger.info("正在重启进程...")
                os.execv(sys.executable, [sys.executable] + sys.argv)
                return

        except Exception as e:
            logger.error(f"CPU监控任务出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

        # 等待下一次检查
        await asyncio.sleep(CPU_CHECK_INTERVAL)

# 信号处理函数
def signal_handler(sig, _):
    """处理终止信号"""
    global SHUTDOWN_FLAG

    logger.info(f"收到信号 {sig}，准备优雅关闭")
    SHUTDOWN_FLAG = True

# 程序入口
if __name__ == "__main__":
    # 检查psutil是否可用
    if 'psutil' not in sys.modules:
        logger.error("未安装psutil库，请使用 'pip install psutil' 安装")
        sys.exit(1)

    try:
        logger.info("正在启动WebSocket数据收集器...")

        # 初始化数据库
        try:
            from init_database import main as init_db
            if not init_db():
                logger.error("数据库初始化失败，程序退出")
                sys.exit(1)
        except ImportError:
            logger.warning("未找到数据库初始化脚本，跳过数据库初始化")
        except Exception as e:
            logger.error(f"数据库初始化出错: {e}")

        # 启动定时更新统计数据的任务
        stats_updater = threading.Thread(target=lambda: asyncio.run(stats_updater_task()))
        stats_updater.daemon = True
        stats_updater.start()

        # 运行主函数
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
        SHUTDOWN_FLAG = True
    except Exception as e:
        logger.error(f"程序出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        # 确保程序正常退出
        if not SHUTDOWN_FLAG:
            logger.info("程序正常退出")
        else:
            logger.info("程序因关闭标志而退出")



