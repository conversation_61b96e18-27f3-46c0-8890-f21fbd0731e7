#!/usr/bin/env python3
"""
真实数据导出器
从实际数据库获取准确的加密货币数据
"""

import os
import json
import time
from datetime import datetime, timedelta
from http.server import HTTPServer, BaseHTTPRequestHandler

# 设置环境变量
os.environ['DB_HOST'] = 'localhost'
os.environ['DB_USER'] = 'root'
os.environ['DB_PASSWORD'] = 'Linuxtest'
os.environ['DB_NAME'] = 'depth_db'

class RealDataExporter:
    """真实数据导出器"""

    def __init__(self):
        self.port = 8001

    def get_real_funding_rates(self):
        """获取真实的资金费率数据"""
        try:
            from utils.db import db_manager

            # 查询最新的资金费率数据
            query = """
            SELECT
                symbol,
                funding_rate_last,
                funding_rate_next,
                funding_rate_predict,
                funding_time,
                created_at
            FROM bitda_ticker
            WHERE symbol IN ('BTCUSDT', 'ETHUSDT')
            AND funding_rate_last IS NOT NULL
            AND created_at >= NOW() - INTERVAL 1 HOUR
            ORDER BY symbol, created_at DESC
            """

            data = db_manager.execute_query(query, fetch=True)

            result = {}
            for row in data:
                symbol = row[0]
                if symbol not in result:  # 只取最新的记录
                    result[symbol] = {
                        'symbol': symbol,
                        'current_rate': float(row[1]) if row[1] else 0,
                        'next_rate': float(row[2]) if row[2] else 0,
                        'predict_rate': float(row[3]) if row[3] else 0,
                        'funding_time': row[4].isoformat() if row[4] and hasattr(row[4], 'isoformat') else str(row[4]) if row[4] else None,
                        'data_time': row[5].isoformat() if row[5] and hasattr(row[5], 'isoformat') else str(row[5]) if row[5] else None,
                        'exchange': 'Bitda',
                        'current_rate_percent': f"{float(row[1])*100:.5f}%" if row[1] else "0.00000%"
                    }

            return {
                'data': result,
                'timestamp': datetime.now().isoformat(),
                'data_source': 'bitda_ticker table',
                'total_records': len(result)
            }

        except Exception as e:
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'data_source': 'bitda_ticker table',
                'total_records': 0
            }

    def get_real_latency_data(self):
        """获取真实的ETHUSDT延时数据"""
        try:
            from utils.db import db_manager

            # 查询最近1小时的ETHUSDT延时数据
            query = """
            SELECT
                message_latency_ms,
                engine_latency_ms,
                depth_ratio,
                timestamp,
                binance_message_time,
                exchange_time
            FROM depth_matches
            WHERE symbol = 'ETHUSDT'
            AND timestamp >= NOW() - INTERVAL 1 HOUR
            AND message_latency_ms BETWEEN 1 AND 5000
            ORDER BY timestamp DESC
            LIMIT 100
            """

            data = db_manager.execute_query(query, fetch=True)

            if not data:
                return {
                    'error': 'No ETHUSDT latency data found in the last hour',
                    'timestamp': datetime.now().isoformat(),
                    'data_source': 'depth_matches table',
                    'symbol': 'ETHUSDT'
                }

            latencies = [row[0] for row in data]

            # 计算统计数据
            avg_latency = sum(latencies) / len(latencies)
            min_latency = min(latencies)
            max_latency = max(latencies)

            # 计算中位数
            sorted_latencies = sorted(latencies)
            n = len(sorted_latencies)
            median_latency = (sorted_latencies[n//2] + sorted_latencies[(n-1)//2]) / 2

            # 最新的几条记录
            latest_records = []
            for i, row in enumerate(data[:10]):
                latest_records.append({
                    'message_latency_ms': row[0],
                    'engine_latency_ms': row[1],
                    'depth_ratio': float(row[2]),
                    'timestamp': row[3].isoformat() if hasattr(row[3], 'isoformat') else str(row[3]),
                    'binance_time': row[4],
                    'exchange_time': row[5]
                })

            return {
                'symbol': 'ETHUSDT',
                'exchange': 'Bitda vs Binance',
                'time_range': f"Last 1 hour",
                'statistics': {
                    'total_matches': len(data),
                    'avg_latency_ms': round(avg_latency, 2),
                    'min_latency_ms': min_latency,
                    'max_latency_ms': max_latency,
                    'median_latency_ms': round(median_latency, 2)
                },
                'latest_records': latest_records,
                'timestamp': datetime.now().isoformat(),
                'data_source': 'depth_matches table'
            }

        except Exception as e:
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'data_source': 'depth_matches table',
                'symbol': 'ETHUSDT'
            }

    def get_real_depth_comparison(self):
        """获取真实的深度对比数据"""
        try:
            from utils.db import db_manager

            # 查询最新的深度对比数据
            query = """
            SELECT
                symbol,
                binance_bid_qty,
                binance_ask_qty,
                exchange_bid_qty,
                exchange_ask_qty,
                binance_depth_sum,
                exchange_depth_sum,
                depth_ratio,
                timestamp
            FROM depth_matches
            WHERE symbol IN ('BTCUSDT', 'ETHUSDT')
            AND timestamp >= NOW() - INTERVAL 30 MINUTE
            ORDER BY symbol, timestamp DESC
            """

            data = db_manager.execute_query(query, fetch=True)

            result = {}
            for row in data:
                symbol = row[0]
                if symbol not in result:  # 只取最新的记录
                    binance_total = float(row[1]) + float(row[2])  # bid + ask
                    exchange_total = float(row[3]) + float(row[4])  # bid + ask

                    result[symbol] = {
                        'symbol': symbol,
                        'exchange': 'Bitda vs Binance',
                        'bitda_bid_qty': float(row[3]),
                        'bitda_ask_qty': float(row[4]),
                        'bitda_total': exchange_total,
                        'binance_bid_qty': float(row[1]),
                        'binance_ask_qty': float(row[2]),
                        'binance_total': binance_total,
                        'bid_ratio': round(float(row[3]) / float(row[1]), 2) if float(row[1]) > 0 else 0,
                        'ask_ratio': round(float(row[4]) / float(row[2]), 2) if float(row[2]) > 0 else 0,
                        'total_ratio': round(exchange_total / binance_total, 2) if binance_total > 0 else 0,
                        'depth_ratio': float(row[7]),
                        'timestamp': row[8].isoformat(),
                        'data_time': f"Latest data from {row[8].strftime('%Y-%m-%d %H:%M:%S')}"
                    }

            return {
                'data': result,
                'timestamp': datetime.now().isoformat(),
                'data_source': 'depth_matches table',
                'time_range': 'Last 30 minutes',
                'total_symbols': len(result)
            }

        except Exception as e:
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'data_source': 'depth_matches table'
            }

    def get_real_price_deviation(self):
        """获取真实的标记价格偏差数据"""
        try:
            from utils.db import db_manager

            # 查询最近的标记价格数据
            query = """
            SELECT
                symbol,
                last_price,
                sign_price,
                created_at
            FROM bitda_ticker
            WHERE symbol IN ('BTCUSDT', 'ETHUSDT')
            AND sign_price IS NOT NULL
            AND last_price IS NOT NULL
            AND created_at >= NOW() - INTERVAL 1 HOUR
            ORDER BY symbol, created_at DESC
            """

            data = db_manager.execute_query(query, fetch=True)

            result = {}
            symbol_data = {}

            # 按symbol分组数据
            for row in data:
                symbol = row[0]
                if symbol not in symbol_data:
                    symbol_data[symbol] = []
                symbol_data[symbol].append(row)

            for symbol, rows in symbol_data.items():
                if not rows:
                    continue

                # 计算偏差
                deviations = []
                for row in rows:
                    last_price = float(row[1])
                    sign_price = float(row[2])
                    if last_price > 0:
                        deviation = (sign_price - last_price) / last_price
                        deviations.append(deviation)

                if deviations:
                    # 最新数据
                    latest_row = rows[0]
                    latest_last_price = float(latest_row[1])
                    latest_sign_price = float(latest_row[2])
                    latest_deviation = (latest_sign_price - latest_last_price) / latest_last_price if latest_last_price > 0 else 0

                    # 统计数据
                    avg_deviation = sum(deviations) / len(deviations)
                    max_deviation = max(deviations)
                    min_deviation = min(deviations)

                    result[symbol] = {
                        'symbol': symbol,
                        'exchange': 'Bitda',
                        'latest_last_price': latest_last_price,
                        'latest_sign_price': latest_sign_price,
                        'latest_deviation_percent': f"{latest_deviation*100:.4f}%",
                        'statistics': {
                            'sample_count': len(deviations),
                            'avg_deviation_percent': f"{avg_deviation*100:.4f}%",
                            'max_deviation_percent': f"{max_deviation*100:.4f}%",
                            'min_deviation_percent': f"{min_deviation*100:.4f}%"
                        },
                        'timestamp': latest_row[3].isoformat(),
                        'data_time': f"Latest: {latest_row[3].strftime('%Y-%m-%d %H:%M:%S')}"
                    }

            return {
                'data': result,
                'timestamp': datetime.now().isoformat(),
                'data_source': 'bitda_ticker table',
                'time_range': 'Last 1 hour',
                'total_symbols': len(result)
            }

        except Exception as e:
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'data_source': 'bitda_ticker table'
            }

class RealDataHandler(BaseHTTPRequestHandler):
    """真实数据HTTP处理器"""

    def __init__(self, *args, **kwargs):
        self.exporter = RealDataExporter()
        super().__init__(*args, **kwargs)

    def do_GET(self):
        """处理GET请求"""
        if self.path == '/api/real-funding-rates':
            self.send_json_response(self.exporter.get_real_funding_rates())
        elif self.path == '/api/real-latency':
            self.send_json_response(self.exporter.get_real_latency_data())
        elif self.path == '/api/real-depth-comparison':
            self.send_json_response(self.exporter.get_real_depth_comparison())
        elif self.path == '/api/real-price-deviation':
            self.send_json_response(self.exporter.get_real_price_deviation())
        elif self.path == '/api/real-all-data':
            # 返回所有真实数据
            all_data = {
                'funding_rates': self.exporter.get_real_funding_rates(),
                'latency_analysis': self.exporter.get_real_latency_data(),
                'depth_comparison': self.exporter.get_real_depth_comparison(),
                'price_deviation': self.exporter.get_real_price_deviation(),
                'metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'data_source': 'MySQL Database (depth_db)',
                    'tables': ['bitda_ticker', 'depth_matches'],
                    'exchanges': ['Bitda', 'Binance'],
                    'symbols': ['BTCUSDT', 'ETHUSDT']
                }
            }
            self.send_json_response(all_data)
        elif self.path == '/':
            self.send_html_response()
        else:
            self.send_404()

    def send_json_response(self, data):
        """发送JSON响应"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data, indent=2, ensure_ascii=False).encode('utf-8'))

    def send_html_response(self):
        """发送HTML响应"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>真实加密货币数据API</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }}
                .container {{ max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .header {{ text-align: center; margin-bottom: 30px; }}
                .endpoint {{ margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #fafafa; }}
                .endpoint h3 {{ margin-top: 0; color: #333; }}
                .endpoint a {{ color: #007bff; text-decoration: none; font-weight: bold; }}
                .endpoint a:hover {{ text-decoration: underline; }}
                .status {{ padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; margin: 20px 0; }}
                .info {{ background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 8px; margin: 20px 0; }}
                .warning {{ background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🎯 真实加密货币数据API</h1>
                    <p>从MySQL数据库获取准确的交易数据</p>
                </div>

                <div class="status">
                    <strong>🟢 状态:</strong> 运行中 | <strong>⏰ 时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                </div>

                <div class="info">
                    <strong>📊 数据源:</strong> MySQL数据库 (depth_db)<br>
                    <strong>📋 数据表:</strong> bitda_ticker, depth_matches<br>
                    <strong>🏢 交易所:</strong> Bitda, Binance<br>
                    <strong>💱 交易对:</strong> BTCUSDT, ETHUSDT
                </div>

                <h2>📡 真实数据API端点</h2>

                <div class="endpoint">
                    <h3>💰 真实资金费率数据</h3>
                    <p><a href="/api/real-funding-rates">/api/real-funding-rates</a></p>
                    <p>从bitda_ticker表获取最新的BTCUSDT和ETHUSDT资金费率数据</p>
                    <small>包含当前费率、下期费率、预测费率和具体时间戳</small>
                </div>

                <div class="endpoint">
                    <h3>⚡ 真实延时分析数据</h3>
                    <p><a href="/api/real-latency">/api/real-latency</a></p>
                    <p>从depth_matches表获取ETHUSDT的真实延时分析数据</p>
                    <small>包含平均延时、最小/最大延时和最新匹配记录</small>
                </div>

                <div class="endpoint">
                    <h3>📊 真实深度对比数据</h3>
                    <p><a href="/api/real-depth-comparison">/api/real-depth-comparison</a></p>
                    <p>从depth_matches表获取Bitda vs Binance的真实深度对比</p>
                    <small>包含买一卖一量对比和具体比值计算</small>
                </div>

                <div class="endpoint">
                    <h3>📈 真实价格偏差数据</h3>
                    <p><a href="/api/real-price-deviation">/api/real-price-deviation</a></p>
                    <p>从bitda_ticker表获取标记价格与最新价格的真实偏差</p>
                    <small>包含最大/最小偏差和统计分析</small>
                </div>

                <div class="endpoint">
                    <h3>🎯 所有真实数据</h3>
                    <p><a href="/api/real-all-data">/api/real-all-data</a></p>
                    <p>获取所有真实数据的完整汇总</p>
                    <small>包含详细的数据源信息和时间戳</small>
                </div>

                <div class="warning">
                    <strong>⚠️ 注意:</strong> 这些是从您的实际数据库获取的真实数据，包含具体的时间戳、交易所信息和准确的数值。
                </div>
            </div>
        </body>
        </html>
        """
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

    def send_404(self):
        """发送404响应"""
        self.send_response(404)
        self.send_header('Content-type', 'text/plain')
        self.end_headers()
        self.wfile.write(b'404 Not Found')

    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def main():
    """主函数"""
    print("🎯 启动真实数据导出器...")
    print(f"📡 监听端口: 8001")
    print(f"🌐 访问地址: http://localhost:8001")
    print(f"📊 真实数据API: http://localhost:8001/api/real-all-data")
    print("按 Ctrl+C 停止服务")

    try:
        server = HTTPServer(('localhost', 8001), RealDataHandler)
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n⏹️  服务已停止")
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")

if __name__ == "__main__":
    main()
