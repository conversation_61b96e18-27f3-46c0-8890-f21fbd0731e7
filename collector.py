"""
数据收集模块
"""
import asyncio
import json
import time
import websockets
from typing import Dict, Any, List
import queue
import threading
from datetime import datetime

from utils.config import SYMBOLS, BITDA_WS_CONFIG, BINANCE_WS_CONFIG, QUEUE_SIZE, BATCH_SIZE
from utils.logging import setup_logger
from storage import storage

logger = setup_logger(__name__)

class DataCollector:
    """数据收集器"""

    def __init__(self):
        self.data_queue = queue.Queue(maxsize=QUEUE_SIZE)
        self.running = False
        self.bitda_ws = None
        self.binance_ws_connections = {}

        # 数据缓存，按类型分组
        self.data_cache = {
            'bitda_kline': [],
            'bitda_trades': [],
            'bitda_depth': [],
            'bitda_ticker': [],
            'binance_depth_5': [],
            'binance_bookticker': []
        }

    async def start(self):
        """启动数据收集"""
        self.running = True
        logger.info("启动数据收集器...")

        # 启动数据处理线程
        processing_thread = threading.Thread(target=self._process_data_queue, daemon=True)
        processing_thread.start()

        # 启动WebSocket连接
        tasks = []

        # Bitda WebSocket连接
        tasks.append(asyncio.create_task(self._connect_bitda()))

        # Binance WebSocket连接
        for symbol in SYMBOLS:
            # BookTicker连接
            tasks.append(asyncio.create_task(self._connect_binance_bookticker(symbol)))
            # 5档深度连接
            tasks.append(asyncio.create_task(self._connect_binance_depth(symbol)))

        # 等待所有任务
        await asyncio.gather(*tasks, return_exceptions=True)

    def stop(self):
        """停止数据收集"""
        self.running = False
        logger.info("停止数据收集器...")

    async def _connect_bitda(self):
        """连接Bitda WebSocket"""
        while self.running:
            try:
                logger.info("连接Bitda WebSocket...")
                async with websockets.connect(BITDA_WS_CONFIG['url']) as websocket:
                    self.bitda_ws = websocket

                    # 订阅数据
                    await self._subscribe_bitda_data(websocket)

                    # 监听消息
                    async for message in websocket:
                        if not self.running:
                            break
                        await self._handle_bitda_message(message)

            except Exception as e:
                logger.error(f"Bitda WebSocket连接错误: {e}")
                if self.running:
                    await asyncio.sleep(BITDA_WS_CONFIG['reconnect_interval'])

    async def _subscribe_bitda_data(self, websocket):
        """订阅Bitda数据"""
        # 根据原有程序和您提供的文档，订阅所有数据类型
        subscriptions = []

        for symbol in SYMBOLS:
            # 1. 订阅K线数据 (update.kline)
            subscriptions.append({
                "method": "subscribe.kline",
                "params": {
                    "market": symbol,
                    "period": "1min"
                },
                "id": 0
            })

            # 2. 订阅成交数据 (update.trade)
            subscriptions.append({
                "method": "subscribe.trade",
                "params": {
                    "market": symbol
                },
                "id": 0
            })

            # 3. 订阅深度数据 (update.depth)
            subscriptions.append({
                "method": "subscribe.depth",
                "params": {
                    "market": symbol,
                    "merge": "0"
                },
                "id": 0
            })

        # 4. 订阅行情数据 (update.state) - 全市场数据
        subscriptions.append({
            "method": "subscribe.state",
            "params": {},
            "id": 0
        })

        # 发送订阅消息
        for sub in subscriptions:
            await websocket.send(json.dumps(sub))
            market_info = f" for {sub['params'].get('market', 'ALL_MARKETS')}" if 'market' in sub.get('params', {}) else " for ALL_MARKETS"
            logger.info(f"发送Bitda订阅: {sub['method']}{market_info}")
            await asyncio.sleep(0.5)  # 避免发送过快

        # 启动心跳任务
        asyncio.create_task(self._bitda_heartbeat(websocket))

    async def _bitda_heartbeat(self, websocket):
        """Bitda心跳任务"""
        while self.running:
            try:
                # 发送ping消息
                ping_obj = {"method": "ping"}
                await websocket.send(json.dumps(ping_obj))
                logger.info("向Bitda发送ping消息")

                # 等待25秒
                for _ in range(25):
                    if not self.running:
                        break
                    await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"Bitda心跳任务出错: {e}")
                break

    async def _handle_bitda_message(self, message: str):
        """处理Bitda消息"""
        try:
            data = json.loads(message)

            # 根据method类型处理不同的消息
            method = data.get('method', '')
            result = data.get('result', {})

            if method == 'update.kline':
                await self._handle_bitda_kline(result)
            elif method == 'update.trade':
                await self._handle_bitda_trade(result)
            elif method == 'update.depth':
                await self._handle_bitda_depth_update(result)
            elif method == 'update.state':
                await self._handle_bitda_state(result)
            elif method == 'pong':
                logger.info("收到Bitda pong响应")
            elif 'subscribe' in method and result == 'success':
                logger.info(f"Bitda订阅成功: {method}")
            else:
                # 只在调试时显示未处理的消息
                if method not in ['ping']:
                    logger.debug(f"未处理的Bitda消息: {method}")

        except json.JSONDecodeError:
            logger.error(f"解析Bitda JSON失败: {message[:100]}...")
        except Exception as e:
            logger.error(f"处理Bitda消息失败: {e}")

    async def _handle_bitda_depth_update(self, result: Dict[str, Any]):
        """处理Bitda深度数据更新"""
        try:
            # 根据价格判断交易对（参考原有程序的逻辑）
            bids = result.get('bids', [])
            asks = result.get('asks', [])

            symbol = None
            if bids and asks:
                first_bid_price = float(bids[0][0])
                # 根据价格范围判断symbol
                # BTCUSDT的价格通常在数万美元，而ETHUSDT的价格通常在数千美元
                if first_bid_price > 10000:  # 如果价格大于10000，很可能是BTCUSDT
                    symbol = "BTCUSDT"
                else:  # 否则可能是ETHUSDT
                    symbol = "ETHUSDT"

                logger.info(f"根据价格 {first_bid_price} 判断symbol为 {symbol}")

            if symbol in SYMBOLS and bids and asks:
                # 获取买一卖一价格和数量
                bid_price = float(bids[0][0])
                bid_qty = float(bids[0][1])
                ask_price = float(asks[0][0])
                ask_qty = float(asks[0][1])

                # 获取其他数据
                index_price = float(result.get('index_price', 0)) if result.get('index_price') else None
                sign_price = float(result.get('sign_price', 0)) if result.get('sign_price') else None
                last_price = float(result.get('last', 0)) if result.get('last') else None
                market_time = int(result.get('time', int(time.time() * 1000)))

                # 打印价格信息
                logger.info(f"Bitda {symbol} 报价: 买一 {bid_price}x{bid_qty}, 卖一 {ask_price}x{ask_qty}")

                # 创建深度数据记录
                depth_record = {
                    'symbol': symbol,
                    'timestamp': market_time,
                    'index_price': index_price,
                    'sign_price': sign_price,
                    'last_price': last_price,
                    'asks': asks,
                    'bids': bids,
                    'merge_level': 0
                }

                self.data_cache['bitda_depth'].append(depth_record)

        except Exception as e:
            logger.error(f"处理Bitda深度数据失败: {e}")

    async def _handle_bitda_kline(self, result: Dict[str, Any]):
        """处理Bitda K线数据"""
        try:
            data_list = result.get('data', [])
            period = result.get('period', '1min')

            for kline_data in data_list:
                if len(kline_data) >= 8:
                    symbol = kline_data[7]  # 交易对

                    # 只收集指定的交易对
                    if symbol in SYMBOLS:
                        kline_record = {
                            'symbol': symbol,
                            'timestamp': kline_data[0],  # 时间戳
                            'open_price': float(kline_data[1]),  # 开盘价
                            'high_price': float(kline_data[2]),  # 最高价
                            'low_price': float(kline_data[3]),   # 最低价
                            'close_price': float(kline_data[4]), # 收盘价
                            'volume': float(kline_data[5]),      # 成交量
                            'amount': float(kline_data[6]),      # 成交金额
                            'period': period
                        }

                        self.data_cache['bitda_kline'].append(kline_record)
                        logger.info(f"收到Bitda K线数据: {symbol} {kline_record['close_price']}")

        except Exception as e:
            logger.error(f"处理Bitda K线数据失败: {e}")

    async def _handle_bitda_trade(self, result: Dict[str, Any]):
        """处理Bitda成交数据"""
        try:
            symbol = result.get('market')

            # 只收集指定的交易对
            if symbol in SYMBOLS:
                trade_record = {
                    'symbol': symbol,
                    'trade_id': result['id'],
                    'price': float(result['price']),
                    'amount': float(result['amount']),
                    'trade_time': result['time'],
                    'trade_type': result['type']
                }

                self.data_cache['bitda_trades'].append(trade_record)
                logger.info(f"收到Bitda成交数据: {symbol} {trade_record['price']} x {trade_record['amount']} ({trade_record['trade_type']})")

        except Exception as e:
            logger.error(f"处理Bitda成交数据失败: {e}")

    async def _handle_bitda_state(self, result: Dict[str, Any]):
        """处理Bitda行情数据"""
        try:
            for symbol, ticker_data in result.items():
                # 只收集指定的交易对
                if symbol in SYMBOLS:
                    ticker_record = {
                        'symbol': symbol,
                        'open_price': float(ticker_data.get('open', 0)),
                        'high_price': float(ticker_data.get('high', 0)),
                        'low_price': float(ticker_data.get('low', 0)),
                        'last_price': float(ticker_data.get('last', 0)),
                        'volume': float(ticker_data.get('volume', 0)),
                        'amount': float(ticker_data.get('amount', 0)),
                        'change_rate': float(ticker_data.get('change', 0)),
                        'funding_time': ticker_data.get('funding_time'),
                        'position_amount': float(ticker_data.get('position_amount', 0)) if ticker_data.get('position_amount') else None,
                        'funding_rate_last': float(ticker_data.get('funding_rate_last', 0)) if ticker_data.get('funding_rate_last') else None,
                        'funding_rate_next': float(ticker_data.get('funding_rate_next', 0)) if ticker_data.get('funding_rate_next') else None,
                        'funding_rate_predict': float(ticker_data.get('funding_rate_predict', 0)) if ticker_data.get('funding_rate_predict') else None,
                        'insurance': float(ticker_data.get('insurance', 0)) if ticker_data.get('insurance') else None,
                        'sign_price': float(ticker_data.get('sign_price', 0)) if ticker_data.get('sign_price') else None,
                        'index_price': float(ticker_data.get('index_price', 0)) if ticker_data.get('index_price') else None,
                        'sell_total': float(ticker_data.get('sell_total', 0)) if ticker_data.get('sell_total') else None,
                        'buy_total': float(ticker_data.get('buy_total', 0)) if ticker_data.get('buy_total') else None,
                        'period': ticker_data.get('period', 86400)
                    }

                    self.data_cache['bitda_ticker'].append(ticker_record)
                    logger.info(f"收到Bitda行情数据: {symbol} 最新价 {ticker_record['last_price']}")

        except Exception as e:
            logger.error(f"处理Bitda行情数据失败: {e}")



    async def _connect_binance_bookticker(self, symbol: str):
        """连接Binance BookTicker WebSocket"""
        stream_name = f"{symbol.lower()}@bookTicker"
        url = f"{BINANCE_WS_CONFIG['base_url']}{stream_name}"

        while self.running:
            try:
                logger.info(f"连接Binance BookTicker WebSocket: {symbol}")
                async with websockets.connect(url) as websocket:
                    self.binance_ws_connections[f"bookticker_{symbol}"] = websocket

                    async for message in websocket:
                        if not self.running:
                            break
                        await self._handle_binance_bookticker(message)

            except Exception as e:
                logger.error(f"Binance BookTicker WebSocket连接错误 ({symbol}): {e}")
                if self.running:
                    await asyncio.sleep(BINANCE_WS_CONFIG['reconnect_interval'])

    async def _connect_binance_depth(self, symbol: str):
        """连接Binance 5档深度WebSocket"""
        stream_name = f"{symbol.lower()}@depth5@100ms"
        url = f"{BINANCE_WS_CONFIG['base_url']}{stream_name}"

        while self.running:
            try:
                logger.info(f"连接Binance 5档深度WebSocket: {symbol}")
                async with websockets.connect(url) as websocket:
                    self.binance_ws_connections[f"depth_{symbol}"] = websocket

                    async for message in websocket:
                        if not self.running:
                            break
                        await self._handle_binance_depth(message)

            except Exception as e:
                logger.error(f"Binance 5档深度WebSocket连接错误 ({symbol}): {e}")
                if self.running:
                    await asyncio.sleep(BINANCE_WS_CONFIG['reconnect_interval'])

    async def _handle_binance_bookticker(self, message: str):
        """处理Binance BookTicker数据"""
        try:
            data = json.loads(message)

            # 只收集指定的交易对
            if data.get('s') in SYMBOLS:
                # 使用当前时间戳作为event_time和trade_time，因为BookTicker数据中没有这些字段
                current_time = int(time.time() * 1000)

                bookticker_record = {
                    'symbol': data['s'],
                    'update_id': data['u'],
                    'bid_price': float(data['b']),
                    'bid_qty': float(data['B']),
                    'ask_price': float(data['a']),
                    'ask_qty': float(data['A']),
                    'event_time': current_time,
                    'trade_time': current_time
                }

                self.data_cache['binance_bookticker'].append(bookticker_record)

        except json.JSONDecodeError:
            logger.error(f"解析Binance BookTicker JSON失败: {message}")
        except Exception as e:
            logger.error(f"处理Binance BookTicker数据失败: {e}")

    async def _handle_binance_depth(self, message: str):
        """处理Binance 5档深度数据"""
        try:
            data = json.loads(message)

            # Binance 5档深度数据格式：{'lastUpdateId': xxx, 'bids': [...], 'asks': [...]}
            # 需要从WebSocket URL中提取symbol，因为数据中没有symbol字段
            # 这里我们需要根据连接的WebSocket来确定symbol
            # 暂时跳过这个数据，因为格式与预期不符
            logger.debug(f"收到Binance 5档深度数据，但格式与预期不符，跳过处理")

        except json.JSONDecodeError:
            logger.error(f"解析Binance 5档深度JSON失败: {message}")
        except Exception as e:
            logger.error(f"处理Binance 5档深度数据失败: {e}")

    def _process_data_queue(self):
        """处理数据队列（在单独线程中运行）"""
        logger.info("启动数据处理线程...")

        while self.running:
            try:
                # 检查是否需要批量保存数据
                self._check_and_save_data()

                # 定期清理旧数据
                if int(time.time()) % 3600 == 0:  # 每小时清理一次
                    storage.cleanup_old_data()

                # 检查磁盘空间
                if int(time.time()) % 300 == 0:  # 每5分钟检查一次
                    if not storage.check_disk_space():
                        logger.critical("磁盘空间不足，停止数据收集！")
                        self.running = False
                        break

                time.sleep(1)  # 每秒检查一次

            except Exception as e:
                logger.error(f"数据处理线程错误: {e}")
                time.sleep(5)

    def _check_and_save_data(self):
        """检查并保存缓存的数据"""
        for data_type, data_list in self.data_cache.items():
            if len(data_list) >= BATCH_SIZE:
                self._save_data_batch(data_type, data_list.copy())
                data_list.clear()

    def _save_data_batch(self, data_type: str, data_list: List[Dict[str, Any]]):
        """保存一批数据"""
        try:
            if data_type == 'bitda_kline':
                storage.save_bitda_kline(data_list)
            elif data_type == 'bitda_trades':
                storage.save_bitda_trades(data_list)
            elif data_type == 'bitda_depth':
                storage.save_bitda_depth(data_list)
            elif data_type == 'bitda_ticker':
                storage.save_bitda_ticker(data_list)
            elif data_type == 'binance_depth_5':
                storage.save_binance_depth_5(data_list)
            elif data_type == 'binance_bookticker':
                storage.save_binance_bookticker(data_list)
            else:
                logger.warning(f"未知的数据类型: {data_type}")

        except Exception as e:
            logger.error(f"保存数据批次失败 ({data_type}): {e}")

    def flush_all_data(self):
        """强制保存所有缓存的数据"""
        logger.info("强制保存所有缓存数据...")
        for data_type, data_list in self.data_cache.items():
            if data_list:
                self._save_data_batch(data_type, data_list.copy())
                data_list.clear()
        logger.info("所有缓存数据已保存")
