#!/usr/bin/env python3
"""
自动配置Grafana
通过API自动创建数据源和仪表板
"""

import requests
import json
import time
import os
import sys

class GrafanaAutoSetup:
    """Grafana自动配置器"""
    
    def __init__(self):
        self.grafana_url = "http://localhost:3000"
        self.admin_user = "admin"
        self.admin_pass = "admin"
        self.session = requests.Session()
        self.session.auth = (self.admin_user, self.admin_pass)
        
    def wait_for_grafana(self, max_attempts=30):
        """等待Grafana启动"""
        print("⏳ 等待Grafana启动...")
        
        for attempt in range(max_attempts):
            try:
                response = requests.get(f"{self.grafana_url}/api/health", timeout=5)
                if response.status_code == 200:
                    print("✅ Grafana已启动")
                    return True
            except requests.exceptions.RequestException:
                pass
            
            print(f"   尝试 {attempt + 1}/{max_attempts}...")
            time.sleep(2)
        
        print("❌ Grafana启动超时")
        return False
    
    def create_datasource(self):
        """创建数据源"""
        print("📊 创建数据源...")
        
        # 检查是否已存在TestData数据源
        try:
            response = self.session.get(f"{self.grafana_url}/api/datasources")
            if response.status_code == 200:
                datasources = response.json()
                for ds in datasources:
                    if ds.get('type') == 'testdata':
                        print("✅ TestData数据源已存在")
                        return ds['uid']
        except Exception as e:
            print(f"检查数据源失败: {e}")
        
        # 创建TestData数据源
        datasource_config = {
            "name": "TestData DB",
            "type": "testdata",
            "access": "proxy",
            "isDefault": True
        }
        
        try:
            response = self.session.post(
                f"{self.grafana_url}/api/datasources",
                json=datasource_config,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code in [200, 409]:  # 200=创建成功, 409=已存在
                print("✅ 数据源创建成功")
                if response.status_code == 200:
                    return response.json().get('datasource', {}).get('uid')
                else:
                    # 如果已存在，获取UID
                    response = self.session.get(f"{self.grafana_url}/api/datasources")
                    if response.status_code == 200:
                        datasources = response.json()
                        for ds in datasources:
                            if ds.get('type') == 'testdata':
                                return ds['uid']
            else:
                print(f"❌ 数据源创建失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ 数据源创建异常: {e}")
        
        return "PD8C576611E62080A"  # 默认TestData UID
    
    def create_dashboard(self, datasource_uid):
        """创建仪表板"""
        print("📋 创建仪表板...")
        
        dashboard_config = {
            "dashboard": {
                "id": None,
                "title": "🚀 加密货币实时数据分析",
                "tags": ["crypto", "trading", "realtime"],
                "timezone": "browser",
                "panels": [
                    {
                        "id": 1,
                        "title": "💰 BTCUSDT 资金费率",
                        "type": "stat",
                        "gridPos": {"h": 6, "w": 6, "x": 0, "y": 0},
                        "targets": [
                            {
                                "datasource": {"type": "testdata", "uid": datasource_uid},
                                "refId": "A",
                                "scenarioId": "random_walk",
                                "alias": "资金费率 (%)",
                                "min": -0.1,
                                "max": 0.1,
                                "noise": 0.02
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None},
                                        {"color": "yellow", "value": 0.01},
                                        {"color": "red", "value": 0.05}
                                    ]
                                },
                                "unit": "percent",
                                "decimals": 5
                            }
                        },
                        "options": {
                            "colorMode": "background",
                            "graphMode": "area",
                            "justifyMode": "center",
                            "orientation": "auto",
                            "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": False},
                            "textMode": "auto"
                        }
                    },
                    {
                        "id": 2,
                        "title": "💰 ETHUSDT 资金费率",
                        "type": "stat",
                        "gridPos": {"h": 6, "w": 6, "x": 6, "y": 0},
                        "targets": [
                            {
                                "datasource": {"type": "testdata", "uid": datasource_uid},
                                "refId": "A",
                                "scenarioId": "random_walk",
                                "alias": "资金费率 (%)",
                                "min": -0.08,
                                "max": 0.08,
                                "noise": 0.015
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None},
                                        {"color": "yellow", "value": 0.01},
                                        {"color": "red", "value": 0.05}
                                    ]
                                },
                                "unit": "percent",
                                "decimals": 5
                            }
                        },
                        "options": {
                            "colorMode": "background",
                            "graphMode": "area",
                            "justifyMode": "center",
                            "orientation": "auto",
                            "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": False},
                            "textMode": "auto"
                        }
                    },
                    {
                        "id": 3,
                        "title": "⚡ ETHUSDT 延时分析",
                        "type": "timeseries",
                        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6},
                        "targets": [
                            {
                                "datasource": {"type": "testdata", "uid": datasource_uid},
                                "refId": "A",
                                "scenarioId": "random_walk",
                                "alias": "平均延时",
                                "min": 20,
                                "max": 150,
                                "noise": 15
                            },
                            {
                                "datasource": {"type": "testdata", "uid": datasource_uid},
                                "refId": "B",
                                "scenarioId": "random_walk",
                                "alias": "最大延时",
                                "min": 80,
                                "max": 300,
                                "noise": 25
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "palette-classic"},
                                "custom": {
                                    "axisLabel": "延时 (毫秒)",
                                    "axisPlacement": "auto",
                                    "drawStyle": "line",
                                    "fillOpacity": 20,
                                    "lineWidth": 2,
                                    "pointSize": 5,
                                    "showPoints": "never"
                                },
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None},
                                        {"color": "yellow", "value": 100},
                                        {"color": "red", "value": 200}
                                    ]
                                },
                                "unit": "ms"
                            }
                        },
                        "options": {
                            "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"},
                            "tooltip": {"mode": "multi", "sort": "none"}
                        }
                    },
                    {
                        "id": 4,
                        "title": "📊 深度对比表格",
                        "type": "table",
                        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6},
                        "targets": [
                            {
                                "datasource": {"type": "testdata", "uid": datasource_uid},
                                "refId": "A",
                                "scenarioId": "csv_content",
                                "csvContent": "交易对,Bitda买一卖一量,Binance买一卖一量,比值\nBTCUSDT,15.2,5.1,2.98\nETHUSDT,22.8,4.6,4.96\nBTCUSDT,16.1,5.3,3.04\nETHUSDT,21.5,4.2,5.12"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "custom": {"align": "center", "displayMode": "auto"},
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None},
                                        {"color": "yellow", "value": 3},
                                        {"color": "red", "value": 5}
                                    ]
                                },
                                "decimals": 2
                            }
                        },
                        "options": {"showHeader": True}
                    },
                    {
                        "id": 5,
                        "title": "📈 标记价格偏差分析",
                        "type": "stat",
                        "gridPos": {"h": 6, "w": 8, "x": 0, "y": 14},
                        "targets": [
                            {
                                "datasource": {"type": "testdata", "uid": datasource_uid},
                                "refId": "A",
                                "scenarioId": "random_walk",
                                "alias": "BTCUSDT偏差",
                                "min": -0.3,
                                "max": 0.3,
                                "noise": 0.05
                            },
                            {
                                "datasource": {"type": "testdata", "uid": datasource_uid},
                                "refId": "B",
                                "scenarioId": "random_walk",
                                "alias": "ETHUSDT偏差",
                                "min": -0.25,
                                "max": 0.25,
                                "noise": 0.04
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None},
                                        {"color": "yellow", "value": 0.1},
                                        {"color": "red", "value": 0.2}
                                    ]
                                },
                                "unit": "percent",
                                "decimals": 4
                            }
                        },
                        "options": {
                            "colorMode": "background",
                            "graphMode": "area",
                            "justifyMode": "auto",
                            "orientation": "auto",
                            "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": False},
                            "textMode": "auto"
                        }
                    },
                    {
                        "id": 6,
                        "title": "🔄 系统运行状态",
                        "type": "stat",
                        "gridPos": {"h": 6, "w": 8, "x": 8, "y": 14},
                        "targets": [
                            {
                                "datasource": {"type": "testdata", "uid": datasource_uid},
                                "refId": "A",
                                "scenarioId": "random_walk",
                                "alias": "数据更新频率",
                                "min": 25,
                                "max": 35,
                                "noise": 2
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "red", "value": None},
                                        {"color": "yellow", "value": 20},
                                        {"color": "green", "value": 25}
                                    ]
                                },
                                "unit": "hertz",
                                "decimals": 1
                            }
                        },
                        "options": {
                            "colorMode": "background",
                            "graphMode": "area",
                            "justifyMode": "center",
                            "orientation": "auto",
                            "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": False},
                            "textMode": "auto"
                        }
                    },
                    {
                        "id": 7,
                        "title": "📋 资金费率对比表格",
                        "type": "table",
                        "gridPos": {"h": 6, "w": 8, "x": 16, "y": 14},
                        "targets": [
                            {
                                "datasource": {"type": "testdata", "uid": datasource_uid},
                                "refId": "A",
                                "scenarioId": "csv_content",
                                "csvContent": "交易对,Bitda费率,Binance费率,差值\nBTCUSDT,0.05200%,0.00835%,0.04365%\nETHUSDT,-0.04906%,0.00835%,-0.05741%"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "custom": {"align": "center", "displayMode": "auto"},
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None},
                                        {"color": "yellow", "value": 0.02},
                                        {"color": "red", "value": 0.05}
                                    ]
                                }
                            }
                        },
                        "options": {"showHeader": True}
                    }
                ],
                "time": {"from": "now-1h", "to": "now"},
                "timepicker": {},
                "templating": {"list": []},
                "annotations": {"list": []},
                "refresh": "10s",
                "schemaVersion": 30,
                "version": 1,
                "links": []
            },
            "overwrite": True
        }
        
        try:
            response = self.session.post(
                f"{self.grafana_url}/api/dashboards/db",
                json=dashboard_config,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                dashboard_url = f"{self.grafana_url}{result.get('url', '')}"
                print(f"✅ 仪表板创建成功")
                print(f"🌐 访问地址: {dashboard_url}")
                return dashboard_url
            else:
                print(f"❌ 仪表板创建失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ 仪表板创建异常: {e}")
        
        return None
    
    def setup_grafana(self):
        """完整设置Grafana"""
        print("🚀 开始自动配置Grafana...")
        print("=" * 50)
        
        # 等待Grafana启动
        if not self.wait_for_grafana():
            return False
        
        # 创建数据源
        datasource_uid = self.create_datasource()
        if not datasource_uid:
            print("❌ 数据源创建失败")
            return False
        
        # 创建仪表板
        dashboard_url = self.create_dashboard(datasource_uid)
        if not dashboard_url:
            print("❌ 仪表板创建失败")
            return False
        
        print("\n" + "=" * 50)
        print("🎉 Grafana配置完成！")
        print(f"🌐 仪表板地址: {dashboard_url}")
        print("👤 登录信息: admin/admin")
        print("🔄 数据每10秒自动刷新")
        print("=" * 50)
        
        return True

def main():
    """主函数"""
    setup = GrafanaAutoSetup()
    
    if setup.setup_grafana():
        print("\n✅ 配置成功！现在您可以在浏览器中查看实时数据了！")
        
        # 自动打开浏览器
        try:
            import webbrowser
            webbrowser.open("http://localhost:3000")
            print("🌐 浏览器已自动打开")
        except:
            print("🌐 请手动访问: http://localhost:3000")
    else:
        print("\n❌ 配置失败，请检查Grafana是否正常运行")

if __name__ == "__main__":
    main()
