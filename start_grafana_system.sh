#!/bin/bash

# 一键启动Grafana数据分析系统
# 包含数据导出器、Grafana服务和浏览器打开

echo "🚀 启动完整的加密货币数据分析和可视化系统"
echo "=================================================="

# 检查必要文件
check_files() {
    echo "🔍 检查必要文件..."
    
    local files=(
        "simple_data_exporter.py"
        "crypto_dashboard.json"
        "analyzer/funding_analyzer.py"
        "analyzer/latency_analyzer.py"
        "analyzer/depth_analyzer.py"
        "analyzer/price_analyzer.py"
    )
    
    for file in "${files[@]}"; do
        if [[ -f "$file" ]]; then
            echo "  ✅ $file"
        else
            echo "  ❌ $file 不存在"
            return 1
        fi
    done
    
    return 0
}

# 检查数据库连接
check_database() {
    echo "🗄️  检查数据库连接..."
    
    if mysql -u root -pLinuxtest -e "SELECT 1;" depth_db &>/dev/null; then
        echo "  ✅ 数据库连接正常"
        return 0
    else
        echo "  ❌ 数据库连接失败"
        return 1
    fi
}

# 启动数据导出器
start_data_exporter() {
    echo "📡 启动数据导出器..."
    
    # 检查端口是否被占用
    if netstat -tlnp 2>/dev/null | grep -q ":8000 "; then
        echo "  ⚠️  端口8000已被占用，尝试停止现有进程..."
        pkill -f "simple_data_exporter.py"
        sleep 2
    fi
    
    # 启动数据导出器
    nohup python simple_data_exporter.py > data_exporter.log 2>&1 &
    DATA_EXPORTER_PID=$!
    
    # 等待启动
    echo "  ⏳ 等待数据导出器启动..."
    sleep 5
    
    # 检查是否启动成功
    if curl -s http://localhost:8000 > /dev/null; then
        echo "  ✅ 数据导出器启动成功 (PID: $DATA_EXPORTER_PID)"
        echo "  🌐 API地址: http://localhost:8000"
        return 0
    else
        echo "  ❌ 数据导出器启动失败"
        return 1
    fi
}

# 检查Grafana状态
check_grafana() {
    echo "📊 检查Grafana状态..."
    
    if netstat -tlnp 2>/dev/null | grep -q ":3000 "; then
        echo "  ✅ Grafana正在运行"
        return 0
    else
        echo "  ❌ Grafana未运行"
        return 1
    fi
}

# 启动Grafana
start_grafana() {
    echo "📊 启动Grafana..."
    
    # 检测操作系统
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        if command -v systemctl &> /dev/null; then
            sudo systemctl start grafana-server
            echo "  ✅ Grafana已通过systemd启动"
        elif command -v service &> /dev/null; then
            sudo service grafana-server start
            echo "  ✅ Grafana已通过service启动"
        else
            echo "  ❌ 无法启动Grafana，请手动启动"
            return 1
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew services start grafana
            echo "  ✅ Grafana已通过Homebrew启动"
        else
            echo "  ❌ 请先安装Homebrew和Grafana"
            return 1
        fi
    else
        echo "  ❌ 不支持的操作系统"
        return 1
    fi
    
    # 等待Grafana启动
    echo "  ⏳ 等待Grafana启动..."
    sleep 10
    
    return 0
}

# 测试API数据
test_api_data() {
    echo "🧪 测试API数据..."
    
    local endpoints=(
        "funding-rates"
        "latency"
        "depth-comparison"
        "price-deviation"
    )
    
    for endpoint in "${endpoints[@]}"; do
        if curl -s "http://localhost:8000/api/$endpoint" | jq . &>/dev/null; then
            echo "  ✅ $endpoint 数据正常"
        else
            echo "  ⚠️  $endpoint 数据异常"
        fi
    done
}

# 打开浏览器
open_browser() {
    echo "🌐 打开浏览器..."
    
    local urls=(
        "http://localhost:3000"  # Grafana
        "http://localhost:8000"  # 数据API
    )
    
    for url in "${urls[@]}"; do
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            open "$url" &
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            # Linux
            if command -v xdg-open &> /dev/null; then
                xdg-open "$url" &
            fi
        fi
    done
    
    echo "  ✅ 浏览器已打开"
}

# 显示使用说明
show_instructions() {
    echo ""
    echo "📋 Grafana配置说明"
    echo "=================="
    echo ""
    echo "1. 访问Grafana: http://localhost:3000"
    echo "   用户名: admin"
    echo "   密码: admin"
    echo ""
    echo "2. 添加数据源:"
    echo "   - 类型: JSON API 或 Infinity"
    echo "   - URL: http://localhost:8000/api/all-data"
    echo "   - 名称: Crypto Data API"
    echo ""
    echo "3. 导入仪表板:"
    echo "   - 点击 + -> Import"
    echo "   - 上传文件: crypto_dashboard.json"
    echo ""
    echo "4. 或者手动创建面板:"
    echo "   - 资金费率: /api/funding-rates"
    echo "   - 延时分析: /api/latency"
    echo "   - 深度对比: /api/depth-comparison"
    echo "   - 价格偏差: /api/price-deviation"
    echo ""
    echo "📊 数据API文档: http://localhost:8000"
    echo ""
}

# 停止服务
stop_services() {
    echo ""
    echo "⏹️  停止服务..."
    
    # 停止数据导出器
    if [[ -n "$DATA_EXPORTER_PID" ]]; then
        kill "$DATA_EXPORTER_PID" 2>/dev/null
        echo "  ✅ 数据导出器已停止"
    fi
    
    # 也可以通过进程名停止
    pkill -f "simple_data_exporter.py" 2>/dev/null
    
    echo "  ✅ 所有服务已停止"
}

# 主函数
main() {
    # 检查文件
    if ! check_files; then
        echo "❌ 文件检查失败，请确保所有必要文件存在"
        exit 1
    fi
    
    # 检查数据库
    if ! check_database; then
        echo "❌ 数据库连接失败，请检查数据库配置"
        exit 1
    fi
    
    # 启动数据导出器
    if ! start_data_exporter; then
        echo "❌ 数据导出器启动失败"
        exit 1
    fi
    
    # 检查Grafana状态
    if ! check_grafana; then
        echo "⚠️  Grafana未运行，尝试启动..."
        start_grafana
    else
        echo "✅ Grafana已在运行"
    fi
    
    # 测试API数据
    test_api_data
    
    # 打开浏览器
    open_browser
    
    # 显示使用说明
    show_instructions
    
    # 等待用户输入
    echo "按 Enter 键停止所有服务..."
    read -r
    
    # 停止服务
    stop_services
}

# 设置信号处理
trap stop_services EXIT INT TERM

# 运行主函数
main
