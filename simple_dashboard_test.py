#!/usr/bin/env python3
"""
简单的仪表板测试
直接测试SQL查询是否工作
"""

import mysql.connector
from datetime import datetime

def test_sql_queries():
    """测试仪表板使用的SQL查询"""
    print("🔍 测试仪表板SQL查询")
    print("=" * 50)
    
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'Linuxtest',
        'database': 'depth_db'
    }
    
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        # 测试简单查询
        print("📊 测试1: 获取最新数据时间")
        cursor.execute("""
            SELECT FROM_UNIXTIME(MAX(timestamp)/1000, '%Y-%m-%d %H:%i:%s') as latest_time
            FROM bitda_depth 
            WHERE symbol IN ('BTCUSDT', 'ETHUSDT')
        """)
        
        result = cursor.fetchone()
        if result:
            print(f"   ✅ 最新数据时间: {result[0]}")
        else:
            print(f"   ❌ 无数据")
        
        # 测试BTCUSDT查询
        print("\n📊 测试2: BTCUSDT深度对比")
        cursor.execute("""
            SELECT 
                '买一量' as 项目,
                ROUND(b.bid_qty_1, 2) as Bitda,
                ROUND(bn.bid_qty_1, 2) as Binance,
                ROUND(b.bid_qty_1 / bn.bid_qty_1, 2) as 深度比
            FROM bitda_depth b
            JOIN (
                SELECT timestamp as bt FROM bitda_depth 
                WHERE symbol = 'BTCUSDT' AND bid_price_1 IS NOT NULL 
                ORDER BY timestamp DESC LIMIT 1
            ) latest ON b.timestamp = latest.bt
            JOIN binance_depth_5 bn ON bn.symbol = 'BTCUSDT' 
                AND bn.event_time <= b.timestamp
                AND bn.bid_price_1 IS NOT NULL
            WHERE b.symbol = 'BTCUSDT'
            ORDER BY bn.event_time DESC
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        if result:
            print(f"   ✅ {result[0]}: Bitda={result[1]}, Binance={result[2]}, 深度比={result[3]}")
        else:
            print(f"   ❌ 无BTCUSDT数据")
        
        # 测试ETHUSDT查询
        print("\n📊 测试3: ETHUSDT深度对比")
        cursor.execute("""
            SELECT 
                '买一量' as 项目,
                ROUND(b.bid_qty_1, 2) as Bitda,
                ROUND(bn.bid_qty_1, 2) as Binance,
                ROUND(b.bid_qty_1 / bn.bid_qty_1, 2) as 深度比
            FROM bitda_depth b
            JOIN (
                SELECT timestamp as bt FROM bitda_depth 
                WHERE symbol = 'ETHUSDT' AND bid_price_1 IS NOT NULL 
                ORDER BY timestamp DESC LIMIT 1
            ) latest ON b.timestamp = latest.bt
            JOIN binance_depth_5 bn ON bn.symbol = 'ETHUSDT' 
                AND bn.event_time <= b.timestamp
                AND bn.bid_price_1 IS NOT NULL
            WHERE b.symbol = 'ETHUSDT'
            ORDER BY bn.event_time DESC
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        if result:
            print(f"   ✅ {result[0]}: Bitda={result[1]}, Binance={result[2]}, 深度比={result[3]}")
        else:
            print(f"   ❌ 无ETHUSDT数据")
        
        cursor.close()
        connection.close()
        
        print(f"\n🎉 SQL查询测试完成！")
        print(f"💡 如果所有查询都有结果，说明仪表板应该能正常显示数据")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔍 简单仪表板测试")
    print("=" * 50)
    print("🌐 仪表板地址: http://localhost:3000/d/e1afad38-af94-4c2d-a082-3e0231e1118f/7b707bf")
    print("🎯 测试目标: 验证SQL查询是否正常工作")
    print()
    
    test_sql_queries()
    
    print(f"\n💡 验证步骤:")
    print(f"   1. 如果上述查询都有结果，说明数据源正常")
    print(f"   2. 打开仪表板页面查看是否显示数据")
    print(f"   3. 观察右上角是否有10秒倒计时")
    print(f"   4. 等待10秒看数据是否更新")

if __name__ == "__main__":
    main()
