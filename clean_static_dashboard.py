#!/usr/bin/env python3
"""
创建完全干净的静态延时分析面板，不显示A-series
"""

import requests
import json
import mysql.connector
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CleanStaticDashboard:
    """完全干净的静态仪表板创建器"""
    
    def __init__(self):
        self.grafana_url = "http://localhost:3000"
        self.admin_user = "admin"
        self.admin_pass = "admin"
        self.session = requests.Session()
        self.session.auth = (self.admin_user, self.admin_pass)
        
        self.latency_db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'ethusdt_latency_db'
        }
    
    def get_latest_data(self):
        """获取最近的延时数据"""
        logger.info("📊 获取最近的延时数据...")
        
        try:
            connection = mysql.connector.connect(**self.latency_db_config)
            cursor = connection.cursor()
            
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_matches,
                    AVG(latency_ms) as avg_latency,
                    MIN(latency_ms) as min_latency,
                    MAX(latency_ms) as max_latency,
                    MAX(created_at) as latest_time,
                    MIN(created_at) as earliest_time
                FROM ethusdt_latency_matches
            """)
            
            result = cursor.fetchone()
            if result and result[0] > 0:
                total, avg_lat, min_lat, max_lat, latest_time, earliest_time = result
                
                logger.info(f"   ✅ 找到数据: 总计{total}条匹配")
                logger.info(f"   📈 平均延时: {avg_lat:.1f}ms")
                logger.info(f"   📊 延时范围: {min_lat}-{max_lat}ms")
                logger.info(f"   ⏰ 最新时间: {latest_time}")
                
                cursor.close()
                connection.close()
                
                return {
                    'total': total,
                    'avg_latency': round(avg_lat, 1),
                    'min_latency': min_lat,
                    'max_latency': max_lat,
                    'latest_time': latest_time,
                    'earliest_time': earliest_time
                }
            else:
                logger.warning("   ❌ 未找到延时数据")
                cursor.close()
                connection.close()
                return None
                
        except Exception as e:
            logger.error(f"   ❌ 获取数据失败: {e}")
            return None
    
    def create_clean_dashboard(self, data):
        """创建完全干净的仪表板"""
        logger.info("📋 创建完全干净的仪表板...")
        
        if not data:
            logger.error("   ❌ 无数据，无法创建仪表板")
            return None
        
        # 格式化时间
        latest_time_str = data['latest_time'].strftime('%Y-%m-%d %H:%M:%S')
        time_span = data['latest_time'] - data['earliest_time']
        days = time_span.days
        hours = time_span.seconds // 3600
        
        # 根据延时设置颜色
        def get_color(latency):
            if latency < 300:
                return "#28a745"  # 绿色
            elif latency < 600:
                return "#ffc107"  # 黄色
            else:
                return "#dc3545"  # 红色
        
        avg_color = get_color(data['avg_latency'])
        
        dashboard_config = {
            "dashboard": {
                "id": None,
                "title": f"⚡ ETHUSDT延时分析 (干净版) - {datetime.now().strftime('%m-%d %H:%M')}",
                "tags": ["ethusdt", "latency", "clean"],
                "timezone": "browser",
                "panels": [
                    {
                        "id": 1,
                        "title": "",
                        "type": "text",
                        "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0},
                        "targets": [],
                        "options": {
                            "mode": "html",
                            "content": f"""
                            <div style="
                                background: {avg_color}; 
                                color: white; 
                                height: 100%; 
                                display: flex; 
                                flex-direction: column; 
                                justify-content: center; 
                                align-items: center; 
                                border-radius: 8px;
                                font-family: Arial, sans-serif;
                            ">
                                <div style="font-size: 16px; margin-bottom: 10px;">📊 平均延时</div>
                                <div style="font-size: 36px; font-weight: bold;">{data['avg_latency']}ms</div>
                            </div>
                            """
                        }
                    },
                    {
                        "id": 2,
                        "title": "",
                        "type": "text",
                        "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0},
                        "targets": [],
                        "options": {
                            "mode": "html",
                            "content": f"""
                            <div style="
                                background: #17a2b8; 
                                color: white; 
                                height: 100%; 
                                display: flex; 
                                flex-direction: column; 
                                justify-content: center; 
                                align-items: center; 
                                border-radius: 8px;
                                font-family: Arial, sans-serif;
                            ">
                                <div style="font-size: 16px; margin-bottom: 10px;">📈 延时范围</div>
                                <div style="font-size: 28px; font-weight: bold;">{data['min_latency']}-{data['max_latency']}ms</div>
                            </div>
                            """
                        }
                    },
                    {
                        "id": 3,
                        "title": "",
                        "type": "text",
                        "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0},
                        "targets": [],
                        "options": {
                            "mode": "html",
                            "content": f"""
                            <div style="
                                background: #28a745; 
                                color: white; 
                                height: 100%; 
                                display: flex; 
                                flex-direction: column; 
                                justify-content: center; 
                                align-items: center; 
                                border-radius: 8px;
                                font-family: Arial, sans-serif;
                            ">
                                <div style="font-size: 16px; margin-bottom: 10px;">📊 总匹配数</div>
                                <div style="font-size: 32px; font-weight: bold;">{data['total']}</div>
                            </div>
                            """
                        }
                    },
                    {
                        "id": 4,
                        "title": "",
                        "type": "text",
                        "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0},
                        "targets": [],
                        "options": {
                            "mode": "html",
                            "content": f"""
                            <div style="
                                background: #6f42c1; 
                                color: white; 
                                height: 100%; 
                                display: flex; 
                                flex-direction: column; 
                                justify-content: center; 
                                align-items: center; 
                                border-radius: 8px;
                                font-family: Arial, sans-serif;
                            ">
                                <div style="font-size: 14px; margin-bottom: 10px;">⏰ 数据时间</div>
                                <div style="font-size: 18px; font-weight: bold; text-align: center;">{latest_time_str}</div>
                            </div>
                            """
                        }
                    },
                    {
                        "id": 5,
                        "title": "",
                        "type": "text",
                        "gridPos": {"h": 10, "w": 24, "x": 0, "y": 8},
                        "targets": [],
                        "options": {
                            "mode": "html",
                            "content": f"""
                            <div style="padding: 20px; background: #f8f9fa; border-radius: 8px; font-family: Arial, sans-serif;">
                                <h2 style="text-align: center; color: #333; margin-bottom: 30px;">📊 ETHUSDT延时分析总结</h2>
                                
                                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 30px;">
                                    <div style="background: {avg_color}; color: white; padding: 20px; border-radius: 8px; text-align: center;">
                                        <h3 style="margin: 0 0 10px 0;">平均延时</h3>
                                        <h1 style="margin: 0; font-size: 2.5em;">{data['avg_latency']}ms</h1>
                                    </div>
                                    <div style="background: #ff7f0e; color: white; padding: 20px; border-radius: 8px; text-align: center;">
                                        <h3 style="margin: 0 0 10px 0;">最小延时</h3>
                                        <h1 style="margin: 0; font-size: 2.5em;">{data['min_latency']}ms</h1>
                                    </div>
                                    <div style="background: #2ca02c; color: white; padding: 20px; border-radius: 8px; text-align: center;">
                                        <h3 style="margin: 0 0 10px 0;">最大延时</h3>
                                        <h1 style="margin: 0; font-size: 2.5em;">{data['max_latency']}ms</h1>
                                    </div>
                                    <div style="background: #d62728; color: white; padding: 20px; border-radius: 8px; text-align: center;">
                                        <h3 style="margin: 0 0 10px 0;">总匹配数</h3>
                                        <h1 style="margin: 0; font-size: 2.5em;">{data['total']}</h1>
                                    </div>
                                </div>
                                
                                <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff;">
                                    <h3 style="color: #007bff; margin-top: 0;">📋 数据说明</h3>
                                    <p><strong>数据时间范围:</strong> {data['earliest_time'].strftime('%Y-%m-%d %H:%M')} ~ {data['latest_time'].strftime('%Y-%m-%d %H:%M')} (跨度: {days}天{hours}小时)</p>
                                    <p><strong>延时含义:</strong> Bitda出现特定买一卖一价格对比Binance首次出现相同价格对晚了多少毫秒</p>
                                    <p><strong>匹配条件:</strong> 要求Bitda和Binance的买一价和卖一价完全相同</p>
                                    <p><strong>数据质量:</strong> 每个延时记录都是高质量的完全匹配</p>
                                </div>
                                
                                <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745; margin-top: 20px;">
                                    <h3 style="color: #28a745; margin-top: 0;">🎯 分析结论</h3>
                                    <ul style="margin: 0; padding-left: 20px;">
                                        <li>平均延时 <strong>{data['avg_latency']}ms</strong> 表示价格信息从Binance传播到Bitda平均需要约 <strong>{data['avg_latency']/1000:.1f}秒</strong></li>
                                        <li>延时范围 <strong>{data['min_latency']}-{data['max_latency']}ms</strong> 显示了网络传播的波动情况</li>
                                        <li>总计 <strong>{data['total']}</strong> 个完全匹配说明了数据的可靠性</li>
                                        <li>延时性能评级: <strong>{'优秀' if data['avg_latency'] < 300 else '良好' if data['avg_latency'] < 600 else '需要优化'}</strong></li>
                                    </ul>
                                </div>
                            </div>
                            """
                        }
                    }
                ],
                "time": {"from": "now-24h", "to": "now"},
                "timepicker": {},
                "templating": {"list": []},
                "annotations": {"list": []},
                "refresh": "5m",
                "schemaVersion": 30,
                "version": 1,
                "links": []
            },
            "overwrite": True
        }
        
        try:
            response = self.session.post(
                f"{self.grafana_url}/api/dashboards/db",
                json=dashboard_config,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                dashboard_url = f"{self.grafana_url}{result.get('url', '')}"
                logger.info(f"   ✅ 干净仪表板创建成功")
                logger.info(f"   🌐 访问地址: {dashboard_url}")
                return dashboard_url
            else:
                logger.error(f"   ❌ 仪表板创建失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            logger.error(f"   ❌ 仪表板创建异常: {e}")
        
        return None
    
    def create_dashboard(self):
        """创建仪表板"""
        logger.info("🧹 开始创建完全干净的延时分析仪表板...")
        logger.info("=" * 60)
        
        # 1. 获取最新数据
        data = self.get_latest_data()
        if not data:
            logger.error("❌ 无法获取数据，创建失败")
            return False
        
        # 2. 创建干净仪表板
        dashboard_url = self.create_clean_dashboard(data)
        if not dashboard_url:
            logger.error("❌ 仪表板创建失败")
            return False
        
        logger.info("\n" + "=" * 60)
        logger.info("🎉 完全干净的延时分析仪表板创建完成！")
        logger.info("📋 特点:")
        logger.info("   ✅ 完全没有A-series标签")
        logger.info("   ✅ 使用HTML文本面板")
        logger.info("   ✅ 美观的颜色和布局")
        logger.info("   ✅ 详细的数据分析")
        logger.info(f"🌐 仪表板地址: {dashboard_url}")
        logger.info("=" * 60)
        
        return dashboard_url

def main():
    """主函数"""
    print("🧹 完全干净的延时分析仪表板创建工具")
    print("=" * 50)
    print("功能:")
    print("  - 完全消除A-series标签")
    print("  - 使用HTML文本面板")
    print("  - 美观的颜色和布局")
    print("  - 详细的数据分析")
    print()
    
    creator = CleanStaticDashboard()
    dashboard_url = creator.create_dashboard()
    
    if dashboard_url:
        print("\n✅ 创建成功！")
        print("🧹 现在您看到的是完全干净的仪表板")
        print("🎨 没有任何A-series标签")
        
        # 自动打开浏览器
        try:
            import webbrowser
            webbrowser.open(dashboard_url)
            print("🌐 浏览器已自动打开")
        except:
            print(f"🌐 请手动访问: {dashboard_url}")
    else:
        print("\n❌ 创建失败，请检查数据状态")

if __name__ == "__main__":
    main()
