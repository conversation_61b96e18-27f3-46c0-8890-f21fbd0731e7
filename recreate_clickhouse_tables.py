#!/usr/bin/env python3
"""
重新创建ClickHouse表结构
"""

import requests
from datetime import datetime

# ClickHouse配置
CLICKHOUSE_URL = "***************************************/"

def log(message):
    """日志输出"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def ch_execute(query):
    """执行ClickHouse查询"""
    try:
        response = requests.post(CLICKHOUSE_URL, data=query.encode('utf-8'), timeout=30)
        if response.status_code == 200:
            return True
        else:
            log(f"❌ ClickHouse查询失败: {response.text}")
            return False
    except Exception as e:
        log(f"❌ ClickHouse连接异常: {e}")
        return False

def create_tables():
    """创建所有表"""
    
    tables = {
        'bitda_depth': """
            CREATE TABLE IF NOT EXISTS crypto.bitda_depth (
                id UInt64 DEFAULT 0,
                symbol String,
                timestamp UInt64,
                index_price Nullable(Decimal(15,8)),
                sign_price Nullable(Decimal(15,8)),
                last_price Nullable(Decimal(15,8)),
                bid_price_1 Decimal(15,2),
                ask_price_1 Decimal(15,2),
                bid_qty_1 Decimal(20,4),
                ask_qty_1 Decimal(20,4),
                bid_price_2 Nullable(Decimal(15,2)) COMMENT '买二价格',
                ask_price_2 Nullable(Decimal(15,2)) COMMENT '卖二价格',
                bid_qty_2 Nullable(Decimal(20,4)) COMMENT '买二数量',
                ask_qty_2 Nullable(Decimal(20,4)) COMMENT '卖二数量',
                bid_price_3 Nullable(Decimal(15,2)) COMMENT '买三价格',
                ask_price_3 Nullable(Decimal(15,2)) COMMENT '卖三价格',
                bid_qty_3 Nullable(Decimal(20,4)) COMMENT '买三数量',
                ask_qty_3 Nullable(Decimal(20,4)) COMMENT '卖三数量',
                bid_price_4 Nullable(Decimal(15,2)) COMMENT '买四价格',
                ask_price_4 Nullable(Decimal(15,2)) COMMENT '卖四价格',
                bid_qty_4 Nullable(Decimal(20,4)) COMMENT '买四数量',
                ask_qty_4 Nullable(Decimal(20,4)) COMMENT '卖四数量',
                bid_price_5 Nullable(Decimal(15,2)) COMMENT '买五价格',
                ask_price_5 Nullable(Decimal(15,2)) COMMENT '卖五价格',
                bid_qty_5 Nullable(Decimal(20,4)) COMMENT '买五数量',
                ask_qty_5 Nullable(Decimal(20,4)) COMMENT '卖五数量',
                asks String COMMENT '卖单深度',
                bids String COMMENT '买单深度',
                merge_level Int32 DEFAULT 0 COMMENT '合并级别',
                created_at DateTime DEFAULT now()
            ) ENGINE = MergeTree() 
            ORDER BY (symbol, timestamp)
            COMMENT='Bitda深度数据表'
        """,
        
        'binance_depth_5': """
            CREATE TABLE IF NOT EXISTS crypto.binance_depth_5 (
                id UInt64 DEFAULT 0,
                symbol String,
                event_time UInt64 COMMENT '事件时间',
                trade_time UInt64 COMMENT '交易时间',
                first_update_id UInt64 COMMENT '从上次推送至今新增的第一个update Id',
                last_update_id UInt64 COMMENT '从上次推送至今新增的最后一个update Id',
                prev_update_id UInt64 COMMENT '上次推送的最后一个update Id',
                bid_price_1 Nullable(Decimal(15,2)) COMMENT '买一价格',
                ask_price_1 Nullable(Decimal(15,2)) COMMENT '卖一价格',
                bid_qty_1 Nullable(Decimal(20,4)) COMMENT '买一数量',
                ask_qty_1 Nullable(Decimal(20,4)) COMMENT '卖一数量',
                bid_price_2 Nullable(Decimal(15,2)) COMMENT '买二价格',
                ask_price_2 Nullable(Decimal(15,2)) COMMENT '卖二价格',
                bid_qty_2 Nullable(Decimal(20,4)) COMMENT '买二数量',
                ask_qty_2 Nullable(Decimal(20,4)) COMMENT '卖二数量',
                bid_price_3 Nullable(Decimal(15,2)) COMMENT '买三价格',
                ask_price_3 Nullable(Decimal(15,2)) COMMENT '卖三价格',
                bid_qty_3 Nullable(Decimal(20,4)) COMMENT '买三数量',
                ask_qty_3 Nullable(Decimal(20,4)) COMMENT '卖三数量',
                bid_price_4 Nullable(Decimal(15,2)) COMMENT '买四价格',
                ask_price_4 Nullable(Decimal(15,2)) COMMENT '卖四价格',
                bid_qty_4 Nullable(Decimal(20,4)) COMMENT '买四数量',
                ask_qty_4 Nullable(Decimal(20,4)) COMMENT '卖四数量',
                bid_price_5 Nullable(Decimal(15,2)) COMMENT '买五价格',
                ask_price_5 Nullable(Decimal(15,2)) COMMENT '卖五价格',
                bid_qty_5 Nullable(Decimal(20,4)) COMMENT '买五数量',
                ask_qty_5 Nullable(Decimal(20,4)) COMMENT '卖五数量',
                bids String COMMENT '买单深度(5档)',
                asks String COMMENT '卖单深度(5档)',
                created_at DateTime DEFAULT now()
            ) ENGINE = MergeTree() 
            ORDER BY (symbol, event_time)
            COMMENT='Binance 5档深度数据表'
        """,
        
        'bitda_kline': """
            CREATE TABLE IF NOT EXISTS crypto.bitda_kline (
                id UInt64 DEFAULT 0,
                symbol String,
                timestamp UInt64 COMMENT '时间戳(秒)',
                open_price Decimal(15,8) COMMENT '开盘价',
                high_price Decimal(15,8) COMMENT '最高价',
                low_price Decimal(15,8) COMMENT '最低价',
                close_price Decimal(15,8) COMMENT '收盘价',
                volume Decimal(20,8) COMMENT '成交数量',
                amount Decimal(25,8) COMMENT '成交金额',
                period String COMMENT '周期',
                created_at DateTime DEFAULT now()
            ) ENGINE = MergeTree() 
            ORDER BY (symbol, timestamp)
            COMMENT='Bitda K线数据表'
        """,
        
        'bitda_trades': """
            CREATE TABLE IF NOT EXISTS crypto.bitda_trades (
                id UInt64 DEFAULT 0,
                symbol String,
                trade_id UInt64 COMMENT '交易ID',
                price Decimal(15,8) COMMENT '成交价格',
                amount Decimal(20,8) COMMENT '成交数量',
                trade_time Decimal(20,6) COMMENT '成交时间',
                trade_type String COMMENT '成交方向',
                created_at DateTime DEFAULT now()
            ) ENGINE = MergeTree() 
            ORDER BY (symbol, trade_time)
            COMMENT='Bitda成交数据表'
        """,
        
        'bitda_ticker': """
            CREATE TABLE IF NOT EXISTS crypto.bitda_ticker (
                id UInt64 DEFAULT 0,
                symbol String,
                open_price Decimal(15,8) COMMENT '开盘价',
                high_price Decimal(15,8) COMMENT '最高价',
                low_price Decimal(15,8) COMMENT '最低价',
                last_price Decimal(15,8) COMMENT '最新价',
                volume Decimal(25,8) COMMENT '成交量',
                amount Decimal(25,8) COMMENT '成交金额',
                change_rate Decimal(10,8) COMMENT '涨跌幅',
                funding_time Nullable(Int32) COMMENT '资金费率时间',
                position_amount Nullable(Decimal(25,8)) COMMENT '持仓量',
                funding_rate_last Nullable(Decimal(10,8)) COMMENT '当前资金费率',
                funding_rate_next Nullable(Decimal(10,8)) COMMENT '下一个资金费率',
                funding_rate_predict Nullable(Decimal(10,8)) COMMENT '预测资金费率',
                insurance Nullable(Decimal(25,8)) COMMENT '保险基金',
                sign_price Nullable(Decimal(15,8)) COMMENT '标记价格',
                index_price Nullable(Decimal(15,8)) COMMENT '指数价格',
                sell_total Nullable(Decimal(25,8)) COMMENT '卖盘总量',
                buy_total Nullable(Decimal(25,8)) COMMENT '买盘总量',
                period Int32 DEFAULT 86400 COMMENT '周期',
                created_at DateTime DEFAULT now()
            ) ENGINE = MergeTree() 
            ORDER BY (symbol, created_at)
            COMMENT='Bitda行情数据表'
        """,
        
        'binance_bookticker': """
            CREATE TABLE IF NOT EXISTS crypto.binance_bookticker (
                id UInt64 DEFAULT 0,
                symbol String,
                update_id UInt64 COMMENT '更新ID',
                bid_price Decimal(15,8) COMMENT '最优买单价',
                bid_qty Decimal(20,8) COMMENT '最优买单量',
                ask_price Decimal(15,8) COMMENT '最优卖单价',
                ask_qty Decimal(20,8) COMMENT '最优卖单量',
                event_time UInt64 COMMENT '事件时间',
                trade_time UInt64 COMMENT '交易时间',
                created_at DateTime DEFAULT now()
            ) ENGINE = MergeTree() 
            ORDER BY (symbol, event_time)
            COMMENT='Binance BookTicker数据表'
        """
    }
    
    for table_name, sql in tables.items():
        log(f"🔧 创建表 {table_name}...")
        if ch_execute(sql):
            log(f"✅ 表 {table_name} 创建成功")
        else:
            log(f"❌ 表 {table_name} 创建失败")
            return False
    
    return True

def main():
    """主函数"""
    log("🚀 开始重新创建ClickHouse表结构")
    log("=" * 50)
    
    if create_tables():
        log("=" * 50)
        log("🎉 所有表创建完成！")
        
        # 验证表创建
        log("🔍 验证表创建...")
        if ch_execute("SELECT 'Tables created successfully' as status"):
            log("✅ 表结构验证成功")
        
    else:
        log("❌ 表创建失败")

if __name__ == "__main__":
    main()
