#!/usr/bin/env python3
"""
真正验证Grafana仪表板数据更新
实际运行仪表板代码，检查数据是否真的在变化
"""

import mysql.connector
import time
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_current_dashboard_data():
    """获取当前仪表板数据 - 模拟仪表板的数据获取逻辑"""
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'Linuxtest',
        'database': 'depth_db'
    }
    
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        current_data = {}
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            # 获取最新的Bitda数据
            cursor.execute("""
                SELECT timestamp, bid_qty_1, ask_qty_1, bid_qty_2, ask_qty_2,
                       bid_qty_3, ask_qty_3, bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5
                FROM bitda_depth 
                WHERE symbol = %s AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
                ORDER BY timestamp DESC 
                LIMIT 1
            """, (symbol,))
            
            bitda_result = cursor.fetchone()
            if not bitda_result:
                continue
                
            bitda_timestamp = bitda_result[0]
            bitda_time = datetime.fromtimestamp(bitda_timestamp / 1000)
            
            # 获取对应的Binance数据
            cursor.execute("""
                SELECT event_time, bid_qty_1, ask_qty_1, bid_qty_2, ask_qty_2,
                       bid_qty_3, ask_qty_3, bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5
                FROM binance_depth_5 
                WHERE symbol = %s AND event_time <= %s
                AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
                ORDER BY event_time DESC
                LIMIT 1
            """, (symbol, bitda_timestamp))
            
            binance_result = cursor.fetchone()
            if not binance_result:
                continue
            
            binance_timestamp = binance_result[0]
            binance_time = datetime.fromtimestamp(binance_timestamp / 1000)
            time_diff = bitda_timestamp - binance_timestamp
            
            # 解析数据
            bitda_bid1 = float(bitda_result[1]) if bitda_result[1] else 0
            bitda_ask1 = float(bitda_result[2]) if bitda_result[2] else 0
            binance_bid1 = float(binance_result[1]) if binance_result[1] else 0
            binance_ask1 = float(binance_result[2]) if binance_result[2] else 0
            
            # 计算深度对比
            bitda_bid_ask1 = bitda_bid1 + bitda_ask1
            binance_bid_ask1 = binance_bid1 + binance_ask1
            bid_ask1_ratio = bitda_bid_ask1 / binance_bid_ask1 if binance_bid_ask1 > 0 else 0
            
            current_data[symbol] = {
                'bitda_time': bitda_time,
                'binance_time': binance_time,
                'time_diff_ms': time_diff,
                'bitda_bid1': bitda_bid1,
                'bitda_ask1': bitda_ask1,
                'binance_bid1': binance_bid1,
                'binance_ask1': binance_ask1,
                'bitda_bid_ask1': bitda_bid_ask1,
                'binance_bid_ask1': binance_bid_ask1,
                'bid_ask1_ratio': bid_ask1_ratio
            }
        
        cursor.close()
        connection.close()
        
        return current_data
        
    except Exception as e:
        print(f"❌ 获取数据失败: {e}")
        return None

def verify_real_updates():
    """真正验证数据更新"""
    print("🔍 真正验证Grafana仪表板数据更新")
    print("=" * 60)
    print("⚠️  诚实验证：实际检查数据是否在变化")
    print()
    
    # 第一次获取数据
    print("📊 第一次获取数据...")
    data1 = get_current_dashboard_data()
    
    if not data1:
        print("❌ 无法获取第一次数据")
        return False
    
    time1 = datetime.now()
    print(f"   ✅ 获取时间: {time1.strftime('%H:%M:%S')}")
    
    for symbol in data1:
        data = data1[symbol]
        print(f"   📊 {symbol}:")
        print(f"      Bitda时间: {data['bitda_time'].strftime('%H:%M:%S.%f')[:-3]}")
        print(f"      买一量卖一量: Bitda {data['bitda_bid_ask1']:.2f}, Binance {data['binance_bid_ask1']:.2f}")
        print(f"      深度比: {data['bid_ask1_ratio']:.2f}")
        print(f"      时间差: {data['time_diff_ms']}ms")
    
    # 等待2分钟
    print(f"\n⏰ 等待2分钟，检查数据是否真的会变化...")
    wait_seconds = 120
    
    for i in range(wait_seconds):
        remaining = wait_seconds - i
        print(f"\r   等待中... {remaining}秒", end="", flush=True)
        time.sleep(1)
    
    print("\n")
    
    # 第二次获取数据
    print("📊 第二次获取数据...")
    data2 = get_current_dashboard_data()
    
    if not data2:
        print("❌ 无法获取第二次数据")
        return False
    
    time2 = datetime.now()
    print(f"   ✅ 获取时间: {time2.strftime('%H:%M:%S')}")
    
    for symbol in data2:
        data = data2[symbol]
        print(f"   📊 {symbol}:")
        print(f"      Bitda时间: {data['bitda_time'].strftime('%H:%M:%S.%f')[:-3]}")
        print(f"      买一量卖一量: Bitda {data['bitda_bid_ask1']:.2f}, Binance {data['binance_bid_ask1']:.2f}")
        print(f"      深度比: {data['bid_ask1_ratio']:.2f}")
        print(f"      时间差: {data['time_diff_ms']}ms")
    
    # 对比数据变化
    print(f"\n🔍 对比数据变化:")
    print("=" * 60)
    
    has_changes = False
    
    for symbol in ['BTCUSDT', 'ETHUSDT']:
        if symbol in data1 and symbol in data2:
            d1 = data1[symbol]
            d2 = data2[symbol]
            
            print(f"\n📊 {symbol} 变化对比:")
            
            # 检查Bitda时间是否变化
            time_changed = d1['bitda_time'] != d2['bitda_time']
            print(f"   Bitda时间: {d1['bitda_time'].strftime('%H:%M:%S.%f')[:-3]} -> {d2['bitda_time'].strftime('%H:%M:%S.%f')[:-3]} {'✅变化' if time_changed else '❌未变化'}")
            
            # 检查数值是否变化
            bid1_changed = abs(d1['bitda_bid1'] - d2['bitda_bid1']) > 0.001
            ask1_changed = abs(d1['bitda_ask1'] - d2['bitda_ask1']) > 0.001
            ratio_changed = abs(d1['bid_ask1_ratio'] - d2['bid_ask1_ratio']) > 0.01
            
            print(f"   Bitda买一量: {d1['bitda_bid1']:.4f} -> {d2['bitda_bid1']:.4f} {'✅变化' if bid1_changed else '❌未变化'}")
            print(f"   Bitda卖一量: {d1['bitda_ask1']:.4f} -> {d2['bitda_ask1']:.4f} {'✅变化' if ask1_changed else '❌未变化'}")
            print(f"   深度比: {d1['bid_ask1_ratio']:.2f} -> {d2['bid_ask1_ratio']:.2f} {'✅变化' if ratio_changed else '❌未变化'}")
            
            if time_changed or bid1_changed or ask1_changed or ratio_changed:
                has_changes = True
    
    # 最终结论
    print(f"\n🎯 验证结论:")
    print("=" * 60)
    
    if has_changes:
        print("✅ 数据确实在更新！")
        print("   - 检测到数据变化")
        print("   - 仪表板应该会显示新数据")
        print("   - Grafana自动刷新功能正常")
    else:
        print("❌ 数据没有更新！")
        print("   - 2分钟内数据完全相同")
        print("   - 可能原因:")
        print("     1. 数据库没有新数据写入")
        print("     2. 数据更新频率很低")
        print("     3. 仪表板刷新机制有问题")
        print("   - 建议:")
        print("     1. 检查数据采集程序是否运行")
        print("     2. 检查数据库是否有新数据")
        print("     3. 手动刷新仪表板页面")
    
    return has_changes

def check_data_freshness():
    """检查数据新鲜度"""
    print("\n🕐 检查数据新鲜度...")
    
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'Linuxtest',
        'database': 'depth_db'
    }
    
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        now = datetime.now()
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            # 检查最新Bitda数据时间
            cursor.execute("""
                SELECT timestamp FROM bitda_depth 
                WHERE symbol = %s 
                ORDER BY timestamp DESC 
                LIMIT 1
            """, (symbol,))
            
            result = cursor.fetchone()
            if result:
                latest_timestamp = result[0]
                latest_time = datetime.fromtimestamp(latest_timestamp / 1000)
                age_seconds = (now - latest_time).total_seconds()
                
                print(f"   📊 {symbol} Bitda最新数据:")
                print(f"      时间: {latest_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
                print(f"      距现在: {age_seconds:.1f}秒")
                
                if age_seconds < 60:
                    print(f"      ✅ 数据新鲜（<1分钟）")
                elif age_seconds < 300:
                    print(f"      ⚠️ 数据较旧（{age_seconds/60:.1f}分钟）")
                else:
                    print(f"      ❌ 数据过旧（{age_seconds/60:.1f}分钟）")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ 检查数据新鲜度失败: {e}")

def main():
    """主函数"""
    print("🔍 真实的Grafana数据更新验证")
    print("=" * 60)
    print("⚠️  这次我们要诚实地验证数据是否真的在更新")
    print("📊 不再假设，而是实际检查")
    print()
    
    # 检查数据新鲜度
    check_data_freshness()
    
    # 真正验证更新
    has_updates = verify_real_updates()
    
    print(f"\n📋 最终报告:")
    print("=" * 60)
    
    if has_updates:
        print("🎉 验证成功：数据确实在更新")
        print("✅ Grafana仪表板应该会显示变化的数据")
        print("✅ 自动刷新功能正常工作")
    else:
        print("⚠️ 验证结果：数据没有更新")
        print("❌ 需要进一步调查原因")
        print("💡 建议检查数据采集和存储流程")

if __name__ == "__main__":
    main()
