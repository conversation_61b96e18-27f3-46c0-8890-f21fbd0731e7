#!/usr/bin/env python3
"""
修复Grafana数据源密码问题
"""

import requests
import json

def fix_datasource_password():
    """修复数据源密码问题"""
    print("🔧 修复数据源密码问题...")
    
    session = requests.Session()
    session.auth = ("admin", "admin")
    
    # 获取现有数据源
    response = session.get("http://localhost:3000/api/datasources")
    if response.status_code != 200:
        print(f"   ❌ 获取数据源失败: {response.status_code}")
        return None
    
    datasources = response.json()
    fresh_ds = None
    
    for ds in datasources:
        if ds['name'] == 'FreshDepthDB':
            fresh_ds = ds
            break
    
    if not fresh_ds:
        print(f"   ❌ 未找到FreshDepthDB数据源")
        return None
    
    print(f"   📊 找到数据源: {fresh_ds['name']} (ID: {fresh_ds['id']})")
    
    # 更新数据源配置，确保密码正确设置
    updated_config = {
        "id": fresh_ds['id'],
        "uid": fresh_ds['uid'],
        "orgId": fresh_ds['orgId'],
        "name": fresh_ds['name'],
        "type": "mysql",
        "url": "localhost:3306",
        "access": "proxy",
        "database": "depth_db",
        "user": "root",
        "password": "Linuxtest",  # 明确设置密码
        "basicAuth": False,
        "isDefault": False,
        "jsonData": {
            "maxOpenConns": 10,
            "maxIdleConns": 2,
            "connMaxLifetime": 14400
        },
        "secureJsonData": {
            "password": "Linuxtest"  # 在secureJsonData中也设置密码
        },
        "version": fresh_ds['version']
    }
    
    try:
        # 更新数据源
        update_response = session.put(
            f"http://localhost:3000/api/datasources/{fresh_ds['id']}",
            json=updated_config,
            headers={"Content-Type": "application/json"}
        )
        
        if update_response.status_code == 200:
            print(f"   ✅ 数据源密码更新成功")
            
            # 测试连接
            test_response = session.get(f"http://localhost:3000/api/datasources/{fresh_ds['id']}/health")
            if test_response.status_code == 200:
                health_result = test_response.json()
                print(f"   ✅ 连接测试成功: {health_result}")
                return fresh_ds['uid']
            else:
                print(f"   ⚠️ 连接测试失败: {test_response.status_code}")
                print(f"   响应: {test_response.text}")
                return fresh_ds['uid']  # 仍然返回UID，可能测试接口有问题
        else:
            print(f"   ❌ 更新数据源失败: {update_response.status_code}")
            print(f"   响应: {update_response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 更新数据源异常: {e}")
        return None

def create_final_working_dashboard(datasource_uid):
    """创建最终工作的仪表板"""
    print("🎨 创建最终工作仪表板...")
    
    session = requests.Session()
    session.auth = ("admin", "admin")
    
    dashboard_config = {
        "dashboard": {
            "id": None,
            "title": "🎉 最终工作仪表板",
            "tags": ["final", "working"],
            "timezone": "browser",
            "panels": [
                {
                    "id": 1,
                    "title": "📊 BTCUSDT买一量",
                    "type": "stat",
                    "gridPos": {"h": 6, "w": 6, "x": 0, "y": 0},
                    "targets": [
                        {
                            "datasource": {
                                "type": "mysql",
                                "uid": datasource_uid
                            },
                            "format": "table",
                            "rawSql": "SELECT ROUND(bid_qty_1, 2) as 买一量 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1",
                            "refId": "A"
                        }
                    ],
                    "options": {
                        "reduceOptions": {
                            "values": False,
                            "calcs": ["lastNotNull"]
                        },
                        "textMode": "auto",
                        "colorMode": "background"
                    }
                },
                {
                    "id": 2,
                    "title": "📊 BTCUSDT卖一量",
                    "type": "stat",
                    "gridPos": {"h": 6, "w": 6, "x": 6, "y": 0},
                    "targets": [
                        {
                            "datasource": {
                                "type": "mysql",
                                "uid": datasource_uid
                            },
                            "format": "table",
                            "rawSql": "SELECT ROUND(ask_qty_1, 2) as 卖一量 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1",
                            "refId": "A"
                        }
                    ],
                    "options": {
                        "reduceOptions": {
                            "values": False,
                            "calcs": ["lastNotNull"]
                        },
                        "textMode": "auto",
                        "colorMode": "background"
                    }
                },
                {
                    "id": 3,
                    "title": "📊 ETHUSDT买一量",
                    "type": "stat",
                    "gridPos": {"h": 6, "w": 6, "x": 12, "y": 0},
                    "targets": [
                        {
                            "datasource": {
                                "type": "mysql",
                                "uid": datasource_uid
                            },
                            "format": "table",
                            "rawSql": "SELECT ROUND(bid_qty_1, 2) as 买一量 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1",
                            "refId": "A"
                        }
                    ],
                    "options": {
                        "reduceOptions": {
                            "values": False,
                            "calcs": ["lastNotNull"]
                        },
                        "textMode": "auto",
                        "colorMode": "background"
                    }
                },
                {
                    "id": 4,
                    "title": "📊 ETHUSDT卖一量",
                    "type": "stat",
                    "gridPos": {"h": 6, "w": 6, "x": 18, "y": 0},
                    "targets": [
                        {
                            "datasource": {
                                "type": "mysql",
                                "uid": datasource_uid
                            },
                            "format": "table",
                            "rawSql": "SELECT ROUND(ask_qty_1, 2) as 卖一量 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1",
                            "refId": "A"
                        }
                    ],
                    "options": {
                        "reduceOptions": {
                            "values": False,
                            "calcs": ["lastNotNull"]
                        },
                        "textMode": "auto",
                        "colorMode": "background"
                    }
                },
                {
                    "id": 5,
                    "title": "📋 BTCUSDT深度对比",
                    "type": "table",
                    "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6},
                    "targets": [
                        {
                            "datasource": {
                                "type": "mysql",
                                "uid": datasource_uid
                            },
                            "format": "table",
                            "rawSql": """
                                SELECT 
                                    '买一量' as 项目,
                                    (SELECT ROUND(bid_qty_1, 2) FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) as Bitda,
                                    (SELECT ROUND(bid_qty_1, 2) FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1) as Binance
                                UNION ALL
                                SELECT 
                                    '卖一量' as 项目,
                                    (SELECT ROUND(ask_qty_1, 2) FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) as Bitda,
                                    (SELECT ROUND(ask_qty_1, 2) FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1) as Binance
                            """,
                            "refId": "A"
                        }
                    ]
                },
                {
                    "id": 6,
                    "title": "📋 ETHUSDT深度对比",
                    "type": "table",
                    "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6},
                    "targets": [
                        {
                            "datasource": {
                                "type": "mysql",
                                "uid": datasource_uid
                            },
                            "format": "table",
                            "rawSql": """
                                SELECT 
                                    '买一量' as 项目,
                                    (SELECT ROUND(bid_qty_1, 2) FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) as Bitda,
                                    (SELECT ROUND(bid_qty_1, 2) FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1) as Binance
                                UNION ALL
                                SELECT 
                                    '卖一量' as 项目,
                                    (SELECT ROUND(ask_qty_1, 2) FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) as Bitda,
                                    (SELECT ROUND(ask_qty_1, 2) FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1) as Binance
                            """,
                            "refId": "A"
                        }
                    ]
                }
            ],
            "time": {"from": "now-1h", "to": "now"},
            "refresh": "5s",
            "schemaVersion": 30,
            "version": 1
        },
        "overwrite": True
    }
    
    try:
        response = session.post(
            "http://localhost:3000/api/dashboards/db",
            json=dashboard_config,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            dashboard_url = f"http://localhost:3000{result['url']}"
            print(f"   ✅ 最终仪表板创建成功")
            print(f"   🌐 地址: {dashboard_url}")
            return dashboard_url
        else:
            print(f"   ❌ 创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return None

def main():
    """主函数"""
    print("🔧 修复Grafana数据源密码问题")
    print("=" * 50)
    
    # 修复数据源密码
    datasource_uid = fix_datasource_password()
    if not datasource_uid:
        print("❌ 数据源修复失败")
        return
    
    # 创建最终仪表板
    dashboard_url = create_final_working_dashboard(datasource_uid)
    if dashboard_url:
        print(f"\n🎉 问题修复完成！")
        print(f"🌐 最终仪表板: {dashboard_url}")
        print(f"⏰ 5秒自动刷新")
        print(f"📊 显示真实数据")
        
        # 打开浏览器
        try:
            import webbrowser
            webbrowser.open(dashboard_url)
            print(f"🌐 已自动打开浏览器")
        except:
            pass
    else:
        print(f"❌ 仪表板创建失败")

if __name__ == "__main__":
    main()
