#!/usr/bin/env python3
"""
分析功能测试脚本
用于测试各种数据分析功能
"""

import sys
import os

# 设置环境变量
os.environ['DB_HOST'] = 'localhost'
os.environ['DB_USER'] = 'root'
os.environ['DB_PASSWORD'] = 'Linuxtest'
os.environ['DB_NAME'] = 'depth_db'

def test_database_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    try:
        import mysql.connector
        
        config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'depth_db'
        }
        
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        # 测试查询
        cursor.execute("SELECT COUNT(*) FROM bitda_ticker WHERE symbol IN ('BTCUSDT', 'ETHUSDT')")
        result = cursor.fetchone()
        
        print(f"✅ 数据库连接成功")
        print(f"📊 bitda_ticker表记录数: {result[0]}")
        
        # 检查depth_matches表
        cursor.execute("SELECT COUNT(*) FROM depth_matches WHERE symbol = 'ETHUSDT'")
        result = cursor.fetchone()
        print(f"📊 ETHUSDT延时匹配记录数: {result[0]}")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_latency_analyzer():
    """测试延时分析器"""
    print("\n🚀 测试延时分析器...")
    try:
        from analyzer.latency_analyzer import LatencyAnalyzer
        
        analyzer = LatencyAnalyzer()
        result = analyzer.analyze_ethusdt_latency(hours=1)
        
        print(f"✅ 延时分析完成")
        print(f"📊 匹配记录数: {result.get('match_count', 0)}")
        print(f"⏱️  平均延时: {result.get('avg_latency', 0):.2f}ms")
        print(f"⏱️  最小延时: {result.get('min_latency', 0)}ms")
        print(f"⏱️  最大延时: {result.get('max_latency', 0)}ms")
        print(f"⏱️  中位数延时: {result.get('median_latency', 0):.2f}ms")
        
        return True
        
    except Exception as e:
        print(f"❌ 延时分析器测试失败: {e}")
        return False

def test_depth_analyzer():
    """测试深度分析器"""
    print("\n📊 测试深度分析器...")
    try:
        from analyzer.depth_analyzer import DepthAnalyzer
        
        analyzer = DepthAnalyzer()
        result = analyzer.analyze_depth_comparison(hours=1)
        
        print(f"✅ 深度分析完成")
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            if symbol in result.get('results', {}):
                data = result['results'][symbol]
                print(f"\n{symbol}:")
                print(f"  样本数: {data.get('sample_count', 0)}")
                print(f"  买一卖一量比值: {data.get('bid_ask_ratio', {}).get('latest', 0):.2f}")
                print(f"  买一量比值: {data.get('bid_ratio', {}).get('latest', 0):.2f}")
                print(f"  卖一量比值: {data.get('ask_ratio', {}).get('latest', 0):.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 深度分析器测试失败: {e}")
        return False

def test_price_analyzer():
    """测试价格分析器"""
    print("\n💰 测试价格分析器...")
    try:
        from analyzer.price_analyzer import PriceAnalyzer
        
        analyzer = PriceAnalyzer()
        result = analyzer.analyze_mark_price_deviation(hours=1)
        
        print(f"✅ 价格分析完成")
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            if symbol in result.get('results', {}):
                data = result['results'][symbol]
                print(f"\n{symbol}:")
                print(f"  样本数: {data.get('sample_count', 0)}")
                print(f"  最大偏差: {data.get('max_deviation', 0):.4f}%")
                print(f"  最小偏差: {data.get('min_deviation', 0):.4f}%")
                print(f"  中位数偏差: {data.get('median_deviation', 0):.4f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 价格分析器测试失败: {e}")
        return False

def test_funding_analyzer():
    """测试资金费率分析器"""
    print("\n📋 测试资金费率分析器...")
    try:
        from analyzer.funding_analyzer import FundingAnalyzer
        
        analyzer = FundingAnalyzer()
        result = analyzer.get_latest_funding_rates()
        
        print(f"✅ 资金费率分析完成")
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            if symbol in result:
                data = result[symbol]
                print(f"\n{symbol}:")
                print(f"  当前费率: {data.get('current_rate', 0)*100:.5f}%")
                print(f"  下期费率: {data.get('next_rate', 0)*100:.5f}%")
                print(f"  预测费率: {data.get('predict_rate', 0)*100:.5f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 资金费率分析器测试失败: {e}")
        return False

def test_data_cleaner():
    """测试数据清理器"""
    print("\n🧹 测试数据清理器...")
    try:
        from clean_test_data import DataCleaner
        
        cleaner = DataCleaner()
        stats = cleaner.get_table_stats()
        
        print(f"✅ 数据清理器测试完成")
        print(f"📊 表统计信息:")
        
        for table, info in stats.items():
            if info['total_records'] > 0:
                print(f"  {table}: {info['total_records']} 条记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据清理器测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 开始测试分析功能...")
    print("=" * 60)
    
    tests = [
        ("数据库连接", test_database_connection),
        ("延时分析器", test_latency_analyzer),
        ("深度分析器", test_depth_analyzer),
        ("价格分析器", test_price_analyzer),
        ("资金费率分析器", test_funding_analyzer),
        ("数据清理器", test_data_cleaner)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = "✅ 通过" if result else "❌ 失败"
        except Exception as e:
            results[test_name] = f"❌ 异常: {e}"
    
    # 打印测试结果
    print("\n" + "=" * 60)
    print("📋 测试结果汇总")
    print("=" * 60)
    
    for test_name, result in results.items():
        print(f"{test_name:15} | {result}")
    
    # 统计
    passed = sum(1 for r in results.values() if "✅" in r)
    total = len(results)
    
    print(f"\n📊 测试统计: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
