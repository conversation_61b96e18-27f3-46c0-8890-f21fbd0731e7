#!/usr/bin/env python3
"""
验证动态仪表板是否真正在更新
监控数据变化并提供实时反馈
"""

import mysql.connector
import time
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_dashboard_data():
    """获取仪表板显示的数据 - 模拟仪表板的SQL查询"""
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'Linuxtest',
        'database': 'depth_db'
    }
    
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        data = {}
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            # 执行与仪表板相同的SQL查询
            cursor.execute("""
                SELECT 
                    ROUND(b.bid_qty_1, 2) as bitda_bid1,
                    ROUND(bn.bid_qty_1, 2) as binance_bid1,
                    ROUND(b.bid_qty_1 / bn.bid_qty_1, 2) as bid1_ratio,
                    ROUND(b.ask_qty_1, 2) as bitda_ask1,
                    ROUND(bn.ask_qty_1, 2) as binance_ask1,
                    ROUND(b.ask_qty_1 / bn.ask_qty_1, 2) as ask1_ratio,
                    ROUND(b.bid_qty_1 + b.ask_qty_1, 2) as bitda_total,
                    ROUND(bn.bid_qty_1 + bn.ask_qty_1, 2) as binance_total,
                    ROUND((b.bid_qty_1 + b.ask_qty_1) / (bn.bid_qty_1 + bn.ask_qty_1), 2) as total_ratio,
                    b.timestamp as bitda_time,
                    bn.event_time as binance_time
                FROM bitda_depth b
                JOIN (
                    SELECT timestamp as bt FROM bitda_depth 
                    WHERE symbol = %s AND bid_price_1 IS NOT NULL 
                    ORDER BY timestamp DESC LIMIT 1
                ) latest ON b.timestamp = latest.bt
                JOIN binance_depth_5 bn ON bn.symbol = %s 
                    AND bn.event_time <= b.timestamp
                    AND bn.bid_price_1 IS NOT NULL
                WHERE b.symbol = %s
                ORDER BY bn.event_time DESC
                LIMIT 1
            """, (symbol, symbol, symbol))
            
            result = cursor.fetchone()
            if result:
                data[symbol] = {
                    'bitda_bid1': result[0],
                    'binance_bid1': result[1],
                    'bid1_ratio': result[2],
                    'bitda_ask1': result[3],
                    'binance_ask1': result[4],
                    'ask1_ratio': result[5],
                    'bitda_total': result[6],
                    'binance_total': result[7],
                    'total_ratio': result[8],
                    'bitda_time': datetime.fromtimestamp(result[9] / 1000),
                    'binance_time': datetime.fromtimestamp(result[10] / 1000)
                }
        
        # 获取最新数据时间
        cursor.execute("""
            SELECT FROM_UNIXTIME(MAX(timestamp)/1000, '%Y-%m-%d %H:%i:%s') as latest_time
            FROM bitda_depth 
            WHERE symbol IN ('BTCUSDT', 'ETHUSDT')
        """)
        
        time_result = cursor.fetchone()
        if time_result:
            data['latest_time'] = time_result[0]
        
        cursor.close()
        connection.close()
        
        return data
        
    except Exception as e:
        print(f"❌ 获取数据失败: {e}")
        return None

def monitor_dashboard_updates():
    """监控仪表板数据更新"""
    print("🔍 监控动态仪表板数据更新")
    print("=" * 60)
    print("🎯 验证目标:")
    print("   ✅ 确认数据每10秒更新")
    print("   ✅ 验证数值确实在变化")
    print("   ✅ 检查时间戳更新")
    print()
    
    previous_data = None
    update_count = 0
    change_count = 0
    
    try:
        while True:
            update_count += 1
            current_time = datetime.now()
            
            print(f"🔄 第{update_count}次检查 - {current_time.strftime('%H:%M:%S')}")
            
            # 获取当前数据
            current_data = get_dashboard_data()
            
            if not current_data:
                print("   ❌ 无法获取数据")
                time.sleep(10)
                continue
            
            # 显示当前数据
            print(f"   📊 最新数据时间: {current_data.get('latest_time', 'N/A')}")
            
            for symbol in ['BTCUSDT', 'ETHUSDT']:
                if symbol in current_data:
                    data = current_data[symbol]
                    print(f"   📈 {symbol}:")
                    print(f"      买一量深度比: {data['bid1_ratio']}")
                    print(f"      卖一量深度比: {data['ask1_ratio']}")
                    print(f"      总量深度比: {data['total_ratio']}")
                    print(f"      Bitda时间: {data['bitda_time'].strftime('%H:%M:%S.%f')[:-3]}")
            
            # 对比变化
            if previous_data:
                has_changes = False
                
                for symbol in ['BTCUSDT', 'ETHUSDT']:
                    if symbol in current_data and symbol in previous_data:
                        curr = current_data[symbol]
                        prev = previous_data[symbol]
                        
                        # 检查时间变化
                        time_changed = curr['bitda_time'] != prev['bitda_time']
                        
                        # 检查数值变化
                        bid_changed = abs(curr['bid1_ratio'] - prev['bid1_ratio']) > 0.01
                        ask_changed = abs(curr['ask1_ratio'] - prev['ask1_ratio']) > 0.01
                        total_changed = abs(curr['total_ratio'] - prev['total_ratio']) > 0.01
                        
                        if time_changed or bid_changed or ask_changed or total_changed:
                            has_changes = True
                            print(f"   ✅ {symbol} 数据发生变化:")
                            if time_changed:
                                print(f"      ⏰ 时间更新: {prev['bitda_time'].strftime('%H:%M:%S.%f')[:-3]} → {curr['bitda_time'].strftime('%H:%M:%S.%f')[:-3]}")
                            if bid_changed:
                                print(f"      📈 买一量比: {prev['bid1_ratio']} → {curr['bid1_ratio']}")
                            if ask_changed:
                                print(f"      📉 卖一量比: {prev['ask1_ratio']} → {curr['ask1_ratio']}")
                            if total_changed:
                                print(f"      📊 总量比: {prev['total_ratio']} → {curr['total_ratio']}")
                
                if has_changes:
                    change_count += 1
                    print(f"   🎉 检测到数据变化！(第{change_count}次)")
                else:
                    print(f"   ⚠️ 数据未变化")
                
                # 统计信息
                change_rate = (change_count / (update_count - 1)) * 100 if update_count > 1 else 0
                print(f"   📊 变化率: {change_rate:.1f}% ({change_count}/{update_count-1})")
            
            previous_data = current_data
            
            print(f"   ⏰ 等待10秒后进行下次检查...")
            print()
            time.sleep(10)
            
    except KeyboardInterrupt:
        print(f"\n🛑 监控停止")
        print(f"📊 总检查次数: {update_count}")
        print(f"🔄 检测到变化: {change_count}次")
        if update_count > 1:
            change_rate = (change_count / (update_count - 1)) * 100
            print(f"📈 变化率: {change_rate:.1f}%")
        
        if change_count > 0:
            print(f"✅ 仪表板数据确实在更新！")
        else:
            print(f"⚠️ 未检测到数据变化")

def test_single_update():
    """测试单次数据更新"""
    print("🔍 测试单次数据更新")
    print("=" * 40)
    
    print("📊 获取第一次数据...")
    data1 = get_dashboard_data()
    if data1:
        print(f"   最新时间: {data1.get('latest_time', 'N/A')}")
        if 'BTCUSDT' in data1:
            print(f"   BTCUSDT总量比: {data1['BTCUSDT']['total_ratio']}")
    
    print(f"\n⏰ 等待15秒...")
    time.sleep(15)
    
    print("📊 获取第二次数据...")
    data2 = get_dashboard_data()
    if data2:
        print(f"   最新时间: {data2.get('latest_time', 'N/A')}")
        if 'BTCUSDT' in data2:
            print(f"   BTCUSDT总量比: {data2['BTCUSDT']['total_ratio']}")
    
    # 对比
    if data1 and data2:
        time_changed = data1.get('latest_time') != data2.get('latest_time')
        
        btc_changed = False
        if 'BTCUSDT' in data1 and 'BTCUSDT' in data2:
            btc_changed = abs(data1['BTCUSDT']['total_ratio'] - data2['BTCUSDT']['total_ratio']) > 0.01
        
        print(f"\n🔍 变化检测:")
        print(f"   时间变化: {'✅是' if time_changed else '❌否'}")
        print(f"   数值变化: {'✅是' if btc_changed else '❌否'}")
        
        if time_changed or btc_changed:
            print(f"   🎉 数据确实在更新！")
            return True
        else:
            print(f"   ⚠️ 数据没有变化")
            return False
    
    return False

def main():
    """主函数"""
    print("🔍 动态仪表板验证器")
    print("=" * 60)
    print("🌐 仪表板地址: http://localhost:3000/d/e1afad38-af94-4c2d-a082-3e0231e1118f/7b707bf")
    print("⏰ 刷新频率: 每10秒")
    print()
    
    print("选择验证方式:")
    print("1. 快速测试 (15秒)")
    print("2. 持续监控 (按Ctrl+C停止)")
    print()
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == '1':
        print(f"\n🚀 开始快速测试...")
        success = test_single_update()
        if success:
            print(f"\n✅ 验证成功！仪表板数据正在更新")
            print(f"💡 您可以打开浏览器观察实时变化")
        else:
            print(f"\n⚠️ 验证失败，数据可能没有更新")
    elif choice == '2':
        print(f"\n🚀 开始持续监控...")
        monitor_dashboard_updates()
    else:
        print(f"\n💡 请打开浏览器访问仪表板地址观察数据变化")

if __name__ == "__main__":
    main()
