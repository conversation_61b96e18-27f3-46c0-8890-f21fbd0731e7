#!/usr/bin/env python3
"""
强制保存缓存数据的脚本
"""

import sys
import os
import signal
import time
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from collector import DataCollector
from utils.logging import setup_logger

logger = setup_logger(__name__)

def find_collector_process():
    """查找正在运行的数据采集进程"""
    import psutil
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = proc.info['cmdline']
            if cmdline and any('main.py' in cmd for cmd in cmdline):
                return proc.info['pid']
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    return None

def send_flush_signal():
    """发送信号给主进程，要求强制保存数据"""
    pid = find_collector_process()
    if pid:
        logger.info(f"找到数据采集进程 PID: {pid}")
        try:
            # 发送SIGUSR1信号
            os.kill(pid, signal.SIGUSR1)
            logger.info("已发送强制保存信号")
            return True
        except Exception as e:
            logger.error(f"发送信号失败: {e}")
            return False
    else:
        logger.warning("未找到数据采集进程")
        return False

def check_cache_status():
    """检查缓存状态（需要修改main.py来支持）"""
    logger.info("检查缓存状态...")
    
    # 这里我们可以通过查看最近的数据来估算缓存状态
    import mysql.connector
    
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'Linuxtest',
        'database': 'depth_db'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        # 检查最近1分钟的数据
        tables = ['bitda_depth', 'binance_depth_5', 'binance_bookticker']
        
        for table in tables:
            cursor.execute(f"""
                SELECT COUNT(*) FROM {table} 
                WHERE created_at >= NOW() - INTERVAL 1 MINUTE
            """)
            count = cursor.fetchone()[0]
            logger.info(f"{table} (最近1分钟): {count} 条")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        logger.error(f"检查缓存状态失败: {e}")

def main():
    """主函数"""
    print("🔄 强制保存缓存数据工具")
    print("=" * 50)
    
    # 1. 检查当前缓存状态
    check_cache_status()
    
    # 2. 发送强制保存信号
    if send_flush_signal():
        print("✅ 已发送强制保存信号")
        print("等待5秒后检查结果...")
        time.sleep(5)
        
        # 3. 再次检查状态
        check_cache_status()
    else:
        print("❌ 无法发送强制保存信号")
        print("建议:")
        print("1. 检查数据采集程序是否正在运行")
        print("2. 手动重启数据采集程序")
        print("3. 降低BATCH_SIZE配置")

if __name__ == "__main__":
    main()
