#!/usr/bin/env python3
"""
系统服务设置脚本
将数据收集器和指标导出器设置为系统服务
"""

import subprocess
import sys
import os
import time
from pathlib import Path

class ServiceManager:
    """系统服务管理器"""
    
    def __init__(self):
        self.services = [
            {
                'name': 'crypto-collector',
                'file': 'crypto-collector.service',
                'description': '加密货币数据收集器'
            },
            {
                'name': 'crypto-exporter', 
                'file': 'crypto-exporter.service',
                'description': 'Prometheus指标导出器'
            }
        ]
        
    def run_command(self, command, description=""):
        """运行命令并显示结果"""
        if description:
            print(f"🔧 {description}...")
        
        try:
            result = subprocess.run(
                command, 
                shell=True, 
                capture_output=True, 
                text=True, 
                check=True
            )
            
            if description:
                print(f"✅ {description}完成")
            
            return True, result.stdout
            
        except subprocess.CalledProcessError as e:
            print(f"❌ {description}失败: {e.stderr}")
            return False, e.stderr
    
    def check_permissions(self):
        """检查权限"""
        print("🔐 检查权限...")
        
        # 检查是否可以使用sudo
        success, _ = self.run_command("sudo -n true")
        if not success:
            print("❌ 需要sudo权限来安装系统服务")
            print("请运行: sudo python setup_services.py")
            return False
        
        print("✅ 权限检查通过")
        return True
    
    def install_services(self):
        """安装系统服务"""
        print("📦 安装系统服务...")
        
        for service in self.services:
            print(f"\n🔧 安装 {service['description']}...")
            
            # 检查服务文件是否存在
            if not Path(service['file']).exists():
                print(f"❌ 服务文件不存在: {service['file']}")
                continue
            
            # 复制服务文件到系统目录
            success, _ = self.run_command(
                f"sudo cp {service['file']} /etc/systemd/system/",
                f"复制 {service['file']}"
            )
            
            if not success:
                continue
            
            # 重新加载systemd
            self.run_command("sudo systemctl daemon-reload", "重新加载systemd")
            
            # 启用服务
            success, _ = self.run_command(
                f"sudo systemctl enable {service['name']}",
                f"启用 {service['name']} 服务"
            )
            
            if success:
                print(f"✅ {service['description']} 安装成功")
            else:
                print(f"❌ {service['description']} 安装失败")
    
    def start_services(self):
        """启动服务"""
        print("\n🚀 启动服务...")
        
        for service in self.services:
            print(f"\n🔧 启动 {service['description']}...")
            
            success, _ = self.run_command(
                f"sudo systemctl start {service['name']}",
                f"启动 {service['name']}"
            )
            
            if success:
                print(f"✅ {service['description']} 启动成功")
                
                # 等待一下再检查状态
                time.sleep(2)
                
                # 检查服务状态
                success, output = self.run_command(
                    f"sudo systemctl is-active {service['name']}",
                    ""
                )
                
                if success and "active" in output:
                    print(f"✅ {service['description']} 运行正常")
                else:
                    print(f"⚠️  {service['description']} 状态异常")
            else:
                print(f"❌ {service['description']} 启动失败")
    
    def show_status(self):
        """显示服务状态"""
        print("\n📊 服务状态")
        print("="*50)
        
        for service in self.services:
            print(f"\n🔍 {service['description']}:")
            
            # 检查服务状态
            success, output = self.run_command(
                f"sudo systemctl status {service['name']} --no-pager -l",
                ""
            )
            
            if success:
                lines = output.split('\n')
                for line in lines[:5]:  # 显示前5行
                    if line.strip():
                        print(f"   {line}")
            else:
                print(f"   ❌ 无法获取状态")
    
    def stop_current_processes(self):
        """停止当前运行的进程"""
        print("⏹️  停止当前运行的进程...")
        
        # 查找并停止当前的数据收集进程
        success, output = self.run_command(
            "ps aux | grep 'python main.py' | grep -v grep | awk '{print $2}'",
            ""
        )
        
        if success and output.strip():
            pids = output.strip().split('\n')
            for pid in pids:
                if pid.strip():
                    self.run_command(f"kill {pid.strip()}", f"停止进程 {pid.strip()}")
        
        # 查找并停止当前的指标导出进程
        success, output = self.run_command(
            "ps aux | grep 'prometheus_exporter.py' | grep -v grep | awk '{print $2}'",
            ""
        )
        
        if success and output.strip():
            pids = output.strip().split('\n')
            for pid in pids:
                if pid.strip():
                    self.run_command(f"kill {pid.strip()}", f"停止进程 {pid.strip()}")
        
        print("✅ 当前进程已停止")
    
    def setup_all(self):
        """完整设置流程"""
        print("🚀 加密货币数据收集系统服务设置")
        print("="*60)
        
        # 检查权限
        if not self.check_permissions():
            return False
        
        # 停止当前进程
        self.stop_current_processes()
        
        # 安装服务
        self.install_services()
        
        # 启动服务
        self.start_services()
        
        # 显示状态
        self.show_status()
        
        print("\n🎉 服务设置完成！")
        print("\n💡 服务管理命令:")
        print("   查看状态: sudo systemctl status crypto-collector crypto-exporter")
        print("   启动服务: sudo systemctl start crypto-collector crypto-exporter")
        print("   停止服务: sudo systemctl stop crypto-collector crypto-exporter")
        print("   重启服务: sudo systemctl restart crypto-collector crypto-exporter")
        print("   查看日志: sudo journalctl -u crypto-collector -f")
        print("   查看日志: sudo journalctl -u crypto-exporter -f")
        
        return True

def main():
    """主函数"""
    manager = ServiceManager()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'install':
            if manager.check_permissions():
                manager.install_services()
        elif command == 'start':
            if manager.check_permissions():
                manager.start_services()
        elif command == 'status':
            manager.show_status()
        elif command == 'stop':
            manager.stop_current_processes()
        else:
            print("❌ 未知命令")
            print("用法: python setup_services.py [install|start|status|stop]")
            print("或直接运行进行完整设置")
    else:
        manager.setup_all()

if __name__ == "__main__":
    main()
