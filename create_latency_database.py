#!/usr/bin/env python3
"""
创建ETHUSDT延时分析数据库
"""

import mysql.connector
from mysql.connector import Error
import os
from datetime import datetime

class LatencyDatabaseCreator:
    """延时分析数据库创建器"""
    
    def __init__(self):
        self.host = 'localhost'
        self.user = 'root'
        self.password = 'Linuxtest'
        self.source_db = 'depth_db'
        self.target_db = 'ethusdt_latency_db'
        
    def create_database(self):
        """创建延时分析数据库"""
        try:
            # 连接MySQL服务器
            connection = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password
            )
            
            cursor = connection.cursor()
            
            # 删除旧数据库（如果存在）
            print("🗑️  删除旧数据库（如果存在）...")
            cursor.execute(f"DROP DATABASE IF EXISTS {self.target_db}")
            
            # 创建新数据库
            print(f"📊 创建数据库: {self.target_db}")
            cursor.execute(f"CREATE DATABASE {self.target_db} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            
            # 选择数据库
            cursor.execute(f"USE {self.target_db}")
            
            # 创建实时延时匹配表
            print("📋 创建实时延时匹配表...")
            realtime_table_sql = """
            CREATE TABLE ethusdt_latency_matches (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                bitda_timestamp BIGINT NOT NULL COMMENT 'Bitda时间戳(毫秒)',
                binance_timestamp BIGINT NOT NULL COMMENT 'Binance时间戳(毫秒)',
                latency_ms INT NOT NULL COMMENT '延时(毫秒)',
                match_type ENUM('bid', 'ask') NOT NULL COMMENT '匹配类型',
                bitda_price DECIMAL(10,2) NOT NULL COMMENT 'Bitda价格',
                binance_price DECIMAL(10,2) NOT NULL COMMENT 'Binance价格',
                bitda_qty DECIMAL(15,3) NOT NULL COMMENT 'Bitda数量',
                binance_qty DECIMAL(15,3) NOT NULL COMMENT 'Binance数量',
                price_spread DECIMAL(10,2) DEFAULT 0 COMMENT '价差',
                match_quality DECIMAL(5,4) DEFAULT 1.0000 COMMENT '匹配质量',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_timestamps (bitda_timestamp, binance_timestamp),
                INDEX idx_latency (latency_ms),
                INDEX idx_match_type (match_type),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            COMMENT='ETHUSDT实时延时匹配数据表'
            """
            cursor.execute(realtime_table_sql)
            
            # 创建分钟级统计表
            print("📈 创建分钟级统计表...")
            minute_stats_sql = """
            CREATE TABLE ethusdt_latency_stats_minute (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                minute_timestamp DATETIME NOT NULL COMMENT '分钟时间戳',
                total_matches INT NOT NULL DEFAULT 0 COMMENT '总匹配数',
                bid_matches INT NOT NULL DEFAULT 0 COMMENT '买一匹配数',
                ask_matches INT NOT NULL DEFAULT 0 COMMENT '卖一匹配数',
                avg_latency DECIMAL(8,2) NOT NULL COMMENT '平均延时(ms)',
                min_latency INT NOT NULL COMMENT '最小延时(ms)',
                max_latency INT NOT NULL COMMENT '最大延时(ms)',
                median_latency INT NOT NULL COMMENT '中位数延时(ms)',
                p95_latency INT NOT NULL COMMENT '95分位延时(ms)',
                std_latency DECIMAL(8,2) NOT NULL COMMENT '延时标准差',
                avg_price_spread DECIMAL(8,4) DEFAULT 0 COMMENT '平均价差',
                data_quality DECIMAL(5,4) DEFAULT 1.0000 COMMENT '数据质量评分',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE KEY uk_minute (minute_timestamp),
                INDEX idx_minute_time (minute_timestamp),
                INDEX idx_avg_latency (avg_latency)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            COMMENT='ETHUSDT分钟级延时统计表'
            """
            cursor.execute(minute_stats_sql)
            
            # 创建小时级统计表
            print("📊 创建小时级统计表...")
            hour_stats_sql = """
            CREATE TABLE ethusdt_latency_stats_hour (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                hour_timestamp DATETIME NOT NULL COMMENT '小时时间戳',
                total_matches INT NOT NULL DEFAULT 0 COMMENT '总匹配数',
                bid_matches INT NOT NULL DEFAULT 0 COMMENT '买一匹配数',
                ask_matches INT NOT NULL DEFAULT 0 COMMENT '卖一匹配数',
                avg_latency DECIMAL(8,2) NOT NULL COMMENT '平均延时(ms)',
                min_latency INT NOT NULL COMMENT '最小延时(ms)',
                max_latency INT NOT NULL COMMENT '最大延时(ms)',
                median_latency INT NOT NULL COMMENT '中位数延时(ms)',
                p95_latency INT NOT NULL COMMENT '95分位延时(ms)',
                std_latency DECIMAL(8,2) NOT NULL COMMENT '延时标准差',
                avg_price_spread DECIMAL(8,4) DEFAULT 0 COMMENT '平均价差',
                data_quality DECIMAL(5,4) DEFAULT 1.0000 COMMENT '数据质量评分',
                latency_distribution JSON COMMENT '延时分布统计',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE KEY uk_hour (hour_timestamp),
                INDEX idx_hour_time (hour_timestamp),
                INDEX idx_avg_latency (avg_latency)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            COMMENT='ETHUSDT小时级延时统计表'
            """
            cursor.execute(hour_stats_sql)
            
            # 创建数据处理日志表
            print("📝 创建数据处理日志表...")
            log_table_sql = """
            CREATE TABLE latency_processing_log (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                process_type ENUM('realtime', 'minute_stats', 'hour_stats') NOT NULL,
                start_time DATETIME NOT NULL COMMENT '处理开始时间',
                end_time DATETIME COMMENT '处理结束时间',
                processed_records INT DEFAULT 0 COMMENT '处理记录数',
                matched_records INT DEFAULT 0 COMMENT '匹配记录数',
                error_count INT DEFAULT 0 COMMENT '错误数量',
                status ENUM('running', 'completed', 'failed') DEFAULT 'running',
                error_message TEXT COMMENT '错误信息',
                processing_duration_ms INT COMMENT '处理耗时(毫秒)',
                data_range_start DATETIME COMMENT '数据范围开始',
                data_range_end DATETIME COMMENT '数据范围结束',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_process_type (process_type),
                INDEX idx_status (status),
                INDEX idx_start_time (start_time)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            COMMENT='延时数据处理日志表'
            """
            cursor.execute(log_table_sql)
            
            # 创建延时分布统计表
            print("📈 创建延时分布统计表...")
            distribution_table_sql = """
            CREATE TABLE ethusdt_latency_distribution (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                time_period ENUM('hour', 'day') NOT NULL,
                period_timestamp DATETIME NOT NULL,
                latency_range VARCHAR(20) NOT NULL COMMENT '延时范围(如: 0-50ms)',
                range_min INT NOT NULL COMMENT '范围最小值(ms)',
                range_max INT NOT NULL COMMENT '范围最大值(ms)',
                match_count INT NOT NULL DEFAULT 0 COMMENT '匹配数量',
                percentage DECIMAL(5,2) NOT NULL DEFAULT 0 COMMENT '占比(%)',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE KEY uk_period_range (time_period, period_timestamp, latency_range),
                INDEX idx_period_time (time_period, period_timestamp),
                INDEX idx_range (range_min, range_max)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            COMMENT='ETHUSDT延时分布统计表'
            """
            cursor.execute(distribution_table_sql)
            
            # 创建实时状态表
            print("⚡ 创建实时状态表...")
            realtime_status_sql = """
            CREATE TABLE ethusdt_realtime_status (
                id INT PRIMARY KEY DEFAULT 1,
                current_latency_ms INT COMMENT '当前延时(ms)',
                avg_latency_1h DECIMAL(8,2) COMMENT '1小时平均延时',
                max_latency_1h INT COMMENT '1小时最大延时',
                min_latency_1h INT COMMENT '1小时最小延时',
                total_matches_1h INT COMMENT '1小时总匹配数',
                last_match_time DATETIME COMMENT '最后匹配时间',
                last_bitda_price DECIMAL(10,2) COMMENT '最后Bitda价格',
                last_binance_price DECIMAL(10,2) COMMENT '最后Binance价格',
                match_rate_per_minute DECIMAL(8,2) COMMENT '每分钟匹配率',
                data_quality_score DECIMAL(5,4) DEFAULT 1.0000 COMMENT '数据质量评分',
                system_status ENUM('normal', 'warning', 'error') DEFAULT 'normal',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                CHECK (id = 1)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            COMMENT='ETHUSDT实时状态表'
            """
            cursor.execute(realtime_status_sql)
            
            # 插入初始状态记录
            cursor.execute("""
                INSERT INTO ethusdt_realtime_status (id, system_status) 
                VALUES (1, 'normal')
            """)
            
            print("✅ 数据库和表结构创建完成！")
            
            # 显示创建的表
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            print(f"\n📋 创建的表列表 ({len(tables)}个):")
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
                count = cursor.fetchone()[0]
                print(f"  - {table[0]} ({count} 条记录)")
            
            cursor.close()
            connection.close()
            
            return True
            
        except Error as e:
            print(f"❌ 数据库创建失败: {e}")
            return False
    
    def verify_database(self):
        """验证数据库创建"""
        try:
            connection = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.target_db
            )
            
            cursor = connection.cursor()
            
            print(f"\n🔍 验证数据库: {self.target_db}")
            print("=" * 50)
            
            # 检查表结构
            tables_to_check = [
                'ethusdt_latency_matches',
                'ethusdt_latency_stats_minute', 
                'ethusdt_latency_stats_hour',
                'latency_processing_log',
                'ethusdt_latency_distribution',
                'ethusdt_realtime_status'
            ]
            
            for table in tables_to_check:
                cursor.execute(f"DESCRIBE {table}")
                columns = cursor.fetchall()
                print(f"✅ {table}: {len(columns)} 个字段")
            
            # 检查索引
            cursor.execute("""
                SELECT TABLE_NAME, INDEX_NAME, COLUMN_NAME 
                FROM INFORMATION_SCHEMA.STATISTICS 
                WHERE TABLE_SCHEMA = %s 
                ORDER BY TABLE_NAME, INDEX_NAME
            """, (self.target_db,))
            
            indexes = cursor.fetchall()
            print(f"✅ 索引总数: {len(indexes)}")
            
            cursor.close()
            connection.close()
            
            return True
            
        except Error as e:
            print(f"❌ 数据库验证失败: {e}")
            return False

def main():
    """主函数"""
    print("🚀 ETHUSDT延时分析数据库创建器")
    print("=" * 60)
    
    creator = LatencyDatabaseCreator()
    
    # 创建数据库
    if creator.create_database():
        print("\n🎉 数据库创建成功！")
        
        # 验证数据库
        if creator.verify_database():
            print("\n✅ 数据库验证通过！")
            print(f"📊 数据库名称: {creator.target_db}")
            print("📋 包含以下核心表:")
            print("  - ethusdt_latency_matches (实时延时匹配)")
            print("  - ethusdt_latency_stats_minute (分钟级统计)")
            print("  - ethusdt_latency_stats_hour (小时级统计)")
            print("  - ethusdt_realtime_status (实时状态)")
            print("  - ethusdt_latency_distribution (延时分布)")
            print("  - latency_processing_log (处理日志)")
            
            print(f"\n🔗 连接信息:")
            print(f"  主机: {creator.host}")
            print(f"  用户: {creator.user}")
            print(f"  数据库: {creator.target_db}")
            
        else:
            print("\n❌ 数据库验证失败")
    else:
        print("\n❌ 数据库创建失败")

if __name__ == "__main__":
    main()
