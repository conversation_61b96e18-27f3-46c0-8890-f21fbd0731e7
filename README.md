# 加密货币数据收集系统 - 程序结构与数据库说明

## 📁 项目结构

```
WS_DATA_ALL/
├── main.py                 # 主程序入口
├── start.py               # 启动脚本（包含系统测试）
├── collector.py           # 数据收集模块
├── storage.py             # 数据存储模块
├── analyzer_main.py       # 数据分析器主程序
├── test_system.py         # 系统测试脚本
├── update_precision.py    # 数据库精度调整脚本
├── analyzer/              # 数据分析模块
│   ├── __init__.py
│   ├── latency_analyzer.py    # 延时分析器
│   ├── kline_analyzer.py      # K线分析器
│   ├── depth_analyzer.py      # 深度分析器
│   ├── price_analyzer.py      # 价格分析器
│   └── funding_analyzer.py    # 资金费率分析器
├── visualizer/            # 数据可视化模块
│   ├── __init__.py
│   ├── dashboard.py           # 主仪表板
│   └── charts.py             # 图表生成器
├── utils/                 # 工具模块
│   ├── __init__.py
│   ├── config.py          # 配置管理
│   ├── logging.py         # 日志管理
│   └── db.py              # 数据库连接管理
├── requirements.txt       # 依赖包列表
├── README.md             # 项目说明文档
└── crypto_collector.log  # 运行日志文件
```

## 🏗️ 系统架构图

```mermaid
graph TB
    A[main.py 主程序] --> B[collector.py 数据收集器]
    A --> C[storage.py 数据存储器]

    B --> D[Bitda WebSocket]
    B --> E[Binance WebSocket]

    D --> F[update.kline K线数据]
    D --> G[update.trade 成交数据]
    D --> H[update.depth 深度数据]
    D --> I[update.state 行情数据]

    E --> J[BookTicker 数据]
    E --> K[5档深度数据]

    F --> C
    G --> C
    H --> C
    I --> C
    J --> C
    K --> C

    C --> L[(MySQL 数据库)]

    M[utils/config.py] --> A
    N[utils/logging.py] --> A
    O[utils/db.py] --> C

    P[start.py] --> Q[test_system.py]
    P --> A
```

## 🔧 核心模块说明

### 1. **main.py** - 主程序
- **功能**: 协调各模块运行，信号处理，优雅停止
- **主要类**: `CryptoDataCollectorApp`
- **启动方式**: `python main.py` 或 `python start.py`

### 2. **collector.py** - 数据收集器
- **功能**: WebSocket 连接管理，数据接收和处理
- **主要类**: `DataCollector`
- **支持的数据源**:
  - Bitda: K线、成交、深度、行情
  - Binance: BookTicker、5档深度

### 3. **storage.py** - 数据存储器
- **功能**: 数据库表管理，批量数据保存，数据清理
- **主要类**: `DataStorage`
- **特性**: 自动建表、批量插入、旧数据清理

### 4. **utils/** - 工具模块
- **config.py**: 系统配置参数
- **logging.py**: 日志系统设置
- **db.py**: 数据库连接池管理

## 🗄️ 数据库结构

### 数据库连接信息
```
主机: ************
端口: 3306
用户名: liuchuan
密码: Linuxtest
数据库: depth_db
```

### 📊 数据分析目标

#### 1. **延时监控** 🚀
- **目标**: 展示ETHUSDT价格相同的延时和统计成功次数
- **数据源**: `bitda_ticker`, `binance_bookticker`
- **分析**: 价格匹配延时统计

#### 2. **K线分析（0插针）** 📈
- **目标**: 分析Bitda 1分钟K线连续相同情况
- **数据源**: `bitda_kline`
- **检测**: 连续两根K线的high、low、last、open完全相同

#### 3. **深度对比** 📊
- **目标**: 比较Bitda与Binance深度数据
- **数据源**: `bitda_depth`, `binance_depth_5`
- **指标**: 买一卖一数量之和的比值

#### 4. **标记价格分析** 💰
- **目标**: 标记价格与最新价格差值统计
- **数据源**: `bitda_ticker`
- **时间范围**: 最近1小时数据

#### 5. **资金费率展示** 📋
- **目标**: 展示BTC/ETH资金费率差值
- **数据源**: `bitda_ticker` (funding_rate_last, funding_rate_next, funding_rate_predict)
- **频率**: 一天3次不同时段

### 数据表结构

#### 1. **bitda_kline** - Bitda K线数据表
| 字段名 | 数据类型 | 说明 | 示例 |
|--------|----------|------|------|
| `id` | BIGINT | 主键，自增 | 1 |
| `symbol` | VARCHAR(20) | 交易对 | BTCUSDT, ETHUSDT |
| `timestamp` | BIGINT | 时间戳(秒) | 1699943820 |
| `open_price` | DECIMAL(15,2) | 开盘价 | 108231.60 |
| `high_price` | DECIMAL(15,2) | 最高价 | 108250.00 |
| `low_price` | DECIMAL(15,2) | 最低价 | 108200.00 |
| `close_price` | DECIMAL(15,2) | 收盘价 | 108231.60 |
| `volume` | DECIMAL(20,4) | 成交数量 | 156.0771 |
| `amount` | DECIMAL(25,2) | 成交金额 | 17327670.11 |
| `period` | VARCHAR(10) | 周期 | 1min |
| `created_at` | TIMESTAMP | 创建时间 | 2025-05-29 10:55:32 |

**索引**:
- `idx_symbol_time (symbol, timestamp)`
- `idx_created_at (created_at)`

#### 2. **bitda_trades** - Bitda成交数据表
| 字段名 | 数据类型 | 说明 | 示例 |
|--------|----------|------|------|
| `id` | BIGINT | 主键，自增 | 1 |
| `symbol` | VARCHAR(20) | 交易对 | BTCUSDT, ETHUSDT |
| `trade_id` | BIGINT | 交易ID | 251988247 |
| `price` | DECIMAL(15,2) | 成交价格 | 108435.00 |
| `amount` | DECIMAL(20,4) | 成交数量 | 1.1892 |
| `trade_time` | DECIMAL(20,6) | 成交时间戳 | 1748487331.781022 |
| `trade_type` | VARCHAR(10) | 成交方向 | buy, sell |
| `created_at` | TIMESTAMP | 创建时间 | 2025-05-29 10:55:32 |

**索引**:
- `idx_symbol_time (symbol, trade_time)`
- `idx_trade_id (trade_id)`
- `idx_created_at (created_at)`

#### 3. **bitda_depth** - Bitda深度数据表
| 字段名 | 数据类型 | 说明 | 示例 |
|--------|----------|------|------|
| `id` | BIGINT | 主键，自增 | 1 |
| `symbol` | VARCHAR(20) | 交易对 | BTCUSDT, ETHUSDT |
| `timestamp` | BIGINT | 时间戳 | 1699944061967 |
| `index_price` | DECIMAL(15,2) | 指数价格 | 36612.36 |
| `sign_price` | DECIMAL(15,2) | 标记价格 | 36589.76 |
| `last_price` | DECIMAL(15,2) | 最新价格 | 36341.59 |
| `asks` | JSON | 卖单深度 | [["36341.6","0.0444"]] |
| `bids` | JSON | 买单深度 | [["36341.25","0.0511"]] |
| `merge_level` | INT | 合并级别 | 0 |
| `created_at` | TIMESTAMP | 创建时间 | 2025-05-29 10:55:32 |

**索引**:
- `idx_symbol_time (symbol, timestamp)`
- `idx_created_at (created_at)`

#### 4. **bitda_ticker** - Bitda行情数据表
| 字段名 | 数据类型 | 说明 | 示例 |
|--------|----------|------|------|
| `id` | BIGINT | 主键，自增 | 1 |
| `symbol` | VARCHAR(20) | 交易对 | BTCUSDT, ETHUSDT |
| `open_price` | DECIMAL(15,2) | 开盘价 | 108000.00 |
| `high_price` | DECIMAL(15,2) | 最高价 | 108500.00 |
| `low_price` | DECIMAL(15,2) | 最低价 | 107800.00 |
| `last_price` | DECIMAL(15,2) | 最新价 | 108231.60 |
| `volume` | DECIMAL(25,4) | 成交量 | 35226256.5735 |
| `amount` | DECIMAL(25,2) | 成交金额 | 3822651777200.00 |
| `change_rate` | DECIMAL(10,4) | 涨跌幅 | -0.0290 |
| `funding_time` | INT | 资金费率时间 | 79 |
| `position_amount` | DECIMAL(25,4) | 持仓量 | 0.0000 |
| `funding_rate_last` | DECIMAL(10,6) | 当前资金费率 | 0.000929 |
| `funding_rate_next` | DECIMAL(10,6) | 下一个资金费率 | 0.000781 |
| `funding_rate_predict` | DECIMAL(10,6) | 预测资金费率 | 0.000591 |
| `insurance` | DECIMAL(25,2) | 保险基金 | 12920.38 |
| `sign_price` | DECIMAL(15,2) | 标记价格 | 108231.60 |
| `index_price` | DECIMAL(15,2) | 指数价格 | 108230.60 |
| `sell_total` | DECIMAL(25,4) | 卖盘总量 | 46470921.0000 |
| `buy_total` | DECIMAL(25,4) | 买盘总量 | 43420303.0000 |
| `period` | INT | 周期 | 86400 |
| `created_at` | TIMESTAMP | 创建时间 | 2025-05-29 10:55:32 |

**索引**:
- `idx_symbol_time (symbol, created_at)`
- `idx_created_at (created_at)`

#### 5. **binance_bookticker** - Binance BookTicker数据表
| 字段名 | 数据类型 | 说明 | 示例 |
|--------|----------|------|------|
| `id` | BIGINT | 主键，自增 | 1 |
| `symbol` | VARCHAR(20) | 交易对 | BTCUSDT, ETHUSDT |
| `update_id` | BIGINT | 更新ID | 123456789 |
| `bid_price` | DECIMAL(15,2) | 最优买单价 | 108266.90 |
| `bid_qty` | DECIMAL(20,4) | 最优买单量 | 4.1230 |
| `ask_price` | DECIMAL(15,2) | 最优卖单价 | 108267.00 |
| `ask_qty` | DECIMAL(20,4) | 最优卖单量 | 9.5050 |
| `event_time` | BIGINT | 事件时间 | 1571889248277 |
| `trade_time` | BIGINT | 交易时间 | 1571889248276 |
| `created_at` | TIMESTAMP | 创建时间 | 2025-05-29 10:55:32 |

**索引**:
- `idx_symbol_time (symbol, event_time)`
- `idx_update_id (update_id)`
- `idx_created_at (created_at)`

#### 6. **binance_depth_5** - Binance 5档深度数据表
| 字段名 | 数据类型 | 说明 | 示例 |
|--------|----------|------|------|
| `id` | BIGINT | 主键，自增 | 1 |
| `symbol` | VARCHAR(20) | 交易对 | BTCUSDT, ETHUSDT |
| `event_time` | BIGINT | 事件时间 | 1571889248277 |
| `trade_time` | BIGINT | 交易时间 | 1571889248276 |
| `first_update_id` | BIGINT | 第一个更新ID | 390497796 |
| `last_update_id` | BIGINT | 最后一个更新ID | 390497878 |
| `prev_update_id` | BIGINT | 上次更新ID | 390497794 |
| `bids` | JSON | 买单深度(5档) | [["7403.89","0.002"]] |
| `asks` | JSON | 卖单深度(5档) | [["7405.96","3.340"]] |
| `created_at` | TIMESTAMP | 创建时间 | 2025-05-29 10:55:32 |

**索引**:
- `idx_symbol_time (symbol, event_time)`
- `idx_created_at (created_at)`

## 📊 数据精度说明

### 价格和数量精度要求
| 交易所 | 交易对 | 价格精度 | 数量精度 | 数据库精度 |
|--------|--------|----------|----------|-----------|
| Bitda | BTCUSDT | 1位小数 | 4位小数 | DECIMAL(15,2) / DECIMAL(20,4) |
| Bitda | ETHUSDT | 2位小数 | 3位小数 | DECIMAL(15,2) / DECIMAL(20,4) |
| Binance | BTCUSDT | 2位小数 | 3位小数 | DECIMAL(15,2) / DECIMAL(20,4) |
| Binance | ETHUSDT | 2位小数 | 3位小数 | DECIMAL(15,2) / DECIMAL(20,4) |

**说明**: 使用统一的高精度格式，满足所有交易对的精度需求。

## 🚀 使用方法

### 数据收集系统
```bash
# 方法1: 完整启动（包含系统测试）
python start.py

# 方法2: 直接启动
python main.py

# 停止系统: 使用 Ctrl+C 优雅停止
# 系统会自动保存所有缓存数据

# 查看日志
tail -f crypto_collector.log
```

### 数据分析系统 🆕

#### 1. 单次综合分析
```bash
# 分析最近1小时数据并生成图表
python analyzer_main.py --mode single --hours 1

# 分析最近24小时数据，不生成图表
python analyzer_main.py --mode single --hours 24 --no-charts
```

#### 2. 持续监控模式
```bash
# 每15分钟执行一次分析
python analyzer_main.py --mode monitor --interval 15

# 每30分钟执行一次分析
python analyzer_main.py --mode monitor --interval 30
```

#### 3. 特定类型分析
```bash
# 延时分析
python analyzer_main.py --mode specific --type latency --hours 1

# K线分析（0插针检测）
python analyzer_main.py --mode specific --type kline --hours 24

# 深度对比分析
python analyzer_main.py --mode specific --type depth --hours 1

# 价格差值分析
python analyzer_main.py --mode specific --type price --hours 1

# 资金费率分析
python analyzer_main.py --mode specific --type funding
```

### 数据库查询示例
```sql
-- 查看最新的K线数据
SELECT * FROM bitda_kline ORDER BY created_at DESC LIMIT 10;

-- 查看成交数据统计
SELECT symbol, COUNT(*) as count FROM bitda_trades GROUP BY symbol;

-- 查看最新的深度数据
SELECT symbol, index_price, sign_price, last_price
FROM bitda_depth ORDER BY created_at DESC LIMIT 5;
```

## ⚙️ 配置参数

### 主要配置 (utils/config.py)
```python
# 收集的交易对
SYMBOLS = ['BTCUSDT', 'ETHUSDT']

# 数据保留天数
DATA_RETENTION_DAYS = 3

# 磁盘空间警告阈值
DISK_SPACE_WARNING_GB = 50

# 批量保存大小
BATCH_SIZE = 100

# WebSocket 配置
BITDA_WS_CONFIG = {
    'url': 'wss://ws.bitda.com/wsf',
    'ping_interval': 30,
    'reconnect_interval': 5
}
```

## 🔍 监控和维护

### 系统监控
- **磁盘空间**: 每5分钟检查一次，低于50GB自动停止
- **数据清理**: 每小时清理超过3天的旧数据
- **连接状态**: 自动重连机制，异常时自动恢复

### 日志级别
- **INFO**: 正常运行信息
- **WARNING**: 警告信息（如磁盘空间不足）
- **ERROR**: 错误信息（如连接失败）
- **CRITICAL**: 严重错误（如系统停止）

### 数据统计
系统会实时显示数据收集统计：
- Bitda K线数据: 实时计数
- Bitda 成交数据: 实时计数
- Bitda 深度数据: 实时计数
- Bitda 行情数据: 实时计数
- Binance BookTicker: 实时计数
- Binance 5档深度: 实时计数

## 📋 依赖要求

### Python 包依赖
```
websockets>=11.0.3
mysql-connector-python>=8.0.33
asyncio
```

### 安装依赖
```bash
# 安装Python依赖
pip install -r requirements.txt
```

## 📊 Grafana 和 Prometheus 安装 (可选)

### 在 Debian 12 系统上安装

#### 方法1: 使用APT包管理器 (推荐)
```bash
# 更新系统
sudo apt update

# 安装Prometheus
sudo apt install prometheus

# 添加Grafana官方仓库
sudo apt-get install -y software-properties-common
wget -q -O - https://packages.grafana.com/gpg.key | sudo apt-key add -
echo "deb https://packages.grafana.com/oss/deb stable main" | sudo tee -a /etc/apt/sources.list.d/grafana.list

# 安装Grafana
sudo apt update
sudo apt install grafana

# 启动服务
sudo systemctl start prometheus
sudo systemctl start grafana-server
sudo systemctl enable prometheus
sudo systemctl enable grafana-server
```

#### 方法2: 使用Docker (替代方案)
```bash
# 安装Docker
sudo apt install docker.io docker-compose

# 创建docker-compose.yml文件
cat > docker-compose.yml << EOF
version: '3.8'
services:
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
EOF

# 启动服务
docker-compose up -d
```

#### 访问地址
- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090

### 配置说明
1. **Grafana**: 用于创建可视化仪表板
2. **Prometheus**: 用于指标收集和存储
3. **集成**: 可以将分析结果导出为Prometheus格式

## 🔧 故障排除

### 常见问题

1. **WebSocket 连接失败**
   - 检查网络连接
   - 确认 WebSocket URL 正确
   - 查看日志文件获取详细错误信息

2. **数据库连接失败**
   - 检查 MySQL 服务是否运行
   - 验证数据库连接参数
   - 确认用户权限

3. **磁盘空间不足**
   - 系统会自动停止收集
   - 清理旧数据或增加存储空间
   - 调整 `DATA_RETENTION_DAYS` 参数

4. **数据收集中断**
   - 系统具有自动重连机制
   - 检查网络稳定性
   - 查看错误日志

### 性能优化建议

- 根据数据量调整 `BATCH_SIZE` 参数
- 定期清理日志文件
- 监控数据库性能
- 考虑使用 SSD 存储提高 I/O 性能

## 🎯 快速开始

### 1. 安装和设置
```bash
# 克隆或下载项目到本地
cd WS_DATA_ALL

# 自动安装依赖和测试系统
python setup_analyzer.py

# 或手动安装依赖
pip install -r requirements.txt
```

### 2. 启动数据收集（如果尚未运行）
```bash
# 暂停现有的数据收集器
# 然后启动新的收集器
python main.py
```

### 3. 运行数据分析
```bash
# 快速演示所有功能
python demo_analyzer.py

# 测试系统是否正常
python test_analyzer.py

# 运行单次分析
python analyzer_main.py --mode single --hours 1

# 开始持续监控
python analyzer_main.py --mode monitor --interval 15
```

## 📊 分析结果说明

### 延时监控 🚀
- **目标**: ETHUSDT价格延时<100ms
- **成功标准**: 匹配次数>10, 平均延时<50ms
- **警告**: 延时>100ms或匹配次数<5

### K线分析 📈
- **目标**: 连续相同K线比例<5%
- **成功标准**: 无长期连续相同K线
- **警告**: 相同比例>5%或最长序列>5根

### 深度对比 📊
- **目标**: Bitda深度比值>1.0
- **成功标准**: 60%以上时间Bitda深度更好
- **警告**: 平均比值<0.8或优势比例<40%

### 价格差值 💰
- **目标**: 标记价格差值<0.05%
- **成功标准**: 平均差值<0.01%, 异常比例<5%
- **警告**: 平均差值>0.05%或异常比例>10%

### 资金费率 📋
- **目标**: 费率绝对值<0.05%
- **成功标准**: BTC/ETH费率差异<0.01%
- **警告**: 费率绝对值>0.1%或差异>0.05%

## 🔧 故障排除

### 常见问题解决

1. **"No module named 'analyzer'"**
   ```bash
   # 确保在项目根目录运行
   cd WS_DATA_ALL
   python analyzer_main.py
   ```

2. **图表生成失败**
   ```bash
   # 安装图表依赖
   pip install matplotlib seaborn pandas numpy

   # Linux系统可能需要安装字体
   sudo apt install fonts-dejavu-core
   ```

3. **数据库连接失败**
   ```bash
   # 检查网络连接
   ping ************

   # 检查数据库配置
   python -c "from utils.db import db_manager; print('连接成功' if db_manager.execute_query('SELECT 1', fetch=True) else '连接失败')"
   ```

4. **分析结果为空**
   - 确保数据收集器正在运行
   - 检查数据库中是否有最近的数据
   - 尝试增加分析时间范围

---

**系统版本**: v2.0 (新增数据分析功能)
**最后更新**: 2025-01-29
**维护者**: 加密货币数据分析系统团队