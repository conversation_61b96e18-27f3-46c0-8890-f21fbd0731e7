#!/usr/bin/env python3
"""
分析匹配率降低的原因
深入分析为什么延时匹配率只有15%
"""

import mysql.connector
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MatchRateAnalyzer:
    """匹配率分析器"""
    
    def __init__(self):
        self.source_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'depth_db'
        }
        
        self.latency_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'ethusdt_latency_db'
        }
    
    def analyze_data_volume(self):
        """分析数据量"""
        logger.info("📊 分析数据量...")
        
        try:
            connection = mysql.connector.connect(**self.source_config)
            cursor = connection.cursor()
            
            # 检查最近1小时的数据量
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_bitda,
                    COUNT(bid_price_1) as bitda_with_prices,
                    MIN(created_at) as earliest,
                    MAX(created_at) as latest
                FROM bitda_depth
                WHERE symbol = 'ETHUSDT'
                AND created_at >= NOW() - INTERVAL 1 HOUR
            """)
            
            bitda_result = cursor.fetchone()
            total_bitda, bitda_with_prices, earliest, latest = bitda_result
            
            cursor.execute("""
                SELECT COUNT(*) as total_binance
                FROM binance_bookticker
                WHERE symbol = 'ETHUSDT'
                AND created_at >= NOW() - INTERVAL 1 HOUR
            """)
            
            binance_result = cursor.fetchone()
            total_binance = binance_result[0]
            
            logger.info(f"   📋 最近1小时数据量:")
            logger.info(f"      Bitda总数: {total_bitda:,}")
            logger.info(f"      Bitda有价格: {bitda_with_prices:,} ({bitda_with_prices/total_bitda*100:.1f}%)")
            logger.info(f"      Binance总数: {total_binance:,}")
            logger.info(f"      时间范围: {earliest} ~ {latest}")
            
            # 计算数据密度
            if earliest and latest:
                time_span = (latest - earliest).total_seconds() / 60  # 分钟
                bitda_per_min = bitda_with_prices / time_span if time_span > 0 else 0
                binance_per_min = total_binance / time_span if time_span > 0 else 0
                
                logger.info(f"      Bitda频率: {bitda_per_min:.1f} 条/分钟")
                logger.info(f"      Binance频率: {binance_per_min:.1f} 条/分钟")
            
            cursor.close()
            connection.close()
            
            return {
                'bitda_total': total_bitda,
                'bitda_with_prices': bitda_with_prices,
                'binance_total': total_binance
            }
            
        except Exception as e:
            logger.error(f"❌ 分析数据量失败: {e}")
            return None
    
    def analyze_price_uniqueness(self):
        """分析价格唯一性"""
        logger.info("\n💰 分析价格唯一性...")
        
        try:
            connection = mysql.connector.connect(**self.source_config)
            cursor = connection.cursor()
            
            # 分析Bitda价格分布
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(DISTINCT bid_price_1, ask_price_1) as unique_price_pairs,
                    COUNT(DISTINCT bid_price_1) as unique_bid_prices,
                    COUNT(DISTINCT ask_price_1) as unique_ask_prices
                FROM bitda_depth
                WHERE symbol = 'ETHUSDT'
                AND created_at >= NOW() - INTERVAL 1 HOUR
                AND bid_price_1 IS NOT NULL
                AND ask_price_1 IS NOT NULL
            """)
            
            bitda_result = cursor.fetchone()
            bitda_total, bitda_unique_pairs, bitda_unique_bids, bitda_unique_asks = bitda_result
            
            # 分析Binance价格分布
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(DISTINCT bid_price, ask_price) as unique_price_pairs,
                    COUNT(DISTINCT bid_price) as unique_bid_prices,
                    COUNT(DISTINCT ask_price) as unique_ask_prices
                FROM binance_bookticker
                WHERE symbol = 'ETHUSDT'
                AND created_at >= NOW() - INTERVAL 1 HOUR
            """)
            
            binance_result = cursor.fetchone()
            binance_total, binance_unique_pairs, binance_unique_bids, binance_unique_asks = binance_result
            
            logger.info(f"   📊 Bitda价格分析:")
            logger.info(f"      总记录: {bitda_total:,}")
            logger.info(f"      唯一价格对: {bitda_unique_pairs:,}")
            logger.info(f"      价格对重复率: {(1-bitda_unique_pairs/bitda_total)*100:.1f}%")
            logger.info(f"      唯一买一价: {bitda_unique_bids:,}")
            logger.info(f"      唯一卖一价: {bitda_unique_asks:,}")
            
            logger.info(f"   📊 Binance价格分析:")
            logger.info(f"      总记录: {binance_total:,}")
            logger.info(f"      唯一价格对: {binance_unique_pairs:,}")
            logger.info(f"      价格对重复率: {(1-binance_unique_pairs/binance_total)*100:.1f}%")
            logger.info(f"      唯一买一价: {binance_unique_bids:,}")
            logger.info(f"      唯一卖一价: {binance_unique_asks:,}")
            
            cursor.close()
            connection.close()
            
            return {
                'bitda_unique_pairs': bitda_unique_pairs,
                'binance_unique_pairs': binance_unique_pairs,
                'bitda_total': bitda_total,
                'binance_total': binance_total
            }
            
        except Exception as e:
            logger.error(f"❌ 分析价格唯一性失败: {e}")
            return None
    
    def analyze_price_overlap(self):
        """分析价格重叠情况"""
        logger.info("\n🔍 分析价格重叠情况...")
        
        try:
            connection = mysql.connector.connect(**self.source_config)
            cursor = connection.cursor()
            
            # 查找最近1小时内有多少Bitda价格对在Binance中出现过
            cursor.execute("""
                SELECT COUNT(DISTINCT b.bid_price_1, b.ask_price_1) as overlapping_pairs
                FROM bitda_depth b
                WHERE b.symbol = 'ETHUSDT'
                AND b.created_at >= NOW() - INTERVAL 1 HOUR
                AND b.bid_price_1 IS NOT NULL
                AND b.ask_price_1 IS NOT NULL
                AND EXISTS (
                    SELECT 1 FROM binance_bookticker bn
                    WHERE bn.symbol = 'ETHUSDT'
                    AND bn.bid_price = b.bid_price_1
                    AND bn.ask_price = b.ask_price_1
                    AND bn.created_at >= NOW() - INTERVAL 1 HOUR
                )
            """)
            
            overlapping_pairs = cursor.fetchone()[0]
            
            # 获取Bitda总的唯一价格对数
            cursor.execute("""
                SELECT COUNT(DISTINCT bid_price_1, ask_price_1) as total_bitda_pairs
                FROM bitda_depth
                WHERE symbol = 'ETHUSDT'
                AND created_at >= NOW() - INTERVAL 1 HOUR
                AND bid_price_1 IS NOT NULL
                AND ask_price_1 IS NOT NULL
            """)
            
            total_bitda_pairs = cursor.fetchone()[0]
            
            overlap_rate = overlapping_pairs / total_bitda_pairs * 100 if total_bitda_pairs > 0 else 0
            
            logger.info(f"   📊 价格重叠分析:")
            logger.info(f"      Bitda唯一价格对: {total_bitda_pairs:,}")
            logger.info(f"      与Binance重叠的价格对: {overlapping_pairs:,}")
            logger.info(f"      重叠率: {overlap_rate:.1f}%")
            
            if overlap_rate < 50:
                logger.warning(f"   ⚠️  重叠率较低，这是匹配率低的主要原因")
            
            cursor.close()
            connection.close()
            
            return overlap_rate
            
        except Exception as e:
            logger.error(f"❌ 分析价格重叠失败: {e}")
            return None
    
    def analyze_timing_issues(self):
        """分析时间同步问题"""
        logger.info("\n⏰ 分析时间同步问题...")
        
        try:
            connection = mysql.connector.connect(**self.source_config)
            cursor = connection.cursor()
            
            # 分析时间戳分布
            cursor.execute("""
                SELECT 
                    AVG(timestamp) as avg_bitda_ts,
                    MIN(timestamp) as min_bitda_ts,
                    MAX(timestamp) as max_bitda_ts
                FROM bitda_depth
                WHERE symbol = 'ETHUSDT'
                AND created_at >= NOW() - INTERVAL 1 HOUR
                AND bid_price_1 IS NOT NULL
            """)
            
            bitda_ts_result = cursor.fetchone()
            avg_bitda_ts, min_bitda_ts, max_bitda_ts = bitda_ts_result
            
            cursor.execute("""
                SELECT 
                    AVG(event_time) as avg_binance_ts,
                    MIN(event_time) as min_binance_ts,
                    MAX(event_time) as max_binance_ts
                FROM binance_bookticker
                WHERE symbol = 'ETHUSDT'
                AND created_at >= NOW() - INTERVAL 1 HOUR
            """)
            
            binance_ts_result = cursor.fetchone()
            avg_binance_ts, min_binance_ts, max_binance_ts = binance_ts_result
            
            # 计算时间差
            avg_time_diff = avg_bitda_ts - avg_binance_ts if avg_bitda_ts and avg_binance_ts else 0
            
            logger.info(f"   📊 时间戳分析:")
            logger.info(f"      Bitda平均时间戳: {avg_bitda_ts}")
            logger.info(f"      Binance平均时间戳: {avg_binance_ts}")
            logger.info(f"      平均时间差: {avg_time_diff:.0f}ms ({avg_time_diff/1000:.1f}秒)")
            
            # 检查时间窗口重叠
            bitda_range = max_bitda_ts - min_bitda_ts if max_bitda_ts and min_bitda_ts else 0
            binance_range = max_binance_ts - min_binance_ts if max_binance_ts and min_binance_ts else 0
            
            logger.info(f"      Bitda时间跨度: {bitda_range:.0f}ms ({bitda_range/1000/60:.1f}分钟)")
            logger.info(f"      Binance时间跨度: {binance_range:.0f}ms ({binance_range/1000/60:.1f}分钟)")
            
            cursor.close()
            connection.close()
            
            return avg_time_diff
            
        except Exception as e:
            logger.error(f"❌ 分析时间同步问题失败: {e}")
            return None
    
    def analyze_matching_constraints(self):
        """分析匹配约束条件"""
        logger.info("\n🔒 分析匹配约束条件...")
        
        try:
            connection = mysql.connector.connect(**self.source_config)
            cursor = connection.cursor()
            
            # 模拟匹配过程，看看每一步过滤掉多少数据
            logger.info("   📋 模拟匹配过程:")
            
            # 步骤1: 获取Bitda数据
            cursor.execute("""
                SELECT COUNT(*) FROM bitda_depth
                WHERE symbol = 'ETHUSDT'
                AND created_at >= NOW() - INTERVAL 10 MINUTE
                AND bid_price_1 IS NOT NULL
                AND ask_price_1 IS NOT NULL
            """)
            step1_count = cursor.fetchone()[0]
            logger.info(f"      步骤1 - Bitda有效数据: {step1_count:,}")
            
            # 步骤2: 对于每个Bitda价格，查找Binance匹配
            cursor.execute("""
                SELECT 
                    b.bid_price_1, 
                    b.ask_price_1, 
                    b.timestamp,
                    (SELECT COUNT(*) FROM binance_bookticker bn
                     WHERE bn.symbol = 'ETHUSDT'
                     AND bn.bid_price = b.bid_price_1
                     AND bn.ask_price = b.ask_price_1
                     AND bn.event_time < b.timestamp
                     AND bn.event_time >= b.timestamp - 10000
                    ) as binance_matches
                FROM bitda_depth b
                WHERE b.symbol = 'ETHUSDT'
                AND b.created_at >= NOW() - INTERVAL 10 MINUTE
                AND b.bid_price_1 IS NOT NULL
                AND b.ask_price_1 IS NOT NULL
                ORDER BY b.created_at DESC
                LIMIT 20
            """)
            
            sample_results = cursor.fetchall()
            
            matches_found = 0
            no_matches = 0
            
            for bid, ask, timestamp, binance_count in sample_results:
                if binance_count > 0:
                    matches_found += 1
                else:
                    no_matches += 1
            
            logger.info(f"      步骤2 - 样本匹配分析 (最近20条):")
            logger.info(f"         有Binance匹配: {matches_found}")
            logger.info(f"         无Binance匹配: {no_matches}")
            logger.info(f"         样本匹配率: {matches_found/(matches_found+no_matches)*100:.1f}%")
            
            # 步骤3: 延时范围过滤
            min_latency = 10
            max_latency = 2000
            
            logger.info(f"      步骤3 - 延时范围过滤 ({min_latency}-{max_latency}ms):")
            logger.info(f"         这一步会过滤掉延时过短或过长的匹配")
            
            cursor.close()
            connection.close()
            
            return matches_found / (matches_found + no_matches) if (matches_found + no_matches) > 0 else 0
            
        except Exception as e:
            logger.error(f"❌ 分析匹配约束失败: {e}")
            return None
    
    def comprehensive_analysis(self):
        """综合分析匹配率低的原因"""
        logger.info("🔍 开始综合分析匹配率降低的原因...")
        logger.info("=" * 80)
        
        # 各项分析
        data_stats = self.analyze_data_volume()
        price_stats = self.analyze_price_uniqueness()
        overlap_rate = self.analyze_price_overlap()
        time_diff = self.analyze_timing_issues()
        sample_match_rate = self.analyze_matching_constraints()
        
        # 综合分析
        logger.info("\n" + "=" * 80)
        logger.info("📋 匹配率低的原因分析:")
        
        reasons = []
        
        # 原因1: 完全匹配要求过严格
        if overlap_rate and overlap_rate < 30:
            reasons.append("🔒 完全匹配要求过严格")
            logger.info("   🔒 完全匹配要求过严格:")
            logger.info("      - 要求买一价和卖一价都完全相同")
            logger.info("      - 两个交易所的价格精度可能不同")
            logger.info(f"      - 价格重叠率只有 {overlap_rate:.1f}%")
        
        # 原因2: 价格变化频繁
        if price_stats:
            bitda_repeat_rate = (1 - price_stats['bitda_unique_pairs'] / price_stats['bitda_total']) * 100
            binance_repeat_rate = (1 - price_stats['binance_unique_pairs'] / price_stats['binance_total']) * 100
            
            if bitda_repeat_rate < 50 and binance_repeat_rate < 50:
                reasons.append("📈 价格变化过于频繁")
                logger.info("   📈 价格变化过于频繁:")
                logger.info(f"      - Bitda价格对重复率: {bitda_repeat_rate:.1f}%")
                logger.info(f"      - Binance价格对重复率: {binance_repeat_rate:.1f}%")
                logger.info("      - 价格变化太快，难以找到完全匹配")
        
        # 原因3: 时间窗口限制
        if time_diff and abs(time_diff) > 5000:
            reasons.append("⏰ 时间同步问题")
            logger.info("   ⏰ 时间同步问题:")
            logger.info(f"      - 平均时间差: {time_diff:.0f}ms")
            logger.info("      - 时间差过大可能影响匹配")
        
        # 原因4: 延时范围限制
        reasons.append("⚖️ 延时范围限制")
        logger.info("   ⚖️ 延时范围限制:")
        logger.info("      - 只接受10-2000ms的延时")
        logger.info("      - 过短或过长的延时被过滤")
        
        # 原因5: 市场特性
        reasons.append("🏪 市场特性")
        logger.info("   🏪 市场特性:")
        logger.info("      - 不同交易所的价格发现机制不同")
        logger.info("      - 流动性和交易量差异")
        logger.info("      - 套利机器人快速消除价差")
        
        logger.info(f"\n🎯 主要原因总结:")
        for i, reason in enumerate(reasons, 1):
            logger.info(f"   {i}. {reason}")
        
        logger.info(f"\n💡 改进建议:")
        logger.info("   1. 考虑放宽匹配条件（如允许小幅价差）")
        logger.info("   2. 增加单边匹配（只匹配买一价或卖一价）")
        logger.info("   3. 扩大时间窗口范围")
        logger.info("   4. 调整延时范围限制")
        logger.info("   5. 添加近似匹配算法")

def main():
    """主函数"""
    print("🔍 匹配率降低原因分析工具")
    print("=" * 50)
    print("功能:")
    print("  - 分析数据量和质量")
    print("  - 检查价格重叠情况")
    print("  - 分析时间同步问题")
    print("  - 评估匹配约束条件")
    print("  - 提供改进建议")
    print()
    
    analyzer = MatchRateAnalyzer()
    analyzer.comprehensive_analysis()
    
    print("\n🎉 分析完成！")

if __name__ == "__main__":
    main()
