#!/usr/bin/env python3
"""
Grafana配置脚本
自动配置Grafana数据源和仪表板
"""

import requests
import json
import time
import sys

class GrafanaSetup:
    """Grafana配置类"""
    
    def __init__(self, grafana_url="http://localhost:3000", username="admin", password="admin"):
        self.grafana_url = grafana_url
        self.username = username
        self.password = password
        self.session = requests.Session()
        self.session.auth = (username, password)
        
    def wait_for_grafana(self, max_attempts=30):
        """等待Grafana启动"""
        print("🔄 等待Grafana启动...")
        
        for attempt in range(max_attempts):
            try:
                response = self.session.get(f"{self.grafana_url}/api/health")
                if response.status_code == 200:
                    print("✅ Grafana已启动")
                    return True
            except requests.exceptions.ConnectionError:
                pass
            
            print(f"   尝试 {attempt + 1}/{max_attempts}...")
            time.sleep(2)
        
        print("❌ Grafana启动超时")
        return False
    
    def add_prometheus_datasource(self):
        """添加Prometheus数据源"""
        print("📊 配置Prometheus数据源...")
        
        datasource_config = {
            "name": "Prometheus",
            "type": "prometheus",
            "url": "http://localhost:9090",
            "access": "proxy",
            "isDefault": True,
            "basicAuth": False
        }
        
        try:
            # 检查数据源是否已存在
            response = self.session.get(f"{self.grafana_url}/api/datasources/name/Prometheus")
            if response.status_code == 200:
                print("✅ Prometheus数据源已存在")
                return True
            
            # 创建新数据源
            response = self.session.post(
                f"{self.grafana_url}/api/datasources",
                json=datasource_config,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                print("✅ Prometheus数据源配置成功")
                return True
            else:
                print(f"❌ 数据源配置失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 数据源配置异常: {e}")
            return False
    
    def import_dashboard(self, dashboard_file="grafana_dashboard.json"):
        """导入仪表板"""
        print("📈 导入加密货币分析仪表板...")
        
        try:
            with open(dashboard_file, 'r', encoding='utf-8') as f:
                dashboard_config = json.load(f)
            
            # 导入仪表板
            response = self.session.post(
                f"{self.grafana_url}/api/dashboards/db",
                json=dashboard_config,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                dashboard_url = f"{self.grafana_url}/d/{result.get('uid', '')}"
                print(f"✅ 仪表板导入成功")
                print(f"🔗 仪表板地址: {dashboard_url}")
                return True
            else:
                print(f"❌ 仪表板导入失败: {response.status_code} - {response.text}")
                return False
                
        except FileNotFoundError:
            print(f"❌ 仪表板文件不存在: {dashboard_file}")
            return False
        except Exception as e:
            print(f"❌ 仪表板导入异常: {e}")
            return False
    
    def setup_all(self):
        """执行完整配置"""
        print("🚀 开始配置Grafana...")
        
        # 等待Grafana启动
        if not self.wait_for_grafana():
            return False
        
        # 配置数据源
        if not self.add_prometheus_datasource():
            return False
        
        # 导入仪表板
        if not self.import_dashboard():
            return False
        
        print("\n🎉 Grafana配置完成！")
        print(f"🔗 Grafana地址: {self.grafana_url}")
        print(f"👤 用户名: {self.username}")
        print(f"🔑 密码: {self.password}")
        
        return True

def main():
    """主函数"""
    print("🔧 Grafana自动配置工具")
    print("="*50)
    
    setup = GrafanaSetup()
    
    if setup.setup_all():
        print("\n✨ 配置成功！现在可以访问Grafana查看监控数据")
    else:
        print("\n❌ 配置失败，请检查Grafana服务状态")
        sys.exit(1)

if __name__ == "__main__":
    main()
