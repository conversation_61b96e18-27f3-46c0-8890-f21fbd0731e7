#!/bin/bash

# 🚀 第1步: 安装和配置ClickHouse
# 自动化安装脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统
check_system() {
    log_info "检查系统环境..."
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        log_error "无法确定操作系统类型"
        exit 1
    fi
    
    source /etc/os-release
    log_info "操作系统: $PRETTY_NAME"
    
    # 检查架构
    ARCH=$(uname -m)
    log_info "系统架构: $ARCH"
    
    if [[ "$ARCH" != "x86_64" ]]; then
        log_warning "ClickHouse在非x86_64架构上可能有兼容性问题"
    fi
    
    # 检查内存
    MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
    log_info "系统内存: ${MEMORY_GB}GB"
    
    if [[ $MEMORY_GB -lt 4 ]]; then
        log_warning "内存少于4GB，建议增加内存以获得更好性能"
    fi
    
    # 检查磁盘空间
    DISK_SPACE=$(df -BG / | awk 'NR==2 {print $4}' | sed 's/G//')
    log_info "可用磁盘空间: ${DISK_SPACE}GB"
    
    if [[ $DISK_SPACE -lt 20 ]]; then
        log_error "磁盘空间不足20GB，无法继续安装"
        exit 1
    fi
}

# 安装ClickHouse
install_clickhouse() {
    log_info "开始安装ClickHouse..."
    
    # 更新包列表
    log_info "更新包列表..."
    sudo apt update
    
    # 安装依赖
    log_info "安装依赖包..."
    sudo apt install -y apt-transport-https ca-certificates dirmngr
    
    # 添加ClickHouse官方仓库
    log_info "添加ClickHouse官方仓库..."
    sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv 8919F6BD2B48D754
    echo "deb https://packages.clickhouse.com/deb stable main" | sudo tee /etc/apt/sources.list.d/clickhouse.list
    
    # 更新包列表
    sudo apt update
    
    # 安装ClickHouse
    log_info "安装ClickHouse服务器和客户端..."
    sudo apt install -y clickhouse-server clickhouse-client
    
    log_success "ClickHouse安装完成"
}

# 配置ClickHouse
configure_clickhouse() {
    log_info "配置ClickHouse..."
    
    # 备份原始配置
    sudo cp /etc/clickhouse-server/config.xml /etc/clickhouse-server/config.xml.backup
    
    # 创建优化配置
    cat << 'EOF' | sudo tee /etc/clickhouse-server/config.d/crypto_optimization.xml
<?xml version="1.0"?>
<clickhouse>
    <!-- 内存优化 -->
    <max_memory_usage>4000000000</max_memory_usage>
    <max_bytes_before_external_group_by>2000000000</max_bytes_before_external_group_by>
    
    <!-- 网络配置 -->
    <listen_host>0.0.0.0</listen_host>
    <http_port>8123</http_port>
    <tcp_port>9000</tcp_port>
    
    <!-- 日志配置 -->
    <logger>
        <level>information</level>
        <log>/var/log/clickhouse-server/clickhouse-server.log</log>
        <errorlog>/var/log/clickhouse-server/clickhouse-server.err.log</errorlog>
        <size>1000M</size>
        <count>10</count>
    </logger>
    
    <!-- 性能优化 -->
    <merge_tree>
        <max_suspicious_broken_parts>5</max_suspicious_broken_parts>
        <parts_to_delay_insert>150</parts_to_delay_insert>
        <parts_to_throw_insert>300</parts_to_throw_insert>
    </merge_tree>
    
    <!-- 压缩配置 -->
    <compression>
        <case>
            <method>lz4</method>
        </case>
    </compression>
</clickhouse>
EOF
    
    log_success "ClickHouse配置完成"
}

# 启动ClickHouse服务
start_clickhouse() {
    log_info "启动ClickHouse服务..."
    
    # 启动服务
    sudo systemctl start clickhouse-server
    
    # 设置开机自启
    sudo systemctl enable clickhouse-server
    
    # 等待服务启动
    log_info "等待ClickHouse服务启动..."
    sleep 5
    
    # 检查服务状态
    if sudo systemctl is-active --quiet clickhouse-server; then
        log_success "ClickHouse服务启动成功"
    else
        log_error "ClickHouse服务启动失败"
        sudo systemctl status clickhouse-server
        exit 1
    fi
}

# 测试ClickHouse连接
test_clickhouse() {
    log_info "测试ClickHouse连接..."
    
    # 测试连接
    if clickhouse-client --query "SELECT 'ClickHouse连接测试成功' as message"; then
        log_success "ClickHouse连接测试成功"
    else
        log_error "ClickHouse连接测试失败"
        exit 1
    fi
    
    # 显示版本信息
    VERSION=$(clickhouse-client --query "SELECT version()")
    log_info "ClickHouse版本: $VERSION"
    
    # 显示系统信息
    log_info "ClickHouse系统信息:"
    clickhouse-client --query "
    SELECT 
        'CPU核心数' as metric, 
        toString(value) as value 
    FROM system.asynchronous_metrics 
    WHERE metric = 'jemalloc.background_thread.num_threads'
    UNION ALL
    SELECT 
        '可用内存(GB)' as metric, 
        toString(round(value/1024/1024/1024, 2)) as value 
    FROM system.asynchronous_metrics 
    WHERE metric = 'MemoryResident'
    "
}

# 安装Python驱动
install_python_driver() {
    log_info "安装Python ClickHouse驱动..."
    
    # 检查Python环境
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        log_error "未找到Python环境"
        exit 1
    fi
    
    # 安装clickhouse-driver
    $PYTHON_CMD -m pip install clickhouse-driver
    
    # 测试Python驱动
    $PYTHON_CMD -c "
from clickhouse_driver import Client
try:
    client = Client('localhost')
    result = client.execute('SELECT 1')
    print('✅ Python ClickHouse驱动测试成功')
except Exception as e:
    print(f'❌ Python ClickHouse驱动测试失败: {e}')
    exit(1)
"
    
    log_success "Python ClickHouse驱动安装成功"
}

# 创建数据库
create_database() {
    log_info "创建crypto数据库..."
    
    clickhouse-client --query "CREATE DATABASE IF NOT EXISTS crypto"
    
    log_success "crypto数据库创建成功"
}

# 主函数
main() {
    echo "🚀 ClickHouse安装和配置脚本"
    echo "=================================="
    echo ""
    
    # 检查是否以root权限运行
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要以root用户运行此脚本"
        exit 1
    fi
    
    # 检查sudo权限
    if ! sudo -n true 2>/dev/null; then
        log_info "需要sudo权限，请输入密码..."
        sudo -v
    fi
    
    # 执行安装步骤
    check_system
    echo ""
    
    install_clickhouse
    echo ""
    
    configure_clickhouse
    echo ""
    
    start_clickhouse
    echo ""
    
    test_clickhouse
    echo ""
    
    install_python_driver
    echo ""
    
    create_database
    echo ""
    
    log_success "🎉 ClickHouse安装和配置完成！"
    echo ""
    echo "📋 下一步:"
    echo "  1. 运行 step2_create_tables.py 创建表结构"
    echo "  2. 运行 step3_migrate_data.py 迁移数据"
    echo ""
    echo "🔧 ClickHouse服务信息:"
    echo "  - HTTP端口: 8123"
    echo "  - TCP端口: 9000"
    echo "  - 配置文件: /etc/clickhouse-server/"
    echo "  - 日志文件: /var/log/clickhouse-server/"
    echo ""
    echo "💡 常用命令:"
    echo "  - 连接: clickhouse-client"
    echo "  - 状态: sudo systemctl status clickhouse-server"
    echo "  - 重启: sudo systemctl restart clickhouse-server"
}

# 运行主函数
main "$@"
