#!/usr/bin/env python3
"""
测试深度数据查询逻辑
"""

import mysql.connector
from datetime import datetime, timedelta

def test_data_query():
    """测试数据查询"""
    print("🔍 测试深度数据查询逻辑")
    print("=" * 50)
    
    try:
        # 连接数据库
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='Linuxtest',
            database='depth_db'
        )
        cursor = connection.cursor()
        
        # 1. 查看最新的Bitda数据
        print("📊 查看最新的Bitda数据...")
        cursor.execute("""
            SELECT symbol, timestamp, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1, created_at
            FROM bitda_depth 
            WHERE symbol IN ('BTCUSDT', 'ETHUSDT')
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        
        results = cursor.fetchall()
        for row in results:
            symbol, ts, bid_price, ask_price, bid_qty, ask_qty, created_at = row
            spread = float(ask_price - bid_price) if bid_price and ask_price else 0
            print(f"   {symbol}: 买一价{bid_price} 卖一价{ask_price} 价差{spread:.4f} 买一量{bid_qty}")
        
        # 2. 查看最新的Binance数据
        print("\n📈 查看最新的Binance数据...")
        cursor.execute("""
            SELECT symbol, event_time, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1, created_at
            FROM binance_depth_5 
            WHERE symbol IN ('BTCUSDT', 'ETHUSDT')
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        
        results = cursor.fetchall()
        for row in results:
            symbol, ts, bid_price, ask_price, bid_qty, ask_qty, created_at = row
            spread = float(ask_price - bid_price) if bid_price and ask_price else 0
            print(f"   {symbol}: 买一价{bid_price} 卖一价{ask_price} 价差{spread:.4f} 买一量{bid_qty}")
        
        # 3. 测试时间匹配逻辑
        print("\n🎯 测试时间匹配逻辑...")
        
        # 获取一个Bitda时间戳
        cursor.execute("""
            SELECT symbol, timestamp, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1
            FROM bitda_depth 
            WHERE symbol = 'BTCUSDT' AND bid_price_1 IS NOT NULL
            ORDER BY created_at DESC 
            LIMIT 1
        """)
        
        bitda_result = cursor.fetchone()
        if bitda_result:
            symbol, bitda_ts, bid_price, ask_price, bid_qty, ask_qty = bitda_result
            print(f"   📊 Bitda样本: {symbol} 时间戳{bitda_ts} 买一价{bid_price} 买一量{bid_qty}")
            
            # 查找最接近的Binance数据
            window_ms = 5 * 60 * 1000  # 5分钟窗口
            cursor.execute("""
                SELECT 
                    event_time, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
                    ABS(event_time - %s) as time_diff
                FROM binance_depth_5 
                WHERE symbol = %s 
                AND event_time BETWEEN %s AND %s
                AND bid_price_1 IS NOT NULL
                ORDER BY time_diff
                LIMIT 1
            """, (bitda_ts, symbol, bitda_ts - window_ms, bitda_ts + window_ms))
            
            binance_result = cursor.fetchone()
            if binance_result:
                bn_ts, bn_bid_price, bn_ask_price, bn_bid_qty, bn_ask_qty, time_diff = binance_result
                print(f"   🎯 匹配Binance: 时间戳{bn_ts} 买一价{bn_bid_price} 买一量{bn_bid_qty}")
                print(f"   ⏱️  时间差: {time_diff}ms")
                
                # 计算深度比值
                if bn_bid_qty and bn_bid_qty > 0:
                    bid_ratio = float(bid_qty) / float(bn_bid_qty)
                    print(f"   📊 买一量比值: {bid_ratio:.4f} (Bitda/Binance)")
                
                # 计算价差对比
                bitda_spread = float(ask_price - bid_price) if bid_price and ask_price else 0
                binance_spread = float(bn_ask_price - bn_bid_price) if bn_bid_price and bn_ask_price else 0
                spread_diff = bitda_spread - binance_spread
                print(f"   💰 价差对比: Bitda{bitda_spread:.4f} vs Binance{binance_spread:.4f} 差值{spread_diff:.4f}")
            else:
                print("   ❌ 未找到匹配的Binance数据")
        
        # 4. 统计数据量
        print("\n📈 数据量统计...")
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            cursor.execute("SELECT COUNT(*) FROM bitda_depth WHERE symbol = %s", (symbol,))
            bitda_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM binance_depth_5 WHERE symbol = %s", (symbol,))
            binance_count = cursor.fetchone()[0]
            
            print(f"   {symbol}: Bitda {bitda_count:,}条, Binance {binance_count:,}条")
        
        cursor.close()
        connection.close()
        
        print("\n✅ 数据查询测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_data_query()
