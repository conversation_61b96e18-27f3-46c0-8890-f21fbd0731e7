#!/usr/bin/env python3
"""
ETHUSDT延时数据处理器 - 专门处理1-5分钟前的数据
避免实时数据的不稳定性，确保数据完整性
"""

import mysql.connector
import json
import argparse
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RecentLatencyProcessor:
    """处理最近延时数据的优化处理器"""
    
    def __init__(self):
        self.source_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'depth_db'
        }
        
        self.target_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'ethusdt_latency_db'
        }
        
        self.min_latency = 10   # 最小有效延时(ms)
        self.max_latency = 2000 # 最大有效延时(ms)
    
    def parse_bitda_depth(self, asks_json, bids_json):
        """解析Bitda深度数据"""
        try:
            if not asks_json or not bids_json:
                return None, None, None, None
            
            asks_data = json.loads(asks_json) if isinstance(asks_json, str) else asks_json
            bids_data = json.loads(bids_json) if isinstance(bids_json, str) else bids_json
            
            if not asks_data or not bids_data:
                return None, None, None, None
            
            # 卖一价格 = asks中价格最小的
            ask_prices = [float(item[0]) for item in asks_data]
            ask_price_1 = min(ask_prices)
            ask_qty_1 = float([item[1] for item in asks_data if float(item[0]) == ask_price_1][0])
            
            # 买一价格 = bids中价格最大的
            bid_prices = [float(item[0]) for item in bids_data]
            bid_price_1 = max(bid_prices)
            bid_qty_1 = float([item[1] for item in bids_data if float(item[0]) == bid_price_1][0])
            
            return ask_price_1, ask_qty_1, bid_price_1, bid_qty_1
            
        except Exception as e:
            logger.error(f"解析Bitda深度数据失败: {e}")
            return None, None, None, None
    
    def process_recent_latency(self, minutes_ago=1, window_minutes=5, batch_size=500):
        """
        处理指定时间前的延时数据
        
        Args:
            minutes_ago: 多少分钟以前的数据
            window_minutes: 处理窗口大小
            batch_size: 批处理大小
        """
        
        # 计算时间范围
        end_time = datetime.now() - timedelta(minutes=minutes_ago)
        start_time = end_time - timedelta(minutes=window_minutes)
        
        logger.info(f"处理时间范围: {start_time} ~ {end_time}")
        logger.info(f"数据延迟: {minutes_ago}分钟前, 窗口: {window_minutes}分钟")
        
        stats = {
            'processed_records': 0,
            'matched_records': 0,
            'error_count': 0,
            'latency_list': []
        }
        
        try:
            # 连接数据库
            source_conn = mysql.connector.connect(**self.source_config)
            target_conn = mysql.connector.connect(**self.target_config)
            
            source_cursor = source_conn.cursor()
            target_cursor = target_conn.cursor()
            
            # 查询Bitda数据
            logger.info("查询Bitda数据...")
            bitda_query = """
            SELECT timestamp, asks, bids, created_at
            FROM bitda_depth 
            WHERE symbol = 'ETHUSDT'
            AND created_at >= %s 
            AND created_at <= %s
            AND asks IS NOT NULL 
            AND bids IS NOT NULL
            ORDER BY timestamp
            LIMIT %s
            """
            
            source_cursor.execute(bitda_query, (start_time, end_time, batch_size))
            bitda_records = source_cursor.fetchall()
            
            logger.info(f"获取到 {len(bitda_records)} 条Bitda记录")
            
            if len(bitda_records) == 0:
                logger.warning("没有找到Bitda数据")
                return stats
            
            matches_to_insert = []
            
            # 处理每条记录
            for i, record in enumerate(bitda_records):
                try:
                    bitda_timestamp, asks_json, bids_json, created_at = record
                    stats['processed_records'] += 1
                    
                    if i % 100 == 0:
                        logger.info(f"处理进度: {i+1}/{len(bitda_records)}")
                    
                    # 解析深度数据
                    ask_price_1, ask_qty_1, bid_price_1, bid_qty_1 = self.parse_bitda_depth(asks_json, bids_json)
                    
                    if ask_price_1 is None or bid_price_1 is None:
                        stats['error_count'] += 1
                        continue
                    
                    # 查找买一卖一价格完全匹配的Binance数据 (使用首次出现时间)
                    binance_query = """
                    SELECT bid_price, ask_price, bid_qty, ask_qty, event_time
                    FROM binance_bookticker 
                    WHERE symbol = 'ETHUSDT'
                    AND bid_price = %s
                    AND ask_price = %s
                    AND event_time < %s
                    ORDER BY event_time ASC
                    LIMIT 1
                    """
                    
                    source_cursor.execute(binance_query, (bid_price_1, ask_price_1, bitda_timestamp))
                    binance_match = source_cursor.fetchone()
                    
                    if binance_match:
                        binance_bid_price, binance_ask_price, binance_bid_qty, binance_ask_qty, binance_timestamp = binance_match
                        latency = bitda_timestamp - binance_timestamp
                        
                        if self.min_latency <= latency <= self.max_latency:
                            # 计算价差
                            price_spread = ask_price_1 - bid_price_1
                            
                            matches_to_insert.append((
                                bitda_timestamp, binance_timestamp, latency, 'complete',
                                (bid_price_1 + ask_price_1) / 2,  # 中间价
                                (binance_bid_price + binance_ask_price) / 2,  # Binance中间价
                                (bid_qty_1 + ask_qty_1) / 2,  # 平均数量
                                (binance_bid_qty + binance_ask_qty) / 2,  # Binance平均数量
                                price_spread, 1.0000, created_at
                            ))
                            
                            stats['matched_records'] += 1
                            stats['latency_list'].append(latency)
                
                except Exception as e:
                    logger.error(f"处理记录失败: {e}")
                    stats['error_count'] += 1
                    continue
            
            # 批量插入匹配数据
            if matches_to_insert:
                logger.info(f"批量插入 {len(matches_to_insert)} 条匹配记录...")
                
                # 清理指定时间段的旧数据
                target_cursor.execute("""
                    DELETE FROM ethusdt_latency_matches 
                    WHERE created_at >= %s AND created_at <= %s
                """, (start_time, end_time))
                
                # 插入新数据
                insert_query = """
                INSERT INTO ethusdt_latency_matches 
                (bitda_timestamp, binance_timestamp, latency_ms, match_type, 
                 bitda_price, binance_price, bitda_qty, binance_qty, 
                 price_spread, match_quality, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                target_cursor.executemany(insert_query, matches_to_insert)
                target_conn.commit()
                
                logger.info("数据插入完成")
            
            # 更新实时状态
            self.update_realtime_status(target_cursor, target_conn)
            
            source_cursor.close()
            target_cursor.close()
            source_conn.close()
            target_conn.close()
            
        except Exception as e:
            logger.error(f"处理失败: {e}")
            stats['error_count'] += 1
        
        return stats
    
    def update_realtime_status(self, cursor, connection):
        """更新实时状态表"""
        try:
            # 计算最近1小时的统计数据
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_matches,
                    AVG(latency_ms) as avg_latency,
                    MIN(latency_ms) as min_latency,
                    MAX(latency_ms) as max_latency,
                    MAX(created_at) as last_match_time
                FROM ethusdt_latency_matches 
                WHERE created_at >= NOW() - INTERVAL 1 HOUR
            """)
            
            result = cursor.fetchone()
            
            if result:
                total_matches, avg_latency, min_latency, max_latency, last_match_time = result
                
                # 获取最新的延时值
                cursor.execute("""
                    SELECT latency_ms, bitda_price, binance_price 
                    FROM ethusdt_latency_matches 
                    ORDER BY created_at DESC 
                    LIMIT 1
                """)
                latest = cursor.fetchone()
                
                current_latency = latest[0] if latest else None
                last_bitda_price = latest[1] if latest else None
                last_binance_price = latest[2] if latest else None
                
                # 更新实时状态
                cursor.execute("""
                    UPDATE ethusdt_realtime_status SET
                        current_latency_ms = %s,
                        avg_latency_1h = %s,
                        max_latency_1h = %s,
                        min_latency_1h = %s,
                        total_matches_1h = %s,
                        last_match_time = %s,
                        last_bitda_price = %s,
                        last_binance_price = %s,
                        updated_at = NOW()
                    WHERE id = 1
                """, (
                    current_latency, avg_latency, max_latency, min_latency,
                    total_matches, last_match_time, last_bitda_price, last_binance_price
                ))
                
                connection.commit()
                logger.info("实时状态更新完成")
                
        except Exception as e:
            logger.error(f"更新实时状态失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='ETHUSDT延时数据处理器')
    parser.add_argument('--minutes-ago', type=int, default=1, help='处理多少分钟前的数据 (默认1分钟)')
    parser.add_argument('--window-minutes', type=int, default=5, help='处理窗口大小 (默认5分钟)')
    parser.add_argument('--batch-size', type=int, default=500, help='批处理大小 (默认500)')
    
    args = parser.parse_args()
    
    print(f"⚡ ETHUSDT延时数据处理器")
    print(f"处理参数: {args.minutes_ago}分钟前, 窗口{args.window_minutes}分钟, 批量{args.batch_size}")
    print("=" * 60)
    
    processor = RecentLatencyProcessor()
    
    start_time = datetime.now()
    stats = processor.process_recent_latency(
        minutes_ago=args.minutes_ago,
        window_minutes=args.window_minutes,
        batch_size=args.batch_size
    )
    process_duration = (datetime.now() - start_time).total_seconds()
    
    print(f"\n📈 处理结果:")
    print(f"  - 处理记录: {stats['processed_records']}")
    print(f"  - 匹配记录: {stats['matched_records']}")
    print(f"  - 错误数量: {stats['error_count']}")
    print(f"  - 处理耗时: {process_duration:.2f}秒")
    
    if stats['latency_list']:
        latencies = stats['latency_list']
        print(f"  - 平均延时: {sum(latencies)/len(latencies):.2f}ms")
        print(f"  - 延时范围: {min(latencies)}ms ~ {max(latencies)}ms")
        print(f"  - 匹配率: {stats['matched_records']/stats['processed_records']*100:.2f}%")
        print("✅ 延时数据处理完成！")
    else:
        print("⚠️  没有找到有效的延时匹配数据")

if __name__ == "__main__":
    main()
