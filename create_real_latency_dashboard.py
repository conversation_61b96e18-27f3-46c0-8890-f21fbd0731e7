#!/usr/bin/env python3
"""
创建真实ETHUSDT延时分析Grafana面板
使用真实的延时数据库数据
"""

import requests
import json
import mysql.connector
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealLatencyDashboard:
    """真实延时分析仪表板创建器"""
    
    def __init__(self):
        self.grafana_url = "http://localhost:3000"
        self.admin_user = "admin"
        self.admin_pass = "admin"
        self.session = requests.Session()
        self.session.auth = (self.admin_user, self.admin_pass)
        
        self.latency_db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'ethusdt_latency_db'
        }
    
    def get_current_latency_stats(self):
        """获取当前延时统计"""
        try:
            connection = mysql.connector.connect(**self.latency_db_config)
            cursor = connection.cursor()
            
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    AVG(latency_ms) as avg_latency,
                    MIN(latency_ms) as min_latency,
                    MAX(latency_ms) as max_latency,
                    MAX(created_at) as latest_time
                FROM ethusdt_latency_matches
                WHERE created_at >= NOW() - INTERVAL 1 HOUR
            """)
            
            result = cursor.fetchone()
            cursor.close()
            connection.close()
            
            if result and result[0] > 0:
                return {
                    'total': result[0],
                    'avg_latency': round(result[1], 1),
                    'min_latency': result[2],
                    'max_latency': result[3],
                    'latest_time': result[4]
                }
            else:
                return None
                
        except Exception as e:
            logger.error(f"获取延时统计失败: {e}")
            return None
    
    def create_latency_mysql_datasource(self):
        """创建延时数据库MySQL数据源"""
        logger.info("📊 创建延时数据库MySQL数据源...")
        
        # 删除旧的数据源
        try:
            response = self.session.get(f"{self.grafana_url}/api/datasources")
            if response.status_code == 200:
                datasources = response.json()
                for ds in datasources:
                    if ds.get('name') == 'ETHUSDT Latency DB':
                        self.session.delete(f"{self.grafana_url}/api/datasources/{ds['id']}")
                        logger.info("  🗑️  删除旧延时数据源")
        except:
            pass
        
        # 创建新的MySQL数据源
        datasource_config = {
            "name": "ETHUSDT Latency DB",
            "type": "mysql",
            "access": "proxy",
            "url": "localhost:3306",
            "database": "ethusdt_latency_db",
            "user": "root",
            "secureJsonData": {
                "password": "Linuxtest"
            },
            "jsonData": {
                "maxOpenConns": 0,
                "maxIdleConns": 2,
                "connMaxLifetime": 14400
            }
        }
        
        try:
            response = self.session.post(
                f"{self.grafana_url}/api/datasources",
                json=datasource_config,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info("✅ 延时数据库MySQL数据源创建成功")
                return result.get('datasource', {}).get('uid')
            else:
                logger.error(f"❌ 延时数据库MySQL数据源创建失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 延时数据库MySQL数据源创建异常: {e}")
            return None
    
    def create_real_latency_dashboard(self, datasource_uid, stats):
        """创建真实延时分析仪表板"""
        logger.info("📋 创建真实ETHUSDT延时分析仪表板...")
        
        # 使用真实统计数据
        if stats:
            avg_latency = stats['avg_latency']
            total_matches = stats['total']
            min_latency = stats['min_latency']
            max_latency = stats['max_latency']
            status_text = f"✅ 真实数据 ({total_matches}个匹配)"
        else:
            avg_latency = 0
            total_matches = 0
            min_latency = 0
            max_latency = 0
            status_text = "⚠️ 暂无数据"
        
        dashboard_config = {
            "dashboard": {
                "id": None,
                "title": f"⚡ ETHUSDT真实延时分析 - {datetime.now().strftime('%H:%M')}",
                "tags": ["ethusdt", "latency", "realtime", "真实数据"],
                "timezone": "browser",
                "panels": [
                    {
                        "id": 1,
                        "title": f"📊 平均延时\n{avg_latency}ms ({status_text})",
                        "type": "stat",
                        "gridPos": {"h": 6, "w": 6, "x": 0, "y": 0},
                        "targets": [
                            {
                                "datasource": {"type": "mysql", "uid": datasource_uid},
                                "refId": "A",
                                "rawSql": """
                                SELECT 
                                    UNIX_TIMESTAMP(created_at) as time_sec,
                                    AVG(latency_ms) as avg_latency
                                FROM ethusdt_latency_matches 
                                WHERE created_at >= $__timeFrom() 
                                AND created_at <= $__timeTo()
                                GROUP BY UNIX_TIMESTAMP(created_at) DIV 60
                                ORDER BY time_sec
                                """,
                                "format": "time_series"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None},
                                        {"color": "yellow", "value": 300},
                                        {"color": "red", "value": 600}
                                    ]
                                },
                                "unit": "ms",
                                "decimals": 1
                            }
                        },
                        "options": {
                            "colorMode": "background",
                            "graphMode": "area",
                            "justifyMode": "center",
                            "orientation": "auto",
                            "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": False},
                            "textMode": "auto"
                        }
                    },
                    {
                        "id": 2,
                        "title": f"📈 最小延时\n{min_latency}ms",
                        "type": "stat",
                        "gridPos": {"h": 6, "w": 6, "x": 6, "y": 0},
                        "targets": [
                            {
                                "datasource": {"type": "mysql", "uid": datasource_uid},
                                "refId": "A",
                                "rawSql": """
                                SELECT 
                                    UNIX_TIMESTAMP(created_at) as time_sec,
                                    MIN(latency_ms) as min_latency
                                FROM ethusdt_latency_matches 
                                WHERE created_at >= $__timeFrom() 
                                AND created_at <= $__timeTo()
                                GROUP BY UNIX_TIMESTAMP(created_at) DIV 60
                                ORDER BY time_sec
                                """,
                                "format": "time_series"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None},
                                        {"color": "yellow", "value": 200},
                                        {"color": "red", "value": 400}
                                    ]
                                },
                                "unit": "ms",
                                "decimals": 0
                            }
                        },
                        "options": {
                            "colorMode": "background",
                            "graphMode": "area",
                            "justifyMode": "center",
                            "orientation": "auto",
                            "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": False},
                            "textMode": "auto"
                        }
                    },
                    {
                        "id": 3,
                        "title": f"📈 最大延时\n{max_latency}ms",
                        "type": "stat",
                        "gridPos": {"h": 6, "w": 6, "x": 12, "y": 0},
                        "targets": [
                            {
                                "datasource": {"type": "mysql", "uid": datasource_uid},
                                "refId": "A",
                                "rawSql": """
                                SELECT 
                                    UNIX_TIMESTAMP(created_at) as time_sec,
                                    MAX(latency_ms) as max_latency
                                FROM ethusdt_latency_matches 
                                WHERE created_at >= $__timeFrom() 
                                AND created_at <= $__timeTo()
                                GROUP BY UNIX_TIMESTAMP(created_at) DIV 60
                                ORDER BY time_sec
                                """,
                                "format": "time_series"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None},
                                        {"color": "yellow", "value": 800},
                                        {"color": "red", "value": 1200}
                                    ]
                                },
                                "unit": "ms",
                                "decimals": 0
                            }
                        },
                        "options": {
                            "colorMode": "background",
                            "graphMode": "area",
                            "justifyMode": "center",
                            "orientation": "auto",
                            "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": False},
                            "textMode": "auto"
                        }
                    },
                    {
                        "id": 4,
                        "title": f"📊 匹配数量\n{total_matches}个",
                        "type": "stat",
                        "gridPos": {"h": 6, "w": 6, "x": 18, "y": 0},
                        "targets": [
                            {
                                "datasource": {"type": "mysql", "uid": datasource_uid},
                                "refId": "A",
                                "rawSql": """
                                SELECT 
                                    UNIX_TIMESTAMP(created_at) as time_sec,
                                    COUNT(*) as match_count
                                FROM ethusdt_latency_matches 
                                WHERE created_at >= $__timeFrom() 
                                AND created_at <= $__timeTo()
                                GROUP BY UNIX_TIMESTAMP(created_at) DIV 60
                                ORDER BY time_sec
                                """,
                                "format": "time_series"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "red", "value": None},
                                        {"color": "yellow", "value": 10},
                                        {"color": "green", "value": 50}
                                    ]
                                },
                                "unit": "short",
                                "decimals": 0
                            }
                        },
                        "options": {
                            "colorMode": "background",
                            "graphMode": "area",
                            "justifyMode": "center",
                            "orientation": "auto",
                            "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": False},
                            "textMode": "auto"
                        }
                    },
                    {
                        "id": 5,
                        "title": "📈 ETHUSDT延时趋势图（真实数据）",
                        "type": "timeseries",
                        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 6},
                        "targets": [
                            {
                                "datasource": {"type": "mysql", "uid": datasource_uid},
                                "refId": "A",
                                "rawSql": """
                                SELECT 
                                    UNIX_TIMESTAMP(created_at) as time_sec,
                                    latency_ms as '延时(ms)'
                                FROM ethusdt_latency_matches 
                                WHERE created_at >= $__timeFrom() 
                                AND created_at <= $__timeTo()
                                ORDER BY created_at
                                """,
                                "format": "time_series"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "palette-classic"},
                                "custom": {
                                    "axisLabel": "延时 (毫秒)",
                                    "axisPlacement": "auto",
                                    "drawStyle": "line",
                                    "fillOpacity": 20,
                                    "lineWidth": 2,
                                    "pointSize": 3,
                                    "showPoints": "auto"
                                },
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None},
                                        {"color": "yellow", "value": 300},
                                        {"color": "red", "value": 600}
                                    ]
                                },
                                "unit": "ms"
                            }
                        },
                        "options": {
                            "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"},
                            "tooltip": {"mode": "multi", "sort": "none"}
                        }
                    }
                ],
                "time": {"from": "now-1h", "to": "now"},
                "timepicker": {},
                "templating": {"list": []},
                "annotations": {"list": []},
                "refresh": "30s",
                "schemaVersion": 30,
                "version": 1,
                "links": []
            },
            "overwrite": True
        }
        
        try:
            response = self.session.post(
                f"{self.grafana_url}/api/dashboards/db",
                json=dashboard_config,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                dashboard_url = f"{self.grafana_url}{result.get('url', '')}"
                logger.info(f"✅ 真实ETHUSDT延时分析仪表板创建成功")
                logger.info(f"🌐 访问地址: {dashboard_url}")
                return dashboard_url
            else:
                logger.error(f"❌ 仪表板创建失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            logger.error(f"❌ 仪表板创建异常: {e}")
        
        return None
    
    def setup_real_latency_dashboard(self):
        """设置真实延时分析仪表板"""
        logger.info("⚡ 开始配置真实ETHUSDT延时分析仪表板...")
        logger.info("=" * 60)
        
        # 1. 获取当前延时统计
        stats = self.get_current_latency_stats()
        if stats:
            logger.info(f"📊 当前延时统计: 平均{stats['avg_latency']}ms, 范围{stats['min_latency']}-{stats['max_latency']}ms, 总计{stats['total']}个匹配")
        else:
            logger.warning("⚠️  暂无延时数据")
        
        # 2. 创建延时数据库MySQL数据源
        datasource_uid = self.create_latency_mysql_datasource()
        if not datasource_uid:
            logger.error("❌ 延时数据库MySQL数据源创建失败")
            return False
        
        # 3. 创建仪表板
        dashboard_url = self.create_real_latency_dashboard(datasource_uid, stats)
        if not dashboard_url:
            logger.error("❌ 仪表板创建失败")
            return False
        
        logger.info("\n" + "=" * 60)
        logger.info("🎉 真实ETHUSDT延时分析仪表板配置完成！")
        logger.info(f"🌐 仪表板地址: {dashboard_url}")
        logger.info("👤 登录信息: admin/admin")
        logger.info("🔄 数据每30秒自动刷新")
        logger.info("📊 显示真实的延时匹配数据")
        if stats:
            logger.info(f"📈 当前数据: 平均延时{stats['avg_latency']}ms, {stats['total']}个匹配")
        logger.info("=" * 60)
        
        return dashboard_url

def main():
    """主函数"""
    print("⚡ 真实ETHUSDT延时分析Grafana仪表板创建工具")
    print("=" * 50)
    print("功能:")
    print("  - 直接从延时数据库获取真实延时数据")
    print("  - 显示平均、最小、最大延时")
    print("  - 实时延时趋势图")
    print("  - 匹配数量统计")
    print()
    
    dashboard = RealLatencyDashboard()
    dashboard_url = dashboard.setup_real_latency_dashboard()
    
    if dashboard_url:
        print("\n✅ 配置成功！")
        print("📊 现在您可以看到真实的ETHUSDT延时分析数据")
        print("🔄 面板会每30秒自动刷新")
        
        # 自动打开浏览器
        try:
            import webbrowser
            webbrowser.open(dashboard_url)
            print("🌐 浏览器已自动打开")
        except:
            print(f"🌐 请手动访问: {dashboard_url}")
    else:
        print("\n❌ 配置失败，请检查服务状态")

if __name__ == "__main__":
    main()
