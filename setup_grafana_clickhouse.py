#!/usr/bin/env python3
"""
配置Grafana ClickHouse数据源
"""

import requests
import json
import time
from datetime import datetime

def log(message):
    """日志输出"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

# Grafana配置
GRAFANA_URL = "http://localhost:3000"
GRAFANA_USER = "admin"
GRAFANA_PASSWORD = "admin"

def wait_for_grafana():
    """等待Grafana启动"""
    log("⏳ 等待Grafana服务启动...")
    
    for i in range(30):
        try:
            response = requests.get(f"{GRAFANA_URL}/api/health", timeout=5)
            if response.status_code == 200:
                log("✅ Grafana服务已启动")
                return True
        except:
            pass
        
        time.sleep(2)
        log(f"   等待中... ({i+1}/30)")
    
    log("❌ Grafana服务启动超时")
    return False

def create_clickhouse_datasource():
    """创建ClickHouse数据源"""
    log("📊 创建ClickHouse数据源...")
    
    # 数据源配置
    datasource_config = {
        "name": "ClickHouse",
        "type": "grafana-clickhouse-datasource",
        "url": "http://localhost:8123",
        "access": "proxy",
        "basicAuth": False,
        "isDefault": True,
        "jsonData": {
            "username": "default",
            "defaultDatabase": "crypto",
            "port": 8123,
            "server": "localhost",
            "protocol": "http"
        },
        "secureJsonData": {
            "password": "Linuxtest"
        }
    }
    
    try:
        # 删除现有数据源
        response = requests.get(
            f"{GRAFANA_URL}/api/datasources/name/ClickHouse",
            auth=(GRAFANA_USER, GRAFANA_PASSWORD)
        )
        
        if response.status_code == 200:
            datasource_id = response.json()['id']
            delete_response = requests.delete(
                f"{GRAFANA_URL}/api/datasources/{datasource_id}",
                auth=(GRAFANA_USER, GRAFANA_PASSWORD)
            )
            log(f"🗑️ 删除现有ClickHouse数据源: {delete_response.status_code}")
        
        # 创建新数据源
        response = requests.post(
            f"{GRAFANA_URL}/api/datasources",
            json=datasource_config,
            auth=(GRAFANA_USER, GRAFANA_PASSWORD)
        )
        
        if response.status_code == 200:
            log("✅ ClickHouse数据源创建成功")
            return True
        else:
            log(f"❌ ClickHouse数据源创建失败: {response.text}")
            return False
            
    except Exception as e:
        log(f"❌ 创建ClickHouse数据源异常: {e}")
        return False

def create_mysql_datasource():
    """创建MySQL数据源（备份）"""
    log("📊 创建MySQL数据源（备份）...")
    
    datasource_config = {
        "name": "MySQL_Backup",
        "type": "mysql",
        "url": "localhost:3306",
        "access": "proxy",
        "basicAuth": False,
        "isDefault": False,
        "database": "depth_db",
        "user": "root",
        "secureJsonData": {
            "password": "Linuxtest"
        }
    }
    
    try:
        response = requests.post(
            f"{GRAFANA_URL}/api/datasources",
            json=datasource_config,
            auth=(GRAFANA_USER, GRAFANA_PASSWORD)
        )
        
        if response.status_code == 200:
            log("✅ MySQL备份数据源创建成功")
            return True
        else:
            log(f"⚠️ MySQL备份数据源创建失败: {response.text}")
            return False
            
    except Exception as e:
        log(f"⚠️ 创建MySQL备份数据源异常: {e}")
        return False

def test_clickhouse_datasource():
    """测试ClickHouse数据源"""
    log("🔍 测试ClickHouse数据源...")
    
    try:
        # 获取数据源ID
        response = requests.get(
            f"{GRAFANA_URL}/api/datasources/name/ClickHouse",
            auth=(GRAFANA_USER, GRAFANA_PASSWORD)
        )
        
        if response.status_code != 200:
            log("❌ 无法获取ClickHouse数据源")
            return False
        
        datasource_id = response.json()['id']
        
        # 测试数据源连接
        test_response = requests.get(
            f"{GRAFANA_URL}/api/datasources/{datasource_id}/health",
            auth=(GRAFANA_USER, GRAFANA_PASSWORD)
        )
        
        if test_response.status_code == 200:
            log("✅ ClickHouse数据源连接测试成功")
            return True
        else:
            log(f"❌ ClickHouse数据源连接测试失败: {test_response.text}")
            return False
            
    except Exception as e:
        log(f"❌ 测试ClickHouse数据源异常: {e}")
        return False

def create_simple_dashboard():
    """创建简单的ClickHouse仪表板"""
    log("📈 创建ClickHouse测试仪表板...")
    
    dashboard_config = {
        "dashboard": {
            "title": "ClickHouse Crypto Dashboard",
            "tags": ["clickhouse", "crypto"],
            "timezone": "browser",
            "panels": [
                {
                    "id": 1,
                    "title": "Bitda Depth Records",
                    "type": "stat",
                    "targets": [
                        {
                            "datasource": {"type": "grafana-clickhouse-datasource", "uid": "clickhouse"},
                            "rawSql": "SELECT COUNT(*) FROM crypto.bitda_depth",
                            "refId": "A"
                        }
                    ],
                    "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
                },
                {
                    "id": 2,
                    "title": "Binance Depth Records",
                    "type": "stat",
                    "targets": [
                        {
                            "datasource": {"type": "grafana-clickhouse-datasource", "uid": "clickhouse"},
                            "rawSql": "SELECT COUNT(*) FROM crypto.binance_depth_5",
                            "refId": "A"
                        }
                    ],
                    "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
                },
                {
                    "id": 3,
                    "title": "Latest Prices",
                    "type": "table",
                    "targets": [
                        {
                            "datasource": {"type": "grafana-clickhouse-datasource", "uid": "clickhouse"},
                            "rawSql": """
                            SELECT 
                                symbol,
                                bid_price_1 as 买一价,
                                ask_price_1 as 卖一价,
                                bid_qty_1 as 买一量,
                                ask_qty_1 as 卖一量
                            FROM crypto.bitda_depth 
                            WHERE symbol IN ('BTCUSDT', 'ETHUSDT')
                            ORDER BY id DESC 
                            LIMIT 10
                            """,
                            "refId": "A"
                        }
                    ],
                    "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}
                }
            ],
            "time": {"from": "now-1h", "to": "now"},
            "refresh": "30s"
        },
        "overwrite": True
    }
    
    try:
        response = requests.post(
            f"{GRAFANA_URL}/api/dashboards/db",
            json=dashboard_config,
            auth=(GRAFANA_USER, GRAFANA_PASSWORD)
        )
        
        if response.status_code == 200:
            dashboard_url = response.json().get('url', '')
            log(f"✅ ClickHouse测试仪表板创建成功")
            log(f"🔗 仪表板URL: {GRAFANA_URL}{dashboard_url}")
            return True
        else:
            log(f"❌ 仪表板创建失败: {response.text}")
            return False
            
    except Exception as e:
        log(f"❌ 创建仪表板异常: {e}")
        return False

def install_clickhouse_plugin():
    """安装ClickHouse插件"""
    log("🔌 检查ClickHouse插件...")
    
    try:
        # 检查已安装的插件
        response = requests.get(
            f"{GRAFANA_URL}/api/plugins",
            auth=(GRAFANA_USER, GRAFANA_PASSWORD)
        )
        
        if response.status_code == 200:
            plugins = response.json()
            clickhouse_plugin = None
            
            for plugin in plugins:
                if plugin.get('id') == 'grafana-clickhouse-datasource':
                    clickhouse_plugin = plugin
                    break
            
            if clickhouse_plugin:
                if clickhouse_plugin.get('enabled'):
                    log("✅ ClickHouse插件已安装并启用")
                    return True
                else:
                    log("⚠️ ClickHouse插件已安装但未启用")
                    return False
            else:
                log("❌ ClickHouse插件未安装")
                log("💡 请手动安装: grafana-cli plugins install grafana-clickhouse-datasource")
                return False
        else:
            log(f"❌ 无法获取插件列表: {response.text}")
            return False
            
    except Exception as e:
        log(f"❌ 检查插件异常: {e}")
        return False

def main():
    """主函数"""
    log("🚀 开始配置Grafana ClickHouse数据源")
    log("=" * 50)
    
    # 1. 等待Grafana启动
    if not wait_for_grafana():
        log("❌ Grafana服务未启动，请先启动Grafana")
        return
    
    # 2. 检查ClickHouse插件
    if not install_clickhouse_plugin():
        log("❌ ClickHouse插件未安装或未启用")
        log("💡 请运行: sudo grafana-cli plugins install grafana-clickhouse-datasource")
        log("💡 然后重启Grafana: sudo systemctl restart grafana-server")
        return
    
    # 3. 创建ClickHouse数据源
    if not create_clickhouse_datasource():
        log("❌ ClickHouse数据源创建失败")
        return
    
    # 4. 创建MySQL备份数据源
    create_mysql_datasource()
    
    # 5. 测试数据源
    if not test_clickhouse_datasource():
        log("❌ ClickHouse数据源测试失败")
        return
    
    # 6. 创建测试仪表板
    if not create_simple_dashboard():
        log("❌ 测试仪表板创建失败")
        return
    
    log("=" * 50)
    log("🎉 Grafana ClickHouse配置完成！")
    log("📋 下一步:")
    log("  1. 访问Grafana: http://localhost:3000")
    log("  2. 查看ClickHouse测试仪表板")
    log("  3. 验证数据显示正常")
    log("")
    log("🔧 配置信息:")
    log("  - ClickHouse数据源: ClickHouse")
    log("  - MySQL备份数据源: MySQL_Backup")
    log("  - 默认数据源: ClickHouse")

if __name__ == "__main__":
    main()
