#!/usr/bin/env python3
"""
快速迁移脚本 - 使用HTTP接口
"""

import mysql.connector
import requests
import json
from datetime import datetime
import time

# 配置
MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'Linuxtest',
    'database': 'depth_db'
}

CLICKHOUSE_URL = "***************************************/"

def log(message):
    """日志输出"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def ch_execute(query):
    """执行ClickHouse查询"""
    try:
        response = requests.post(CLICKHOUSE_URL, data=query)
        if response.status_code == 200:
            return response.text.strip()
        else:
            log(f"❌ ClickHouse查询失败: {response.text}")
            return None
    except Exception as e:
        log(f"❌ ClickHouse连接异常: {e}")
        return None

def create_database():
    """创建数据库"""
    log("📊 创建ClickHouse数据库...")
    result = ch_execute("CREATE DATABASE IF NOT EXISTS crypto")
    if result is not None:
        log("✅ crypto数据库创建成功")
        return True
    return False

def create_tables():
    """创建表结构"""
    log("🏗️ 创建ClickHouse表结构...")
    
    tables = {
        'bitda_depth': """
            CREATE TABLE IF NOT EXISTS crypto.bitda_depth (
                id UInt64,
                symbol String,
                timestamp UInt64,
                bid_price_1 Decimal(15,2),
                ask_price_1 Decimal(15,2),
                bid_qty_1 Decimal(20,4),
                ask_qty_1 Decimal(20,4),
                bid_price_2 Decimal(15,2),
                ask_price_2 Decimal(15,2),
                bid_qty_2 Decimal(20,4),
                ask_qty_2 Decimal(20,4),
                bid_price_3 Decimal(15,2),
                ask_price_3 Decimal(15,2),
                bid_qty_3 Decimal(20,4),
                ask_qty_3 Decimal(20,4),
                bid_price_4 Decimal(15,2),
                ask_price_4 Decimal(15,2),
                bid_qty_4 Decimal(20,4),
                ask_qty_4 Decimal(20,4),
                bid_price_5 Decimal(15,2),
                ask_price_5 Decimal(15,2),
                bid_qty_5 Decimal(20,4),
                ask_qty_5 Decimal(20,4),
                asks String,
                bids String,
                created_at DateTime DEFAULT now()
            ) ENGINE = MergeTree()
            ORDER BY (symbol, timestamp)
            PARTITION BY toYYYYMM(toDateTime(timestamp/1000))
        """,
        
        'binance_depth_5': """
            CREATE TABLE IF NOT EXISTS crypto.binance_depth_5 (
                id UInt64,
                symbol String,
                event_time UInt64,
                bid_price_1 Decimal(15,2),
                ask_price_1 Decimal(15,2),
                bid_qty_1 Decimal(20,4),
                ask_qty_1 Decimal(20,4),
                bid_price_2 Decimal(15,2),
                ask_price_2 Decimal(15,2),
                bid_qty_2 Decimal(20,4),
                ask_qty_2 Decimal(20,4),
                bid_price_3 Decimal(15,2),
                ask_price_3 Decimal(15,2),
                bid_qty_3 Decimal(20,4),
                ask_qty_3 Decimal(20,4),
                bid_price_4 Decimal(15,2),
                ask_price_4 Decimal(15,2),
                bid_qty_4 Decimal(20,4),
                ask_qty_4 Decimal(20,4),
                bid_price_5 Decimal(15,2),
                ask_price_5 Decimal(15,2),
                bid_qty_5 Decimal(20,4),
                ask_qty_5 Decimal(20,4),
                created_at DateTime DEFAULT now()
            ) ENGINE = MergeTree()
            ORDER BY (symbol, event_time)
            PARTITION BY toYYYYMM(toDateTime(event_time/1000))
        """
    }
    
    for table_name, create_sql in tables.items():
        result = ch_execute(create_sql)
        if result is not None:
            log(f"✅ 表 {table_name} 创建成功")
        else:
            log(f"❌ 表 {table_name} 创建失败")
            return False
    
    return True

def migrate_sample_data():
    """迁移样本数据"""
    log("📦 迁移样本数据...")
    
    mysql_conn = mysql.connector.connect(**MYSQL_CONFIG)
    mysql_cursor = mysql_conn.cursor()
    
    try:
        # 迁移bitda_depth最新1000条数据
        log("   📊 迁移bitda_depth最新1000条数据...")
        mysql_cursor.execute("""
            SELECT id, symbol, timestamp, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
                   bid_price_2, ask_price_2, bid_qty_2, ask_qty_2,
                   bid_price_3, ask_price_3, bid_qty_3, ask_qty_3,
                   bid_price_4, ask_price_4, bid_qty_4, ask_qty_4,
                   bid_price_5, ask_price_5, bid_qty_5, ask_qty_5,
                   COALESCE(asks, '[]'), COALESCE(bids, '[]')
            FROM bitda_depth 
            ORDER BY id DESC 
            LIMIT 1000
        """)
        
        rows = mysql_cursor.fetchall()
        log(f"   📈 获取到 {len(rows)} 条bitda_depth数据")
        
        if rows:
            # 构建INSERT语句
            values = []
            for row in rows:
                # 处理NULL值
                processed_row = []
                for i, val in enumerate(row):
                    if val is None:
                        if i in [24, 25]:  # asks, bids字段
                            processed_row.append("'[]'")
                        else:
                            processed_row.append("0")
                    elif isinstance(val, str):
                        processed_row.append(f"'{val.replace(chr(39), chr(39)+chr(39))}'")  # 转义单引号
                    else:
                        processed_row.append(str(val))
                values.append(f"({','.join(processed_row)})")
            
            insert_query = f"""
                INSERT INTO crypto.bitda_depth 
                (id, symbol, timestamp, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
                 bid_price_2, ask_price_2, bid_qty_2, ask_qty_2,
                 bid_price_3, ask_price_3, bid_qty_3, ask_qty_3,
                 bid_price_4, ask_price_4, bid_qty_4, ask_qty_4,
                 bid_price_5, ask_price_5, bid_qty_5, ask_qty_5,
                 asks, bids) 
                VALUES {','.join(values)}
            """
            
            result = ch_execute(insert_query)
            if result is not None:
                log(f"✅ bitda_depth数据迁移成功: {len(rows)} 条")
            else:
                log(f"❌ bitda_depth数据迁移失败")
        
        # 迁移binance_depth_5最新1000条数据
        log("   📊 迁移binance_depth_5最新1000条数据...")
        mysql_cursor.execute("""
            SELECT id, symbol, event_time, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
                   bid_price_2, ask_price_2, bid_qty_2, ask_qty_2,
                   bid_price_3, ask_price_3, bid_qty_3, ask_qty_3,
                   bid_price_4, ask_price_4, bid_qty_4, ask_qty_4,
                   bid_price_5, ask_price_5, bid_qty_5, ask_qty_5
            FROM binance_depth_5 
            ORDER BY id DESC 
            LIMIT 1000
        """)
        
        rows = mysql_cursor.fetchall()
        log(f"   📈 获取到 {len(rows)} 条binance_depth_5数据")
        
        if rows:
            values = []
            for row in rows:
                processed_row = []
                for val in row:
                    if val is None:
                        processed_row.append("0")
                    elif isinstance(val, str):
                        processed_row.append(f"'{val.replace(chr(39), chr(39)+chr(39))}'")
                    else:
                        processed_row.append(str(val))
                values.append(f"({','.join(processed_row)})")
            
            insert_query = f"""
                INSERT INTO crypto.binance_depth_5 
                (id, symbol, event_time, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
                 bid_price_2, ask_price_2, bid_qty_2, ask_qty_2,
                 bid_price_3, ask_price_3, bid_qty_3, ask_qty_3,
                 bid_price_4, ask_price_4, bid_qty_4, ask_qty_4,
                 bid_price_5, ask_price_5, bid_qty_5, ask_qty_5) 
                VALUES {','.join(values)}
            """
            
            result = ch_execute(insert_query)
            if result is not None:
                log(f"✅ binance_depth_5数据迁移成功: {len(rows)} 条")
            else:
                log(f"❌ binance_depth_5数据迁移失败")
        
        return True
        
    except Exception as e:
        log(f"❌ 数据迁移异常: {e}")
        return False
    finally:
        mysql_cursor.close()
        mysql_conn.close()

def test_queries():
    """测试查询"""
    log("🔍 测试ClickHouse查询...")
    
    # 测试基础查询
    result = ch_execute("SELECT COUNT(*) FROM crypto.bitda_depth")
    if result:
        log(f"✅ bitda_depth记录数: {result}")
    
    result = ch_execute("SELECT COUNT(*) FROM crypto.binance_depth_5")
    if result:
        log(f"✅ binance_depth_5记录数: {result}")
    
    # 测试深度对比查询
    result = ch_execute("""
        SELECT 
            '买一量' as 项目,
            (SELECT ROUND(bid_qty_1, 2) FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1) as Bitda,
            (SELECT ROUND(bid_qty_1, 2) FROM crypto.binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1) as Binance
    """)
    if result:
        log(f"✅ 深度对比查询成功: {result}")

def main():
    """主函数"""
    log("🚀 开始ClickHouse快速迁移")
    log("=" * 50)
    
    start_time = time.time()
    
    # 1. 创建数据库
    if not create_database():
        log("❌ 创建数据库失败")
        return
    
    # 2. 创建表结构
    if not create_tables():
        log("❌ 创建表结构失败")
        return
    
    # 3. 迁移样本数据
    if not migrate_sample_data():
        log("❌ 数据迁移失败")
        return
    
    # 4. 测试查询
    test_queries()
    
    end_time = time.time()
    duration = end_time - start_time
    
    log("=" * 50)
    log(f"🎉 快速迁移完成！耗时: {duration:.1f} 秒")
    log("📋 下一步:")
    log("  1. 修改代码使用ClickHouse")
    log("  2. 配置Grafana ClickHouse数据源")
    log("  3. 测试完整功能")

if __name__ == "__main__":
    main()
