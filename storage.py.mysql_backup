"""
数据存储模块
"""
import json
import shutil
from datetime import datetime, timedelta
from typing import List, Dict, Any
from utils.db import db_manager
from utils.logging import setup_logger
from utils.config import DATA_RETENTION_DAYS, DISK_SPACE_WARNING_GB

logger = setup_logger(__name__)

class DataStorage:
    """数据存储管理器"""

    def __init__(self):
        self._create_tables()

    def _create_tables(self):
        """创建数据库表"""
        tables = {
            'bitda_kline': """
                CREATE TABLE IF NOT EXISTS bitda_kline (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY,
                    symbol VARCHAR(20) NOT NULL,
                    timestamp BIGINT NOT NULL COMMENT '时间戳(秒)',
                    open_price DECIMAL(15,8) NOT NULL COMMENT '开盘价',
                    high_price DECIMAL(15,8) NOT NULL COMMENT '最高价',
                    low_price DECIMAL(15,8) NOT NULL COMMENT '最低价',
                    close_price DECIMAL(15,8) NOT NULL COMMENT '收盘价',
                    volume DECIMAL(20,8) NOT NULL COMMENT '成交数量',
                    amount DECIMAL(25,8) NOT NULL COMMENT '成交金额',
                    period VARCHAR(10) NOT NULL COMMENT '周期',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_symbol_time (symbol, timestamp),
                    INDEX idx_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                COMMENT='Bitda K线数据表'
            """,

            'bitda_trades': """
                CREATE TABLE IF NOT EXISTS bitda_trades (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY,
                    symbol VARCHAR(20) NOT NULL,
                    trade_id BIGINT NOT NULL COMMENT '交易ID',
                    price DECIMAL(15,8) NOT NULL COMMENT '成交价格',
                    amount DECIMAL(20,8) NOT NULL COMMENT '成交数量',
                    trade_time DECIMAL(20,6) NOT NULL COMMENT '成交时间',
                    trade_type VARCHAR(10) NOT NULL COMMENT '成交方向',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_symbol_time (symbol, trade_time),
                    INDEX idx_trade_id (trade_id),
                    INDEX idx_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                COMMENT='Bitda成交数据表'
            """,

            'bitda_depth': """
                CREATE TABLE IF NOT EXISTS bitda_depth (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY,
                    symbol VARCHAR(20) NOT NULL,
                    timestamp BIGINT NOT NULL COMMENT '时间戳',
                    index_price DECIMAL(15,8) DEFAULT NULL COMMENT '指数价格',
                    sign_price DECIMAL(15,8) DEFAULT NULL COMMENT '标记价格',
                    last_price DECIMAL(15,8) DEFAULT NULL COMMENT '最新价格',
                    bid_price_1 DECIMAL(15,2) NOT NULL COMMENT '买一价格',
                    ask_price_1 DECIMAL(15,2) NOT NULL COMMENT '卖一价格',
                    bid_qty_1 DECIMAL(20,4) NOT NULL COMMENT '买一数量',
                    ask_qty_1 DECIMAL(20,4) NOT NULL COMMENT '卖一数量',
                    bid_price_2 DECIMAL(15,2) COMMENT '买二价格',
                    ask_price_2 DECIMAL(15,2) COMMENT '卖二价格',
                    bid_qty_2 DECIMAL(20,4) COMMENT '买二数量',
                    ask_qty_2 DECIMAL(20,4) COMMENT '卖二数量',
                    bid_price_3 DECIMAL(15,2) COMMENT '买三价格',
                    ask_price_3 DECIMAL(15,2) COMMENT '卖三价格',
                    bid_qty_3 DECIMAL(20,4) COMMENT '买三数量',
                    ask_qty_3 DECIMAL(20,4) COMMENT '卖三数量',
                    bid_price_4 DECIMAL(15,2) COMMENT '买四价格',
                    ask_price_4 DECIMAL(15,2) COMMENT '卖四价格',
                    bid_qty_4 DECIMAL(20,4) COMMENT '买四数量',
                    ask_qty_4 DECIMAL(20,4) COMMENT '卖四数量',
                    bid_price_5 DECIMAL(15,2) COMMENT '买五价格',
                    ask_price_5 DECIMAL(15,2) COMMENT '卖五价格',
                    bid_qty_5 DECIMAL(20,4) COMMENT '买五数量',
                    ask_qty_5 DECIMAL(20,4) COMMENT '卖五数量',
                    asks JSON COMMENT '卖单深度',
                    bids JSON COMMENT '买单深度',
                    merge_level INT DEFAULT 0 COMMENT '合并级别',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_symbol_time (symbol, timestamp),
                    INDEX idx_created_at (created_at),
                    INDEX idx_price_match (symbol, bid_price_1, ask_price_1, timestamp),
                    INDEX idx_depth_5 (symbol, bid_price_1, ask_price_1, bid_price_2, ask_price_2)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                COMMENT='Bitda深度数据表'
            """,

            'bitda_ticker': """
                CREATE TABLE IF NOT EXISTS bitda_ticker (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY,
                    symbol VARCHAR(20) NOT NULL,
                    open_price DECIMAL(15,8) NOT NULL COMMENT '开盘价',
                    high_price DECIMAL(15,8) NOT NULL COMMENT '最高价',
                    low_price DECIMAL(15,8) NOT NULL COMMENT '最低价',
                    last_price DECIMAL(15,8) NOT NULL COMMENT '最新价',
                    volume DECIMAL(25,8) NOT NULL COMMENT '成交量',
                    amount DECIMAL(25,8) NOT NULL COMMENT '成交金额',
                    change_rate DECIMAL(10,8) NOT NULL COMMENT '涨跌幅',
                    funding_time INT DEFAULT NULL COMMENT '资金费率时间',
                    position_amount DECIMAL(25,8) DEFAULT NULL COMMENT '持仓量',
                    funding_rate_last DECIMAL(10,8) DEFAULT NULL COMMENT '当前资金费率',
                    funding_rate_next DECIMAL(10,8) DEFAULT NULL COMMENT '下一个资金费率',
                    funding_rate_predict DECIMAL(10,8) DEFAULT NULL COMMENT '预测资金费率',
                    insurance DECIMAL(25,8) DEFAULT NULL COMMENT '保险基金',
                    sign_price DECIMAL(15,8) DEFAULT NULL COMMENT '标记价格',
                    index_price DECIMAL(15,8) DEFAULT NULL COMMENT '指数价格',
                    sell_total DECIMAL(25,8) DEFAULT NULL COMMENT '卖盘总量',
                    buy_total DECIMAL(25,8) DEFAULT NULL COMMENT '买盘总量',
                    period INT DEFAULT 86400 COMMENT '周期',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_symbol_time (symbol, created_at),
                    INDEX idx_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                COMMENT='Bitda行情数据表'
            """
        }

        # 继续创建Binance相关表
        binance_tables = {
            'binance_depth_5': """
                CREATE TABLE IF NOT EXISTS binance_depth_5 (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY,
                    symbol VARCHAR(20) NOT NULL,
                    event_time BIGINT NOT NULL COMMENT '事件时间',
                    trade_time BIGINT NOT NULL COMMENT '交易时间',
                    first_update_id BIGINT NOT NULL COMMENT '从上次推送至今新增的第一个update Id',
                    last_update_id BIGINT NOT NULL COMMENT '从上次推送至今新增的最后一个update Id',
                    prev_update_id BIGINT NOT NULL COMMENT '上次推送的最后一个update Id',
                    bid_price_1 DECIMAL(15,2) COMMENT '买一价格',
                    ask_price_1 DECIMAL(15,2) COMMENT '卖一价格',
                    bid_qty_1 DECIMAL(20,4) COMMENT '买一数量',
                    ask_qty_1 DECIMAL(20,4) COMMENT '卖一数量',
                    bid_price_2 DECIMAL(15,2) COMMENT '买二价格',
                    ask_price_2 DECIMAL(15,2) COMMENT '卖二价格',
                    bid_qty_2 DECIMAL(20,4) COMMENT '买二数量',
                    ask_qty_2 DECIMAL(20,4) COMMENT '卖二数量',
                    bid_price_3 DECIMAL(15,2) COMMENT '买三价格',
                    ask_price_3 DECIMAL(15,2) COMMENT '卖三价格',
                    bid_qty_3 DECIMAL(20,4) COMMENT '买三数量',
                    ask_qty_3 DECIMAL(20,4) COMMENT '卖三数量',
                    bid_price_4 DECIMAL(15,2) COMMENT '买四价格',
                    ask_price_4 DECIMAL(15,2) COMMENT '卖四价格',
                    bid_qty_4 DECIMAL(20,4) COMMENT '买四数量',
                    ask_qty_4 DECIMAL(20,4) COMMENT '卖四数量',
                    bid_price_5 DECIMAL(15,2) COMMENT '买五价格',
                    ask_price_5 DECIMAL(15,2) COMMENT '卖五价格',
                    bid_qty_5 DECIMAL(20,4) COMMENT '买五数量',
                    ask_qty_5 DECIMAL(20,4) COMMENT '卖五数量',
                    bids JSON COMMENT '买单深度(5档)',
                    asks JSON COMMENT '卖单深度(5档)',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_symbol_time (symbol, event_time),
                    INDEX idx_created_at (created_at),
                    INDEX idx_binance_depth_match (symbol, bid_price_1, ask_price_1, event_time)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                COMMENT='Binance 5档深度数据表'
            """,

            'binance_bookticker': """
                CREATE TABLE IF NOT EXISTS binance_bookticker (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY,
                    symbol VARCHAR(20) NOT NULL,
                    update_id BIGINT NOT NULL COMMENT '更新ID',
                    bid_price DECIMAL(15,8) NOT NULL COMMENT '最优买单价',
                    bid_qty DECIMAL(20,8) NOT NULL COMMENT '最优买单量',
                    ask_price DECIMAL(15,8) NOT NULL COMMENT '最优卖单价',
                    ask_qty DECIMAL(20,8) NOT NULL COMMENT '最优卖单量',
                    event_time BIGINT NOT NULL COMMENT '事件时间',
                    trade_time BIGINT NOT NULL COMMENT '交易时间',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_symbol_time (symbol, event_time),
                    INDEX idx_update_id (update_id),
                    INDEX idx_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                COMMENT='Binance BookTicker数据表'
            """
        }

        # 合并所有表
        all_tables = {**tables, **binance_tables}

        for table_name, sql in all_tables.items():
            try:
                db_manager.execute_query(sql)
                logger.info(f"表 {table_name} 创建成功或已存在")
            except Exception as e:
                logger.error(f"创建表 {table_name} 失败: {e}")
                raise

    def save_bitda_kline(self, data_list: List[Dict[str, Any]]):
        """保存Bitda K线数据"""
        if not data_list:
            return

        query = """
        INSERT INTO bitda_kline (
            symbol, timestamp, open_price, high_price, low_price,
            close_price, volume, amount, period
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

        values = []
        for data in data_list:
            values.append((
                data['symbol'],
                data['timestamp'],
                data['open_price'],
                data['high_price'],
                data['low_price'],
                data['close_price'],
                data['volume'],
                data['amount'],
                data['period']
            ))

        try:
            db_manager.execute_many(query, values)
            logger.info(f"成功保存 {len(values)} 条Bitda K线数据")
        except Exception as e:
            logger.error(f"保存Bitda K线数据失败: {e}")
            raise

    def save_bitda_trades(self, data_list: List[Dict[str, Any]]):
        """保存Bitda成交数据"""
        if not data_list:
            return

        query = """
        INSERT INTO bitda_trades (
            symbol, trade_id, price, amount, trade_time, trade_type
        ) VALUES (%s, %s, %s, %s, %s, %s)
        """

        values = []
        for data in data_list:
            values.append((
                data['symbol'],
                data['trade_id'],
                data['price'],
                data['amount'],
                data['trade_time'],
                data['trade_type']
            ))

        try:
            db_manager.execute_many(query, values)
            logger.info(f"成功保存 {len(values)} 条Bitda成交数据")
        except Exception as e:
            logger.error(f"保存Bitda成交数据失败: {e}")
            raise

    def save_bitda_depth(self, data_list: List[Dict[str, Any]]):
        """保存Bitda深度数据 - 优化版，提取买一卖一价格"""
        if not data_list:
            return

        query = """
        INSERT INTO bitda_depth (
            symbol, timestamp, index_price, sign_price, last_price,
            bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
            bid_price_2, ask_price_2, bid_qty_2, ask_qty_2,
            bid_price_3, ask_price_3, bid_qty_3, ask_qty_3,
            bid_price_4, ask_price_4, bid_qty_4, ask_qty_4,
            bid_price_5, ask_price_5, bid_qty_5, ask_qty_5,
            asks, bids, merge_level
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

        values = []
        for data in data_list:
            # 提取买一到买五价格和数量
            try:
                asks = data['asks']
                bids = data['bids']

                if asks and bids:
                    # 排序获取买一到买五
                    sorted_bids = sorted(bids, key=lambda x: float(x[0]), reverse=True)  # 价格从高到低
                    sorted_asks = sorted(asks, key=lambda x: float(x[0]))  # 价格从低到高

                    # 提取买一到买五
                    bid_prices = [None] * 5
                    bid_qtys = [None] * 5
                    ask_prices = [None] * 5
                    ask_qtys = [None] * 5

                    # 买单（取前5档）
                    for i in range(min(5, len(sorted_bids))):
                        bid_prices[i] = float(sorted_bids[i][0])
                        bid_qtys[i] = float(sorted_bids[i][1])

                    # 卖单（取前5档）
                    for i in range(min(5, len(sorted_asks))):
                        ask_prices[i] = float(sorted_asks[i][0])
                        ask_qtys[i] = float(sorted_asks[i][1])

                    # 至少要有买一卖一
                    if bid_prices[0] is None or ask_prices[0] is None:
                        logger.warning(f"缺少买一卖一数据，跳过记录: {data.get('symbol', 'Unknown')}")
                        continue

                else:
                    # 如果没有深度数据，跳过这条记录
                    logger.warning(f"跳过无深度数据的记录: {data.get('symbol', 'Unknown')}")
                    continue

            except (ValueError, TypeError, IndexError) as e:
                logger.warning(f"解析深度数据失败，跳过记录: {e}")
                continue

            values.append((
                data['symbol'],
                data['timestamp'],
                data.get('index_price'),
                data.get('sign_price'),
                data.get('last_price'),
                bid_prices[0], ask_prices[0], bid_qtys[0], ask_qtys[0],  # 买一卖一
                bid_prices[1], ask_prices[1], bid_qtys[1], ask_qtys[1],  # 买二卖二
                bid_prices[2], ask_prices[2], bid_qtys[2], ask_qtys[2],  # 买三卖三
                bid_prices[3], ask_prices[3], bid_qtys[3], ask_qtys[3],  # 买四卖四
                bid_prices[4], ask_prices[4], bid_qtys[4], ask_qtys[4],  # 买五卖五
                json.dumps(data['asks']),
                json.dumps(data['bids']),
                data.get('merge_level', 0)
            ))

        if not values:
            logger.warning("没有有效的深度数据可保存")
            return

        try:
            db_manager.execute_many(query, values)
            logger.info(f"成功保存 {len(values)} 条Bitda深度数据（含买一到买五价格）")
        except Exception as e:
            logger.error(f"保存Bitda深度数据失败: {e}")
            raise

    def save_bitda_ticker(self, data_list: List[Dict[str, Any]]):
        """保存Bitda行情数据"""
        if not data_list:
            return

        query = """
        INSERT INTO bitda_ticker (
            symbol, open_price, high_price, low_price, last_price, volume, amount,
            change_rate, funding_time, position_amount, funding_rate_last,
            funding_rate_next, funding_rate_predict, insurance, sign_price,
            index_price, sell_total, buy_total, period
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

        values = []
        for data in data_list:
            values.append((
                data['symbol'],
                data['open_price'],
                data['high_price'],
                data['low_price'],
                data['last_price'],
                data['volume'],
                data['amount'],
                data['change_rate'],
                data.get('funding_time'),
                data.get('position_amount'),
                data.get('funding_rate_last'),
                data.get('funding_rate_next'),
                data.get('funding_rate_predict'),
                data.get('insurance'),
                data.get('sign_price'),
                data.get('index_price'),
                data.get('sell_total'),
                data.get('buy_total'),
                data.get('period', 86400)
            ))

        try:
            db_manager.execute_many(query, values)
            logger.info(f"成功保存 {len(values)} 条Bitda行情数据")
        except Exception as e:
            logger.error(f"保存Bitda行情数据失败: {e}")
            raise

    def save_binance_depth_5(self, data_list: List[Dict[str, Any]]):
        """保存Binance 5档深度数据 - 优化版，提取5档价格"""
        if not data_list:
            return

        query = """
        INSERT INTO binance_depth_5 (
            symbol, event_time, trade_time, first_update_id, last_update_id,
            prev_update_id,
            bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
            bid_price_2, ask_price_2, bid_qty_2, ask_qty_2,
            bid_price_3, ask_price_3, bid_qty_3, ask_qty_3,
            bid_price_4, ask_price_4, bid_qty_4, ask_qty_4,
            bid_price_5, ask_price_5, bid_qty_5, ask_qty_5,
            bids, asks
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

        values = []
        for data in data_list:
            # 提取5档深度价格和数量
            try:
                bids = data['bids']
                asks = data['asks']

                if bids and asks:
                    # 提取5档深度
                    bid_prices = [None] * 5
                    bid_qtys = [None] * 5
                    ask_prices = [None] * 5
                    ask_qtys = [None] * 5

                    # 买单（已按价格排序，取前5档）
                    for i in range(min(5, len(bids))):
                        bid_prices[i] = float(bids[i][0])
                        bid_qtys[i] = float(bids[i][1])

                    # 卖单（已按价格排序，取前5档）
                    for i in range(min(5, len(asks))):
                        ask_prices[i] = float(asks[i][0])
                        ask_qtys[i] = float(asks[i][1])
                else:
                    # 如果没有深度数据，跳过这条记录
                    logger.warning(f"跳过无深度数据的Binance记录: {data.get('symbol', 'Unknown')}")
                    continue

            except (ValueError, TypeError, IndexError) as e:
                logger.warning(f"解析Binance深度数据失败，跳过记录: {e}")
                continue

            values.append((
                data['symbol'],
                data['event_time'],
                data['trade_time'],
                data['first_update_id'],
                data['last_update_id'],
                data['prev_update_id'],
                bid_prices[0], ask_prices[0], bid_qtys[0], ask_qtys[0],  # 买一卖一
                bid_prices[1], ask_prices[1], bid_qtys[1], ask_qtys[1],  # 买二卖二
                bid_prices[2], ask_prices[2], bid_qtys[2], ask_qtys[2],  # 买三卖三
                bid_prices[3], ask_prices[3], bid_qtys[3], ask_qtys[3],  # 买四卖四
                bid_prices[4], ask_prices[4], bid_qtys[4], ask_qtys[4],  # 买五卖五
                json.dumps(data['bids']),
                json.dumps(data['asks'])
            ))

        if not values:
            logger.warning("没有有效的Binance深度数据可保存")
            return

        try:
            db_manager.execute_many(query, values)
            logger.info(f"成功保存 {len(values)} 条Binance 5档深度数据（含5档价格）")
        except Exception as e:
            logger.error(f"保存Binance 5档深度数据失败: {e}")
            raise

    def save_binance_bookticker(self, data_list: List[Dict[str, Any]]):
        """保存Binance BookTicker数据"""
        if not data_list:
            return

        query = """
        INSERT INTO binance_bookticker (
            symbol, update_id, bid_price, bid_qty, ask_price, ask_qty,
            event_time, trade_time
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """

        values = []
        for data in data_list:
            values.append((
                data['symbol'],
                data['update_id'],
                data['bid_price'],
                data['bid_qty'],
                data['ask_price'],
                data['ask_qty'],
                data['event_time'],
                data['trade_time']
            ))

        try:
            db_manager.execute_many(query, values)
            logger.info(f"成功保存 {len(values)} 条Binance BookTicker数据")
        except Exception as e:
            logger.error(f"保存Binance BookTicker数据失败: {e}")
            raise

    def cleanup_old_data(self):
        """清理超过保留期的旧数据"""
        cutoff_date = datetime.now() - timedelta(days=DATA_RETENTION_DAYS)

        tables = [
            'bitda_kline', 'bitda_trades', 'bitda_depth', 'bitda_ticker',
            'binance_depth_5', 'binance_bookticker'
        ]

        total_deleted = 0
        for table in tables:
            try:
                query = f"DELETE FROM {table} WHERE created_at < %s"
                deleted_count = db_manager.execute_query(query, (cutoff_date,))
                total_deleted += deleted_count
                if deleted_count > 0:
                    logger.info(f"从表 {table} 删除了 {deleted_count} 条旧数据")
            except Exception as e:
                logger.error(f"清理表 {table} 的旧数据失败: {e}")

        if total_deleted > 0:
            logger.info(f"总共清理了 {total_deleted} 条旧数据")

        return total_deleted

    def check_disk_space(self) -> bool:
        """检查磁盘空间，返回是否有足够空间"""
        try:
            total, used, free = shutil.disk_usage("/")
            free_gb = free // (1024**3)

            logger.info(f"磁盘剩余空间: {free_gb} GB")

            if free_gb <= DISK_SPACE_WARNING_GB:
                logger.warning(f"磁盘空间不足！剩余 {free_gb} GB，低于警告阈值 {DISK_SPACE_WARNING_GB} GB")
                return False

            return True
        except Exception as e:
            logger.error(f"检查磁盘空间失败: {e}")
            return True  # 检查失败时假设空间充足，避免误停止

# 全局存储管理器实例
storage = DataStorage()
