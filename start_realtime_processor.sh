#!/bin/bash

# ETHUSDT实时延时处理器启动脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖..."
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装"
        exit 1
    fi
    
    # 检查MySQL连接
    python3 -c "
import mysql.connector
try:
    # 检查源数据库
    conn = mysql.connector.connect(
        host='localhost',
        user='root',
        password='Linuxtest',
        database='depth_db'
    )
    conn.close()
    print('✅ 源数据库连接正常')
    
    # 检查目标数据库
    conn = mysql.connector.connect(
        host='localhost',
        user='root',
        password='Linuxtest',
        database='ethusdt_latency_db'
    )
    conn.close()
    print('✅ 目标数据库连接正常')
    
except Exception as e:
    print(f'❌ 数据库连接失败: {e}')
    exit(1)
" || exit 1
    
    # 检查必要的Python包
    python3 -c "
import mysql.connector, json, schedule
print('✅ Python依赖包正常')
" 2>/dev/null || {
        print_error "缺少必要的Python包，请安装: pip install mysql-connector-python schedule"
        exit 1
    }
}

# 检查数据
check_data() {
    print_info "检查数据状态..."
    
    python3 -c "
import mysql.connector
from datetime import datetime, timedelta

# 连接源数据库
conn = mysql.connector.connect(
    host='localhost',
    user='root',
    password='Linuxtest',
    database='depth_db'
)
cursor = conn.cursor()

# 检查最近的Bitda数据
cursor.execute('''
    SELECT COUNT(*) FROM bitda_depth 
    WHERE symbol = 'ETHUSDT' 
    AND created_at >= NOW() - INTERVAL 10 MINUTE
''')
bitda_count = cursor.fetchone()[0]

# 检查最近的Binance数据
cursor.execute('''
    SELECT COUNT(*) FROM binance_bookticker 
    WHERE symbol = 'ETHUSDT' 
    AND created_at >= NOW() - INTERVAL 10 MINUTE
''')
binance_count = cursor.fetchone()[0]

cursor.close()
conn.close()

print(f'📊 最近10分钟数据统计:')
print(f'  - Bitda ETHUSDT: {bitda_count} 条')
print(f'  - Binance ETHUSDT: {binance_count} 条')

if bitda_count == 0:
    print('⚠️  警告: 最近无Bitda数据')
if binance_count == 0:
    print('⚠️  警告: 最近无Binance数据')
"
}

# 停止现有进程
stop_existing() {
    print_info "检查并停止现有的延时处理进程..."
    
    # 查找相关进程
    PIDS=$(pgrep -f "ethusdt.*processor" 2>/dev/null || true)
    
    if [ -n "$PIDS" ]; then
        print_warning "发现现有进程: $PIDS"
        echo "是否停止现有进程? (y/N)"
        read -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            kill $PIDS
            sleep 2
            print_success "已停止现有进程"
        else
            print_warning "保留现有进程，可能会有冲突"
        fi
    else
        print_info "未发现现有进程"
    fi
}

# 启动处理器
start_processor() {
    print_info "启动ETHUSDT实时延时处理器..."
    
    # 检查脚本文件
    if [ ! -f "ethusdt_realtime_processor.py" ]; then
        print_error "处理器脚本不存在: ethusdt_realtime_processor.py"
        exit 1
    fi
    
    print_success "🚀 启动处理器..."
    print_info "日志文件: ethusdt_realtime_processor.log"
    print_info "按 Ctrl+C 停止处理器"
    echo ""
    
    # 启动处理器
    python3 ethusdt_realtime_processor.py
}

# 显示帮助
show_help() {
    echo "ETHUSDT实时延时处理器启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --check-only    仅检查环境和数据，不启动处理器"
    echo "  --force         强制启动，跳过确认"
    echo "  --help          显示帮助信息"
    echo ""
    echo "功能:"
    echo "  - 每分钟处理1分钟前的ETHUSDT延时数据"
    echo "  - 高效批量处理，避免实时数据不稳定"
    echo "  - 自动更新Grafana数据源"
    echo "  - 完善的错误处理和日志记录"
}

# 主函数
main() {
    echo "🚀 ETHUSDT实时延时处理器"
    echo "=" * 50
    
    case "$1" in
        --check-only)
            check_dependencies
            check_data
            print_success "环境检查完成"
            ;;
        --force)
            check_dependencies
            start_processor
            ;;
        --help|-h)
            show_help
            ;;
        "")
            check_dependencies
            check_data
            stop_existing
            echo ""
            print_info "准备启动处理器..."
            echo "继续启动? (Y/n)"
            read -r response
            if [[ "$response" =~ ^[Nn]$ ]]; then
                print_info "已取消启动"
                exit 0
            fi
            start_processor
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
