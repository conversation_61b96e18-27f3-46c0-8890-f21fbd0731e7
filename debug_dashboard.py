#!/usr/bin/env python3
"""
调试仪表板问题 - 一次性解决
"""

import mysql.connector
import requests
import json

def test_database_connection():
    """测试数据库连接和数据"""
    print("🔍 测试数据库连接...")
    
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'Linuxtest',
        'database': 'depth_db'
    }
    
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        # 检查表是否存在
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        print(f"   ✅ 数据库连接成功，表: {[t[0] for t in tables]}")
        
        # 检查bitda_depth表数据
        cursor.execute("SELECT COUNT(*) FROM bitda_depth WHERE symbol = 'BTCUSDT'")
        btc_count = cursor.fetchone()[0]
        print(f"   📊 BTCUSDT数据条数: {btc_count}")
        
        cursor.execute("SELECT COUNT(*) FROM binance_depth_5 WHERE symbol = 'BTCUSDT'")
        btc_binance_count = cursor.fetchone()[0]
        print(f"   📊 BTCUSDT Binance数据条数: {btc_binance_count}")
        
        # 检查最新数据
        cursor.execute("""
            SELECT timestamp, bid_qty_1, ask_qty_1 
            FROM bitda_depth 
            WHERE symbol = 'BTCUSDT' AND bid_price_1 IS NOT NULL 
            ORDER BY timestamp DESC 
            LIMIT 1
        """)
        latest = cursor.fetchone()
        if latest:
            print(f"   📈 最新BTCUSDT数据: 时间戳={latest[0]}, 买一量={latest[1]}, 卖一量={latest[2]}")
        else:
            print(f"   ❌ 无BTCUSDT数据")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 数据库连接失败: {e}")
        return False

def test_grafana_datasource():
    """测试Grafana数据源"""
    print("\n🔍 测试Grafana数据源...")
    
    grafana_url = "http://localhost:3000"
    session = requests.Session()
    session.auth = ("admin", "admin")
    
    try:
        # 获取数据源列表
        response = session.get(f"{grafana_url}/api/datasources")
        if response.status_code == 200:
            datasources = response.json()
            print(f"   ✅ 数据源列表:")
            for ds in datasources:
                print(f"      - {ds['name']} (type: {ds['type']}, uid: {ds['uid']})")
            
            # 找到我们的数据源
            our_ds = None
            for ds in datasources:
                if ds['name'] == 'DepthDB_Dynamic':
                    our_ds = ds
                    break
            
            if our_ds:
                print(f"   ✅ 找到数据源: {our_ds['name']}")
                
                # 测试数据源连接
                test_response = session.get(f"{grafana_url}/api/datasources/{our_ds['id']}/health")
                if test_response.status_code == 200:
                    print(f"   ✅ 数据源连接正常")
                else:
                    print(f"   ❌ 数据源连接失败: {test_response.status_code}")
                    print(f"   响应: {test_response.text}")
                
                return our_ds['uid']
            else:
                print(f"   ❌ 未找到DepthDB_Dynamic数据源")
                return None
        else:
            print(f"   ❌ 获取数据源失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ 测试Grafana数据源失败: {e}")
        return None

def create_working_dashboard(datasource_uid):
    """创建能工作的仪表板"""
    print(f"\n🎨 创建能工作的仪表板...")
    
    grafana_url = "http://localhost:3000"
    session = requests.Session()
    session.auth = ("admin", "admin")
    
    # 简化的仪表板配置
    dashboard_config = {
        "dashboard": {
            "id": None,
            "title": "✅ 工作的深度对比仪表板",
            "tags": ["working", "depth"],
            "timezone": "browser",
            "panels": [
                # 简单的数据计数面板
                {
                    "id": 1,
                    "title": "📊 数据统计",
                    "type": "stat",
                    "gridPos": {"h": 6, "w": 12, "x": 0, "y": 0},
                    "targets": [
                        {
                            "datasource": {
                                "type": "mysql",
                                "uid": datasource_uid
                            },
                            "format": "table",
                            "rawSql": "SELECT COUNT(*) as BTCUSDT数据条数 FROM bitda_depth WHERE symbol = 'BTCUSDT'",
                            "refId": "A"
                        }
                    ],
                    "fieldConfig": {
                        "defaults": {
                            "color": {"mode": "thresholds"},
                            "thresholds": {"steps": [{"color": "green", "value": None}]}
                        }
                    },
                    "options": {
                        "reduceOptions": {"values": False, "calcs": ["lastNotNull"], "fields": ""},
                        "orientation": "auto",
                        "textMode": "auto",
                        "colorMode": "background"
                    }
                },
                
                # 最新数据时间
                {
                    "id": 2,
                    "title": "⏰ 最新数据时间",
                    "type": "stat",
                    "gridPos": {"h": 6, "w": 12, "x": 12, "y": 0},
                    "targets": [
                        {
                            "datasource": {
                                "type": "mysql",
                                "uid": datasource_uid
                            },
                            "format": "table",
                            "rawSql": "SELECT FROM_UNIXTIME(MAX(timestamp)/1000) as 最新时间 FROM bitda_depth WHERE symbol = 'BTCUSDT'",
                            "refId": "A"
                        }
                    ],
                    "fieldConfig": {
                        "defaults": {
                            "color": {"mode": "thresholds"},
                            "thresholds": {"steps": [{"color": "blue", "value": None}]}
                        }
                    },
                    "options": {
                        "reduceOptions": {"values": False, "calcs": ["lastNotNull"], "fields": ""},
                        "orientation": "auto",
                        "textMode": "auto",
                        "colorMode": "background"
                    }
                },
                
                # 简单的深度对比表格
                {
                    "id": 3,
                    "title": "📊 BTCUSDT 最新深度数据",
                    "type": "table",
                    "gridPos": {"h": 8, "w": 24, "x": 0, "y": 6},
                    "targets": [
                        {
                            "datasource": {
                                "type": "mysql",
                                "uid": datasource_uid
                            },
                            "format": "table",
                            "rawSql": """
                                SELECT 
                                    FROM_UNIXTIME(timestamp/1000) as 时间,
                                    bid_qty_1 as 买一量,
                                    ask_qty_1 as 卖一量,
                                    ROUND(bid_qty_1 + ask_qty_1, 2) as 总量
                                FROM bitda_depth 
                                WHERE symbol = 'BTCUSDT' 
                                    AND bid_price_1 IS NOT NULL 
                                ORDER BY timestamp DESC 
                                LIMIT 5
                            """,
                            "refId": "A"
                        }
                    ],
                    "fieldConfig": {
                        "defaults": {
                            "custom": {"align": "center", "displayMode": "basic"}
                        }
                    }
                }
            ],
            "time": {"from": "now-1h", "to": "now"},
            "refresh": "5s",
            "schemaVersion": 30,
            "version": 1
        },
        "overwrite": True
    }
    
    try:
        response = session.post(
            f"{grafana_url}/api/dashboards/db",
            json=dashboard_config,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            dashboard_url = f"{grafana_url}{result['url']}"
            print(f"   ✅ 工作的仪表板创建成功")
            print(f"   🌐 访问地址: {dashboard_url}")
            return dashboard_url
        else:
            print(f"   ❌ 创建仪表板失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 创建仪表板异常: {e}")
        return None

def main():
    """主函数 - 一次性解决问题"""
    print("🔧 一次性解决仪表板问题")
    print("=" * 50)
    
    # 1. 测试数据库
    if not test_database_connection():
        print("❌ 数据库问题，无法继续")
        return
    
    # 2. 测试Grafana数据源
    datasource_uid = test_grafana_datasource()
    if not datasource_uid:
        print("❌ Grafana数据源问题，无法继续")
        return
    
    # 3. 创建能工作的仪表板
    dashboard_url = create_working_dashboard(datasource_uid)
    
    if dashboard_url:
        print(f"\n🎉 问题解决！")
        print(f"🌐 新的工作仪表板: {dashboard_url}")
        print(f"⏰ 5秒自动刷新")
        print(f"📊 显示真实数据")
        
        # 自动打开浏览器
        try:
            import webbrowser
            webbrowser.open(dashboard_url)
            print(f"🌐 已自动打开浏览器")
        except:
            pass
    else:
        print(f"\n❌ 仍有问题，需要进一步调试")

if __name__ == "__main__":
    main()
