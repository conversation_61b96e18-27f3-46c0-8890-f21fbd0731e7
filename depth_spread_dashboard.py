#!/usr/bin/env python3
"""
创建深度价差对比分析的炫酷Grafana仪表板
"""

import requests
import json
import mysql.connector
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DepthSpreadDashboard:
    """深度价差对比仪表板创建器"""
    
    def __init__(self):
        self.grafana_url = "http://localhost:3000"
        self.admin_user = "admin"
        self.admin_pass = "admin"
        self.session = requests.Session()
        self.session.auth = (self.admin_user, self.admin_pass)
        
        self.analysis_db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'depth_spread_analysis'
        }
    
    def get_latest_analysis_data(self):
        """获取最新的分析数据"""
        logger.info("📊 获取最新分析数据...")
        
        try:
            connection = mysql.connector.connect(**self.analysis_db_config)
            cursor = connection.cursor()
            
            data = {}
            
            for symbol in ['BTCUSDT', 'ETHUSDT']:
                # 获取最新的深度对比数据
                cursor.execute("""
                    SELECT
                        bid1_ratio, ask1_ratio, bid_ask1_ratio, bid_ask2_ratio, bid_ask5_ratio,
                        bitda_bid1_qty, bitda_ask1_qty, bitda_bid2_qty, bitda_ask2_qty,
                        bitda_bid5_total, bitda_ask5_total,
                        binance_bid1_qty, binance_ask1_qty, binance_bid2_qty, binance_ask2_qty,
                        binance_bid5_total, binance_ask5_total,
                        analysis_time, time_diff_ms
                    FROM depth_comparison
                    WHERE symbol = %s
                    ORDER BY analysis_time DESC
                    LIMIT 1
                """, (symbol,))
                
                depth_result = cursor.fetchone()
                
                # 获取最新的价差对比数据
                cursor.execute("""
                    SELECT
                        bitda_spread, binance_spread_latest, binance_spread_avg,
                        binance_spread_max, binance_spread_min, binance_spread_median,
                        bid_ask1_spread_diff, binance_bid5_ask5_spread_max, binance_bid5_ask5_spread_min,
                        analysis_time, sample_count
                    FROM spread_comparison
                    WHERE symbol = %s
                    ORDER BY analysis_time DESC
                    LIMIT 1
                """, (symbol,))
                
                spread_result = cursor.fetchone()
                
                if depth_result and spread_result:
                    data[symbol] = {
                        'depth': {
                            'bid1_ratio': float(depth_result[0]),
                            'ask1_ratio': float(depth_result[1]),
                            'bid_ask1_ratio': float(depth_result[2]),
                            'bid_ask2_ratio': float(depth_result[3]),
                            'bid_ask5_ratio': float(depth_result[4]),
                            'bitda_bid1_qty': float(depth_result[5]),
                            'bitda_ask1_qty': float(depth_result[6]),
                            'bitda_bid2_qty': float(depth_result[7]),
                            'bitda_ask2_qty': float(depth_result[8]),
                            'bitda_bid5_total': float(depth_result[9]),
                            'bitda_ask5_total': float(depth_result[10]),
                            'binance_bid1_qty': float(depth_result[11]),
                            'binance_ask1_qty': float(depth_result[12]),
                            'binance_bid2_qty': float(depth_result[13]),
                            'binance_ask2_qty': float(depth_result[14]),
                            'binance_bid5_total': float(depth_result[15]),
                            'binance_ask5_total': float(depth_result[16]),
                            'analysis_time': depth_result[17],
                            'time_diff_ms': int(depth_result[18])
                        },
                        'spread': {
                            'bitda_spread': float(spread_result[0]),
                            'binance_spread_latest': float(spread_result[1]),
                            'binance_spread_avg': float(spread_result[2]),
                            'binance_spread_max': float(spread_result[3]),
                            'binance_spread_min': float(spread_result[4]),
                            'binance_spread_median': float(spread_result[5]),
                            'spread_diff': float(spread_result[6]),
                            'binance_bid5_ask5_spread_max': float(spread_result[7]),
                            'binance_bid5_ask5_spread_min': float(spread_result[8]),
                            'analysis_time': spread_result[9],
                            'sample_count': int(spread_result[10])
                        }
                    }
                    
                    logger.info(f"   ✅ {symbol}: 深度比值 买一{data[symbol]['depth']['bid1_ratio']:.2f} 卖一{data[symbol]['depth']['ask1_ratio']:.2f}")
                    logger.info(f"      价差对比 Bitda{data[symbol]['spread']['bitda_spread']:.4f} vs Binance{data[symbol]['spread']['binance_spread_latest']:.4f}")
            
            cursor.close()
            connection.close()
            
            return data
            
        except Exception as e:
            logger.error(f"❌ 获取分析数据失败: {e}")
            return {}
    
    def create_dashboard(self):
        """创建炫酷的深度价差对比仪表板"""
        logger.info("🎨 创建深度价差对比仪表板...")
        
        # 获取最新数据
        data = self.get_latest_analysis_data()
        if not data:
            logger.error("❌ 无分析数据，无法创建仪表板")
            return None
        
        # 获取时间信息
        latest_time = None
        for symbol_data in data.values():
            if symbol_data['depth']['analysis_time']:
                latest_time = symbol_data['depth']['analysis_time']
                break
        
        time_str = latest_time.strftime('%Y-%m-%d %H:%M:%S') if latest_time else "未知"
        
        dashboard_config = {
            "dashboard": {
                "id": None,
                "title": f"🔥 BTCUSDT vs ETHUSDT 深度价差对比分析 - {datetime.now().strftime('%m-%d %H:%M')}",
                "tags": ["depth", "spread", "comparison", "btc", "eth"],
                "timezone": "browser",
                "panels": [
                    # 标题面板
                    {
                        "id": 1,
                        "title": "",
                        "type": "text",
                        "gridPos": {"h": 4, "w": 24, "x": 0, "y": 0},
                        "targets": [],
                        "options": {
                            "mode": "html",
                            "content": f"""
                            <div style="
                                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                color: white;
                                height: 100%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                border-radius: 8px;
                                font-family: Arial, sans-serif;
                            ">
                                <div style="text-align: center;">
                                    <h1 style="margin: 0; font-size: 28px;">🔥 BTCUSDT vs ETHUSDT 深度价差对比分析</h1>
                                    <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">
                                        📊 实时深度对比 | 💰 价差分析 | ⏰ 数据时间: {time_str}
                                    </p>
                                </div>
                            </div>
                            """
                        }
                    }
                ],
                "time": {"from": "now-1h", "to": "now"},
                "timepicker": {},
                "templating": {"list": []},
                "annotations": {"list": []},
                "refresh": "1m",
                "schemaVersion": 30,
                "version": 1,
                "links": []
            },
            "overwrite": True
        }
        
        # 为每个币种创建面板
        panel_id = 2
        y_pos = 4
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            if symbol not in data:
                continue
                
            symbol_data = data[symbol]
            depth_data = symbol_data['depth']
            spread_data = symbol_data['spread']
            
            # 深度对比表格面板
            depth_table_content = f"""
            <div style="padding: 20px; background: #f8f9fa; border-radius: 8px; font-family: Arial, sans-serif;">
                <h2 style="text-align: center; color: #333; margin-bottom: 20px;">📊 {symbol} 深度对比</h2>
                
                <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <thead style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                        <tr>
                            <th style="padding: 15px; text-align: left; border: none;">项目</th>
                            <th style="padding: 15px; text-align: right; border: none;">Bitda</th>
                            <th style="padding: 15px; text-align: right; border: none;">Binance</th>
                            <th style="padding: 15px; text-align: right; border: none;">深度比</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 12px 15px; font-weight: bold;">买一</td>
                            <td style="padding: 12px 15px; text-align: right;">{depth_data['bitda_bid1_qty']:.4f}</td>
                            <td style="padding: 12px 15px; text-align: right;">{depth_data['binance_bid1_qty']:.4f}</td>
                            <td style="padding: 12px 15px; text-align: right; color: {'#28a745' if depth_data['bid1_ratio'] > 1 else '#dc3545'}; font-weight: bold;">{depth_data['bid1_ratio']:.2f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee; background: #f8f9fa;">
                            <td style="padding: 12px 15px; font-weight: bold;">卖一</td>
                            <td style="padding: 12px 15px; text-align: right;">{depth_data['bitda_ask1_qty']:.4f}</td>
                            <td style="padding: 12px 15px; text-align: right;">{depth_data['binance_ask1_qty']:.4f}</td>
                            <td style="padding: 12px 15px; text-align: right; color: {'#28a745' if depth_data['ask1_ratio'] > 1 else '#dc3545'}; font-weight: bold;">{depth_data['ask1_ratio']:.2f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 12px 15px; font-weight: bold;">买一卖一</td>
                            <td style="padding: 12px 15px; text-align: right;">{depth_data['bitda_bid1_qty'] + depth_data['bitda_ask1_qty']:.4f}</td>
                            <td style="padding: 12px 15px; text-align: right;">{depth_data['binance_bid1_qty'] + depth_data['binance_ask1_qty']:.4f}</td>
                            <td style="padding: 12px 15px; text-align: right; color: {'#28a745' if depth_data['bid_ask1_ratio'] > 1 else '#dc3545'}; font-weight: bold;">{depth_data['bid_ask1_ratio']:.2f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee; background: #f8f9fa;">
                            <td style="padding: 12px 15px; font-weight: bold;">买二卖二</td>
                            <td style="padding: 12px 15px; text-align: right;">{depth_data['bitda_bid1_qty'] + depth_data['bitda_bid2_qty'] + depth_data['bitda_ask1_qty'] + depth_data['bitda_ask2_qty']:.4f}</td>
                            <td style="padding: 12px 15px; text-align: right;">{depth_data['binance_bid1_qty'] + depth_data['binance_bid2_qty'] + depth_data['binance_ask1_qty'] + depth_data['binance_ask2_qty']:.4f}</td>
                            <td style="padding: 12px 15px; text-align: right; color: {'#28a745' if depth_data['bid_ask2_ratio'] > 1 else '#dc3545'}; font-weight: bold;">{depth_data['bid_ask2_ratio']:.2f}</td>
                        </tr>
                        <tr>
                            <td style="padding: 12px 15px; font-weight: bold;">买五卖五</td>
                            <td style="padding: 12px 15px; text-align: right;">{depth_data['bitda_bid5_total'] + depth_data['bitda_ask5_total']:.4f}</td>
                            <td style="padding: 12px 15px; text-align: right;">{depth_data['binance_bid5_total'] + depth_data['binance_ask5_total']:.4f}</td>
                            <td style="padding: 12px 15px; text-align: right; color: {'#28a745' if depth_data['bid_ask5_ratio'] > 1 else '#dc3545'}; font-weight: bold;">{depth_data['bid_ask5_ratio']:.2f}</td>
                        </tr>
                    </tbody>
                </table>
                
                <div style="margin-top: 15px; padding: 10px; background: #e3f2fd; border-radius: 5px; border-left: 4px solid #2196f3;">
                    <small><strong>📊 数据说明:</strong> 深度比 = Bitda数量 / Binance数量，>1表示Bitda深度更好</small><br>
                    <small><strong>⏰ 时间差:</strong> {depth_data['time_diff_ms']}ms (Bitda与Binance数据的时间差)</small>
                </div>
            </div>
            """
            
            dashboard_config["dashboard"]["panels"].append({
                "id": panel_id,
                "title": "",
                "type": "text",
                "gridPos": {"h": 12, "w": 12, "x": 0, "y": y_pos},
                "targets": [],
                "options": {
                    "mode": "html",
                    "content": depth_table_content
                }
            })
            
            panel_id += 1
            
            # 价差对比表格面板
            spread_table_content = f"""
            <div style="padding: 20px; background: #f8f9fa; border-radius: 8px; font-family: Arial, sans-serif;">
                <h2 style="text-align: center; color: #333; margin-bottom: 20px;">💰 {symbol} 价差对比</h2>
                
                <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <thead style="background: linear-gradient(135deg, #ff7f0e 0%, #ff4757 100%); color: white;">
                        <tr>
                            <th style="padding: 15px; text-align: left; border: none;">项目</th>
                            <th style="padding: 15px; text-align: right; border: none;">Bitda</th>
                            <th style="padding: 15px; text-align: right; border: none;">Binance</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 12px 15px; font-weight: bold;">买一卖一最近价差</td>
                            <td style="padding: 12px 15px; text-align: right;">{spread_data['bitda_spread']:.4f}</td>
                            <td style="padding: 12px 15px; text-align: right;">{spread_data['binance_spread_latest']:.4f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee; background: #f8f9fa;">
                            <td style="padding: 12px 15px; font-weight: bold;">买一卖一最大价差</td>
                            <td style="padding: 12px 15px; text-align: right;">-</td>
                            <td style="padding: 12px 15px; text-align: right;">{spread_data['binance_spread_max']:.4f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 12px 15px; font-weight: bold;">买一卖一最小价差</td>
                            <td style="padding: 12px 15px; text-align: right;">-</td>
                            <td style="padding: 12px 15px; text-align: right;">{spread_data['binance_spread_min']:.4f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee; background: #f8f9fa;">
                            <td style="padding: 12px 15px; font-weight: bold;">买一卖一平均价差</td>
                            <td style="padding: 12px 15px; text-align: right;">-</td>
                            <td style="padding: 12px 15px; text-align: right;">{spread_data['binance_spread_avg']:.4f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 12px 15px; font-weight: bold;">买一卖一价差中位数</td>
                            <td style="padding: 12px 15px; text-align: right;">-</td>
                            <td style="padding: 12px 15px; text-align: right;">{spread_data['binance_spread_median']:.4f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee; background: #f8f9fa;">
                            <td style="padding: 12px 15px; font-weight: bold;">买五卖五最大价差</td>
                            <td style="padding: 12px 15px; text-align: right;">-</td>
                            <td style="padding: 12px 15px; text-align: right;">{spread_data['binance_bid5_ask5_spread_max']:.4f}</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 12px 15px; font-weight: bold;">买五卖五最小价差</td>
                            <td style="padding: 12px 15px; text-align: right;">-</td>
                            <td style="padding: 12px 15px; text-align: right;">{spread_data['binance_bid5_ask5_spread_min']:.4f}</td>
                        </tr>
                        <tr>
                            <td style="padding: 12px 15px; font-weight: bold;">价差差值</td>
                            <td style="padding: 12px 15px; text-align: right; color: {'#28a745' if spread_data['spread_diff'] > 0 else '#dc3545'}; font-weight: bold;" colspan="2">{spread_data['spread_diff']:.4f}</td>
                        </tr>
                    </tbody>
                </table>
                
                <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border-radius: 5px; border-left: 4px solid #ffc107;">
                    <small><strong>💰 价差说明:</strong> 价差 = 卖一价 - 买一价，价差差值 = Bitda价差 - Binance价差</small><br>
                    <small><strong>📊 样本数量:</strong> {spread_data['sample_count']}条 (5分钟窗口内的Binance数据)</small>
                </div>
            </div>
            """
            
            dashboard_config["dashboard"]["panels"].append({
                "id": panel_id,
                "title": "",
                "type": "text",
                "gridPos": {"h": 12, "w": 12, "x": 12, "y": y_pos},
                "targets": [],
                "options": {
                    "mode": "html",
                    "content": spread_table_content
                }
            })
            
            panel_id += 1
            y_pos += 12
        
        # 创建仪表板
        try:
            response = self.session.post(
                f"{self.grafana_url}/api/dashboards/db",
                json=dashboard_config,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                dashboard_url = f"{self.grafana_url}{result.get('url', '')}"
                logger.info(f"✅ 仪表板创建成功")
                logger.info(f"🌐 访问地址: {dashboard_url}")
                return dashboard_url
            else:
                logger.error(f"❌ 仪表板创建失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            logger.error(f"❌ 仪表板创建异常: {e}")
        
        return None

def main():
    """主函数"""
    print("🎨 深度价差对比分析仪表板创建工具")
    print("=" * 50)
    print("功能:")
    print("  - 展示BTCUSDT和ETHUSDT的深度对比表格")
    print("  - 展示价差对比分析结果")
    print("  - 炫酷的HTML表格设计")
    print("  - 实时数据更新")
    print()
    
    creator = DepthSpreadDashboard()
    dashboard_url = creator.create_dashboard()
    
    if dashboard_url:
        print("✅ 仪表板创建成功!")
        print("🎨 特点:")
        print("  ✅ 炫酷的渐变色设计")
        print("  ✅ 清晰的表格展示")
        print("  ✅ 颜色编码的比值显示")
        print("  ✅ 详细的数据说明")
        
        # 自动打开浏览器
        try:
            import webbrowser
            webbrowser.open(dashboard_url)
            print("🌐 浏览器已自动打开")
        except:
            print(f"🌐 请手动访问: {dashboard_url}")
    else:
        print("❌ 仪表板创建失败")

if __name__ == "__main__":
    main()
