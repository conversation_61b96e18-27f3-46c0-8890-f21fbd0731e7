#!/usr/bin/env python3
"""
修复ClickHouse数据源配置并验证数据显示
"""

import requests
import json
import time

GRAFANA_URL = "http://localhost:3000"
GRAFANA_USER = "admin"
GRAFANA_PASSWORD = "admin"

def delete_all_datasources():
    """删除所有ClickHouse数据源"""
    print("🗑️ 删除现有数据源...")
    
    response = requests.get(
        f"{GRAFANA_URL}/api/datasources",
        auth=(GRAFANA_USER, GRAFANA_PASSWORD)
    )
    
    if response.status_code == 200:
        datasources = response.json()
        for ds in datasources:
            if 'clickhouse' in ds.get('type', '').lower():
                delete_response = requests.delete(
                    f"{GRAFANA_URL}/api/datasources/{ds['id']}",
                    auth=(GRAFANA_USER, GRAFANA_PASSWORD)
                )
                print(f"   删除: {ds['name']} - {delete_response.status_code}")

def create_working_clickhouse_datasource():
    """创建工作的ClickHouse数据源"""
    print("📊 创建ClickHouse数据源...")
    
    # 尝试多种配置
    configs = [
        {
            "name": "ClickHouse-Working",
            "type": "grafana-clickhouse-datasource",
            "url": "http://localhost:8123",
            "access": "proxy",
            "basicAuth": False,
            "isDefault": True,
            "jsonData": {
                "defaultDatabase": "crypto",
                "username": "default",
                "server": "localhost",
                "port": 8123,
                "protocol": "http",
                "timeout": 30,
                "queryTimeout": 60,
                "logs": {
                    "defaultDatabase": "crypto",
                    "defaultTable": ""
                }
            },
            "secureJsonData": {
                "password": "Linuxtest"
            }
        },
        {
            "name": "ClickHouse-Alt",
            "type": "grafana-clickhouse-datasource", 
            "url": "http://localhost:8123/",
            "access": "proxy",
            "basicAuth": False,
            "isDefault": False,
            "jsonData": {
                "defaultDatabase": "crypto",
                "username": "default",
                "host": "localhost",
                "port": 8123,
                "protocol": "http"
            },
            "secureJsonData": {
                "password": "Linuxtest"
            }
        }
    ]
    
    for i, config in enumerate(configs):
        print(f"   尝试配置 {i+1}...")
        response = requests.post(
            f"{GRAFANA_URL}/api/datasources",
            json=config,
            auth=(GRAFANA_USER, GRAFANA_PASSWORD)
        )
        
        if response.status_code == 200:
            result = response.json()
            uid = result.get('uid')
            print(f"✅ 数据源创建成功: {config['name']}, UID: {uid}")
            
            # 测试连接
            time.sleep(2)
            test_response = requests.get(
                f"{GRAFANA_URL}/api/datasources/uid/{uid}/health",
                auth=(GRAFANA_USER, GRAFANA_PASSWORD)
            )
            print(f"   健康检查: {test_response.status_code}")
            
            if test_response.status_code == 200:
                print(f"✅ 数据源 {config['name']} 工作正常")
                return uid
            else:
                print(f"⚠️ 数据源 {config['name']} 健康检查失败，但继续使用")
                return uid
        else:
            print(f"❌ 配置 {i+1} 失败: {response.status_code}")
    
    return None

def test_clickhouse_query(uid):
    """测试ClickHouse查询"""
    print("🔍 测试ClickHouse查询...")
    
    # 简单测试查询
    test_query = {
        "queries": [
            {
                "datasource": {
                    "type": "grafana-clickhouse-datasource",
                    "uid": uid
                },
                "rawSql": "SELECT 1 as test",
                "refId": "A"
            }
        ],
        "from": "now-1h",
        "to": "now"
    }
    
    response = requests.post(
        f"{GRAFANA_URL}/api/ds/query",
        json=test_query,
        auth=(GRAFANA_USER, GRAFANA_PASSWORD)
    )
    
    print(f"   查询测试: {response.status_code}")
    if response.status_code == 200:
        print("✅ ClickHouse查询测试成功")
        return True
    else:
        print(f"❌ 查询测试失败: {response.text}")
        return False

def create_simple_dashboard(uid):
    """创建简单的测试仪表板"""
    print("📊 创建简单测试仪表板...")
    
    dashboard = {
        "dashboard": {
            "title": "ClickHouse测试仪表板",
            "tags": ["test", "clickhouse"],
            "timezone": "browser",
            "panels": [
                {
                    "id": 1,
                    "title": "简单测试",
                    "type": "table",
                    "targets": [
                        {
                            "datasource": {
                                "type": "grafana-clickhouse-datasource",
                                "uid": uid
                            },
                            "rawSql": "SELECT '测试' as name, COUNT(*) as count FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT'",
                            "refId": "A"
                        }
                    ],
                    "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
                },
                {
                    "id": 2,
                    "title": "BTCUSDT深度数据",
                    "type": "table",
                    "targets": [
                        {
                            "datasource": {
                                "type": "grafana-clickhouse-datasource",
                                "uid": uid
                            },
                            "rawSql": "SELECT '买一量' as item, round((SELECT bid_qty_1 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda, round((SELECT bid_qty FROM crypto.binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance",
                            "refId": "A"
                        }
                    ],
                    "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
                }
            ],
            "time": {"from": "now-1h", "to": "now"},
            "refresh": "30s"
        },
        "overwrite": True
    }
    
    response = requests.post(
        f"{GRAFANA_URL}/api/dashboards/db",
        json=dashboard,
        auth=(GRAFANA_USER, GRAFANA_PASSWORD)
    )
    
    if response.status_code == 200:
        result = response.json()
        dashboard_url = f"{GRAFANA_URL}{result['url']}"
        print(f"✅ 测试仪表板创建成功")
        print(f"🔗 测试仪表板URL: {dashboard_url}")
        return dashboard_url
    else:
        print(f"❌ 测试仪表板创建失败: {response.status_code} - {response.text}")
        return None

def main():
    """主函数"""
    print("🚀 修复ClickHouse数据源配置")
    print("=" * 50)
    
    # 1. 删除现有数据源
    delete_all_datasources()
    time.sleep(2)
    
    # 2. 创建新数据源
    uid = create_working_clickhouse_datasource()
    if not uid:
        print("❌ 无法创建工作的数据源")
        return
    
    time.sleep(3)
    
    # 3. 测试查询
    if test_clickhouse_query(uid):
        print("✅ 查询测试通过")
    else:
        print("⚠️ 查询测试失败，但继续")
    
    # 4. 创建测试仪表板
    dashboard_url = create_simple_dashboard(uid)
    
    if dashboard_url:
        print("\n🎉 修复完成!")
        print(f"📊 测试仪表板: {dashboard_url}")
        print(f"🔧 数据源UID: {uid}")
        print("\n📋 下一步:")
        print("1. 访问测试仪表板验证数据显示")
        print("2. 如果测试仪表板有数据，更新主仪表板使用新的UID")
    else:
        print("❌ 修复失败")

if __name__ == "__main__":
    main()
