#!/usr/bin/env python3
"""
修复版延时处理器 - 解决时间戳匹配问题
"""

import mysql.connector
import time
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FixedLatencyProcessor:
    """修复版ETHUSDT延时处理器"""

    def __init__(self):
        self.source_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'depth_db'
        }

        self.target_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'ethusdt_latency_db'
        }

        self.min_latency = 10   # 最小有效延时(ms)
        self.max_latency = 2000 # 最大有效延时(ms)

    def test_single_minute_processing(self, target_minute: datetime) -> int:
        """测试处理单分钟数据 - 修复版"""
        matches_found = 0

        try:
            # 计算时间窗口
            start_time = target_minute
            end_time = target_minute + timedelta(minutes=1)

            logger.info(f"🔄 测试处理时间窗口: {start_time.strftime('%H:%M:%S')} - {end_time.strftime('%H:%M:%S')}")

            # 连接数据库
            source_conn = mysql.connector.connect(**self.source_config)
            target_conn = mysql.connector.connect(**self.target_config)

            source_cursor = source_conn.cursor()
            target_cursor = target_conn.cursor()

            # 获取Bitda数据
            bitda_query = """
            SELECT timestamp, bid_price_1, ask_price_1, created_at
            FROM bitda_depth
            WHERE symbol = 'ETHUSDT'
            AND created_at >= %s
            AND created_at < %s
            AND bid_price_1 IS NOT NULL
            AND ask_price_1 IS NOT NULL
            ORDER BY timestamp
            LIMIT 50
            """

            source_cursor.execute(bitda_query, (start_time, end_time))
            bitda_records = source_cursor.fetchall()

            if not bitda_records:
                logger.info("  无Bitda数据")
                return 0

            logger.info(f"  找到 {len(bitda_records)} 条Bitda记录")

            # 测试前几条记录
            test_matches = []
            
            for i, record in enumerate(bitda_records[:5]):  # 只测试前5条
                bitda_timestamp, bid_price_1, ask_price_1, created_at = record
                
                logger.info(f"  测试记录 {i+1}: Bitda时间戳={bitda_timestamp}, 价格={bid_price_1}/{ask_price_1}")

                # 修复版匹配查询 - 使用时间窗口而不是精确匹配
                binance_query = """
                SELECT bid_price, ask_price, event_time
                FROM binance_bookticker
                WHERE symbol = 'ETHUSDT'
                AND bid_price = %s
                AND ask_price = %s
                AND event_time <= %s
                AND event_time >= %s
                ORDER BY event_time DESC
                LIMIT 1
                """
                
                # 在Bitda时间戳前后5秒内查找匹配
                time_window_start = bitda_timestamp - 5000  # 5秒前
                time_window_end = bitda_timestamp  # Bitda时间戳

                source_cursor.execute(binance_query, (
                    bid_price_1, ask_price_1, time_window_end, time_window_start
                ))
                binance_match = source_cursor.fetchone()

                if binance_match:
                    binance_bid, binance_ask, binance_timestamp = binance_match
                    latency_ms = bitda_timestamp - binance_timestamp
                    
                    logger.info(f"    ✅ 找到匹配: Binance时间戳={binance_timestamp}, 延时={latency_ms}ms")
                    
                    # 验证延时范围
                    if self.min_latency <= latency_ms <= self.max_latency:
                        test_matches.append({
                            'bitda_timestamp': bitda_timestamp,
                            'binance_timestamp': binance_timestamp,
                            'latency_ms': latency_ms,
                            'bitda_price': f"{bid_price_1}/{ask_price_1}",
                            'binance_price': f"{binance_bid}/{binance_ask}"
                        })
                        matches_found += 1
                        logger.info(f"    ✅ 有效匹配: 延时={latency_ms}ms")
                    else:
                        logger.info(f"    ❌ 延时超出范围: {latency_ms}ms (范围: {self.min_latency}-{self.max_latency}ms)")
                else:
                    logger.info(f"    ❌ 无匹配")

            # 显示匹配结果
            if test_matches:
                logger.info(f"📊 测试结果: 找到 {len(test_matches)} 个有效匹配")
                for i, match in enumerate(test_matches):
                    logger.info(f"  匹配 {i+1}: 延时={match['latency_ms']}ms, 价格={match['bitda_price']}")
            else:
                logger.info("📊 测试结果: 无有效匹配")

            source_cursor.close()
            target_cursor.close()
            source_conn.close()
            target_conn.close()

        except Exception as e:
            logger.error(f"测试处理失败: {e}")

        return matches_found

    def analyze_time_sync_issue(self):
        """分析时间同步问题"""
        try:
            connection = mysql.connector.connect(**self.source_config)
            cursor = connection.cursor()
            
            logger.info("🔍 分析时间同步问题...")
            
            # 获取最近的Bitda和Binance数据
            cursor.execute("""
                SELECT timestamp, bid_price_1, ask_price_1, created_at
                FROM bitda_depth
                WHERE symbol = 'ETHUSDT'
                AND bid_price_1 IS NOT NULL
                ORDER BY created_at DESC
                LIMIT 5
            """)
            bitda_data = cursor.fetchall()
            
            cursor.execute("""
                SELECT bid_price, ask_price, event_time, created_at
                FROM binance_bookticker
                WHERE symbol = 'ETHUSDT'
                ORDER BY created_at DESC
                LIMIT 5
            """)
            binance_data = cursor.fetchall()
            
            logger.info("📊 最近的Bitda数据:")
            for i, (timestamp, bid, ask, created_at) in enumerate(bitda_data):
                logger.info(f"  {i+1}. 时间戳={timestamp}, 价格={bid}/{ask}, 创建时间={created_at}")
            
            logger.info("📊 最近的Binance数据:")
            for i, (bid, ask, event_time, created_at) in enumerate(binance_data):
                logger.info(f"  {i+1}. 时间戳={event_time}, 价格={bid}/{ask}, 创建时间={created_at}")
            
            # 分析时间差
            if bitda_data and binance_data:
                bitda_latest = bitda_data[0][0]  # timestamp
                binance_latest = binance_data[0][2]  # event_time
                time_diff = abs(bitda_latest - binance_latest)
                
                logger.info(f"📊 时间差分析:")
                logger.info(f"  Bitda最新时间戳: {bitda_latest}")
                logger.info(f"  Binance最新时间戳: {binance_latest}")
                logger.info(f"  时间差: {time_diff}ms ({time_diff/1000:.2f}秒)")
            
            cursor.close()
            connection.close()
            
        except Exception as e:
            logger.error(f"时间同步分析失败: {e}")

    def test_price_matching_with_tolerance(self):
        """测试价格匹配（带容差）"""
        try:
            connection = mysql.connector.connect(**self.source_config)
            cursor = connection.cursor()
            
            logger.info("🎯 测试价格匹配（带容差）...")
            
            # 获取一个Bitda价格
            cursor.execute("""
                SELECT timestamp, bid_price_1, ask_price_1
                FROM bitda_depth
                WHERE symbol = 'ETHUSDT'
                AND bid_price_1 IS NOT NULL
                ORDER BY created_at DESC
                LIMIT 1
            """)
            
            bitda_record = cursor.fetchone()
            if not bitda_record:
                logger.warning("无Bitda数据")
                return
            
            bitda_timestamp, bid_price, ask_price = bitda_record
            logger.info(f"测试价格: {bid_price}/{ask_price}, 时间戳: {bitda_timestamp}")
            
            # 精确匹配
            cursor.execute("""
                SELECT COUNT(*) FROM binance_bookticker
                WHERE symbol = 'ETHUSDT'
                AND bid_price = %s
                AND ask_price = %s
                AND event_time <= %s
                AND event_time >= %s
            """, (bid_price, ask_price, bitda_timestamp, bitda_timestamp - 10000))
            
            exact_matches = cursor.fetchone()[0]
            logger.info(f"精确匹配数量: {exact_matches}")
            
            # 单边匹配（只匹配买一价格）
            cursor.execute("""
                SELECT COUNT(*) FROM binance_bookticker
                WHERE symbol = 'ETHUSDT'
                AND bid_price = %s
                AND event_time <= %s
                AND event_time >= %s
            """, (bid_price, bitda_timestamp, bitda_timestamp - 10000))
            
            bid_matches = cursor.fetchone()[0]
            logger.info(f"买一价格匹配数量: {bid_matches}")
            
            # 单边匹配（只匹配卖一价格）
            cursor.execute("""
                SELECT COUNT(*) FROM binance_bookticker
                WHERE symbol = 'ETHUSDT'
                AND ask_price = %s
                AND event_time <= %s
                AND event_time >= %s
            """, (ask_price, bitda_timestamp, bitda_timestamp - 10000))
            
            ask_matches = cursor.fetchone()[0]
            logger.info(f"卖一价格匹配数量: {ask_matches}")
            
            cursor.close()
            connection.close()
            
        except Exception as e:
            logger.error(f"价格匹配测试失败: {e}")

    def run_comprehensive_test(self):
        """运行综合测试"""
        logger.info("🧪 开始修复版延时处理器测试")
        logger.info("=" * 60)
        
        # 1. 分析时间同步问题
        logger.info("1️⃣ 分析时间同步问题...")
        self.analyze_time_sync_issue()
        
        # 2. 测试价格匹配
        logger.info("\n2️⃣ 测试价格匹配...")
        self.test_price_matching_with_tolerance()
        
        # 3. 测试单分钟处理
        logger.info("\n3️⃣ 测试单分钟处理...")
        test_time = datetime.now() - timedelta(minutes=2)
        test_minute = test_time.replace(second=0, microsecond=0)
        matches = self.test_single_minute_processing(test_minute)
        
        logger.info(f"\n📋 测试总结:")
        logger.info(f"  测试时间窗口: {test_minute}")
        logger.info(f"  找到匹配数: {matches}")
        
        if matches > 0:
            logger.info("✅ 延时处理器逻辑正常，可以找到匹配数据")
        else:
            logger.warning("⚠️  延时处理器未找到匹配数据，可能需要调整匹配逻辑")

def main():
    """主函数"""
    print("🧪 修复版延时处理器测试工具")
    print("=" * 50)
    print("功能:")
    print("  - 分析时间同步问题")
    print("  - 测试价格匹配逻辑")
    print("  - 验证延时计算准确性")
    print()
    
    processor = FixedLatencyProcessor()
    processor.run_comprehensive_test()

if __name__ == "__main__":
    main()
