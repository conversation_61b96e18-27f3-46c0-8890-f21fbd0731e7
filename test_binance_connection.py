#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试Binance WebSocket连接
"""

import asyncio
import websockets
import json
import time
import ssl
import socket
from urllib.parse import urlparse

async def test_websocket_connection(url, timeout=10):
    """测试WebSocket连接"""
    print(f"正在测试连接: {url}")
    print(f"超时时间: {timeout}秒")
    print("-" * 50)
    
    try:
        # 解析URL
        parsed = urlparse(url)
        host = parsed.hostname
        port = parsed.port or (443 if parsed.scheme == 'wss' else 80)
        
        print(f"目标主机: {host}")
        print(f"端口: {port}")
        
        # 1. 测试DNS解析
        print("\n1. 测试DNS解析...")
        try:
            ip_address = socket.gethostbyname(host)
            print(f"✅ DNS解析成功: {host} -> {ip_address}")
        except socket.gaierror as e:
            print(f"❌ DNS解析失败: {e}")
            return False
        
        # 2. 测试TCP连接
        print("\n2. 测试TCP连接...")
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"✅ TCP连接成功: {host}:{port}")
            else:
                print(f"❌ TCP连接失败: 错误代码 {result}")
                return False
        except Exception as e:
            print(f"❌ TCP连接测试出错: {e}")
            return False
        
        # 3. 测试WebSocket连接
        print("\n3. 测试WebSocket连接...")
        try:
            async with websockets.connect(url, ping_timeout=timeout) as ws:
                print("✅ WebSocket连接成功!")
                
                # 等待接收消息
                print("\n4. 等待接收数据...")
                try:
                    message = await asyncio.wait_for(ws.recv(), timeout=timeout)
                    data = json.loads(message)
                    print("✅ 成功接收到数据:")
                    print(f"   数据类型: {type(data)}")
                    if isinstance(data, dict):
                        print(f"   数据字段: {list(data.keys())}")
                        if 's' in data:
                            print(f"   交易对: {data['s']}")
                        if 'b' in data and 'a' in data:
                            print(f"   买一价: {data['b']}, 卖一价: {data['a']}")
                    print(f"   原始数据: {message[:200]}...")
                    return True
                    
                except asyncio.TimeoutError:
                    print(f"⚠️  {timeout}秒内未收到数据")
                    return True  # 连接成功但无数据
                    
        except websockets.exceptions.InvalidStatusCode as e:
            print(f"❌ WebSocket连接失败: HTTP状态码 {e.status_code}")
            return False
        except websockets.exceptions.ConnectionClosedError as e:
            print(f"❌ WebSocket连接被关闭: {e}")
            return False
        except ConnectionResetError as e:
            print(f"❌ 连接被重置: {e}")
            print("   可能原因:")
            print("   - 网络防火墙阻止连接")
            print("   - 代理配置问题")
            print("   - 地区访问限制")
            return False
        except OSError as e:
            print(f"❌ 网络错误: {e}")
            if "Connection refused" in str(e):
                print("   连接被拒绝，可能是端口未开放或服务不可用")
            elif "Network is unreachable" in str(e):
                print("   网络不可达，检查网络连接")
            return False
        except Exception as e:
            print(f"❌ WebSocket连接出错: {type(e).__name__}: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程出错: {type(e).__name__}: {e}")
        return False

async def test_multiple_urls():
    """测试多个Binance WebSocket URL"""
    urls = [
        "wss://fstream.binance.com/ws/btcusdt@bookTicker",
        "wss://stream.binance.com:9443/ws/btcusdt@bookTicker", 
        "wss://ws-api.binance.com:443/ws-api/v3",
        "wss://fstream.binance.com/stream?streams=btcusdt@bookTicker"
    ]
    
    results = {}
    
    for url in urls:
        print(f"\n{'='*60}")
        print(f"测试URL: {url}")
        print(f"{'='*60}")
        
        success = await test_websocket_connection(url, timeout=15)
        results[url] = success
        
        if success:
            print("🎉 测试通过!")
        else:
            print("💥 测试失败!")
        
        # 等待一下再测试下一个
        await asyncio.sleep(2)
    
    # 总结
    print(f"\n{'='*60}")
    print("测试总结:")
    print(f"{'='*60}")
    
    for url, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{status} {url}")
    
    successful_urls = [url for url, success in results.items() if success]
    if successful_urls:
        print(f"\n推荐使用: {successful_urls[0]}")
    else:
        print("\n⚠️  所有URL都无法连接，可能需要:")
        print("   1. 检查网络连接")
        print("   2. 配置代理服务器")
        print("   3. 使用VPN")
        print("   4. 检查防火墙设置")

def main():
    """主函数"""
    print("Binance WebSocket连接测试工具")
    print("=" * 60)
    
    try:
        asyncio.run(test_multiple_urls())
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n\n测试过程出错: {e}")

if __name__ == "__main__":
    main()
