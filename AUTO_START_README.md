# WebSocket数据收集器自动启动配置

## 概述
本文档介绍如何配置WebSocket数据收集器程序，使其在系统重启后自动运行。

## 🚀 快速安装

### 1. 安装服务
```bash
cd WS_DATA_Collector
./install_service.sh
```

这个脚本会：
- ✅ 检查系统要求
- ✅ 安装Python依赖
- ✅ 创建systemd服务
- ✅ 启用开机自启动
- ✅ 启动服务

### 2. 验证安装
```bash
# 检查服务状态
sudo systemctl status ws-data-collector

# 查看实时日志
sudo journalctl -u ws-data-collector -f
```

## 📋 服务管理

### 使用图形化管理工具
```bash
./service_manager.sh
```

这会打开一个交互式菜单，提供以下功能：
1. 查看服务状态
2. 启动/停止/重启服务
3. 查看日志
4. 管理开机自启动

### 使用命令行管理
```bash
# 启动服务
sudo systemctl start ws-data-collector

# 停止服务
sudo systemctl stop ws-data-collector

# 重启服务
sudo systemctl restart ws-data-collector

# 查看状态
sudo systemctl status ws-data-collector

# 查看日志
sudo journalctl -u ws-data-collector -f

# 启用开机自启动
sudo systemctl enable ws-data-collector

# 禁用开机自启动
sudo systemctl disable ws-data-collector
```

## 📁 文件说明

### 服务配置文件
- `ws-data-collector.service` - systemd服务配置文件
- `install_service.sh` - 服务安装脚本
- `uninstall_service.sh` - 服务卸载脚本
- `service_manager.sh` - 服务管理工具

### 日志文件
- `/tmp/ws_data_collector.log` - 应用程序日志
- `sudo journalctl -u ws-data-collector` - 系统服务日志

## 🔧 配置说明

### 环境变量
服务会自动加载以下环境：
- `PATH` - 包含conda和系统路径
- `PYTHONPATH` - Python模块搜索路径
- `CONDA_DEFAULT_ENV=base` - 使用conda base环境

### 工作目录
- 工作目录：`/home/<USER>/project/WS_DATA_Collector`
- 用户：`code`
- 组：`code`

### 依赖服务
- 网络服务：`network.target`
- MySQL服务：`mysql.service`

## 🛠️ 故障排除

### 1. 服务启动失败
```bash
# 查看详细错误信息
sudo systemctl status ws-data-collector -l
sudo journalctl -u ws-data-collector --no-pager

# 检查配置文件
sudo systemctl cat ws-data-collector
```

### 2. 权限问题
```bash
# 确保文件权限正确
sudo chown -R code:code /home/<USER>/project/WS_DATA_Collector
chmod +x /home/<USER>/project/WS_DATA_Collector/*.sh
```

### 3. 依赖问题
```bash
# 重新安装Python依赖
pip install websockets mysql-connector-python python-dotenv psutil

# 检查MySQL连接
mysql -u root -pLinuxtest -e "SHOW DATABASES;"
```

### 4. 网络问题
```bash
# 检查网络连接
ping google.com
curl -I https://api.binance.com

# 检查防火墙
sudo ufw status
```

## 🔄 卸载服务

如果需要卸载服务：
```bash
./uninstall_service.sh
```

这会：
- 停止服务
- 禁用开机自启动
- 删除服务文件
- 清理相关进程

## 📊 监控和维护

### 1. 定期检查
```bash
# 每日检查服务状态
sudo systemctl is-active ws-data-collector

# 检查日志大小
ls -lh /tmp/ws_data_collector.log
```

### 2. 日志轮转
建议设置日志轮转以防止日志文件过大：
```bash
# 创建logrotate配置
sudo tee /etc/logrotate.d/ws-data-collector << EOF
/tmp/ws_data_collector.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    copytruncate
}
EOF
```

### 3. 性能监控
```bash
# 监控资源使用
top -p $(pgrep -f ws_data_collector.py)

# 检查数据库连接
mysql -u root -pLinuxtest depth_db -e "SELECT COUNT(*) FROM binance_raw_data;"
```

## ✅ 验证自动启动

### 测试重启
```bash
# 重启系统测试
sudo reboot

# 重启后检查服务状态
sudo systemctl status ws-data-collector
```

### 检查开机启动时间
```bash
# 查看服务启动时间
sudo systemctl show ws-data-collector --property=ActiveEnterTimestamp
```

## 🎯 最佳实践

1. **定期备份**：定期备份数据库和配置文件
2. **监控日志**：设置日志监控和告警
3. **资源限制**：监控CPU和内存使用情况
4. **网络监控**：监控网络连接状态
5. **数据验证**：定期验证数据完整性

通过这些配置，WebSocket数据收集器将在系统重启后自动启动，确保数据收集的连续性！
