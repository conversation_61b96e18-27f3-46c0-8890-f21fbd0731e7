#!/usr/bin/env python3
"""
测试数据插入功能
"""

import sys
import json
from datetime import datetime

# 添加当前目录到路径
sys.path.append('.')

from storage import storage

def log(message):
    """日志输出"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def test_bitda_depth():
    """测试Bitda深度数据插入"""
    log("🔍 测试Bitda深度数据插入...")
    
    test_data = [{
        'symbol': 'BTCUSDT',
        'timestamp': 1733844000000,
        'index_price': 100000.0,
        'sign_price': 100000.0,
        'last_price': 100000.0,
        'asks': [['100001.0', '1.0'], ['100002.0', '2.0']],
        'bids': [['99999.0', '1.0'], ['99998.0', '2.0']],
        'merge_level': 0
    }]
    
    try:
        storage.save_bitda_depth(test_data)
        log("✅ Bitda深度数据插入成功")
        return True
    except Exception as e:
        log(f"❌ Bitda深度数据插入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_binance_depth():
    """测试Binance深度数据插入"""
    log("🔍 测试Binance深度数据插入...")
    
    test_data = [{
        'symbol': 'BTCUSDT',
        'event_time': 1733844000000,
        'trade_time': 1733844000000,
        'first_update_id': 1000,
        'last_update_id': 1001,
        'prev_update_id': 999,
        'bids': [['99999.0', '1.0'], ['99998.0', '2.0']],
        'asks': [['100001.0', '1.0'], ['100002.0', '2.0']]
    }]
    
    try:
        storage.save_binance_depth_5(test_data)
        log("✅ Binance深度数据插入成功")
        return True
    except Exception as e:
        log(f"❌ Binance深度数据插入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bitda_kline():
    """测试Bitda K线数据插入"""
    log("🔍 测试Bitda K线数据插入...")
    
    test_data = [{
        'symbol': 'BTCUSDT',
        'timestamp': 1733844000,
        'open_price': 100000.0,
        'high_price': 100100.0,
        'low_price': 99900.0,
        'close_price': 100050.0,
        'volume': 10.5,
        'amount': 1000000.0,
        'period': '1min'
    }]
    
    try:
        storage.save_bitda_kline(test_data)
        log("✅ Bitda K线数据插入成功")
        return True
    except Exception as e:
        log(f"❌ Bitda K线数据插入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bitda_trades():
    """测试Bitda成交数据插入"""
    log("🔍 测试Bitda成交数据插入...")
    
    test_data = [{
        'symbol': 'BTCUSDT',
        'trade_id': 12345,
        'price': 100000.0,
        'amount': 1.5,
        'trade_time': 1733844000.123,
        'trade_type': 'buy'
    }]
    
    try:
        storage.save_bitda_trades(test_data)
        log("✅ Bitda成交数据插入成功")
        return True
    except Exception as e:
        log(f"❌ Bitda成交数据插入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bitda_ticker():
    """测试Bitda行情数据插入"""
    log("🔍 测试Bitda行情数据插入...")
    
    test_data = [{
        'symbol': 'BTCUSDT',
        'open_price': 100000.0,
        'high_price': 100100.0,
        'low_price': 99900.0,
        'last_price': 100050.0,
        'volume': 1000.0,
        'amount': 100000000.0,
        'change_rate': 0.0005,
        'funding_time': 1733844000,
        'position_amount': 50000.0,
        'funding_rate_last': 0.0001,
        'funding_rate_next': 0.0002,
        'funding_rate_predict': 0.00015,
        'insurance': 1000000.0,
        'sign_price': 100000.0,
        'index_price': 100000.0,
        'sell_total': 500000.0,
        'buy_total': 500000.0,
        'period': 86400
    }]
    
    try:
        storage.save_bitda_ticker(test_data)
        log("✅ Bitda行情数据插入成功")
        return True
    except Exception as e:
        log(f"❌ Bitda行情数据插入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_binance_bookticker():
    """测试Binance BookTicker数据插入"""
    log("🔍 测试Binance BookTicker数据插入...")
    
    test_data = [{
        'symbol': 'BTCUSDT',
        'update_id': 12345,
        'bid_price': 99999.0,
        'bid_qty': 1.5,
        'ask_price': 100001.0,
        'ask_qty': 2.0,
        'event_time': 1733844000000,
        'trade_time': 1733844000000
    }]
    
    try:
        storage.save_binance_bookticker(test_data)
        log("✅ Binance BookTicker数据插入成功")
        return True
    except Exception as e:
        log(f"❌ Binance BookTicker数据插入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    log("🚀 开始测试数据插入功能")
    log("=" * 50)
    
    tests = [
        test_bitda_depth,
        test_binance_depth,
        test_bitda_kline,
        test_bitda_trades,
        test_bitda_ticker,
        test_binance_bookticker
    ]
    
    success_count = 0
    for test_func in tests:
        if test_func():
            success_count += 1
        log("")
    
    log("=" * 50)
    log(f"🎯 测试结果: {success_count}/{len(tests)} 个测试通过")
    
    if success_count == len(tests):
        log("🎉 所有数据插入测试通过！")
    else:
        log("❌ 部分测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
