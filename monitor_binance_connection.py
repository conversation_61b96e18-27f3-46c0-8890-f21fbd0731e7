#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
币安WebSocket连接监控脚本
用于监控币安连接状态和24小时重连机制
"""

import time
import re
import subprocess
from datetime import datetime, timedelta

def monitor_binance_connections():
    """监控币安连接状态"""
    print("开始监控币安WebSocket连接...")
    print("=" * 60)
    
    while True:
        try:
            # 读取最新的日志
            result = subprocess.run(['tail', '-100', '/tmp/ws_data_collector.log'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                log_lines = result.stdout.split('\n')
                
                # 查找连接相关信息
                connection_info = {}
                message_rates = {}
                connection_times = {}
                
                for line in log_lines:
                    # 查找连接建立信息
                    if "已连接到Binance" in line:
                        match = re.search(r'已连接到Binance (\w+) WebSocket', line)
                        if match:
                            symbol = match.group(1)
                            timestamp = line.split(' - ')[0]
                            connection_info[symbol] = timestamp
                    
                    # 查找消息率信息
                    if "消息率:" in line and "连接时长:" in line:
                        match = re.search(r'Binance (\w+) 消息率: ([\d.]+) 消息/秒，连接时长: ([\d.]+)小时', line)
                        if match:
                            symbol = match.group(1)
                            rate = match.group(2)
                            duration = match.group(3)
                            message_rates[symbol] = rate
                            connection_times[symbol] = duration
                    
                    # 查找主动重连信息
                    if "主动重连避免24小时限制" in line:
                        print(f"🔄 检测到主动重连: {line}")
                
                # 显示当前状态
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print(f"\n📊 连接状态监控 - {current_time}")
                print("-" * 50)
                
                for symbol in ['BTCUSDT', 'ETHUSDT']:
                    if symbol in connection_info:
                        print(f"✅ {symbol}:")
                        print(f"   连接时间: {connection_info[symbol]}")
                        if symbol in message_rates:
                            print(f"   消息率: {message_rates[symbol]} 消息/秒")
                        if symbol in connection_times:
                            duration_hours = float(connection_times[symbol])
                            remaining_hours = 23.0 - duration_hours
                            print(f"   连接时长: {connection_times[symbol]} 小时")
                            print(f"   距离重连: {remaining_hours:.1f} 小时")
                            
                            # 警告即将重连
                            if remaining_hours < 1.0:
                                print(f"   ⚠️  即将在 {remaining_hours:.1f} 小时后重连")
                    else:
                        print(f"❌ {symbol}: 未检测到连接")
                
                print("-" * 50)
                
            else:
                print("❌ 无法读取日志文件")
            
            # 等待30秒后再次检查
            time.sleep(30)
            
        except KeyboardInterrupt:
            print("\n👋 监控已停止")
            break
        except Exception as e:
            print(f"❌ 监控出错: {e}")
            time.sleep(10)

if __name__ == "__main__":
    monitor_binance_connections()
