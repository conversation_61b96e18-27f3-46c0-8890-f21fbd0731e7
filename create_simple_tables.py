#!/usr/bin/env python3
"""
创建简化的ClickHouse表结构
"""

import requests
from datetime import datetime

# ClickHouse配置
CLICKHOUSE_URL = "***************************************/"

def log(message):
    """日志输出"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def ch_execute(query):
    """执行ClickHouse查询"""
    try:
        response = requests.post(CLICKHOUSE_URL, data=query.encode('utf-8'), timeout=30)
        if response.status_code == 200:
            return True
        else:
            log(f"❌ ClickHouse查询失败: {response.text}")
            return False
    except Exception as e:
        log(f"❌ ClickHouse连接异常: {e}")
        return False

def create_tables():
    """创建所有表"""
    
    tables = [
        """
        CREATE TABLE IF NOT EXISTS crypto.bitda_depth (
            id UInt64 DEFAULT 0,
            symbol String,
            timestamp UInt64,
            index_price Nullable(Decimal(15,8)),
            sign_price Nullable(Decimal(15,8)),
            last_price Nullable(Decimal(15,8)),
            bid_price_1 Decimal(15,2),
            ask_price_1 Decimal(15,2),
            bid_qty_1 Decimal(20,4),
            ask_qty_1 Decimal(20,4),
            bid_price_2 Nullable(Decimal(15,2)),
            ask_price_2 Nullable(Decimal(15,2)),
            bid_qty_2 Nullable(Decimal(20,4)),
            ask_qty_2 Nullable(Decimal(20,4)),
            bid_price_3 Nullable(Decimal(15,2)),
            ask_price_3 Nullable(Decimal(15,2)),
            bid_qty_3 Nullable(Decimal(20,4)),
            ask_qty_3 Nullable(Decimal(20,4)),
            bid_price_4 Nullable(Decimal(15,2)),
            ask_price_4 Nullable(Decimal(15,2)),
            bid_qty_4 Nullable(Decimal(20,4)),
            ask_qty_4 Nullable(Decimal(20,4)),
            bid_price_5 Nullable(Decimal(15,2)),
            ask_price_5 Nullable(Decimal(15,2)),
            bid_qty_5 Nullable(Decimal(20,4)),
            ask_qty_5 Nullable(Decimal(20,4)),
            asks String,
            bids String,
            merge_level Int32 DEFAULT 0,
            created_at DateTime DEFAULT now()
        ) ENGINE = MergeTree() 
        ORDER BY (symbol, timestamp)
        """,
        
        """
        CREATE TABLE IF NOT EXISTS crypto.binance_depth_5 (
            id UInt64 DEFAULT 0,
            symbol String,
            event_time UInt64,
            trade_time UInt64,
            first_update_id UInt64,
            last_update_id UInt64,
            prev_update_id UInt64,
            bid_price_1 Nullable(Decimal(15,2)),
            ask_price_1 Nullable(Decimal(15,2)),
            bid_qty_1 Nullable(Decimal(20,4)),
            ask_qty_1 Nullable(Decimal(20,4)),
            bid_price_2 Nullable(Decimal(15,2)),
            ask_price_2 Nullable(Decimal(15,2)),
            bid_qty_2 Nullable(Decimal(20,4)),
            ask_qty_2 Nullable(Decimal(20,4)),
            bid_price_3 Nullable(Decimal(15,2)),
            ask_price_3 Nullable(Decimal(15,2)),
            bid_qty_3 Nullable(Decimal(20,4)),
            ask_qty_3 Nullable(Decimal(20,4)),
            bid_price_4 Nullable(Decimal(15,2)),
            ask_price_4 Nullable(Decimal(15,2)),
            bid_qty_4 Nullable(Decimal(20,4)),
            ask_qty_4 Nullable(Decimal(20,4)),
            bid_price_5 Nullable(Decimal(15,2)),
            ask_price_5 Nullable(Decimal(15,2)),
            bid_qty_5 Nullable(Decimal(20,4)),
            ask_qty_5 Nullable(Decimal(20,4)),
            bids String,
            asks String,
            created_at DateTime DEFAULT now()
        ) ENGINE = MergeTree() 
        ORDER BY (symbol, event_time)
        """,
        
        """
        CREATE TABLE IF NOT EXISTS crypto.bitda_kline (
            id UInt64 DEFAULT 0,
            symbol String,
            timestamp UInt64,
            open_price Decimal(15,8),
            high_price Decimal(15,8),
            low_price Decimal(15,8),
            close_price Decimal(15,8),
            volume Decimal(20,8),
            amount Decimal(25,8),
            period String,
            created_at DateTime DEFAULT now()
        ) ENGINE = MergeTree() 
        ORDER BY (symbol, timestamp)
        """,
        
        """
        CREATE TABLE IF NOT EXISTS crypto.bitda_trades (
            id UInt64 DEFAULT 0,
            symbol String,
            trade_id UInt64,
            price Decimal(15,8),
            amount Decimal(20,8),
            trade_time Decimal(20,6),
            trade_type String,
            created_at DateTime DEFAULT now()
        ) ENGINE = MergeTree() 
        ORDER BY (symbol, trade_time)
        """,
        
        """
        CREATE TABLE IF NOT EXISTS crypto.bitda_ticker (
            id UInt64 DEFAULT 0,
            symbol String,
            open_price Decimal(15,8),
            high_price Decimal(15,8),
            low_price Decimal(15,8),
            last_price Decimal(15,8),
            volume Decimal(25,8),
            amount Decimal(25,8),
            change_rate Decimal(10,8),
            funding_time Nullable(Int32),
            position_amount Nullable(Decimal(25,8)),
            funding_rate_last Nullable(Decimal(10,8)),
            funding_rate_next Nullable(Decimal(10,8)),
            funding_rate_predict Nullable(Decimal(10,8)),
            insurance Nullable(Decimal(25,8)),
            sign_price Nullable(Decimal(15,8)),
            index_price Nullable(Decimal(15,8)),
            sell_total Nullable(Decimal(25,8)),
            buy_total Nullable(Decimal(25,8)),
            period Int32 DEFAULT 86400,
            created_at DateTime DEFAULT now()
        ) ENGINE = MergeTree() 
        ORDER BY (symbol, created_at)
        """,
        
        """
        CREATE TABLE IF NOT EXISTS crypto.binance_bookticker (
            id UInt64 DEFAULT 0,
            symbol String,
            update_id UInt64,
            bid_price Decimal(15,8),
            bid_qty Decimal(20,8),
            ask_price Decimal(15,8),
            ask_qty Decimal(20,8),
            event_time UInt64,
            trade_time UInt64,
            created_at DateTime DEFAULT now()
        ) ENGINE = MergeTree() 
        ORDER BY (symbol, event_time)
        """
    ]
    
    table_names = ['bitda_depth', 'binance_depth_5', 'bitda_kline', 'bitda_trades', 'bitda_ticker', 'binance_bookticker']
    
    for i, sql in enumerate(tables):
        table_name = table_names[i]
        log(f"🔧 创建表 {table_name}...")
        if ch_execute(sql):
            log(f"✅ 表 {table_name} 创建成功")
        else:
            log(f"❌ 表 {table_name} 创建失败")
            return False
    
    return True

def main():
    """主函数"""
    log("🚀 开始创建ClickHouse表结构")
    log("=" * 50)
    
    if create_tables():
        log("=" * 50)
        log("🎉 所有表创建完成！")
        
        # 验证表创建
        log("🔍 验证表创建...")
        if ch_execute("SELECT 'Tables created successfully' as status"):
            log("✅ 表结构验证成功")
        
    else:
        log("❌ 表创建失败")

if __name__ == "__main__":
    main()
