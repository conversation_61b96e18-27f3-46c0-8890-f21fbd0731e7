[Unit]
Description=Crypto Prometheus Exporter Service
Documentation=https://github.com/crypto-data-collector
After=network.target crypto-collector.service
Wants=network.target

[Service]
Type=simple
User=code
Group=code
WorkingDirectory=/home/<USER>/project/WS_DATA_ALL
Environment=PATH=/home/<USER>/anaconda3/bin:/usr/local/bin:/usr/bin:/bin
Environment=PYTHONPATH=/home/<USER>/project/WS_DATA_ALL
ExecStart=/home/<USER>/anaconda3/bin/python prometheus_exporter.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=append:/home/<USER>/project/WS_DATA_ALL/prometheus_exporter.log
StandardError=append:/home/<USER>/project/WS_DATA_ALL/prometheus_exporter_error.log

# 资源限制
LimitNOFILE=65536
MemoryMax=1G

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/home/<USER>/project/WS_DATA_ALL

[Install]
WantedBy=multi-user.target
