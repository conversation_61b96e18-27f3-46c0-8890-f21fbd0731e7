"""
主程序 - 协调各模块运行
"""
import asyncio
import signal
import sys
from datetime import datetime

from collector import DataCollector
from storage import storage
from utils.logging import setup_logger
from utils.config import SYMBOLS

logger = setup_logger(__name__)

class CryptoDataCollectorApp:
    """加密货币数据收集应用"""

    def __init__(self):
        self.collector = DataCollector()
        self.running = False

    async def start(self):
        """启动应用"""
        logger.info("=" * 60)
        logger.info("启动加密货币数据收集系统")
        logger.info(f"收集交易对: {', '.join(SYMBOLS)}")
        logger.info(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("=" * 60)

        # 检查磁盘空间
        if not storage.check_disk_space():
            logger.critical("磁盘空间不足，无法启动程序！")
            return

        # 设置信号处理
        self._setup_signal_handlers()

        self.running = True

        try:
            # 启动数据收集器
            await self.collector.start()
        except KeyboardInterrupt:
            logger.info("收到中断信号，正在停止...")
        except Exception as e:
            logger.error(f"应用运行错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
        finally:
            await self.stop()

    async def stop(self):
        """停止应用"""
        if not self.running:
            return

        logger.info("正在停止加密货币数据收集系统...")
        self.running = False

        # 停止数据收集器
        self.collector.stop()

        # 保存所有缓存的数据
        self.collector.flush_all_data()

        logger.info("系统已安全停止")
        logger.info("=" * 60)

    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，准备停止...")
            asyncio.create_task(self.stop())

        def flush_handler(signum, frame):
            logger.info(f"收到强制保存信号 {signum}，保存缓存数据...")
            self.collector.flush_all_data()
            logger.info("缓存数据已强制保存")

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGUSR1, flush_handler)  # 强制保存信号

async def main():
    """主函数"""
    app = CryptoDataCollectorApp()
    await app.start()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        sys.exit(1)
