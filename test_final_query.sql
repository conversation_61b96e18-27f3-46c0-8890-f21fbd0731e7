WITH bitda_latest AS (
  SELECT bid_qty_1, ask_qty_1
  FROM crypto.bitda_depth 
  WHERE symbol = 'BTCUSDT' 
  ORDER BY timestamp DESC 
  LIMIT 1
),
binance_latest AS (
  SELECT bid_qty, ask_qty
  FROM crypto.binance_bookticker 
  WHERE symbol = 'BTCUSDT' 
  ORDER BY event_time DESC 
  LIMIT 1
)
SELECT 
  'Bid Qty 1' as item,
  round(b.bid_qty_1, 2) as Bitda,
  round(bn.bid_qty, 2) as Binance,
  round(b.bid_qty_1 / bn.bid_qty, 2) as ratio
FROM bitda_latest b, binance_latest bn
