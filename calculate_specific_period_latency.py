#!/usr/bin/env python3
"""
计算指定时间段的ETHUSDT延时数据
UTC+8时间：2025年5月30日12点至13点
"""

import mysql.connector
import json
from datetime import datetime, timedelta
import pytz

class SpecificPeriodLatencyCalculator:
    """指定时间段延时计算器"""
    
    def __init__(self):
        self.source_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'depth_db'
        }
        
        self.target_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'ethusdt_latency_db'
        }
        
        # 设置时区
        self.utc8_tz = pytz.timezone('Asia/Shanghai')
        
        # 目标时间段 (UTC+8)
        self.start_time_utc8 = datetime(2025, 5, 30, 12, 0, 0)
        self.end_time_utc8 = datetime(2025, 5, 30, 13, 0, 0)
        
        # 转换为UTC时间
        self.start_time_utc = self.start_time_utc8 - timedelta(hours=8)
        self.end_time_utc = self.end_time_utc8 - timedelta(hours=8)
        
        print(f"📅 目标时间段 (UTC+8): {self.start_time_utc8} ~ {self.end_time_utc8}")
        print(f"📅 对应UTC时间: {self.start_time_utc} ~ {self.end_time_utc}")
    
    def parse_bitda_depth(self, asks_json, bids_json):
        """解析Bitda深度数据"""
        try:
            if not asks_json or not bids_json:
                return None, None, None, None
            
            asks_data = json.loads(asks_json) if isinstance(asks_json, str) else asks_json
            bids_data = json.loads(bids_json) if isinstance(bids_json, str) else bids_json
            
            if not asks_data or not bids_data:
                return None, None, None, None
            
            # 卖一价格 = asks中价格最小的
            ask_prices = [float(item[0]) for item in asks_data]
            ask_price_1 = min(ask_prices)
            ask_qty_1 = float([item[1] for item in asks_data if float(item[0]) == ask_price_1][0])
            
            # 买一价格 = bids中价格最大的
            bid_prices = [float(item[0]) for item in bids_data]
            bid_price_1 = max(bid_prices)
            bid_qty_1 = float([item[1] for item in bids_data if float(item[0]) == bid_price_1][0])
            
            return ask_price_1, ask_qty_1, bid_price_1, bid_qty_1
            
        except Exception as e:
            print(f"解析失败: {e}")
            return None, None, None, None
    
    def calculate_period_latency(self):
        """计算指定时间段的延时数据"""
        print(f"\n🔄 开始计算延时数据...")
        
        try:
            # 连接数据库
            source_conn = mysql.connector.connect(**self.source_config)
            target_conn = mysql.connector.connect(**self.target_config)
            
            source_cursor = source_conn.cursor()
            target_cursor = target_conn.cursor()
            
            # 清理目标表中的旧数据
            print("🗑️  清理旧的延时数据...")
            target_cursor.execute("DELETE FROM ethusdt_latency_matches")
            target_conn.commit()
            
            # 查询指定时间段的Bitda数据
            print(f"📊 查询Bitda数据: {self.start_time_utc} ~ {self.end_time_utc}")
            bitda_query = """
            SELECT timestamp, asks, bids, created_at
            FROM bitda_depth 
            WHERE symbol = 'ETHUSDT'
            AND created_at >= %s 
            AND created_at < %s
            AND asks IS NOT NULL 
            AND bids IS NOT NULL
            ORDER BY timestamp
            """
            
            source_cursor.execute(bitda_query, (self.start_time_utc, self.end_time_utc))
            bitda_records = source_cursor.fetchall()
            
            print(f"📋 获取到 {len(bitda_records)} 条Bitda记录")
            
            if len(bitda_records) == 0:
                print("❌ 指定时间段内没有Bitda数据")
                return
            
            matches_found = 0
            total_processed = 0
            latency_list = []
            
            # 处理每条Bitda记录
            for i, record in enumerate(bitda_records):
                bitda_timestamp, asks_json, bids_json, created_at = record
                total_processed += 1
                
                if i % 100 == 0:  # 每100条显示进度
                    print(f"  处理进度: {i+1}/{len(bitda_records)}")
                
                # 解析深度数据
                ask_price_1, ask_qty_1, bid_price_1, bid_qty_1 = self.parse_bitda_depth(asks_json, bids_json)
                
                if ask_price_1 is None or bid_price_1 is None:
                    continue
                
                # 查找买一卖一价格完全匹配的Binance数据 (使用首次出现时间)
                binance_query = """
                SELECT bid_price, ask_price, bid_qty, ask_qty, event_time
                FROM binance_bookticker 
                WHERE symbol = 'ETHUSDT'
                AND bid_price = %s
                AND ask_price = %s
                AND event_time < %s
                ORDER BY event_time ASC
                LIMIT 1
                """
                
                source_cursor.execute(binance_query, (bid_price_1, ask_price_1, bitda_timestamp))
                binance_match = source_cursor.fetchone()
                
                if binance_match:
                    binance_bid_price, binance_ask_price, binance_bid_qty, binance_ask_qty, binance_timestamp = binance_match
                    latency = bitda_timestamp - binance_timestamp
                    
                    # 只保存有效延时范围的数据
                    if 10 <= latency <= 2000:
                        # 插入完全匹配记录
                        insert_query = """
                        INSERT INTO ethusdt_latency_matches 
                        (bitda_timestamp, binance_timestamp, latency_ms, match_type, 
                         bitda_price, binance_price, bitda_qty, binance_qty, 
                         price_spread, match_quality, created_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """
                        
                        # 计算价差
                        price_spread = ask_price_1 - bid_price_1
                        
                        target_cursor.execute(insert_query, (
                            bitda_timestamp, binance_timestamp, latency, 'complete',
                            (bid_price_1 + ask_price_1) / 2,  # 中间价
                            (binance_bid_price + binance_ask_price) / 2,  # Binance中间价
                            (bid_qty_1 + ask_qty_1) / 2,  # 平均数量
                            (binance_bid_qty + binance_ask_qty) / 2,  # Binance平均数量
                            price_spread, 1.0000, created_at
                        ))
                        
                        matches_found += 1
                        latency_list.append(latency)
                        
                        if matches_found <= 10:  # 显示前10条详细信息
                            utc8_time = created_at + timedelta(hours=8)
                            print(f"  ✅ 匹配 {matches_found}: {utc8_time.strftime('%H:%M:%S')} 延时={latency}ms 买一={bid_price_1} 卖一={ask_price_1}")
            
            # 提交事务
            target_conn.commit()
            
            # 统计结果
            print(f"\n📈 处理完成:")
            print(f"  - 处理记录: {total_processed}")
            print(f"  - 完全匹配: {matches_found}")
            print(f"  - 匹配率: {matches_found/total_processed*100:.2f}%")
            
            if latency_list:
                print(f"\n📊 延时统计:")
                print(f"  - 平均延时: {sum(latency_list)/len(latency_list):.2f}ms")
                print(f"  - 最小延时: {min(latency_list)}ms")
                print(f"  - 最大延时: {max(latency_list)}ms")
                print(f"  - 中位数延时: {sorted(latency_list)[len(latency_list)//2]}ms")
                
                # 延时分布
                ranges = [(0, 50), (51, 100), (101, 200), (201, 500), (501, 1000), (1001, 2000)]
                print(f"\n📈 延时分布:")
                for min_val, max_val in ranges:
                    count = len([l for l in latency_list if min_val <= l <= max_val])
                    percentage = count / len(latency_list) * 100
                    print(f"  - {min_val}-{max_val}ms: {count}条 ({percentage:.1f}%)")
            
            source_cursor.close()
            target_cursor.close()
            source_conn.close()
            target_conn.close()
            
            return matches_found > 0
            
        except Exception as e:
            print(f"❌ 计算失败: {e}")
            return False
    
    def generate_summary_report(self):
        """生成汇总报告"""
        try:
            conn = mysql.connector.connect(**self.target_config)
            cursor = conn.cursor()
            
            # 查询统计数据
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_matches,
                    AVG(latency_ms) as avg_latency,
                    MIN(latency_ms) as min_latency,
                    MAX(latency_ms) as max_latency,
                    MIN(created_at) as earliest_time,
                    MAX(created_at) as latest_time
                FROM ethusdt_latency_matches
            """)
            
            result = cursor.fetchone()
            
            if result and result[0] > 0:
                total, avg_lat, min_lat, max_lat, earliest, latest = result
                
                print(f"\n📋 汇总报告")
                print("=" * 50)
                print(f"时间段: {self.start_time_utc8.strftime('%Y-%m-%d %H:%M')} ~ {self.end_time_utc8.strftime('%H:%M')} (UTC+8)")
                print(f"总匹配数: {total}")
                print(f"平均延时: {avg_lat:.2f}ms")
                print(f"最小延时: {min_lat}ms")
                print(f"最大延时: {max_lat}ms")
                print(f"数据时间范围: {(earliest + timedelta(hours=8)).strftime('%H:%M:%S')} ~ {(latest + timedelta(hours=8)).strftime('%H:%M:%S')} (UTC+8)")
                
                # 查询最新的几条记录
                cursor.execute("""
                    SELECT latency_ms, bitda_price, binance_price, created_at
                    FROM ethusdt_latency_matches 
                    ORDER BY created_at DESC 
                    LIMIT 5
                """)
                
                recent_records = cursor.fetchall()
                print(f"\n📊 最新5条记录:")
                for record in recent_records:
                    latency, bitda_price, binance_price, created_at = record
                    utc8_time = created_at + timedelta(hours=8)
                    print(f"  - {utc8_time.strftime('%H:%M:%S')}: {latency}ms (Bitda:{bitda_price:.2f} vs Binance:{binance_price:.2f})")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            print(f"❌ 报告生成失败: {e}")

def main():
    """主函数"""
    print("⏰ ETHUSDT指定时间段延时计算器")
    print("=" * 60)
    
    calculator = SpecificPeriodLatencyCalculator()
    
    if calculator.calculate_period_latency():
        calculator.generate_summary_report()
        print("\n✅ 延时数据计算完成！")
        print("📊 数据已保存到 ethusdt_latency_matches 表")
        print("🌐 可在Grafana仪表板中查看: http://localhost:3000")
    else:
        print("\n❌ 延时数据计算失败")

if __name__ == "__main__":
    main()
