# ETHUSDT实时延时处理器部署指南

**创建时间**: 2025-05-30  
**版本**: v2.0 - 优化版  
**目标**: 每分钟自动处理1分钟前的ETHUSDT延时数据

---

## 🎯 系统概述

### 核心优势
- ✅ **每分钟执行**: 处理1分钟前的数据，避免实时数据不稳定
- ✅ **高效批量处理**: 优化的数据库查询和批量插入
- ✅ **服务化部署**: 支持systemd服务，开机自启
- ✅ **完善监控**: 健康检查、日志记录、状态更新
- ✅ **优雅退出**: 支持信号处理，安全停止

### 处理逻辑
1. **时间窗口**: 每分钟处理前1分钟的数据
2. **完全匹配**: 买一价格 AND 卖一价格必须同时相等
3. **首次出现**: 使用Binance价格组合的首次出现时间计算延时
4. **有效范围**: 10ms ≤ 延时 ≤ 2000ms
5. **实时更新**: 自动更新Grafana数据源

---

## 📁 核心文件

### 1. 主处理器
- **文件**: `ethusdt_realtime_processor.py`
- **功能**: 高效的延时数据处理器
- **特点**: 批量处理、错误处理、信号支持

### 2. 服务配置
- **文件**: `ethusdt-latency-processor.service`
- **功能**: systemd服务配置
- **特点**: 自动重启、资源限制、安全设置

### 3. 管理脚本
- **文件**: `manage_latency_service.sh`
- **功能**: 服务安装、启动、停止、监控
- **特点**: 完整的服务生命周期管理

### 4. 启动脚本
- **文件**: `start_realtime_processor.sh`
- **功能**: 快速启动和测试
- **特点**: 环境检查、数据验证、交互式启动

---

## 🚀 部署方案

### 方案A: 直接运行（推荐用于测试）

```bash
# 1. 检查环境
./start_realtime_processor.sh --check-only

# 2. 启动处理器
./start_realtime_processor.sh

# 3. 查看日志
tail -f ethusdt_realtime_processor.log
```

### 方案B: 服务化部署（推荐用于生产）

```bash
# 1. 安装服务
sudo ./manage_latency_service.sh install

# 2. 查看状态
sudo ./manage_latency_service.sh status

# 3. 查看日志
sudo ./manage_latency_service.sh logs

# 4. 健康检查
sudo ./manage_latency_service.sh health
```

---

## 📊 性能指标

### 预期性能
- **处理延迟**: 1分钟（处理1分钟前的数据）
- **匹配率**: 60-80%
- **平均延时**: 50-200ms
- **处理速度**: 200条记录/分钟
- **资源占用**: <512MB内存

### 监控指标
- **数据匹配数**: 每分钟的匹配记录数
- **处理延迟**: 数据处理的时间延迟
- **错误率**: 处理失败的比例
- **数据库连接**: 连接状态和响应时间

---

## 🔧 配置说明

### 数据库配置
```python
source_config = {
    'host': 'localhost',
    'user': 'root',
    'password': 'Linuxtest',
    'database': 'depth_db'        # 原始数据库
}

target_config = {
    'host': 'localhost',
    'user': 'root',
    'password': 'Linuxtest',
    'database': 'ethusdt_latency_db'  # 延时分析数据库
}
```

### 处理参数
```python
min_latency = 10      # 最小有效延时(ms)
max_latency = 2000    # 最大有效延时(ms)
batch_size = 200      # 批处理大小
```

### 调度设置
```python
# 每分钟第5秒执行处理
schedule.every().minute.at(":05").do(process_current_minute)

# 每10分钟健康检查
schedule.every(10).minutes.do(health_check)
```

---

## 📋 常用命令

### 服务管理
```bash
# 安装服务
sudo ./manage_latency_service.sh install

# 启动服务
sudo ./manage_latency_service.sh start

# 停止服务
sudo ./manage_latency_service.sh stop

# 重启服务
sudo ./manage_latency_service.sh restart

# 查看状态
sudo ./manage_latency_service.sh status

# 卸载服务
sudo ./manage_latency_service.sh uninstall
```

### 日志查看
```bash
# 查看系统日志
sudo ./manage_latency_service.sh logs

# 查看应用日志
sudo ./manage_latency_service.sh app-logs

# 查看最近日志
tail -f ethusdt_realtime_processor.log
```

### 健康检查
```bash
# 完整健康检查
sudo ./manage_latency_service.sh health

# 检查服务状态
systemctl is-active ethusdt-latency-processor

# 检查数据库连接
python3 -c "import mysql.connector; mysql.connector.connect(host='localhost', user='root', password='Linuxtest', database='ethusdt_latency_db')"
```

---

## 🔍 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查服务状态
sudo systemctl status ethusdt-latency-processor

# 查看详细日志
sudo journalctl -u ethusdt-latency-processor -f

# 检查文件权限
ls -la ethusdt_realtime_processor.py
```

#### 2. 数据库连接失败
```bash
# 测试数据库连接
mysql -u root -pLinuxtest -e "USE ethusdt_latency_db; SHOW TABLES;"

# 检查数据库服务
sudo systemctl status mysql
```

#### 3. 无匹配数据
```bash
# 检查源数据
mysql -u root -pLinuxtest -e "
SELECT COUNT(*) as bitda_count FROM depth_db.bitda_depth 
WHERE symbol='ETHUSDT' AND created_at >= NOW() - INTERVAL 10 MINUTE;
"

# 检查Binance数据
mysql -u root -pLinuxtest -e "
SELECT COUNT(*) as binance_count FROM depth_db.binance_bookticker 
WHERE symbol='ETHUSDT' AND created_at >= NOW() - INTERVAL 10 MINUTE;
"
```

#### 4. 处理速度慢
- 减少批处理大小 (`batch_size`)
- 检查数据库索引
- 监控系统资源使用

---

## 📈 Grafana集成

### 数据源配置
- **数据源名称**: ETHUSDT_Latency_MySQL
- **数据库**: ethusdt_latency_db
- **刷新频率**: 30秒

### 核心表
- `ethusdt_latency_matches`: 实时匹配数据
- `ethusdt_realtime_status`: 实时状态数据
- `ethusdt_latency_stats_minute`: 分钟级统计

### 仪表板地址
🌐 **http://localhost:3000/d/d22941aa-51af-4604-a10b-859bf9e1fb10/a825c4d**

---

## 🎉 部署完成检查

### 验证步骤
1. ✅ 服务正常运行
2. ✅ 数据库连接正常
3. ✅ 每分钟有新的匹配数据
4. ✅ Grafana显示实时数据
5. ✅ 日志无错误信息

### 成功标志
```bash
# 1. 服务状态
sudo systemctl is-active ethusdt-latency-processor
# 输出: active

# 2. 最近匹配数据
mysql -u root -pLinuxtest -e "
SELECT COUNT(*) FROM ethusdt_latency_db.ethusdt_latency_matches 
WHERE created_at >= NOW() - INTERVAL 10 MINUTE;
"
# 输出: > 0

# 3. 实时状态更新
mysql -u root -pLinuxtest -e "
SELECT updated_at FROM ethusdt_latency_db.ethusdt_realtime_status WHERE id=1;
"
# 输出: 最近的时间戳
```

---

## 📞 支持信息

### 日志位置
- **应用日志**: `ethusdt_realtime_processor.log`
- **系统日志**: `journalctl -u ethusdt-latency-processor`

### 配置文件
- **服务配置**: `/etc/systemd/system/ethusdt-latency-processor.service`
- **应用配置**: `ethusdt_realtime_processor.py`

### 数据库表
- **源数据**: `depth_db.bitda_depth`, `depth_db.binance_bookticker`
- **结果数据**: `ethusdt_latency_db.ethusdt_latency_matches`
- **状态数据**: `ethusdt_latency_db.ethusdt_realtime_status`
