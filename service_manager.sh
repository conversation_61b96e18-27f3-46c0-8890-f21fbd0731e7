#!/bin/bash

# WebSocket数据收集器服务管理脚本
# 提供简单的服务管理界面

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 服务名称
SERVICE_NAME="ws-data-collector"

# 显示服务状态
show_status() {
    echo -e "${BLUE}=== 服务状态 ===${NC}"
    
    if sudo systemctl is-active --quiet $SERVICE_NAME.service; then
        echo -e "状态: ${GREEN}运行中${NC}"
    else
        echo -e "状态: ${RED}已停止${NC}"
    fi
    
    if sudo systemctl is-enabled --quiet $SERVICE_NAME.service; then
        echo -e "开机自启: ${GREEN}已启用${NC}"
    else
        echo -e "开机自启: ${RED}已禁用${NC}"
    fi
    
    echo ""
    sudo systemctl status $SERVICE_NAME.service --no-pager -l
}

# 启动服务
start_service() {
    echo -e "${BLUE}启动服务...${NC}"
    sudo systemctl start $SERVICE_NAME.service
    sleep 2
    if sudo systemctl is-active --quiet $SERVICE_NAME.service; then
        echo -e "${GREEN}服务启动成功${NC}"
    else
        echo -e "${RED}服务启动失败${NC}"
        sudo systemctl status $SERVICE_NAME.service --no-pager
    fi
}

# 停止服务
stop_service() {
    echo -e "${BLUE}停止服务...${NC}"
    sudo systemctl stop $SERVICE_NAME.service
    sleep 2
    if ! sudo systemctl is-active --quiet $SERVICE_NAME.service; then
        echo -e "${GREEN}服务已停止${NC}"
    else
        echo -e "${RED}服务停止失败${NC}"
    fi
}

# 重启服务
restart_service() {
    echo -e "${BLUE}重启服务...${NC}"
    sudo systemctl restart $SERVICE_NAME.service
    sleep 3
    if sudo systemctl is-active --quiet $SERVICE_NAME.service; then
        echo -e "${GREEN}服务重启成功${NC}"
    else
        echo -e "${RED}服务重启失败${NC}"
        sudo systemctl status $SERVICE_NAME.service --no-pager
    fi
}

# 查看日志
show_logs() {
    echo -e "${BLUE}=== 系统日志 (最新50行) ===${NC}"
    sudo journalctl -u $SERVICE_NAME.service --no-pager -n 50
    
    echo ""
    echo -e "${BLUE}=== 应用日志 (最新20行) ===${NC}"
    if [[ -f /tmp/ws_data_collector.log ]]; then
        tail -20 /tmp/ws_data_collector.log
    else
        echo "应用日志文件不存在"
    fi
}

# 实时查看日志
follow_logs() {
    echo -e "${BLUE}实时查看日志 (按Ctrl+C退出)...${NC}"
    echo ""
    sudo journalctl -u $SERVICE_NAME.service -f
}

# 启用开机自启
enable_autostart() {
    echo -e "${BLUE}启用开机自启动...${NC}"
    sudo systemctl enable $SERVICE_NAME.service
    echo -e "${GREEN}开机自启动已启用${NC}"
}

# 禁用开机自启
disable_autostart() {
    echo -e "${BLUE}禁用开机自启动...${NC}"
    sudo systemctl disable $SERVICE_NAME.service
    echo -e "${YELLOW}开机自启动已禁用${NC}"
}

# 显示菜单
show_menu() {
    clear
    echo -e "${CYAN}=== WebSocket数据收集器服务管理 ===${NC}"
    echo ""
    echo "1. 查看服务状态"
    echo "2. 启动服务"
    echo "3. 停止服务"
    echo "4. 重启服务"
    echo "5. 查看日志"
    echo "6. 实时查看日志"
    echo "7. 启用开机自启动"
    echo "8. 禁用开机自启动"
    echo "9. 退出"
    echo ""
    echo -n "请选择操作 [1-9]: "
}

# 主循环
main() {
    while true; do
        show_menu
        read -r choice
        
        case $choice in
            1)
                clear
                show_status
                echo ""
                read -p "按回车键继续..." -r
                ;;
            2)
                clear
                start_service
                echo ""
                read -p "按回车键继续..." -r
                ;;
            3)
                clear
                stop_service
                echo ""
                read -p "按回车键继续..." -r
                ;;
            4)
                clear
                restart_service
                echo ""
                read -p "按回车键继续..." -r
                ;;
            5)
                clear
                show_logs
                echo ""
                read -p "按回车键继续..." -r
                ;;
            6)
                clear
                follow_logs
                ;;
            7)
                clear
                enable_autostart
                echo ""
                read -p "按回车键继续..." -r
                ;;
            8)
                clear
                disable_autostart
                echo ""
                read -p "按回车键继续..." -r
                ;;
            9)
                echo -e "${GREEN}再见！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}无效选择，请重新输入${NC}"
                sleep 1
                ;;
        esac
    done
}

# 检查服务是否存在
if ! sudo systemctl list-unit-files | grep -q $SERVICE_NAME.service; then
    echo -e "${RED}错误: 服务未安装${NC}"
    echo "请先运行 ./install_service.sh 安装服务"
    exit 1
fi

# 运行主程序
main
