#!/usr/bin/env python3
"""
测试ClickHouse连接和表结构
"""

import requests
import json
from datetime import datetime

# ClickHouse配置
CLICKHOUSE_URL = "***************************************/"

def log(message):
    """日志输出"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def ch_execute(query):
    """执行ClickHouse查询"""
    try:
        response = requests.post(CLICKHOUSE_URL, data=query, timeout=10)
        if response.status_code == 200:
            return response.text.strip()
        else:
            log(f"❌ ClickHouse查询失败: {response.text}")
            return None
    except Exception as e:
        log(f"❌ ClickHouse连接异常: {e}")
        return None

def test_connection():
    """测试连接"""
    log("🔍 测试ClickHouse连接...")
    result = ch_execute("SELECT 1")
    if result == "1":
        log("✅ ClickHouse连接正常")
        return True
    else:
        log("❌ ClickHouse连接失败")
        return False

def test_database():
    """测试数据库"""
    log("🔍 检查crypto数据库...")
    result = ch_execute("SHOW DATABASES")
    if result and "crypto" in result:
        log("✅ crypto数据库存在")
        return True
    else:
        log("❌ crypto数据库不存在")
        return False

def test_tables():
    """测试表结构"""
    log("🔍 检查表结构...")
    result = ch_execute("SHOW TABLES FROM crypto")
    if result:
        tables = result.split('\n')
        log(f"📋 现有表: {', '.join(tables)}")
        
        # 检查每个表的记录数
        for table in tables:
            if table.strip():
                count_result = ch_execute(f"SELECT COUNT(*) FROM crypto.{table.strip()}")
                log(f"📊 {table.strip()}: {count_result} 条记录")
        return True
    else:
        log("❌ 无法获取表列表")
        return False

def test_insert():
    """测试数据插入"""
    log("🔍 测试数据插入...")
    
    # 测试插入一条简单的数据到bitda_depth表
    insert_query = """
    INSERT INTO crypto.bitda_depth (
        symbol, timestamp, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
        asks, bids, merge_level
    ) VALUES (
        'TESTUSDT', 1733844000000, 50000.0, 50001.0, 1.0, 1.0,
        '[["50001.0", "1.0"]]', '[["50000.0", "1.0"]]', 0
    )
    """
    
    result = ch_execute(insert_query)
    if result is not None:
        log("✅ 数据插入测试成功")
        
        # 验证插入的数据
        verify_result = ch_execute("SELECT COUNT(*) FROM crypto.bitda_depth WHERE symbol = 'TESTUSDT'")
        log(f"📊 测试数据验证: {verify_result} 条")
        
        # 清理测试数据
        ch_execute("DELETE FROM crypto.bitda_depth WHERE symbol = 'TESTUSDT'")
        log("🧹 测试数据已清理")
        return True
    else:
        log("❌ 数据插入测试失败")
        return False

def test_latest_data():
    """检查最新数据"""
    log("🔍 检查最新数据...")
    
    # 检查bitda_depth最新数据
    result = ch_execute("""
        SELECT 
            symbol, 
            toDateTime(MAX(timestamp)/1000) as latest_time,
            COUNT(*) as count
        FROM crypto.bitda_depth 
        GROUP BY symbol 
        ORDER BY symbol
    """)
    
    if result:
        log("📊 Bitda深度数据:")
        for line in result.split('\n'):
            if line.strip():
                log(f"   {line}")
    
    # 检查binance_depth_5最新数据
    result = ch_execute("""
        SELECT 
            symbol, 
            toDateTime(MAX(event_time)/1000) as latest_time,
            COUNT(*) as count
        FROM crypto.binance_depth_5 
        GROUP BY symbol 
        ORDER BY symbol
    """)
    
    if result:
        log("📊 Binance深度数据:")
        for line in result.split('\n'):
            if line.strip():
                log(f"   {line}")

def main():
    """主函数"""
    log("🚀 开始ClickHouse连接和表结构测试")
    log("=" * 50)
    
    # 1. 测试连接
    if not test_connection():
        return
    
    # 2. 测试数据库
    if not test_database():
        return
    
    # 3. 测试表结构
    if not test_tables():
        return
    
    # 4. 测试数据插入
    if not test_insert():
        return
    
    # 5. 检查最新数据
    test_latest_data()
    
    log("=" * 50)
    log("🎉 ClickHouse测试完成！")

if __name__ == "__main__":
    main()
