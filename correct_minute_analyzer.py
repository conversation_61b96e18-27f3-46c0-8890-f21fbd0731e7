#!/usr/bin/env python3
"""
正确的按分钟统计的深度价差分析器
"""

import mysql.connector
from datetime import datetime, timedelta
import logging
from typing import Dict, List
import json

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CorrectMinuteAnalyzer:
    """正确的按分钟统计分析器"""
    
    def __init__(self):
        self.source_db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'depth_db'
        }
        
        self.analysis_db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'depth_spread_analysis'
        }
    
    def get_minute_data_stats(self, symbol: str, target_minute: datetime) -> Dict:
        """获取指定分钟内的所有数据统计"""
        try:
            connection = mysql.connector.connect(**self.source_db_config)
            cursor = connection.cursor()
            
            # 计算分钟的开始和结束时间戳
            start_time = target_minute.replace(second=0, microsecond=0)
            end_time = start_time + timedelta(minutes=1)
            
            start_ts = int(start_time.timestamp() * 1000)
            end_ts = int(end_time.timestamp() * 1000)
            
            logger.info(f"📊 分析时间段: {start_time.strftime('%H:%M:%S')} - {end_time.strftime('%H:%M:%S')}")
            logger.info(f"📊 时间戳范围: {start_ts} - {end_ts}")
            
            # 获取Bitda数据
            bitda_query = """
            SELECT 
                timestamp, bid_price_1, ask_price_1, bid_price_5, ask_price_5,
                bid_qty_1, ask_qty_1, bid_qty_2, ask_qty_2, bid_qty_3, ask_qty_3,
                bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5
            FROM bitda_depth 
            WHERE symbol = %s AND timestamp BETWEEN %s AND %s
            AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
            ORDER BY timestamp
            """
            
            cursor.execute(bitda_query, (symbol, start_ts, end_ts))
            bitda_results = cursor.fetchall()
            
            # 获取Binance数据
            binance_query = """
            SELECT 
                event_time, bid_price_1, ask_price_1, bid_price_5, ask_price_5,
                bid_qty_1, ask_qty_1, bid_qty_2, ask_qty_2, bid_qty_3, ask_qty_3,
                bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5
            FROM binance_depth_5 
            WHERE symbol = %s AND event_time BETWEEN %s AND %s
            AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
            ORDER BY event_time
            """
            
            cursor.execute(binance_query, (symbol, start_ts, end_ts))
            binance_results = cursor.fetchall()
            
            cursor.close()
            connection.close()
            
            logger.info(f"📊 {symbol} 该分钟数据: Bitda {len(bitda_results)}条, Binance {len(binance_results)}条")
            
            if not bitda_results or not binance_results:
                logger.warning(f"❌ {symbol} 该分钟数据不足")
                return {}
            
            # 计算Bitda统计
            bitda_stats = self.calculate_exchange_stats(bitda_results, 'Bitda')
            
            # 计算Binance统计  
            binance_stats = self.calculate_exchange_stats(binance_results, 'Binance')
            
            return {
                'symbol': symbol,
                'minute': target_minute,
                'bitda_stats': bitda_stats,
                'binance_stats': binance_stats,
                'bitda_sample_count': len(bitda_results),
                'binance_sample_count': len(binance_results)
            }
            
        except Exception as e:
            logger.error(f"❌ 获取分钟数据统计失败: {e}")
            return {}
    
    def calculate_exchange_stats(self, results: List, exchange_name: str) -> Dict:
        """计算交易所统计数据"""
        if not results:
            return {}
        
        # 计算价差统计 (只保留买一卖一价差)
        bid1_ask1_spreads = []

        # 计算深度统计
        bid1_qtys = []
        ask1_qtys = []
        bid2_qtys = []
        ask2_qtys = []
        bid5_totals = []
        ask5_totals = []

        for row in results:
            # 价格数据
            bid1_price = float(row[1]) if row[1] else 0
            ask1_price = float(row[2]) if row[2] else 0

            # 数量数据
            qtys = [float(row[i]) if row[i] else 0 for i in range(5, 15)]
            bid_qtys = qtys[::2]  # 买盘数量 [买1, 买2, 买3, 买4, 买5]
            ask_qtys = qtys[1::2]  # 卖盘数量 [卖1, 卖2, 卖3, 卖4, 卖5]

            # 价差计算 (只计算买一卖一价差)
            if bid1_price > 0 and ask1_price > 0:
                bid1_ask1_spreads.append(ask1_price - bid1_price)

            # 深度统计
            bid1_qtys.append(bid_qtys[0])
            ask1_qtys.append(ask_qtys[0])
            bid2_qtys.append(bid_qtys[1])
            ask2_qtys.append(ask_qtys[1])
            bid5_totals.append(sum(bid_qtys))
            ask5_totals.append(sum(ask_qtys))
        
        # 计算统计值
        def calc_stats(data_list):
            if not data_list:
                return {'min': 0, 'max': 0, 'avg': 0, 'median': 0, 'latest': 0}
            
            sorted_data = sorted(data_list)
            n = len(sorted_data)
            
            return {
                'min': min(sorted_data),
                'max': max(sorted_data),
                'avg': sum(sorted_data) / n,
                'median': sorted_data[n//2],
                'latest': data_list[-1]  # 最新的数据
            }
        
        return {
            'bid1_ask1_spread': calc_stats(bid1_ask1_spreads),
            'bid1_qty': calc_stats(bid1_qtys),
            'ask1_qty': calc_stats(ask1_qtys),
            'bid2_qty': calc_stats(bid2_qtys),
            'ask2_qty': calc_stats(ask2_qtys),
            'bid5_total': calc_stats(bid5_totals),
            'ask5_total': calc_stats(ask5_totals),
            'sample_count': len(results)
        }
    
    def analyze_specific_minute(self, symbol: str, target_minute: datetime):
        """分析指定分钟的数据"""
        logger.info(f"🔍 分析 {symbol} 在 {target_minute.strftime('%Y-%m-%d %H:%M')} 的数据")
        
        stats = self.get_minute_data_stats(symbol, target_minute)
        if not stats:
            logger.error(f"❌ 无法获取 {symbol} 的分钟统计数据")
            return
        
        bitda_stats = stats['bitda_stats']
        binance_stats = stats['binance_stats']
        
        print(f"\n📊 {symbol} {target_minute.strftime('%H:%M')} 分钟统计分析")
        print("=" * 60)
        print(f"📈 数据样本: Bitda {stats['bitda_sample_count']}条, Binance {stats['binance_sample_count']}条")
        print()
        
        # 价差对比 (只保留买一卖一价差)
        print("💰 买一卖一价差统计:")
        print("-" * 50)
        print(f"{'项目':<12} {'Bitda':<12} {'Binance':<12} {'比值':<10}")
        print("-" * 50)

        # 计算价差比值 (Bitda/Binance)
        def safe_ratio(a, b):
            return a / b if b > 0 else 0

        latest_ratio = safe_ratio(bitda_stats['bid1_ask1_spread']['latest'], binance_stats['bid1_ask1_spread']['latest'])
        max_ratio = safe_ratio(bitda_stats['bid1_ask1_spread']['max'], binance_stats['bid1_ask1_spread']['max'])
        min_ratio = safe_ratio(bitda_stats['bid1_ask1_spread']['min'], binance_stats['bid1_ask1_spread']['min'])
        avg_ratio = safe_ratio(bitda_stats['bid1_ask1_spread']['avg'], binance_stats['bid1_ask1_spread']['avg'])

        print(f"{'最新价差':<12} {bitda_stats['bid1_ask1_spread']['latest']:<12.4f} {binance_stats['bid1_ask1_spread']['latest']:<12.4f} {latest_ratio:<10.2f}")
        print(f"{'最大价差':<12} {bitda_stats['bid1_ask1_spread']['max']:<12.4f} {binance_stats['bid1_ask1_spread']['max']:<12.4f} {max_ratio:<10.2f}")
        print(f"{'最小价差':<12} {bitda_stats['bid1_ask1_spread']['min']:<12.4f} {binance_stats['bid1_ask1_spread']['min']:<12.4f} {min_ratio:<10.2f}")
        print(f"{'平均价差':<12} {bitda_stats['bid1_ask1_spread']['avg']:<12.4f} {binance_stats['bid1_ask1_spread']['avg']:<12.4f} {avg_ratio:<10.2f}")
        print(f"{'价差中位数':<12} {bitda_stats['bid1_ask1_spread']['median']:<12.4f} {binance_stats['bid1_ask1_spread']['median']:<12.4f}")
        print()

        # 深度对比 (增加更多有意义的指标)
        print("📊 深度统计:")
        print("-" * 50)
        print(f"{'项目':<12} {'Bitda':<12} {'Binance':<12} {'比值':<10}")
        print("-" * 50)

        # 计算深度比值
        bid1_ratio = safe_ratio(bitda_stats['bid1_qty']['avg'], binance_stats['bid1_qty']['avg'])
        ask1_ratio = safe_ratio(bitda_stats['ask1_qty']['avg'], binance_stats['ask1_qty']['avg'])
        bid2_ratio = safe_ratio(bitda_stats['bid2_qty']['avg'], binance_stats['bid2_qty']['avg'])
        ask2_ratio = safe_ratio(bitda_stats['ask2_qty']['avg'], binance_stats['ask2_qty']['avg'])
        bid5_ratio = safe_ratio(bitda_stats['bid5_total']['avg'], binance_stats['bid5_total']['avg'])
        ask5_ratio = safe_ratio(bitda_stats['ask5_total']['avg'], binance_stats['ask5_total']['avg'])

        print(f"{'买一平均量':<12} {bitda_stats['bid1_qty']['avg']:<12.2f} {binance_stats['bid1_qty']['avg']:<12.2f} {bid1_ratio:<10.2f}")
        print(f"{'卖一平均量':<12} {bitda_stats['ask1_qty']['avg']:<12.2f} {binance_stats['ask1_qty']['avg']:<12.2f} {ask1_ratio:<10.2f}")
        print(f"{'买二平均量':<12} {bitda_stats['bid2_qty']['avg']:<12.2f} {binance_stats['bid2_qty']['avg']:<12.2f} {bid2_ratio:<10.2f}")
        print(f"{'卖二平均量':<12} {bitda_stats['ask2_qty']['avg']:<12.2f} {binance_stats['ask2_qty']['avg']:<12.2f} {ask2_ratio:<10.2f}")
        print(f"{'买五总平均':<12} {bitda_stats['bid5_total']['avg']:<12.2f} {binance_stats['bid5_total']['avg']:<12.2f} {bid5_ratio:<10.2f}")
        print(f"{'卖五总平均':<12} {bitda_stats['ask5_total']['avg']:<12.2f} {binance_stats['ask5_total']['avg']:<12.2f} {ask5_ratio:<10.2f}")

        print()
        print("💡 分析说明:")
        print(f"  📊 数据更新频率: Binance是Bitda的 {stats['binance_sample_count']/stats['bitda_sample_count']:.1f} 倍")
        print(f"  💰 价差稳定性: {'Binance更稳定' if avg_ratio > 1 else 'Bitda更稳定'}")
        print(f"  📈 深度优势: {'Bitda深度更好' if bid1_ratio > 1 else 'Binance深度更好'}")
        print(f"  🎯 流动性: {'Bitda流动性更好' if (bid1_ratio > 1 and avg_ratio <= 1) else 'Binance流动性更好'}")

def analyze_1_3_spread():
    """分析1.3价差的具体数据"""
    print("🔍 分析最大价差1.3的原始数据")
    print("=" * 50)
    
    # 时间戳1748654232705对应的时间
    timestamp = 1748654232705
    dt = datetime.fromtimestamp(timestamp / 1000)
    print(f"📅 时间戳: {timestamp}")
    print(f"🕐 北京时间: {dt.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
    
    # 获取原始数据
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'Linuxtest',
        'database': 'depth_db'
    }
    
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        query = """
        SELECT timestamp, bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
               (ask_price_1 - bid_price_1) as spread
        FROM bitda_depth 
        WHERE timestamp = %s AND symbol = 'BTCUSDT'
        LIMIT 1
        """
        
        cursor.execute(query, (timestamp,))
        result = cursor.fetchone()
        
        if result:
            print(f"\n📊 BTCUSDT 1.3价差原始数据:")
            print("-" * 40)
            print(f"买一价格: {result[1]:.2f}")
            print(f"卖一价格: {result[2]:.2f}")
            print(f"买一数量: {result[3]:.4f}")
            print(f"卖一数量: {result[4]:.4f}")
            print(f"价差: {result[5]:.4f}")
            print()
            print("💡 这个1.3的价差是买一卖一之间的正常价差")
            print("💡 相对于10万+的价格水平，1.3的价差约为0.0013%")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")

def main():
    """主函数"""
    print("🔧 正确的按分钟统计深度价差分析器 (优化版)")
    print("=" * 60)
    print("修正问题:")
    print("  ❌ 原逻辑: 随机取5条最新数据")
    print("  ✅ 新逻辑: 按分钟统计该分钟内所有数据")
    print("  ❌ 原逻辑: 基于时间窗口的价差统计")
    print("  ✅ 新逻辑: 基于分钟内所有数据的统计")
    print("  ❌ 原逻辑: 包含买五卖五价差对比")
    print("  ✅ 新逻辑: 取消买五卖五价差，专注有意义指标")
    print()

    # 分析1.3价差的数据
    analyze_1_3_spread()

    # 分析指定分钟的数据
    analyzer = CorrectMinuteAnalyzer()

    # 分析包含1.3价差的那一分钟
    target_time = datetime.fromtimestamp(1748654232705 / 1000)
    target_minute = target_time.replace(second=0, microsecond=0)

    analyzer.analyze_specific_minute('BTCUSDT', target_minute)

if __name__ == "__main__":
    main()
