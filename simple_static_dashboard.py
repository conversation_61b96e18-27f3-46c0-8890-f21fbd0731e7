#!/usr/bin/env python3
"""
创建简单的静态延时分析面板
显示最近一次可用的数据，不要求实时更新
"""

import requests
import json
import mysql.connector
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleStaticDashboard:
    """简单静态仪表板创建器"""
    
    def __init__(self):
        self.grafana_url = "http://localhost:3000"
        self.admin_user = "admin"
        self.admin_pass = "admin"
        self.session = requests.Session()
        self.session.auth = (self.admin_user, self.admin_pass)
        
        self.latency_db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'ethusdt_latency_db'
        }
    
    def get_latest_data(self):
        """获取最近的延时数据"""
        logger.info("📊 获取最近的延时数据...")
        
        try:
            connection = mysql.connector.connect(**self.latency_db_config)
            cursor = connection.cursor()
            
            # 获取最近的统计数据
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_matches,
                    AVG(latency_ms) as avg_latency,
                    MIN(latency_ms) as min_latency,
                    MAX(latency_ms) as max_latency,
                    MAX(created_at) as latest_time,
                    MIN(created_at) as earliest_time
                FROM ethusdt_latency_matches
            """)
            
            result = cursor.fetchone()
            if result and result[0] > 0:
                total, avg_lat, min_lat, max_lat, latest_time, earliest_time = result
                
                # 获取最近24小时的数据用于图表
                cursor.execute("""
                    SELECT 
                        UNIX_TIMESTAMP(created_at) * 1000 as timestamp_ms,
                        latency_ms
                    FROM ethusdt_latency_matches
                    WHERE created_at >= NOW() - INTERVAL 24 HOUR
                    ORDER BY created_at
                    LIMIT 1000
                """)
                
                chart_data = cursor.fetchall()
                
                logger.info(f"   ✅ 找到数据: 总计{total}条匹配")
                logger.info(f"   📈 平均延时: {avg_lat:.1f}ms")
                logger.info(f"   📊 延时范围: {min_lat}-{max_lat}ms")
                logger.info(f"   ⏰ 最新时间: {latest_time}")
                logger.info(f"   📋 图表数据: {len(chart_data)}个点")
                
                cursor.close()
                connection.close()
                
                return {
                    'total': total,
                    'avg_latency': round(avg_lat, 1),
                    'min_latency': min_lat,
                    'max_latency': max_lat,
                    'latest_time': latest_time,
                    'earliest_time': earliest_time,
                    'chart_data': chart_data
                }
            else:
                logger.warning("   ❌ 未找到延时数据")
                cursor.close()
                connection.close()
                return None
                
        except Exception as e:
            logger.error(f"   ❌ 获取数据失败: {e}")
            return None
    
    def create_simple_dashboard(self, data):
        """创建简单的静态仪表板"""
        logger.info("📋 创建简单静态仪表板...")
        
        if not data:
            logger.error("   ❌ 无数据，无法创建仪表板")
            return None
        
        # 格式化时间
        latest_time_str = data['latest_time'].strftime('%Y-%m-%d %H:%M:%S')
        time_span = data['latest_time'] - data['earliest_time']
        days = time_span.days
        hours = time_span.seconds // 3600
        
        dashboard_config = {
            "dashboard": {
                "id": None,
                "title": f"⚡ ETHUSDT延时分析 (静态数据) - {datetime.now().strftime('%m-%d %H:%M')}",
                "tags": ["ethusdt", "latency", "static"],
                "timezone": "browser",
                "panels": [
                    {
                        "id": 1,
                        "title": f"📊 平均延时\n{data['avg_latency']}ms",
                        "type": "stat",
                        "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0},
                        "targets": [],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None},
                                        {"color": "yellow", "value": 300},
                                        {"color": "red", "value": 600}
                                    ]
                                },
                                "unit": "ms",
                                "decimals": 1,
                                "displayName": ""
                            }
                        },
                        "options": {
                            "colorMode": "background",
                            "graphMode": "none",
                            "justifyMode": "center",
                            "orientation": "auto",
                            "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": False},
                            "textMode": "value_and_name",
                            "text": {
                                "titleSize": 16,
                                "valueSize": 32
                            }
                        },
                        "pluginVersion": "8.0.0"
                    },
                    {
                        "id": 2,
                        "title": f"📈 延时范围\n{data['min_latency']}-{data['max_latency']}ms",
                        "type": "stat",
                        "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0},
                        "targets": [],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None}
                                    ]
                                },
                                "unit": "ms",
                                "decimals": 0,
                                "displayName": ""
                            }
                        },
                        "options": {
                            "colorMode": "background",
                            "graphMode": "none",
                            "justifyMode": "center",
                            "orientation": "auto",
                            "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": False},
                            "textMode": "value_and_name",
                            "text": {
                                "titleSize": 16,
                                "valueSize": 24
                            }
                        }
                    },
                    {
                        "id": 3,
                        "title": f"📊 总匹配数\n{data['total']}个",
                        "type": "stat",
                        "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0},
                        "targets": [],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "red", "value": None},
                                        {"color": "yellow", "value": 100},
                                        {"color": "green", "value": 500}
                                    ]
                                },
                                "unit": "short",
                                "decimals": 0,
                                "displayName": ""
                            }
                        },
                        "options": {
                            "colorMode": "background",
                            "graphMode": "none",
                            "justifyMode": "center",
                            "orientation": "auto",
                            "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": False},
                            "textMode": "value_and_name",
                            "text": {
                                "titleSize": 16,
                                "valueSize": 28
                            }
                        }
                    },
                    {
                        "id": 4,
                        "title": f"⏰ 数据时间\n{latest_time_str}",
                        "type": "stat",
                        "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0},
                        "targets": [],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None}
                                    ]
                                },
                                "unit": "dateTimeAsIso",
                                "decimals": 0,
                                "displayName": ""
                            }
                        },
                        "options": {
                            "colorMode": "background",
                            "graphMode": "none",
                            "justifyMode": "center",
                            "orientation": "auto",
                            "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": False},
                            "textMode": "name",
                            "text": {
                                "titleSize": 14,
                                "valueSize": 16
                            }
                        }
                    },
                    {
                        "id": 5,
                        "title": f"📈 延时趋势 (数据跨度: {days}天{hours}小时)",
                        "type": "text",
                        "gridPos": {"h": 10, "w": 24, "x": 0, "y": 8},
                        "targets": [],
                        "options": {
                            "mode": "html",
                            "content": f"""
                            <div style="padding: 20px; text-align: center;">
                                <h3>📊 ETHUSDT延时分析总结</h3>
                                <div style="display: flex; justify-content: space-around; margin: 20px 0;">
                                    <div style="background: #1f77b4; color: white; padding: 15px; border-radius: 8px; min-width: 150px;">
                                        <h4>平均延时</h4>
                                        <h2>{data['avg_latency']}ms</h2>
                                    </div>
                                    <div style="background: #ff7f0e; color: white; padding: 15px; border-radius: 8px; min-width: 150px;">
                                        <h4>最小延时</h4>
                                        <h2>{data['min_latency']}ms</h2>
                                    </div>
                                    <div style="background: #2ca02c; color: white; padding: 15px; border-radius: 8px; min-width: 150px;">
                                        <h4>最大延时</h4>
                                        <h2>{data['max_latency']}ms</h2>
                                    </div>
                                    <div style="background: #d62728; color: white; padding: 15px; border-radius: 8px; min-width: 150px;">
                                        <h4>总匹配数</h4>
                                        <h2>{data['total']}</h2>
                                    </div>
                                </div>
                                <div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                                    <h4>📋 数据说明</h4>
                                    <p><strong>数据时间范围:</strong> {data['earliest_time'].strftime('%Y-%m-%d %H:%M')} ~ {data['latest_time'].strftime('%Y-%m-%d %H:%M')}</p>
                                    <p><strong>延时含义:</strong> Bitda出现特定买一卖一价格对比Binance首次出现相同价格对晚了多少毫秒</p>
                                    <p><strong>匹配条件:</strong> 要求Bitda和Binance的买一价和卖一价完全相同</p>
                                    <p><strong>数据质量:</strong> 每个延时记录都是高质量的完全匹配</p>
                                </div>
                                <div style="margin: 20px 0; padding: 15px; background: #e8f5e8; border-radius: 8px;">
                                    <h4>🎯 分析结论</h4>
                                    <p>• 平均延时 <strong>{data['avg_latency']}ms</strong> 表示价格信息从Binance传播到Bitda平均需要约 <strong>{data['avg_latency']/1000:.1f}秒</strong></p>
                                    <p>• 延时范围 <strong>{data['min_latency']}-{data['max_latency']}ms</strong> 显示了网络传播的波动情况</p>
                                    <p>• 总计 <strong>{data['total']}</strong> 个完全匹配说明了数据的可靠性</p>
                                </div>
                            </div>
                            """
                        }
                    }
                ],
                "time": {"from": "now-24h", "to": "now"},
                "timepicker": {},
                "templating": {"list": []},
                "annotations": {"list": []},
                "refresh": "5m",
                "schemaVersion": 30,
                "version": 1,
                "links": []
            },
            "overwrite": True
        }
        
        try:
            response = self.session.post(
                f"{self.grafana_url}/api/dashboards/db",
                json=dashboard_config,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                dashboard_url = f"{self.grafana_url}{result.get('url', '')}"
                logger.info(f"   ✅ 静态仪表板创建成功")
                logger.info(f"   🌐 访问地址: {dashboard_url}")
                return dashboard_url
            else:
                logger.error(f"   ❌ 仪表板创建失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            logger.error(f"   ❌ 仪表板创建异常: {e}")
        
        return None
    
    def create_static_dashboard(self):
        """创建静态仪表板"""
        logger.info("📊 开始创建简单静态延时分析仪表板...")
        logger.info("=" * 60)
        
        # 1. 获取最新数据
        data = self.get_latest_data()
        if not data:
            logger.error("❌ 无法获取数据，创建失败")
            return False
        
        # 2. 创建静态仪表板
        dashboard_url = self.create_simple_dashboard(data)
        if not dashboard_url:
            logger.error("❌ 仪表板创建失败")
            return False
        
        logger.info("\n" + "=" * 60)
        logger.info("🎉 简单静态延时分析仪表板创建完成！")
        logger.info("📋 特点:")
        logger.info("   ✅ 显示最近一次可用的数据")
        logger.info("   ✅ 不依赖实时查询")
        logger.info("   ✅ 简单清晰的数据展示")
        logger.info("   ✅ 包含详细的数据说明")
        logger.info(f"🌐 仪表板地址: {dashboard_url}")
        logger.info("🔄 每5分钟刷新一次")
        logger.info("=" * 60)
        
        return dashboard_url

def main():
    """主函数"""
    print("📊 简单静态延时分析仪表板创建工具")
    print("=" * 50)
    print("功能:")
    print("  - 显示最近一次可用的延时数据")
    print("  - 不要求实时更新")
    print("  - 简单清晰的数据展示")
    print("  - 包含数据时间标注")
    print()
    
    creator = SimpleStaticDashboard()
    dashboard_url = creator.create_static_dashboard()
    
    if dashboard_url:
        print("\n✅ 创建成功！")
        print("📊 现在您可以看到简单清晰的延时分析数据")
        print("⏰ 数据包含时间标注，不要求实时更新")
        
        # 自动打开浏览器
        try:
            import webbrowser
            webbrowser.open(dashboard_url)
            print("🌐 浏览器已自动打开")
        except:
            print(f"🌐 请手动访问: {dashboard_url}")
    else:
        print("\n❌ 创建失败，请检查数据状态")

if __name__ == "__main__":
    main()
