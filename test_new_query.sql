WITH bitda AS (
  SELECT ask_qty_1, bid_qty_1, bid_qty_2, ask_qty_2 
  FROM crypto.bitda_depth 
  WHERE symbol = 'BTCUSDT' 
  ORDER BY timestamp DESC 
  LIMIT 1
), 
binance AS (
  SELECT ask_qty, bid_qty 
  FROM crypto.binance_bookticker 
  WHERE symbol = 'BTCUSDT' 
  ORDER BY event_time DESC 
  LIMIT 1
) 
SELECT 
  arrayJoin([
    ('Ask Qty 1', round(bitda.ask_qty_1, 2), round(binance.ask_qty, 2), round(bitda.ask_qty_1 / binance.ask_qty, 2)),
    ('Bid Qty 1', round(bitda.bid_qty_1, 2), round(binance.bid_qty, 2), round(bitda.bid_qty_1 / binance.bid_qty, 2))
  ]) AS data, 
  data.1 AS item, 
  data.2 AS Bitda, 
  data.3 AS Binance, 
  data.4 AS ratio 
FROM bitda, binance
