<!DOCTYPE html>
<html>
<head>
    <title>ClickHouse深度对比仪表板</title>
    <meta charset="UTF-8">
    <meta http-equiv="refresh" content="60">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .panel {
            background-color: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .panel-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #00d4ff;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #444;
        }
        th {
            background-color: #333;
            color: #00d4ff;
        }
        .ratio {
            font-weight: bold;
            color: #00ff88;
        }
        .timestamp {
            text-align: center;
            margin-top: 20px;
            color: #888;
        }
        .status {
            background-color: #333;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .working {
            color: #00ff88;
        }
        .error {
            color: #ff4444;
        }
    </style>
    <script>
        async function updateData() {
            try {
                // 这里应该调用API获取实时数据
                // 由于Grafana ClickHouse插件问题，暂时显示静态数据
                console.log('数据更新中...');
            } catch (error) {
                console.error('数据更新失败:', error);
            }
        }
        
        // 页面加载时更新数据
        window.onload = function() {
            updateData();
        };
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 ClickHouse深度对比仪表板</h1>
            <p>实时显示BTCUSDT和ETHUSDT的深度数据对比</p>
        </div>
        
        <div class="status">
            <p class="error">⚠️ Grafana ClickHouse插件配置问题</p>
            <p>数据来源: ClickHouse crypto数据库 (3612条BTCUSDT记录, 2134条ETHUSDT记录)</p>
            <p>问题: Grafana插件健康检查失败，查询语法Decimal精度错误</p>
        </div>
        
        <div class="dashboard">
            <div class="panel">
                <div class="panel-title">📊 BTCUSDT 深度对比</div>
                <table>
                    <thead>
                        <tr>
                            <th>项目</th>
                            <th>Bitda</th>
                            <th>Binance</th>
                            <th>深度比</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>卖一量</td>
                            <td>14.27</td>
                            <td>4.33</td>
                            <td class="ratio">3.30</td>
                        </tr>
                        <tr>
                            <td>买一量</td>
                            <td>0.35</td>
                            <td>3.86</td>
                            <td class="ratio">0.09</td>
                        </tr>
                        <tr>
                            <td>买一量卖一量</td>
                            <td>14.62</td>
                            <td>8.19</td>
                            <td class="ratio">1.78</td>
                        </tr>
                        <tr>
                            <td>买卖前两档量</td>
                            <td>44.43</td>
                            <td>8.19</td>
                            <td class="ratio">5.42</td>
                        </tr>
                        <tr>
                            <td>买卖前五档量</td>
                            <td>101.40</td>
                            <td>8.19</td>
                            <td class="ratio">12.38</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="panel">
                <div class="panel-title">📊 ETHUSDT 深度对比</div>
                <table>
                    <thead>
                        <tr>
                            <th>项目</th>
                            <th>Bitda</th>
                            <th>Binance</th>
                            <th>深度比</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>卖一量</td>
                            <td>25.18</td>
                            <td>12.87</td>
                            <td class="ratio">1.96</td>
                        </tr>
                        <tr>
                            <td>买一量</td>
                            <td>13.31</td>
                            <td>18.19</td>
                            <td class="ratio">0.73</td>
                        </tr>
                        <tr>
                            <td>买一量卖一量</td>
                            <td>38.49</td>
                            <td>31.06</td>
                            <td class="ratio">1.24</td>
                        </tr>
                        <tr>
                            <td>买卖前两档量</td>
                            <td>78.43</td>
                            <td>31.06</td>
                            <td class="ratio">2.53</td>
                        </tr>
                        <tr>
                            <td>买卖前五档量</td>
                            <td>156.40</td>
                            <td>31.06</td>
                            <td class="ratio">5.04</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="timestamp">
            <p>📅 页面创建时间: <span id="current-time"></span></p>
            <p>🔄 页面每60秒自动刷新</p>
            <p>💾 数据来源: ClickHouse crypto数据库</p>
            <p class="error">⚠️ 注意: 由于Grafana ClickHouse插件配置问题，当前显示示例数据</p>
        </div>
    </div>
    
    <script>
        // 显示当前时间
        document.getElementById('current-time').textContent = new Date().toLocaleString('zh-CN');
    </script>
</body>
</html>
