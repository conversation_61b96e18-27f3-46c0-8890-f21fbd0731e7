#!/usr/bin/env python3
"""
修复后的深度价差对比分析器
正确计算买二卖二、买五卖五
"""

import mysql.connector
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FixedDepthSpreadAnalyzer:
    """修复后的深度价差分析器"""
    
    def __init__(self):
        # 数据库配置
        self.source_db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'depth_db'
        }
        
        self.analysis_db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'depth_spread_analysis'
        }
        
        # 确保分析数据库存在
        self.init_analysis_database()
    
    def init_analysis_database(self):
        """初始化分析结果数据库"""
        try:
            # 连接MySQL创建数据库
            connection = mysql.connector.connect(
                host='localhost',
                user='root',
                password='Linuxtest'
            )
            cursor = connection.cursor()
            
            # 创建数据库
            cursor.execute("CREATE DATABASE IF NOT EXISTS depth_spread_analysis")
            cursor.execute("USE depth_spread_analysis")
            
            # 创建深度对比表
            depth_comparison_sql = """
            CREATE TABLE IF NOT EXISTS depth_comparison (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                symbol VARCHAR(20) NOT NULL COMMENT '交易对',
                analysis_time DATETIME NOT NULL COMMENT '分析时间',
                bitda_timestamp BIGINT NOT NULL COMMENT 'Bitda时间戳',
                binance_timestamp BIGINT NOT NULL COMMENT '匹配的Binance时间戳',
                time_diff_ms INT NOT NULL COMMENT '时间差(毫秒)',
                
                -- Bitda深度数据
                bitda_bid1_qty DECIMAL(20,4) COMMENT 'Bitda买一量',
                bitda_ask1_qty DECIMAL(20,4) COMMENT 'Bitda卖一量',
                bitda_bid2_qty DECIMAL(20,4) COMMENT 'Bitda买二量',
                bitda_ask2_qty DECIMAL(20,4) COMMENT 'Bitda卖二量',
                bitda_bid5_total DECIMAL(20,4) COMMENT 'Bitda买一到买五总量',
                bitda_ask5_total DECIMAL(20,4) COMMENT 'Bitda卖一到卖五总量',
                
                -- Binance深度数据
                binance_bid1_qty DECIMAL(20,4) COMMENT 'Binance买一量',
                binance_ask1_qty DECIMAL(20,4) COMMENT 'Binance卖一量',
                binance_bid2_qty DECIMAL(20,4) COMMENT 'Binance买二量',
                binance_ask2_qty DECIMAL(20,4) COMMENT 'Binance卖二量',
                binance_bid5_total DECIMAL(20,4) COMMENT 'Binance买一到买五总量',
                binance_ask5_total DECIMAL(20,4) COMMENT 'Binance卖一到卖五总量',
                
                -- 深度比值
                bid1_ratio DECIMAL(10,4) COMMENT '买一量比值(Bitda/Binance)',
                ask1_ratio DECIMAL(10,4) COMMENT '卖一量比值(Bitda/Binance)',
                bid_ask1_ratio DECIMAL(10,4) COMMENT '买一卖一总量比值',
                bid_ask2_ratio DECIMAL(10,4) COMMENT '买二卖二总量比值',
                bid_ask5_ratio DECIMAL(10,4) COMMENT '买五卖五总量比值',
                
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_symbol_time (symbol, analysis_time),
                INDEX idx_created_at (created_at)
            ) COMMENT='深度对比分析结果'
            """
            
            # 创建价差对比表
            spread_comparison_sql = """
            CREATE TABLE IF NOT EXISTS spread_comparison (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                symbol VARCHAR(20) NOT NULL COMMENT '交易对',
                analysis_time DATETIME NOT NULL COMMENT '分析时间',
                bitda_timestamp BIGINT NOT NULL COMMENT 'Bitda时间戳',

                -- Bitda价差数据
                bitda_bid1_price DECIMAL(15,2) COMMENT 'Bitda买一价',
                bitda_ask1_price DECIMAL(15,2) COMMENT 'Bitda卖一价',
                bitda_spread DECIMAL(15,4) COMMENT 'Bitda价差(卖一-买一)',

                -- Bitda价差统计(基于时间窗口)
                bitda_spread_latest DECIMAL(15,4) COMMENT 'Bitda最近价差',
                bitda_spread_max DECIMAL(15,4) COMMENT 'Bitda最大价差',
                bitda_spread_min DECIMAL(15,4) COMMENT 'Bitda最小价差',
                bitda_spread_avg DECIMAL(15,4) COMMENT 'Bitda平均价差',
                bitda_spread_median DECIMAL(15,4) COMMENT 'Bitda价差中位数',
                bitda_bid5_ask5_spread_max DECIMAL(15,4) COMMENT 'Bitda买五卖五最大价差',
                bitda_bid5_ask5_spread_min DECIMAL(15,4) COMMENT 'Bitda买五卖五最小价差',
                bitda_sample_count INT COMMENT 'Bitda样本数量',

                -- Binance价差统计(基于时间窗口)
                binance_spread_latest DECIMAL(15,4) COMMENT 'Binance最近价差',
                binance_spread_max DECIMAL(15,4) COMMENT 'Binance最大价差',
                binance_spread_min DECIMAL(15,4) COMMENT 'Binance最小价差',
                binance_spread_avg DECIMAL(15,4) COMMENT 'Binance平均价差',
                binance_spread_median DECIMAL(15,4) COMMENT 'Binance价差中位数',
                binance_bid5_ask5_spread_max DECIMAL(15,4) COMMENT 'Binance买五卖五最大价差',
                binance_bid5_ask5_spread_min DECIMAL(15,4) COMMENT 'Binance买五卖五最小价差',
                binance_sample_count INT COMMENT 'Binance样本数量',

                -- 买一卖一价差对比
                bid_ask1_spread_diff DECIMAL(15,4) COMMENT '买一卖一价差差值(Bitda-Binance)',

                time_window_minutes INT DEFAULT 5 COMMENT '时间窗口(分钟)',

                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_symbol_time (symbol, analysis_time),
                INDEX idx_created_at (created_at)
            ) COMMENT='价差对比分析结果'
            """
            
            cursor.execute(depth_comparison_sql)
            cursor.execute(spread_comparison_sql)
            
            cursor.close()
            connection.close()
            
            logger.info("✅ 分析数据库初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 数据库初始化失败: {e}")
            raise
    
    def get_bitda_data_sample(self, symbol: str, limit: int = 10) -> List[Dict]:
        """获取Bitda数据样本"""
        try:
            connection = mysql.connector.connect(**self.source_db_config)
            cursor = connection.cursor()
            
            query = """
            SELECT 
                timestamp, bid_price_1, ask_price_1,
                bid_qty_1, ask_qty_1, bid_qty_2, ask_qty_2, bid_qty_3, ask_qty_3, 
                bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5
            FROM bitda_depth 
            WHERE symbol = %s AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
            ORDER BY created_at DESC 
            LIMIT %s
            """
            
            cursor.execute(query, (symbol, limit))
            results = cursor.fetchall()
            
            data = []
            for row in results:
                # 正确计算买五卖五总量
                bid_qtys = [float(row[i]) if row[i] else 0 for i in [3, 5, 7, 9, 11]]  # 买一到买五
                ask_qtys = [float(row[i]) if row[i] else 0 for i in [4, 6, 8, 10, 12]]  # 卖一到卖五
                
                bid5_total = sum(bid_qtys)
                ask5_total = sum(ask_qtys)
                
                data.append({
                    'timestamp': row[0],
                    'bid1_price': float(row[1]) if row[1] else 0,
                    'ask1_price': float(row[2]) if row[2] else 0,
                    'bid1_qty': bid_qtys[0],
                    'ask1_qty': ask_qtys[0],
                    'bid2_qty': bid_qtys[1],
                    'ask2_qty': ask_qtys[1],
                    'bid5_total': bid5_total,
                    'ask5_total': ask5_total,
                    'spread': float(row[2] - row[1]) if row[1] and row[2] else 0
                })
            
            cursor.close()
            connection.close()
            
            logger.info(f"📊 获取{symbol} Bitda数据: {len(data)}条")
            return data
            
        except Exception as e:
            logger.error(f"❌ 获取Bitda数据失败: {e}")
            return []
    
    def find_closest_binance_data(self, symbol: str, bitda_timestamp: int, window_minutes: int = 5) -> Dict:
        """找到最接近的Binance数据"""
        try:
            connection = mysql.connector.connect(**self.source_db_config)
            cursor = connection.cursor()
            
            # 时间窗口
            window_ms = window_minutes * 60 * 1000
            start_ts = bitda_timestamp - window_ms
            end_ts = bitda_timestamp + window_ms
            
            # 查找最接近的数据
            query = """
            SELECT 
                event_time, bid_price_1, ask_price_1,
                bid_qty_1, ask_qty_1, bid_qty_2, ask_qty_2, bid_qty_3, ask_qty_3,
                bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5,
                ABS(event_time - %s) as time_diff
            FROM binance_depth_5 
            WHERE symbol = %s AND event_time BETWEEN %s AND %s
            AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
            ORDER BY time_diff
            LIMIT 1
            """
            
            cursor.execute(query, (bitda_timestamp, symbol, start_ts, end_ts))
            result = cursor.fetchone()
            
            if result:
                # 正确计算买五卖五总量
                bid_qtys = [float(result[i]) if result[i] else 0 for i in [3, 5, 7, 9, 11]]  # 买一到买五
                ask_qtys = [float(result[i]) if result[i] else 0 for i in [4, 6, 8, 10, 12]]  # 卖一到卖五
                
                bid5_total = sum(bid_qtys)
                ask5_total = sum(ask_qtys)
                
                data = {
                    'timestamp': result[0],
                    'bid1_price': float(result[1]) if result[1] else 0,
                    'ask1_price': float(result[2]) if result[2] else 0,
                    'bid1_qty': bid_qtys[0],
                    'ask1_qty': ask_qtys[0],
                    'bid2_qty': bid_qtys[1],
                    'ask2_qty': ask_qtys[1],
                    'bid5_total': bid5_total,
                    'ask5_total': ask5_total,
                    'spread': float(result[2] - result[1]) if result[1] and result[2] else 0,
                    'time_diff_ms': int(result[13])
                }
                
                cursor.close()
                connection.close()
                return data
            
            cursor.close()
            connection.close()
            return {}
            
        except Exception as e:
            logger.error(f"❌ 查找Binance数据失败: {e}")
            return {}
    
    def analyze_symbol(self, symbol: str) -> bool:
        """分析单个交易对"""
        logger.info(f"🔍 开始分析 {symbol}...")
        
        try:
            analysis_time = datetime.now()
            
            # 获取Bitda数据样本
            bitda_data = self.get_bitda_data_sample(symbol, 5)  # 只取5条最新数据
            if not bitda_data:
                logger.warning(f"   ❌ {symbol} 无Bitda数据")
                return False
            
            success_count = 0
            
            for bitda_record in bitda_data:
                # 查找匹配的Binance数据
                binance_data = self.find_closest_binance_data(symbol, bitda_record['timestamp'])
                if not binance_data:
                    continue
                
                # 计算深度对比
                depth_result = self.calculate_depth_comparison(symbol, bitda_record, binance_data, analysis_time)
                if depth_result:
                    self.save_depth_comparison_result(depth_result)
                    success_count += 1
                
                # 计算价差对比
                bitda_spread_stats = self.get_bitda_spread_stats(symbol, bitda_record['timestamp'])
                binance_spread_stats = self.get_binance_spread_stats(symbol, bitda_record['timestamp'])
                if bitda_spread_stats and binance_spread_stats:
                    spread_result = self.calculate_spread_comparison(symbol, bitda_record, bitda_spread_stats, binance_spread_stats, analysis_time)
                    if spread_result:
                        self.save_spread_comparison_result(spread_result)
            
            logger.info(f"   ✅ {symbol}: 成功分析 {success_count} 条数据")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"   ❌ {symbol} 分析失败: {e}")
            return False
    
    def calculate_depth_comparison(self, symbol: str, bitda_data: Dict, binance_data: Dict, analysis_time: datetime) -> Dict:
        """计算深度对比"""
        try:
            # 计算深度比值 (避免除零)
            def safe_ratio(a, b):
                return float(a) / float(b) if b and float(b) > 0 else 0
            
            bid1_ratio = safe_ratio(bitda_data['bid1_qty'], binance_data['bid1_qty'])
            ask1_ratio = safe_ratio(bitda_data['ask1_qty'], binance_data['ask1_qty'])
            
            # 买一卖一总量比值
            bitda_bid_ask1_total = bitda_data['bid1_qty'] + bitda_data['ask1_qty']
            binance_bid_ask1_total = binance_data['bid1_qty'] + binance_data['ask1_qty']
            bid_ask1_ratio = safe_ratio(bitda_bid_ask1_total, binance_bid_ask1_total)
            
            # 买二卖二总量比值 (买一+买二+卖一+卖二)
            bitda_bid_ask2_total = bitda_data['bid1_qty'] + bitda_data['bid2_qty'] + bitda_data['ask1_qty'] + bitda_data['ask2_qty']
            binance_bid_ask2_total = binance_data['bid1_qty'] + binance_data['bid2_qty'] + binance_data['ask1_qty'] + binance_data['ask2_qty']
            bid_ask2_ratio = safe_ratio(bitda_bid_ask2_total, binance_bid_ask2_total)
            
            # 买五卖五总量比值
            bitda_bid_ask5_total = bitda_data['bid5_total'] + bitda_data['ask5_total']
            binance_bid_ask5_total = binance_data['bid5_total'] + binance_data['ask5_total']
            bid_ask5_ratio = safe_ratio(bitda_bid_ask5_total, binance_bid_ask5_total)
            
            return {
                'symbol': symbol,
                'analysis_time': analysis_time,
                'bitda_timestamp': bitda_data['timestamp'],
                'binance_timestamp': binance_data['timestamp'],
                'time_diff_ms': binance_data['time_diff_ms'],
                
                # Bitda深度数据
                'bitda_bid1_qty': bitda_data['bid1_qty'],
                'bitda_ask1_qty': bitda_data['ask1_qty'],
                'bitda_bid2_qty': bitda_data['bid2_qty'],
                'bitda_ask2_qty': bitda_data['ask2_qty'],
                'bitda_bid5_total': bitda_data['bid5_total'],
                'bitda_ask5_total': bitda_data['ask5_total'],
                
                # Binance深度数据
                'binance_bid1_qty': binance_data['bid1_qty'],
                'binance_ask1_qty': binance_data['ask1_qty'],
                'binance_bid2_qty': binance_data['bid2_qty'],
                'binance_ask2_qty': binance_data['ask2_qty'],
                'binance_bid5_total': binance_data['bid5_total'],
                'binance_ask5_total': binance_data['ask5_total'],
                
                # 深度比值
                'bid1_ratio': bid1_ratio,
                'ask1_ratio': ask1_ratio,
                'bid_ask1_ratio': bid_ask1_ratio,
                'bid_ask2_ratio': bid_ask2_ratio,
                'bid_ask5_ratio': bid_ask5_ratio
            }
            
        except Exception as e:
            logger.error(f"计算深度对比失败: {e}")
            return {}
    
    def get_bitda_spread_stats(self, symbol: str, bitda_timestamp: int, window_minutes: int = 5) -> Dict:
        """获取Bitda价差统计数据"""
        try:
            connection = mysql.connector.connect(**self.source_db_config)
            cursor = connection.cursor()

            # 时间窗口 - 查找Bitda时间戳前后的数据
            window_ms = window_minutes * 60 * 1000
            start_ts = bitda_timestamp - window_ms
            end_ts = bitda_timestamp + window_ms

            query = """
            SELECT
                bid_price_1, ask_price_1, bid_price_5, ask_price_5
            FROM bitda_depth
            WHERE symbol = %s AND timestamp BETWEEN %s AND %s
            AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
            ORDER BY timestamp DESC
            LIMIT 100
            """

            cursor.execute(query, (symbol, start_ts, end_ts))
            results = cursor.fetchall()

            if not results:
                cursor.close()
                connection.close()
                return {}

            # 计算价差统计
            spreads = []
            bid5_ask5_spreads = []

            for row in results:
                bid1, ask1 = float(row[0]), float(row[1])
                spread = ask1 - bid1
                spreads.append(spread)

                # 买五卖五价差
                if row[2] and row[3]:
                    bid5, ask5 = float(row[2]), float(row[3])
                    bid5_ask5_spreads.append(ask5 - bid5)

            # 计算统计值
            spreads.sort()
            n = len(spreads)
            median = spreads[n//2] if n > 0 else 0

            stats = {
                'latest': spreads[0] if spreads else 0,
                'max': max(spreads) if spreads else 0,
                'min': min(spreads) if spreads else 0,
                'avg': sum(spreads) / len(spreads) if spreads else 0,
                'median': median,
                'bid5_ask5_max': max(bid5_ask5_spreads) if bid5_ask5_spreads else 0,
                'bid5_ask5_min': min(bid5_ask5_spreads) if bid5_ask5_spreads else 0,
                'sample_count': len(spreads)
            }

            cursor.close()
            connection.close()
            return stats

        except Exception as e:
            logger.error(f"❌ 获取Bitda价差统计失败: {e}")
            return {}

    def get_binance_spread_stats(self, symbol: str, bitda_timestamp: int, window_minutes: int = 5) -> Dict:
        """获取Binance价差统计数据"""
        try:
            connection = mysql.connector.connect(**self.source_db_config)
            cursor = connection.cursor()
            
            # 时间窗口 - 只查找Bitda时间戳之前的数据
            window_ms = window_minutes * 60 * 1000
            start_ts = bitda_timestamp - window_ms
            end_ts = bitda_timestamp
            
            query = """
            SELECT 
                bid_price_1, ask_price_1, bid_price_5, ask_price_5
            FROM binance_depth_5 
            WHERE symbol = %s AND event_time BETWEEN %s AND %s
            AND bid_price_1 IS NOT NULL AND ask_price_1 IS NOT NULL
            ORDER BY event_time DESC
            LIMIT 100
            """
            
            cursor.execute(query, (symbol, start_ts, end_ts))
            results = cursor.fetchall()
            
            if not results:
                cursor.close()
                connection.close()
                return {}
            
            # 计算价差统计
            spreads = []
            bid5_ask5_spreads = []
            
            for row in results:
                bid1, ask1 = float(row[0]), float(row[1])
                spread = ask1 - bid1
                spreads.append(spread)
                
                # 买五卖五价差
                if row[2] and row[3]:
                    bid5, ask5 = float(row[2]), float(row[3])
                    bid5_ask5_spreads.append(ask5 - bid5)
            
            # 计算统计值
            spreads.sort()
            n = len(spreads)
            median = spreads[n//2] if n > 0 else 0
            
            stats = {
                'latest': spreads[0] if spreads else 0,
                'max': max(spreads) if spreads else 0,
                'min': min(spreads) if spreads else 0,
                'avg': sum(spreads) / len(spreads) if spreads else 0,
                'median': median,
                'bid5_ask5_max': max(bid5_ask5_spreads) if bid5_ask5_spreads else 0,
                'bid5_ask5_min': min(bid5_ask5_spreads) if bid5_ask5_spreads else 0,
                'sample_count': len(spreads)
            }
            
            cursor.close()
            connection.close()
            return stats
            
        except Exception as e:
            logger.error(f"❌ 获取Binance价差统计失败: {e}")
            return {}
    
    def calculate_spread_comparison(self, symbol: str, bitda_data: Dict, bitda_spread_stats: Dict, binance_spread_stats: Dict, analysis_time: datetime) -> Dict:
        """计算价差对比"""
        try:
            return {
                'symbol': symbol,
                'analysis_time': analysis_time,
                'bitda_timestamp': bitda_data['timestamp'],

                # Bitda价差数据
                'bitda_bid1_price': bitda_data['bid1_price'],
                'bitda_ask1_price': bitda_data['ask1_price'],
                'bitda_spread': bitda_data['spread'],

                # Bitda价差统计
                'bitda_spread_latest': bitda_spread_stats['latest'],
                'bitda_spread_max': bitda_spread_stats['max'],
                'bitda_spread_min': bitda_spread_stats['min'],
                'bitda_spread_avg': bitda_spread_stats['avg'],
                'bitda_spread_median': bitda_spread_stats['median'],
                'bitda_bid5_ask5_spread_max': bitda_spread_stats['bid5_ask5_max'],
                'bitda_bid5_ask5_spread_min': bitda_spread_stats['bid5_ask5_min'],
                'bitda_sample_count': bitda_spread_stats['sample_count'],

                # Binance价差统计
                'binance_spread_latest': binance_spread_stats['latest'],
                'binance_spread_max': binance_spread_stats['max'],
                'binance_spread_min': binance_spread_stats['min'],
                'binance_spread_avg': binance_spread_stats['avg'],
                'binance_spread_median': binance_spread_stats['median'],
                'binance_bid5_ask5_spread_max': binance_spread_stats['bid5_ask5_max'],
                'binance_bid5_ask5_spread_min': binance_spread_stats['bid5_ask5_min'],
                'binance_sample_count': binance_spread_stats['sample_count'],

                # 价差差值
                'bid_ask1_spread_diff': bitda_data['spread'] - binance_spread_stats['latest'],

                'time_window_minutes': 5
            }
            
        except Exception as e:
            logger.error(f"计算价差对比失败: {e}")
            return {}
    
    def save_depth_comparison_result(self, result: Dict):
        """保存深度对比结果"""
        try:
            connection = mysql.connector.connect(**self.analysis_db_config)
            cursor = connection.cursor()
            
            query = """
            INSERT INTO depth_comparison (
                symbol, analysis_time, bitda_timestamp, binance_timestamp, time_diff_ms,
                bitda_bid1_qty, bitda_ask1_qty, bitda_bid2_qty, bitda_ask2_qty, 
                bitda_bid5_total, bitda_ask5_total,
                binance_bid1_qty, binance_ask1_qty, binance_bid2_qty, binance_ask2_qty,
                binance_bid5_total, binance_ask5_total,
                bid1_ratio, ask1_ratio, bid_ask1_ratio, bid_ask2_ratio, bid_ask5_ratio
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            cursor.execute(query, (
                result['symbol'], result['analysis_time'], result['bitda_timestamp'], 
                result['binance_timestamp'], result['time_diff_ms'],
                result['bitda_bid1_qty'], result['bitda_ask1_qty'], result['bitda_bid2_qty'], 
                result['bitda_ask2_qty'], result['bitda_bid5_total'], result['bitda_ask5_total'],
                result['binance_bid1_qty'], result['binance_ask1_qty'], result['binance_bid2_qty'],
                result['binance_ask2_qty'], result['binance_bid5_total'], result['binance_ask5_total'],
                result['bid1_ratio'], result['ask1_ratio'], result['bid_ask1_ratio'],
                result['bid_ask2_ratio'], result['bid_ask5_ratio']
            ))
            
            connection.commit()
            cursor.close()
            connection.close()
            
        except Exception as e:
            logger.error(f"保存深度对比结果失败: {e}")
    
    def save_spread_comparison_result(self, result: Dict):
        """保存价差对比结果"""
        try:
            connection = mysql.connector.connect(**self.analysis_db_config)
            cursor = connection.cursor()
            
            query = """
            INSERT INTO spread_comparison (
                symbol, analysis_time, bitda_timestamp,
                bitda_bid1_price, bitda_ask1_price, bitda_spread,
                bitda_spread_latest, bitda_spread_max, bitda_spread_min, bitda_spread_avg, bitda_spread_median,
                bitda_bid5_ask5_spread_max, bitda_bid5_ask5_spread_min, bitda_sample_count,
                binance_spread_latest, binance_spread_max, binance_spread_min,
                binance_spread_avg, binance_spread_median,
                binance_bid5_ask5_spread_max, binance_bid5_ask5_spread_min, binance_sample_count,
                bid_ask1_spread_diff, time_window_minutes
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            cursor.execute(query, (
                result['symbol'], result['analysis_time'], result['bitda_timestamp'],
                result['bitda_bid1_price'], result['bitda_ask1_price'], result['bitda_spread'],
                result['bitda_spread_latest'], result['bitda_spread_max'], result['bitda_spread_min'],
                result['bitda_spread_avg'], result['bitda_spread_median'],
                result['bitda_bid5_ask5_spread_max'], result['bitda_bid5_ask5_spread_min'], result['bitda_sample_count'],
                result['binance_spread_latest'], result['binance_spread_max'], result['binance_spread_min'],
                result['binance_spread_avg'], result['binance_spread_median'],
                result['binance_bid5_ask5_spread_max'], result['binance_bid5_ask5_spread_min'], result['binance_sample_count'],
                result['bid_ask1_spread_diff'], result['time_window_minutes']
            ))
            
            connection.commit()
            cursor.close()
            connection.close()
            
        except Exception as e:
            logger.error(f"保存价差对比结果失败: {e}")

def main():
    """主函数 - 测试"""
    print("🔧 修复后的深度价差对比分析器")
    print("=" * 50)
    
    analyzer = FixedDepthSpreadAnalyzer()
    
    for symbol in ['BTCUSDT', 'ETHUSDT']:
        success = analyzer.analyze_symbol(symbol)
        print(f"📊 {symbol}: {'✅ 成功' if success else '❌ 失败'}")

if __name__ == "__main__":
    main()
