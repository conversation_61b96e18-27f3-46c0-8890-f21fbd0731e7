#!/bin/bash

# WebSocket数据收集器启动脚本
# 使用方法: bash start_collector.sh

echo "🚀 启动WebSocket数据收集器..."
echo "📍 切换到项目目录..."

# 切换到项目目录
cd /Users/<USER>/code/Bit/WS_DATA_Collector

# 激活conda base环境
echo "🐍 激活conda base环境..."
source ~/miniconda3/etc/profile.d/conda.sh
conda activate base

# 检查Python环境
echo "✅ 当前Python版本:"
python --version

echo "✅ 当前工作目录:"
pwd

echo "✅ 检查必要文件:"
if [ -f "ws_data_collector.py" ]; then
    echo "   ✓ ws_data_collector.py 存在"
else
    echo "   ✗ ws_data_collector.py 不存在"
    exit 1
fi

if [ -f ".env" ]; then
    echo "   ✓ .env 配置文件存在"
else
    echo "   ✗ .env 配置文件不存在"
    exit 1
fi

echo ""
echo "🎯 启动数据收集器..."
echo "💡 按 Ctrl+C 停止程序"
echo "----------------------------------------"

# 启动Python脚本
python ws_data_collector.py
