#!/usr/bin/env python3
"""
调试Binance WebSocket数据格式
"""

import asyncio
import websockets
import json
from datetime import datetime

def log(message):
    """日志输出"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

async def debug_binance_bookticker():
    """调试Binance BookTicker数据"""
    log("🔍 开始调试Binance BookTicker数据格式...")
    
    # Binance BookTicker WebSocket URL
    url = "wss://stream.binance.com:9443/ws/btcusdt@bookTicker"
    
    try:
        async with websockets.connect(url) as websocket:
            log("✅ 连接Binance BookTicker WebSocket成功")
            
            # 接收前几条消息进行分析
            for i in range(5):
                message = await websocket.recv()
                log(f"📨 收到消息 {i+1}:")
                log(f"原始数据: {message}")
                
                try:
                    data = json.loads(message)
                    log(f"解析后数据: {json.dumps(data, indent=2)}")
                    log(f"数据字段: {list(data.keys())}")
                    
                    # 检查预期字段是否存在
                    expected_fields = ['s', 'u', 'b', 'B', 'a', 'A', 'E', 'T']
                    missing_fields = []
                    for field in expected_fields:
                        if field not in data:
                            missing_fields.append(field)
                    
                    if missing_fields:
                        log(f"❌ 缺少字段: {missing_fields}")
                    else:
                        log("✅ 所有预期字段都存在")
                        
                except json.JSONDecodeError as e:
                    log(f"❌ JSON解析失败: {e}")
                
                log("-" * 50)
                
    except Exception as e:
        log(f"❌ 连接失败: {e}")

async def debug_binance_depth():
    """调试Binance 5档深度数据"""
    log("🔍 开始调试Binance 5档深度数据格式...")
    
    # Binance 5档深度WebSocket URL
    url = "wss://stream.binance.com:9443/ws/btcusdt@depth5@100ms"
    
    try:
        async with websockets.connect(url) as websocket:
            log("✅ 连接Binance 5档深度WebSocket成功")
            
            # 接收前几条消息进行分析
            for i in range(3):
                message = await websocket.recv()
                log(f"📨 收到深度消息 {i+1}:")
                log(f"原始数据: {message}")
                
                try:
                    data = json.loads(message)
                    log(f"解析后数据: {json.dumps(data, indent=2)}")
                    log(f"数据字段: {list(data.keys())}")
                    
                    # 检查预期字段是否存在
                    expected_fields = ['s', 'E', 'T', 'U', 'u', 'pu', 'b', 'a']
                    missing_fields = []
                    for field in expected_fields:
                        if field not in data:
                            missing_fields.append(field)
                    
                    if missing_fields:
                        log(f"❌ 缺少字段: {missing_fields}")
                    else:
                        log("✅ 所有预期字段都存在")
                        
                except json.JSONDecodeError as e:
                    log(f"❌ JSON解析失败: {e}")
                
                log("-" * 50)
                
    except Exception as e:
        log(f"❌ 连接失败: {e}")

async def main():
    """主函数"""
    log("🚀 开始Binance WebSocket数据格式调试")
    log("=" * 60)
    
    # 调试BookTicker
    await debug_binance_bookticker()
    
    log("=" * 60)
    
    # 调试5档深度
    await debug_binance_depth()
    
    log("=" * 60)
    log("🎉 调试完成！")

if __name__ == "__main__":
    asyncio.run(main())
