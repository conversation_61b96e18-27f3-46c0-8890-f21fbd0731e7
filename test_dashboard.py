#!/usr/bin/env python3
"""
测试仪表板创建
"""

import requests
import json

def test_dashboard():
    """测试仪表板创建"""
    print("🎨 测试仪表板创建...")
    
    session = requests.Session()
    session.auth = ("admin", "admin")
    datasource_uid = "cenigejcatslce"  # WorkingDepthDB
    
    dashboard_config = {
        "dashboard": {
            "id": None,
            "title": "🔄 测试仪表板 - 1分钟刷新",
            "tags": ["test"],
            "timezone": "browser",
            "panels": [
                # 1. 简单测试面板
                {
                    "id": 1,
                    "title": "📊 BTCUSDT 买一量",
                    "type": "stat",
                    "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": "SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1",
                        "refId": "A"
                    }],
                    "options": {
                        "reduceOptions": {"values": False, "calcs": ["lastNotNull"]},
                        "textMode": "auto", 
                        "colorMode": "background"
                    }
                },
                
                # 2. 简单表格
                {
                    "id": 2,
                    "title": "📊 简单深度对比",
                    "type": "table",
                    "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            SELECT 
                                'BTCUSDT买一量' as 项目,
                                bid_qty_1 as 数量
                            FROM bitda_depth 
                            WHERE symbol = 'BTCUSDT' 
                            ORDER BY timestamp DESC 
                            LIMIT 1
                        """,
                        "refId": "A"
                    }]
                }
            ],
            "time": {"from": "now-1h", "to": "now"},
            "refresh": "1m",  # 1分钟刷新
            "schemaVersion": 30,
            "version": 1
        },
        "overwrite": True
    }
    
    try:
        print("   📡 发送请求到Grafana...")
        response = session.post(
            "http://localhost:3000/api/dashboards/db",
            json=dashboard_config,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"   📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            dashboard_url = f"http://localhost:3000{result['url']}"
            print(f"   ✅ 测试仪表板创建成功")
            print(f"   🌐 地址: {dashboard_url}")
            return dashboard_url
        else:
            print(f"   ❌ 创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return None

def main():
    """主函数"""
    print("🎨 测试仪表板创建")
    print("=" * 40)
    
    dashboard_url = test_dashboard()
    
    if dashboard_url:
        print(f"\n🎉 测试成功！")
        print(f"🌐 访问地址: {dashboard_url}")
        
        # 打开浏览器
        try:
            import webbrowser
            webbrowser.open(dashboard_url)
            print(f"🌐 已自动打开浏览器")
        except:
            pass
    else:
        print(f"\n❌ 测试失败")

if __name__ == "__main__":
    main()
