#!/usr/bin/env python3
"""
验证数据来源和计算正确性
"""

import mysql.connector
from datetime import datetime, timedelta
import json

def verify_data_source():
    """验证数据来源和计算逻辑"""
    print("🔍 验证数据来源和计算正确性")
    print("=" * 60)
    
    try:
        # 连接数据库
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='Linuxtest',
            database='depth_db',
            connection_timeout=10
        )
        cursor = connection.cursor()
        
        # 1. 验证Bitda数据结构和来源
        print("📊 1. 验证Bitda数据结构...")
        cursor.execute("DESCRIBE bitda_depth")
        bitda_fields = cursor.fetchall()
        
        print("   Bitda深度表字段:")
        for field in bitda_fields:
            if 'bid' in field[0] or 'ask' in field[0]:
                print(f"     {field[0]}: {field[1]}")
        
        # 获取一条Bitda样本数据
        cursor.execute("""
            SELECT 
                symbol, timestamp, 
                bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
                bid_price_2, ask_price_2, bid_qty_2, ask_qty_2,
                bid_price_3, ask_price_3, bid_qty_3, ask_qty_3,
                bid_price_4, ask_price_4, bid_qty_4, ask_qty_4,
                bid_price_5, ask_price_5, bid_qty_5, ask_qty_5,
                bids, asks
            FROM bitda_depth 
            WHERE symbol = 'BTCUSDT' AND bid_price_1 IS NOT NULL
            ORDER BY created_at DESC 
            LIMIT 1
        """)
        
        bitda_sample = cursor.fetchone()
        if bitda_sample:
            symbol = bitda_sample[0]
            timestamp = bitda_sample[1]
            
            print(f"\n   📈 Bitda样本数据 ({symbol}):")
            print(f"     时间戳: {timestamp}")
            print(f"     买一: 价格{bitda_sample[2]} 数量{bitda_sample[4]}")
            print(f"     卖一: 价格{bitda_sample[3]} 数量{bitda_sample[5]}")
            print(f"     买二: 价格{bitda_sample[6]} 数量{bitda_sample[8]}")
            print(f"     卖二: 价格{bitda_sample[7]} 数量{bitda_sample[9]}")
            print(f"     买三: 价格{bitda_sample[10]} 数量{bitda_sample[12]}")
            print(f"     卖三: 价格{bitda_sample[11]} 数量{bitda_sample[13]}")
            print(f"     买四: 价格{bitda_sample[14]} 数量{bitda_sample[16]}")
            print(f"     卖四: 价格{bitda_sample[15]} 数量{bitda_sample[17]}")
            print(f"     买五: 价格{bitda_sample[18]} 数量{bitda_sample[20]}")
            print(f"     卖五: 价格{bitda_sample[19]} 数量{bitda_sample[21]}")
            
            # 验证JSON数据
            if bitda_sample[22] and bitda_sample[23]:
                bids_json = bitda_sample[22]
                asks_json = bitda_sample[23]
                print(f"\n   📋 JSON数据验证:")
                print(f"     bids JSON: {str(bids_json)[:100]}...")
                print(f"     asks JSON: {str(asks_json)[:100]}...")
                
                # 解析JSON验证
                try:
                    bids = json.loads(bids_json)
                    asks = json.loads(asks_json)
                    print(f"     bids解析: {len(bids)}档")
                    print(f"     asks解析: {len(asks)}档")
                    if bids:
                        print(f"     买一JSON: 价格{bids[0][0]} 数量{bids[0][1]}")
                    if asks:
                        print(f"     卖一JSON: 价格{asks[0][0]} 数量{asks[0][1]}")
                except Exception as e:
                    print(f"     ❌ JSON解析失败: {e}")
        
        # 2. 验证Binance数据结构
        print("\n📊 2. 验证Binance数据结构...")
        cursor.execute("DESCRIBE binance_depth_5")
        binance_fields = cursor.fetchall()
        
        print("   Binance深度表字段:")
        for field in binance_fields:
            if 'bid' in field[0] or 'ask' in field[0]:
                print(f"     {field[0]}: {field[1]}")
        
        # 获取一条Binance样本数据
        cursor.execute("""
            SELECT 
                symbol, event_time,
                bid_price_1, ask_price_1, bid_qty_1, ask_qty_1,
                bid_price_2, ask_price_2, bid_qty_2, ask_qty_2,
                bid_price_3, ask_price_3, bid_qty_3, ask_qty_3,
                bid_price_4, ask_price_4, bid_qty_4, ask_qty_4,
                bid_price_5, ask_price_5, bid_qty_5, ask_qty_5,
                bids, asks
            FROM binance_depth_5 
            WHERE symbol = 'BTCUSDT' AND bid_price_1 IS NOT NULL
            ORDER BY created_at DESC 
            LIMIT 1
        """)
        
        binance_sample = cursor.fetchone()
        if binance_sample:
            symbol = binance_sample[0]
            event_time = binance_sample[1]
            
            print(f"\n   📈 Binance样本数据 ({symbol}):")
            print(f"     事件时间: {event_time}")
            print(f"     买一: 价格{binance_sample[2]} 数量{binance_sample[4]}")
            print(f"     卖一: 价格{binance_sample[3]} 数量{binance_sample[5]}")
            print(f"     买二: 价格{binance_sample[6]} 数量{binance_sample[8]}")
            print(f"     卖二: 价格{binance_sample[7]} 数量{binance_sample[9]}")
            print(f"     买三: 价格{binance_sample[10]} 数量{binance_sample[12]}")
            print(f"     卖三: 价格{binance_sample[11]} 数量{binance_sample[13]}")
            print(f"     买四: 价格{binance_sample[14]} 数量{binance_sample[16]}")
            print(f"     卖四: 价格{binance_sample[15]} 数量{binance_sample[17]}")
            print(f"     买五: 价格{binance_sample[18]} 数量{binance_sample[20]}")
            print(f"     卖五: 价格{binance_sample[19]} 数量{binance_sample[21]}")
        
        # 3. 验证计算逻辑
        print("\n🧮 3. 验证计算逻辑...")
        
        if bitda_sample and binance_sample:
            print("   📊 深度数量计算验证:")
            
            # Bitda数量
            bitda_bid1 = float(bitda_sample[4]) if bitda_sample[4] else 0
            bitda_ask1 = float(bitda_sample[5]) if bitda_sample[5] else 0
            bitda_bid2 = float(bitda_sample[8]) if bitda_sample[8] else 0
            bitda_ask2 = float(bitda_sample[9]) if bitda_sample[9] else 0
            
            # 计算买二卖二总量（买一+买二+卖一+卖二）
            bitda_bid_ask2_total = bitda_bid1 + bitda_bid2 + bitda_ask1 + bitda_ask2
            
            # 计算买五卖五总量（买一到买五+卖一到卖五）
            bitda_bid_qtys = [float(bitda_sample[i]) if bitda_sample[i] else 0 for i in [4, 8, 12, 16, 20]]
            bitda_ask_qtys = [float(bitda_sample[i]) if bitda_sample[i] else 0 for i in [5, 9, 13, 17, 21]]
            bitda_bid5_total = sum(bitda_bid_qtys)
            bitda_ask5_total = sum(bitda_ask_qtys)
            bitda_bid_ask5_total = bitda_bid5_total + bitda_ask5_total
            
            print(f"     Bitda买一量: {bitda_bid1}")
            print(f"     Bitda卖一量: {bitda_ask1}")
            print(f"     Bitda买二量: {bitda_bid2}")
            print(f"     Bitda卖二量: {bitda_ask2}")
            print(f"     Bitda买一卖一总量: {bitda_bid1 + bitda_ask1}")
            print(f"     Bitda买二卖二总量: {bitda_bid_ask2_total}")
            print(f"     Bitda买五总量: {bitda_bid5_total}")
            print(f"     Bitda卖五总量: {bitda_ask5_total}")
            print(f"     Bitda买五卖五总量: {bitda_bid_ask5_total}")
            
            # Binance数量
            binance_bid1 = float(binance_sample[4]) if binance_sample[4] else 0
            binance_ask1 = float(binance_sample[5]) if binance_sample[5] else 0
            binance_bid2 = float(binance_sample[8]) if binance_sample[8] else 0
            binance_ask2 = float(binance_sample[9]) if binance_sample[9] else 0
            
            binance_bid_ask2_total = binance_bid1 + binance_bid2 + binance_ask1 + binance_ask2
            
            binance_bid_qtys = [float(binance_sample[i]) if binance_sample[i] else 0 for i in [4, 8, 12, 16, 20]]
            binance_ask_qtys = [float(binance_sample[i]) if binance_sample[i] else 0 for i in [5, 9, 13, 17, 21]]
            binance_bid5_total = sum(binance_bid_qtys)
            binance_ask5_total = sum(binance_ask_qtys)
            binance_bid_ask5_total = binance_bid5_total + binance_ask5_total
            
            print(f"\n     Binance买一量: {binance_bid1}")
            print(f"     Binance卖一量: {binance_ask1}")
            print(f"     Binance买二量: {binance_bid2}")
            print(f"     Binance卖二量: {binance_ask2}")
            print(f"     Binance买一卖一总量: {binance_bid1 + binance_ask1}")
            print(f"     Binance买二卖二总量: {binance_bid_ask2_total}")
            print(f"     Binance买五总量: {binance_bid5_total}")
            print(f"     Binance卖五总量: {binance_ask5_total}")
            print(f"     Binance买五卖五总量: {binance_bid_ask5_total}")
            
            # 计算比值
            def safe_ratio(a, b):
                return a / b if b > 0 else 0
            
            print(f"\n   📊 深度比值计算:")
            print(f"     买一比值: {safe_ratio(bitda_bid1, binance_bid1):.4f}")
            print(f"     卖一比值: {safe_ratio(bitda_ask1, binance_ask1):.4f}")
            print(f"     买一卖一比值: {safe_ratio(bitda_bid1 + bitda_ask1, binance_bid1 + binance_ask1):.4f}")
            print(f"     买二卖二比值: {safe_ratio(bitda_bid_ask2_total, binance_bid_ask2_total):.4f}")
            print(f"     买五卖五比值: {safe_ratio(bitda_bid_ask5_total, binance_bid_ask5_total):.4f}")
        
        # 4. 验证价差计算
        print("\n💰 4. 验证价差计算...")
        
        if bitda_sample and binance_sample:
            bitda_bid_price = float(bitda_sample[2])
            bitda_ask_price = float(bitda_sample[3])
            bitda_spread = bitda_ask_price - bitda_bid_price
            
            binance_bid_price = float(binance_sample[2])
            binance_ask_price = float(binance_sample[3])
            binance_spread = binance_ask_price - binance_bid_price
            
            spread_diff = bitda_spread - binance_spread
            
            print(f"     Bitda买一价: {bitda_bid_price}")
            print(f"     Bitda卖一价: {bitda_ask_price}")
            print(f"     Bitda价差: {bitda_spread:.4f}")
            print(f"     Binance买一价: {binance_bid_price}")
            print(f"     Binance卖一价: {binance_ask_price}")
            print(f"     Binance价差: {binance_spread:.4f}")
            print(f"     价差差值: {spread_diff:.4f}")
        
        cursor.close()
        connection.close()
        
        print("\n✅ 数据来源和计算逻辑验证完成!")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")

if __name__ == "__main__":
    verify_data_source()
