#!/usr/bin/env python3
"""
数据分析器安装和设置脚本
自动安装依赖并测试系统
"""

import subprocess
import sys
import os
import asyncio
from pathlib import Path

def run_command(command, description=""):
    """运行命令并显示结果"""
    if description:
        print(f"🔧 {description}...")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            check=True
        )
        
        if result.stdout:
            print(f"✅ {description}完成")
            if result.stdout.strip():
                print(f"   输出: {result.stdout.strip()}")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败")
        print(f"   错误: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("   需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def install_dependencies():
    """安装Python依赖"""
    print("\n📦 安装Python依赖...")
    
    # 检查requirements.txt是否存在
    if not Path("requirements.txt").exists():
        print("❌ requirements.txt文件不存在")
        return False
    
    # 升级pip
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "升级pip"):
        return False
    
    # 安装依赖
    if not run_command(f"{sys.executable} -m pip install -r requirements.txt", "安装依赖包"):
        return False
    
    return True

def check_system_packages():
    """检查系统包（用于图表生成）"""
    print("\n🖼️  检查图表生成依赖...")
    
    try:
        import matplotlib
        import seaborn
        import pandas
        import numpy
        print("✅ 图表生成依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("   请运行: pip install matplotlib seaborn pandas numpy")
        return False

def create_directories():
    """创建必要的目录"""
    print("\n📁 创建目录结构...")
    
    directories = [
        "analyzer",
        "visualizer",
        "charts_output",
        "reports_output"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ 目录 {directory} 已创建")
    
    return True

def test_database_connection():
    """测试数据库连接"""
    print("\n🔌 测试数据库连接...")
    
    try:
        from utils.db import db_manager
        
        # 测试连接
        result = db_manager.execute_query("SELECT 1", fetch=True)
        if result:
            print("✅ 数据库连接成功")
            return True
        else:
            print("❌ 数据库连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据库连接错误: {e}")
        print("   请检查数据库配置和网络连接")
        return False

async def run_analyzer_test():
    """运行分析器测试"""
    print("\n🧪 运行分析器测试...")
    
    try:
        # 导入测试模块
        from test_analyzer import AnalyzerTester
        
        tester = AnalyzerTester()
        await tester.test_all_analyzers()
        
        # 检查测试结果
        passed = sum(1 for result in tester.test_results.values() if result == "PASS")
        total = len(tester.test_results)
        
        if passed == total:
            print("✅ 所有分析器测试通过")
            return True
        else:
            print(f"⚠️  {total - passed}个测试未通过")
            return False
            
    except Exception as e:
        print(f"❌ 分析器测试失败: {e}")
        return False

def show_usage_examples():
    """显示使用示例"""
    print("\n" + "="*60)
    print("🎯 使用示例")
    print("="*60)
    
    examples = [
        ("单次分析", "python analyzer_main.py --mode single --hours 1"),
        ("持续监控", "python analyzer_main.py --mode monitor --interval 15"),
        ("延时分析", "python analyzer_main.py --mode specific --type latency"),
        ("K线分析", "python analyzer_main.py --mode specific --type kline"),
        ("深度分析", "python analyzer_main.py --mode specific --type depth"),
        ("价格分析", "python analyzer_main.py --mode specific --type price"),
        ("资金费率分析", "python analyzer_main.py --mode specific --type funding"),
        ("测试系统", "python test_analyzer.py")
    ]
    
    for description, command in examples:
        print(f"\n📋 {description}:")
        print(f"   {command}")
    
    print("\n💡 提示:")
    print("   - 首次运行建议使用测试命令验证系统")
    print("   - 生成的图表保存在当前目录")
    print("   - 分析报告保存为JSON格式")
    print("   - 使用 --help 查看完整参数说明")

async def main():
    """主函数"""
    print("🚀 数据分析器安装和设置")
    print("="*60)
    
    # 检查步骤
    steps = [
        ("检查Python版本", check_python_version),
        ("安装Python依赖", install_dependencies),
        ("检查图表依赖", check_system_packages),
        ("创建目录结构", create_directories),
        ("测试数据库连接", test_database_connection),
    ]
    
    # 执行检查步骤
    all_passed = True
    for step_name, step_func in steps:
        if not step_func():
            all_passed = False
            print(f"\n❌ {step_name}失败，请解决后重试")
            break
    
    if not all_passed:
        print("\n⚠️  安装未完成，请解决上述问题")
        return
    
    # 运行分析器测试
    print("\n🧪 运行系统测试...")
    test_passed = await run_analyzer_test()
    
    if test_passed:
        print("\n🎉 数据分析器安装和测试完成！")
        show_usage_examples()
    else:
        print("\n⚠️  系统测试未完全通过，但基本功能可用")
        show_usage_examples()
    
    print("\n✨ 安装完成！")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  安装被中断")
    except Exception as e:
        print(f"❌ 安装失败: {e}")
