#!/usr/bin/env python3
"""
快速分析测试 - 只处理少量数据生成样本结果
"""

import mysql.connector
from datetime import datetime, timedelta
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def quick_analysis():
    """快速分析生成样本数据"""
    print("⚡ 快速分析测试")
    print("=" * 40)
    
    try:
        # 1. 初始化分析数据库
        logger.info("🔧 初始化分析数据库...")
        
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='Linuxtest'
        )
        cursor = connection.cursor()
        
        # 创建数据库
        cursor.execute("CREATE DATABASE IF NOT EXISTS depth_spread_analysis")
        cursor.execute("USE depth_spread_analysis")
        
        # 创建深度对比表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS depth_comparison (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                symbol VARCHAR(20) NOT NULL,
                analysis_time DATETIME NOT NULL,
                bitda_timestamp BIGINT NOT NULL,
                binance_timestamp BIGINT NOT NULL,
                time_diff_ms INT NOT NULL,
                bitda_bid1_qty DECIMAL(20,4),
                bitda_ask1_qty DECIMAL(20,4),
                bitda_bid2_qty DECIMAL(20,4),
                bitda_ask2_qty DECIMAL(20,4),
                bitda_bid5_qty DECIMAL(20,4),
                bitda_ask5_qty DECIMAL(20,4),
                binance_bid1_qty DECIMAL(20,4),
                binance_ask1_qty DECIMAL(20,4),
                binance_bid2_qty DECIMAL(20,4),
                binance_ask2_qty DECIMAL(20,4),
                binance_bid5_qty DECIMAL(20,4),
                binance_ask5_qty DECIMAL(20,4),
                bid1_ratio DECIMAL(10,4),
                ask1_ratio DECIMAL(10,4),
                bid_ask1_ratio DECIMAL(10,4),
                bid_ask2_ratio DECIMAL(10,4),
                bid_ask5_ratio DECIMAL(10,4),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 创建价差对比表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS spread_comparison (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                symbol VARCHAR(20) NOT NULL,
                analysis_time DATETIME NOT NULL,
                bitda_timestamp BIGINT NOT NULL,
                bitda_bid1_price DECIMAL(15,2),
                bitda_ask1_price DECIMAL(15,2),
                bitda_spread DECIMAL(15,4),
                binance_spread_latest DECIMAL(15,4),
                binance_spread_max DECIMAL(15,4),
                binance_spread_min DECIMAL(15,4),
                binance_spread_avg DECIMAL(15,4),
                binance_spread_median DECIMAL(15,4),
                bid_ask1_spread_diff DECIMAL(15,4),
                binance_bid5_ask5_spread_max DECIMAL(15,4),
                binance_bid5_ask5_spread_min DECIMAL(15,4),
                sample_count INT,
                time_window_minutes INT DEFAULT 5,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        cursor.close()
        connection.close()
        
        # 2. 连接源数据库进行分析
        logger.info("📊 连接源数据库...")
        
        source_connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='Linuxtest',
            database='depth_db'
        )
        source_cursor = source_connection.cursor()
        
        # 连接分析数据库
        analysis_connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='Linuxtest',
            database='depth_spread_analysis'
        )
        analysis_cursor = analysis_connection.cursor()
        
        analysis_time = datetime.now()
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            logger.info(f"🔍 分析 {symbol}...")
            
            # 获取最新的Bitda数据 (只取1条)
            source_cursor.execute("""
                SELECT 
                    timestamp, bid_price_1, ask_price_1,
                    bid_qty_1, ask_qty_1, bid_qty_2, ask_qty_2,
                    bid_qty_3, ask_qty_3, bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5
                FROM bitda_depth 
                WHERE symbol = %s AND bid_price_1 IS NOT NULL
                ORDER BY created_at DESC 
                LIMIT 1
            """, (symbol,))
            
            bitda_result = source_cursor.fetchone()
            if not bitda_result:
                logger.warning(f"   ❌ {symbol} 无Bitda数据")
                continue
            
            bitda_ts = bitda_result[0]
            bitda_bid_price, bitda_ask_price = float(bitda_result[1]), float(bitda_result[2])
            bitda_qtys = [float(q) if q else 0 for q in bitda_result[3:]]
            
            bitda_bid5_total = sum(bitda_qtys[0:5:2]) + sum(bitda_qtys[1:6:2])  # 买一到买五
            bitda_ask5_total = sum(bitda_qtys[1:6:2]) + sum(bitda_qtys[2:7:2])  # 卖一到卖五
            bitda_spread = bitda_ask_price - bitda_bid_price
            
            # 查找匹配的Binance数据
            window_ms = 5 * 60 * 1000
            source_cursor.execute("""
                SELECT 
                    event_time, bid_price_1, ask_price_1,
                    bid_qty_1, ask_qty_1, bid_qty_2, ask_qty_2,
                    bid_qty_3, ask_qty_3, bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5,
                    ABS(event_time - %s) as time_diff
                FROM binance_depth_5 
                WHERE symbol = %s 
                AND event_time BETWEEN %s AND %s
                AND bid_price_1 IS NOT NULL
                ORDER BY time_diff
                LIMIT 1
            """, (bitda_ts, symbol, bitda_ts - window_ms, bitda_ts + window_ms))
            
            binance_result = source_cursor.fetchone()
            if not binance_result:
                logger.warning(f"   ❌ {symbol} 无匹配的Binance数据")
                continue
            
            bn_ts = binance_result[0]
            bn_bid_price, bn_ask_price = float(binance_result[1]), float(binance_result[2])
            bn_qtys = [float(q) if q else 0 for q in binance_result[3:11]]
            time_diff = int(binance_result[12])
            
            bn_bid5_total = sum(bn_qtys[0:5:2]) + sum(bn_qtys[1:6:2])
            bn_ask5_total = sum(bn_qtys[1:6:2]) + sum(bn_qtys[2:7:2])
            
            # 计算深度比值
            def safe_ratio(a, b):
                return a / b if b > 0 else 0
            
            bid1_ratio = safe_ratio(bitda_qtys[0], bn_qtys[0])
            ask1_ratio = safe_ratio(bitda_qtys[1], bn_qtys[1])
            bid_ask1_ratio = safe_ratio(bitda_qtys[0] + bitda_qtys[1], bn_qtys[0] + bn_qtys[1])
            bid_ask2_ratio = safe_ratio(sum(bitda_qtys[:4]), sum(bn_qtys[:4]))
            bid_ask5_ratio = safe_ratio(bitda_bid5_total + bitda_ask5_total, bn_bid5_total + bn_ask5_total)
            
            # 保存深度对比结果
            analysis_cursor.execute("""
                INSERT INTO depth_comparison (
                    symbol, analysis_time, bitda_timestamp, binance_timestamp, time_diff_ms,
                    bitda_bid1_qty, bitda_ask1_qty, bitda_bid2_qty, bitda_ask2_qty, 
                    bitda_bid5_qty, bitda_ask5_qty,
                    binance_bid1_qty, binance_ask1_qty, binance_bid2_qty, binance_ask2_qty,
                    binance_bid5_qty, binance_ask5_qty,
                    bid1_ratio, ask1_ratio, bid_ask1_ratio, bid_ask2_ratio, bid_ask5_ratio
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                symbol, analysis_time, bitda_ts, bn_ts, time_diff,
                bitda_qtys[0], bitda_qtys[1], bitda_qtys[2], bitda_qtys[3],
                bitda_bid5_total, bitda_ask5_total,
                bn_qtys[0], bn_qtys[1], bn_qtys[2], bn_qtys[3],
                bn_bid5_total, bn_ask5_total,
                bid1_ratio, ask1_ratio, bid_ask1_ratio, bid_ask2_ratio, bid_ask5_ratio
            ))
            
            # 获取Binance价差统计
            source_cursor.execute("""
                SELECT bid_price_1, ask_price_1, bid_price_5, ask_price_5
                FROM binance_depth_5 
                WHERE symbol = %s AND event_time BETWEEN %s AND %s
                AND bid_price_1 IS NOT NULL
                ORDER BY event_time DESC
                LIMIT 50
            """, (symbol, bitda_ts - window_ms, bitda_ts))
            
            bn_spread_data = source_cursor.fetchall()
            if bn_spread_data:
                spreads = [float(row[1] - row[0]) for row in bn_spread_data]
                bid5_ask5_spreads = [float(row[3] - row[2]) for row in bn_spread_data if row[2] and row[3]]
                
                spreads.sort()
                n = len(spreads)
                
                bn_spread_latest = spreads[0] if spreads else 0
                bn_spread_max = max(spreads) if spreads else 0
                bn_spread_min = min(spreads) if spreads else 0
                bn_spread_avg = sum(spreads) / n if spreads else 0
                bn_spread_median = spreads[n//2] if spreads else 0
                
                bid5_ask5_max = max(bid5_ask5_spreads) if bid5_ask5_spreads else 0
                bid5_ask5_min = min(bid5_ask5_spreads) if bid5_ask5_spreads else 0
                
                spread_diff = bitda_spread - bn_spread_latest
                
                # 保存价差对比结果
                analysis_cursor.execute("""
                    INSERT INTO spread_comparison (
                        symbol, analysis_time, bitda_timestamp,
                        bitda_bid1_price, bitda_ask1_price, bitda_spread,
                        binance_spread_latest, binance_spread_max, binance_spread_min,
                        binance_spread_avg, binance_spread_median,
                        bid_ask1_spread_diff, binance_bid5_ask5_spread_max, binance_bid5_ask5_spread_min,
                        sample_count, time_window_minutes
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    symbol, analysis_time, bitda_ts,
                    bitda_bid_price, bitda_ask_price, bitda_spread,
                    bn_spread_latest, bn_spread_max, bn_spread_min,
                    bn_spread_avg, bn_spread_median,
                    spread_diff, bid5_ask5_max, bid5_ask5_min,
                    len(spreads), 5
                ))
                
                logger.info(f"   ✅ {symbol}: 深度比值 {bid1_ratio:.2f}, 价差差值 {spread_diff:.4f}")
        
        analysis_connection.commit()
        
        source_cursor.close()
        source_connection.close()
        analysis_cursor.close()
        analysis_connection.close()
        
        logger.info("✅ 快速分析完成!")
        
    except Exception as e:
        logger.error(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    quick_analysis()
