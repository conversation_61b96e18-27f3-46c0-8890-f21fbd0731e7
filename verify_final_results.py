#!/usr/bin/env python3
"""
验证最终结果的正确性
"""

import mysql.connector
from datetime import datetime

def verify_final_results():
    """验证最终分析结果"""
    print("🔍 验证最终分析结果的正确性")
    print("=" * 60)
    
    try:
        # 连接分析数据库
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='Linuxtest',
            database='depth_spread_analysis'
        )
        cursor = connection.cursor()
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            print(f"\n📊 {symbol} 分析结果验证:")
            print("-" * 40)
            
            # 获取最新的深度对比数据
            cursor.execute("""
                SELECT 
                    bid1_ratio, ask1_ratio, bid_ask1_ratio, bid_ask2_ratio, bid_ask5_ratio,
                    bitda_bid1_qty, bitda_ask1_qty, bitda_bid2_qty, bitda_ask2_qty,
                    bitda_bid5_total, bitda_ask5_total,
                    binance_bid1_qty, binance_ask1_qty, binance_bid2_qty, binance_ask2_qty,
                    binance_bid5_total, binance_ask5_total,
                    analysis_time, time_diff_ms
                FROM depth_comparison 
                WHERE symbol = %s 
                ORDER BY analysis_time DESC 
                LIMIT 1
            """, (symbol,))
            
            depth_result = cursor.fetchone()
            
            if depth_result:
                print("🎯 深度对比数据:")
                print(f"   分析时间: {depth_result[17]}")
                print(f"   时间差: {depth_result[18]}ms")
                print()
                
                # 验证计算
                print("📊 深度对比表格:")
                print("   ┌─────────┬─────────┬─────────┬─────────┐")
                print("   │   项目  │  Bitda  │ Binance │  深度比 │")
                print("   ├─────────┼─────────┼─────────┼─────────┤")
                
                # 买一
                bitda_bid1 = float(depth_result[5])
                binance_bid1 = float(depth_result[11])
                bid1_ratio = float(depth_result[0])
                calculated_bid1_ratio = bitda_bid1 / binance_bid1 if binance_bid1 > 0 else 0
                print(f"   │  买一   │{bitda_bid1:8.4f} │{binance_bid1:8.4f} │{bid1_ratio:8.2f} │")
                print(f"   │         │         │         │({calculated_bid1_ratio:8.2f})│")
                
                # 卖一
                bitda_ask1 = float(depth_result[6])
                binance_ask1 = float(depth_result[12])
                ask1_ratio = float(depth_result[1])
                calculated_ask1_ratio = bitda_ask1 / binance_ask1 if binance_ask1 > 0 else 0
                print(f"   │  卖一   │{bitda_ask1:8.4f} │{binance_ask1:8.4f} │{ask1_ratio:8.2f} │")
                print(f"   │         │         │         │({calculated_ask1_ratio:8.2f})│")
                
                # 买一卖一
                bitda_bid_ask1 = bitda_bid1 + bitda_ask1
                binance_bid_ask1 = binance_bid1 + binance_ask1
                bid_ask1_ratio = float(depth_result[2])
                calculated_bid_ask1_ratio = bitda_bid_ask1 / binance_bid_ask1 if binance_bid_ask1 > 0 else 0
                print(f"   │ 买一卖一│{bitda_bid_ask1:8.4f} │{binance_bid_ask1:8.4f} │{bid_ask1_ratio:8.2f} │")
                print(f"   │         │         │         │({calculated_bid_ask1_ratio:8.2f})│")
                
                # 买二卖二
                bitda_bid2 = float(depth_result[7])
                bitda_ask2 = float(depth_result[8])
                binance_bid2 = float(depth_result[13])
                binance_ask2 = float(depth_result[14])
                bitda_bid_ask2 = bitda_bid1 + bitda_bid2 + bitda_ask1 + bitda_ask2
                binance_bid_ask2 = binance_bid1 + binance_bid2 + binance_ask1 + binance_ask2
                bid_ask2_ratio = float(depth_result[3])
                calculated_bid_ask2_ratio = bitda_bid_ask2 / binance_bid_ask2 if binance_bid_ask2 > 0 else 0
                print(f"   │ 买二卖二│{bitda_bid_ask2:8.4f} │{binance_bid_ask2:8.4f} │{bid_ask2_ratio:8.2f} │")
                print(f"   │         │         │         │({calculated_bid_ask2_ratio:8.2f})│")
                
                # 买五卖五
                bitda_bid5_total = float(depth_result[9])
                bitda_ask5_total = float(depth_result[10])
                binance_bid5_total = float(depth_result[15])
                binance_ask5_total = float(depth_result[16])
                bitda_bid_ask5 = bitda_bid5_total + bitda_ask5_total
                binance_bid_ask5 = binance_bid5_total + binance_ask5_total
                bid_ask5_ratio = float(depth_result[4])
                calculated_bid_ask5_ratio = bitda_bid_ask5 / binance_bid_ask5 if binance_bid_ask5 > 0 else 0
                print(f"   │ 买五卖五│{bitda_bid_ask5:8.4f} │{binance_bid_ask5:8.4f} │{bid_ask5_ratio:8.2f} │")
                print(f"   │         │         │         │({calculated_bid_ask5_ratio:8.2f})│")
                
                print("   └─────────┴─────────┴─────────┴─────────┘")
                print("   注: 括号内为验证计算结果")
                
                # 验证计算准确性
                tolerance = 0.01  # 1%容差
                errors = []
                
                if abs(bid1_ratio - calculated_bid1_ratio) > tolerance:
                    errors.append(f"买一比值计算错误: {bid1_ratio} vs {calculated_bid1_ratio}")
                if abs(ask1_ratio - calculated_ask1_ratio) > tolerance:
                    errors.append(f"卖一比值计算错误: {ask1_ratio} vs {calculated_ask1_ratio}")
                if abs(bid_ask1_ratio - calculated_bid_ask1_ratio) > tolerance:
                    errors.append(f"买一卖一比值计算错误: {bid_ask1_ratio} vs {calculated_bid_ask1_ratio}")
                if abs(bid_ask2_ratio - calculated_bid_ask2_ratio) > tolerance:
                    errors.append(f"买二卖二比值计算错误: {bid_ask2_ratio} vs {calculated_bid_ask2_ratio}")
                if abs(bid_ask5_ratio - calculated_bid_ask5_ratio) > tolerance:
                    errors.append(f"买五卖五比值计算错误: {bid_ask5_ratio} vs {calculated_bid_ask5_ratio}")
                
                if errors:
                    print("\n❌ 计算验证失败:")
                    for error in errors:
                        print(f"   {error}")
                else:
                    print("\n✅ 深度对比计算验证通过!")
            
            # 获取最新的价差对比数据
            cursor.execute("""
                SELECT 
                    bitda_bid1_price, bitda_ask1_price, bitda_spread,
                    binance_spread_latest, binance_spread_max, binance_spread_min,
                    binance_spread_avg, binance_spread_median,
                    bid_ask1_spread_diff, sample_count
                FROM spread_comparison 
                WHERE symbol = %s 
                ORDER BY analysis_time DESC 
                LIMIT 1
            """, (symbol,))
            
            spread_result = cursor.fetchone()
            
            if spread_result:
                print("\n💰 价差对比数据:")
                bitda_bid_price = float(spread_result[0])
                bitda_ask_price = float(spread_result[1])
                bitda_spread = float(spread_result[2])
                calculated_bitda_spread = bitda_ask_price - bitda_bid_price
                
                print(f"   Bitda买一价: {bitda_bid_price}")
                print(f"   Bitda卖一价: {bitda_ask_price}")
                print(f"   Bitda价差: {bitda_spread:.4f} (计算值: {calculated_bitda_spread:.4f})")
                
                binance_spread_latest = float(spread_result[3])
                spread_diff = float(spread_result[8])
                calculated_spread_diff = bitda_spread - binance_spread_latest
                
                print(f"   Binance最近价差: {binance_spread_latest:.4f}")
                print(f"   价差差值: {spread_diff:.4f} (计算值: {calculated_spread_diff:.4f})")
                print(f"   样本数量: {spread_result[9]}")
                
                # 验证价差计算
                if abs(bitda_spread - calculated_bitda_spread) > 0.0001:
                    print("   ❌ Bitda价差计算错误")
                elif abs(spread_diff - calculated_spread_diff) > 0.0001:
                    print("   ❌ 价差差值计算错误")
                else:
                    print("   ✅ 价差对比计算验证通过!")
        
        cursor.close()
        connection.close()
        
        print("\n" + "=" * 60)
        print("📋 数据来源说明:")
        print("   📊 Bitda数据: depth_db.bitda_depth 表")
        print("   📈 Binance数据: depth_db.binance_depth_5 表")
        print("   🎯 时间匹配: 5分钟窗口内最接近的数据")
        print("   📐 深度计算: 买一到买五、卖一到卖五的总量")
        print("   💰 价差计算: 卖一价 - 买一价")
        print("   📊 比值计算: Bitda数量 / Binance数量")
        
        print("\n📊 取值说明:")
        print("   🎯 买一: 买一档的数量")
        print("   🎯 卖一: 卖一档的数量")
        print("   🎯 买一卖一: 买一量 + 卖一量")
        print("   🎯 买二卖二: 买一量 + 买二量 + 卖一量 + 卖二量")
        print("   🎯 买五卖五: 买一到买五总量 + 卖一到卖五总量")
        
        print("\n✅ 验证完成!")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")

if __name__ == "__main__":
    verify_final_results()
