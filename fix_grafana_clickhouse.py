#!/usr/bin/env python3
"""
修复Grafana ClickHouse数据源配置
"""

import requests
import json
import time

GRAFANA_URL = "http://localhost:3000"
GRAFANA_USER = "admin"
GRAFANA_PASSWORD = "admin"

def delete_all_clickhouse_datasources():
    """删除所有ClickHouse数据源"""
    print("🗑️ 删除现有ClickHouse数据源...")
    
    response = requests.get(
        f"{GRAFANA_URL}/api/datasources",
        auth=(GRAFANA_USER, GRAFANA_PASSWORD)
    )
    
    if response.status_code == 200:
        datasources = response.json()
        for ds in datasources:
            if 'clickhouse' in ds.get('type', '').lower():
                delete_response = requests.delete(
                    f"{GRAFANA_URL}/api/datasources/{ds['id']}",
                    auth=(GRAFANA_USER, GRAFANA_PASSWORD)
                )
                print(f"   删除数据源 {ds['name']}: {delete_response.status_code}")

def create_clickhouse_datasource():
    """创建新的ClickHouse数据源"""
    print("📊 创建新的ClickHouse数据源...")
    
    datasource_config = {
        "name": "ClickHouse-Fixed",
        "type": "grafana-clickhouse-datasource",
        "url": "http://localhost:8123",
        "access": "proxy",
        "basicAuth": False,
        "isDefault": True,
        "jsonData": {
            "username": "default",
            "defaultDatabase": "crypto",
            "port": 8123,
            "server": "localhost",
            "protocol": "http",
            "timeout": 30,
            "queryTimeout": 60
        },
        "secureJsonData": {
            "password": "Linuxtest"
        }
    }
    
    response = requests.post(
        f"{GRAFANA_URL}/api/datasources",
        json=datasource_config,
        auth=(GRAFANA_USER, GRAFANA_PASSWORD)
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ ClickHouse数据源创建成功，UID: {result.get('uid')}")
        return result.get('uid')
    else:
        print(f"❌ 创建数据源失败: {response.status_code} - {response.text}")
        return None

def test_datasource(uid):
    """测试数据源连接"""
    print("🔍 测试数据源连接...")
    
    response = requests.get(
        f"{GRAFANA_URL}/api/datasources/uid/{uid}/health",
        auth=(GRAFANA_USER, GRAFANA_PASSWORD)
    )
    
    print(f"   连接测试结果: {response.status_code}")
    if response.status_code == 200:
        print(f"   响应: {response.text}")
        return True
    else:
        print(f"   错误: {response.text}")
        return False

def update_dashboard_datasource(uid):
    """更新仪表板数据源UID"""
    print("🔄 更新仪表板数据源...")
    
    # 读取仪表板JSON
    with open('grafana_clickhouse_depth_dashboard.json', 'r', encoding='utf-8') as f:
        dashboard = json.load(f)
    
    # 更新数据源UID
    for panel in dashboard.get('panels', []):
        if 'datasource' in panel:
            panel['datasource']['uid'] = uid
        
        for target in panel.get('targets', []):
            if 'datasource' in target:
                target['datasource']['uid'] = uid
    
    # 保存更新后的仪表板
    with open('grafana_clickhouse_depth_dashboard_fixed.json', 'w', encoding='utf-8') as f:
        json.dump(dashboard, f, ensure_ascii=False, indent=2)
    
    print("✅ 仪表板数据源已更新")
    return 'grafana_clickhouse_depth_dashboard_fixed.json'

def import_dashboard(dashboard_file):
    """导入仪表板"""
    print(f"📊 导入仪表板: {dashboard_file}")
    
    with open(dashboard_file, 'r', encoding='utf-8') as f:
        dashboard = json.load(f)
    
    import_config = {
        "dashboard": dashboard,
        "overwrite": True,
        "inputs": []
    }
    
    response = requests.post(
        f"{GRAFANA_URL}/api/dashboards/import",
        json=import_config,
        auth=(GRAFANA_USER, GRAFANA_PASSWORD)
    )
    
    if response.status_code == 200:
        result = response.json()
        dashboard_url = f"{GRAFANA_URL}/d/{result['uid']}"
        print(f"✅ 仪表板导入成功")
        print(f"🔗 仪表板URL: {dashboard_url}")
        return dashboard_url
    else:
        print(f"❌ 导入仪表板失败: {response.status_code} - {response.text}")
        return None

def main():
    """主函数"""
    print("🚀 修复Grafana ClickHouse配置")
    print("=" * 50)
    
    # 1. 删除现有数据源
    delete_all_clickhouse_datasources()
    time.sleep(2)
    
    # 2. 创建新数据源
    uid = create_clickhouse_datasource()
    if not uid:
        print("❌ 数据源创建失败")
        return
    
    time.sleep(3)
    
    # 3. 测试数据源
    if not test_datasource(uid):
        print("⚠️ 数据源测试失败，但继续尝试")
    
    # 4. 更新仪表板
    dashboard_file = update_dashboard_datasource(uid)
    
    # 5. 导入仪表板
    dashboard_url = import_dashboard(dashboard_file)
    
    if dashboard_url:
        print("\n🎉 修复完成!")
        print(f"📊 仪表板地址: {dashboard_url}")
        print(f"👤 用户名: {GRAFANA_USER}")
        print(f"🔑 密码: {GRAFANA_PASSWORD}")
    else:
        print("❌ 修复失败")

if __name__ == "__main__":
    main()
