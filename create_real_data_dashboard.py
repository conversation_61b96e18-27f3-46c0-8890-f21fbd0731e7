#!/usr/bin/env python3
"""
创建使用真实数据的6个表格仪表板
1分钟刷新率，真实统计数据
"""

import requests
import json

def create_real_data_dashboard():
    """创建使用真实数据的完整仪表板"""
    print("🎨 创建使用真实数据的6个表格仪表板...")
    
    session = requests.Session()
    session.auth = ("admin", "admin")
    datasource_uid = "cenigejcatslce"  # WorkingDepthDB
    
    dashboard_config = {
        "dashboard": {
            "id": None,
            "title": "🔄 真实数据深度价差分析 - 1分钟刷新",
            "tags": ["real-data", "1min-refresh"],
            "timezone": "browser",
            "panels": [
                # 1. BTCUSDT深度对比
                {
                    "id": 1,
                    "title": "📊 BTCUSDT 深度对比",
                    "type": "table",
                    "gridPos": {"h": 7, "w": 8, "x": 0, "y": 0},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            SELECT '买一量' as 项目,
                                ROUND((SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
                                ROUND((SELECT bid_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
                                ROUND((SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                      (SELECT bid_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                            UNION ALL SELECT '卖一量' as 项目,
                                ROUND((SELECT ask_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
                                ROUND((SELECT ask_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
                                ROUND((SELECT ask_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                      (SELECT ask_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                            UNION ALL SELECT '买一量卖一量' as 项目,
                                ROUND((SELECT bid_qty_1 + ask_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
                                ROUND((SELECT bid_qty_1 + ask_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
                                ROUND((SELECT bid_qty_1 + ask_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                      (SELECT bid_qty_1 + ask_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                            UNION ALL SELECT '买卖前两档量' as 项目,
                                ROUND((SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
                                ROUND((SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
                                ROUND((SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                      (SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                            UNION ALL SELECT '买卖前五档量' as 项目,
                                ROUND((SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
                                ROUND((SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
                                ROUND((SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                      (SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                        """,
                        "refId": "A"
                    }]
                },
                
                # 2. ETHUSDT深度对比
                {
                    "id": 2,
                    "title": "📊 ETHUSDT 深度对比",
                    "type": "table",
                    "gridPos": {"h": 7, "w": 8, "x": 8, "y": 0},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            SELECT '买一量' as 项目,
                                ROUND((SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
                                ROUND((SELECT bid_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
                                ROUND((SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                      (SELECT bid_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                            UNION ALL SELECT '卖一量' as 项目,
                                ROUND((SELECT ask_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
                                ROUND((SELECT ask_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
                                ROUND((SELECT ask_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                      (SELECT ask_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                            UNION ALL SELECT '买一量卖一量' as 项目,
                                ROUND((SELECT bid_qty_1 + ask_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
                                ROUND((SELECT bid_qty_1 + ask_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
                                ROUND((SELECT bid_qty_1 + ask_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                      (SELECT bid_qty_1 + ask_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                            UNION ALL SELECT '买卖前两档量' as 项目,
                                ROUND((SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
                                ROUND((SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
                                ROUND((SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                      (SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                            UNION ALL SELECT '买卖前五档量' as 项目,
                                ROUND((SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
                                ROUND((SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
                                ROUND((SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                      (SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                        """,
                        "refId": "A"
                    }]
                },
                
                # 3. 数据更新时间 - 修复NoData问题
                {
                    "id": 3,
                    "title": "⏰ 数据更新时间",
                    "type": "stat",
                    "gridPos": {"h": 7, "w": 8, "x": 16, "y": 0},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": "SELECT DATE_FORMAT(FROM_UNIXTIME(MAX(timestamp)/1000), '%Y-%m-%d %H:%i:%s') as value FROM bitda_depth WHERE symbol IN ('BTCUSDT', 'ETHUSDT')",
                        "refId": "A"
                    }],
                    "options": {
                        "reduceOptions": {"values": False, "calcs": ["lastNotNull"], "fields": ""},
                        "textMode": "auto", 
                        "colorMode": "background",
                        "orientation": "auto",
                        "justifyMode": "center"
                    },
                    "fieldConfig": {
                        "defaults": {
                            "color": {"mode": "thresholds"},
                            "thresholds": {"steps": [{"color": "green", "value": None}]}
                        }
                    }
                },

                # 4. BTCUSDT深度统计 - 真实数据
                {
                    "id": 4,
                    "title": "📈 BTCUSDT 深度统计",
                    "type": "table",
                    "gridPos": {"h": 6, "w": 12, "x": 0, "y": 7},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            WITH depth_ratios AS (
                                SELECT
                                    (b.bid_qty_1 + b.ask_qty_1) / (bn.bid_qty_1 + bn.ask_qty_1) as ratio1,
                                    (b.bid_qty_1 + b.bid_qty_2 + b.ask_qty_1 + b.ask_qty_2) / (bn.bid_qty_1 + bn.bid_qty_2 + bn.ask_qty_1 + bn.ask_qty_2) as ratio2,
                                    (b.bid_qty_1 + b.bid_qty_2 + b.bid_qty_3 + b.bid_qty_4 + b.bid_qty_5 + b.ask_qty_1 + b.ask_qty_2 + b.ask_qty_3 + b.ask_qty_4 + b.ask_qty_5) /
                                    (bn.bid_qty_1 + bn.bid_qty_2 + bn.bid_qty_3 + bn.bid_qty_4 + bn.bid_qty_5 + bn.ask_qty_1 + bn.ask_qty_2 + bn.ask_qty_3 + bn.ask_qty_4 + bn.ask_qty_5) as ratio5
                                FROM bitda_depth b
                                JOIN binance_depth_5 bn ON bn.symbol = 'BTCUSDT'
                                    AND bn.event_time <= b.timestamp
                                    AND bn.event_time >= b.timestamp - 60000
                                WHERE b.symbol = 'BTCUSDT'
                                    AND b.timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 5 MINUTE)) * 1000
                                    AND b.bid_price_1 IS NOT NULL AND bn.bid_price_1 IS NOT NULL
                                    AND (bn.bid_qty_1 + bn.ask_qty_1) > 0
                                    AND (bn.bid_qty_1 + bn.bid_qty_2 + bn.ask_qty_1 + bn.ask_qty_2) > 0
                                    AND (bn.bid_qty_1 + bn.bid_qty_2 + bn.bid_qty_3 + bn.bid_qty_4 + bn.bid_qty_5 + bn.ask_qty_1 + bn.ask_qty_2 + bn.ask_qty_3 + bn.ask_qty_4 + bn.ask_qty_5) > 0
                            )
                            SELECT '买一量卖一量深度比' as 项目, ROUND(MAX(ratio1), 2) as 最大值, ROUND(MIN(ratio1), 2) as 最小值, ROUND(AVG(ratio1), 2) as 平均值 FROM depth_ratios WHERE ratio1 IS NOT NULL
                            UNION ALL
                            SELECT '买卖前两档量深度比' as 项目, ROUND(MAX(ratio2), 2) as 最大值, ROUND(MIN(ratio2), 2) as 最小值, ROUND(AVG(ratio2), 2) as 平均值 FROM depth_ratios WHERE ratio2 IS NOT NULL
                            UNION ALL
                            SELECT '买卖前五档量深度比' as 项目, ROUND(MAX(ratio5), 2) as 最大值, ROUND(MIN(ratio5), 2) as 最小值, ROUND(AVG(ratio5), 2) as 平均值 FROM depth_ratios WHERE ratio5 IS NOT NULL
                        """,
                        "refId": "A"
                    }]
                },

                # 5. ETHUSDT深度统计 - 真实数据
                {
                    "id": 5,
                    "title": "📈 ETHUSDT 深度统计",
                    "type": "table",
                    "gridPos": {"h": 6, "w": 12, "x": 12, "y": 7},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            WITH depth_ratios AS (
                                SELECT
                                    (b.bid_qty_1 + b.ask_qty_1) / (bn.bid_qty_1 + bn.ask_qty_1) as ratio1,
                                    (b.bid_qty_1 + b.bid_qty_2 + b.ask_qty_1 + b.ask_qty_2) / (bn.bid_qty_1 + bn.bid_qty_2 + bn.ask_qty_1 + bn.ask_qty_2) as ratio2,
                                    (b.bid_qty_1 + b.bid_qty_2 + b.bid_qty_3 + b.bid_qty_4 + b.bid_qty_5 + b.ask_qty_1 + b.ask_qty_2 + b.ask_qty_3 + b.ask_qty_4 + b.ask_qty_5) /
                                    (bn.bid_qty_1 + bn.bid_qty_2 + bn.bid_qty_3 + bn.bid_qty_4 + bn.bid_qty_5 + bn.ask_qty_1 + bn.ask_qty_2 + bn.ask_qty_3 + bn.ask_qty_4 + bn.ask_qty_5) as ratio5
                                FROM bitda_depth b
                                JOIN binance_depth_5 bn ON bn.symbol = 'ETHUSDT'
                                    AND bn.event_time <= b.timestamp
                                    AND bn.event_time >= b.timestamp - 60000
                                WHERE b.symbol = 'ETHUSDT'
                                    AND b.timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 5 MINUTE)) * 1000
                                    AND b.bid_price_1 IS NOT NULL AND bn.bid_price_1 IS NOT NULL
                                    AND (bn.bid_qty_1 + bn.ask_qty_1) > 0
                                    AND (bn.bid_qty_1 + bn.bid_qty_2 + bn.ask_qty_1 + bn.ask_qty_2) > 0
                                    AND (bn.bid_qty_1 + bn.bid_qty_2 + bn.bid_qty_3 + bn.bid_qty_4 + bn.bid_qty_5 + bn.ask_qty_1 + bn.ask_qty_2 + bn.ask_qty_3 + bn.ask_qty_4 + bn.ask_qty_5) > 0
                            )
                            SELECT '买一量卖一量深度比' as 项目, ROUND(MAX(ratio1), 2) as 最大值, ROUND(MIN(ratio1), 2) as 最小值, ROUND(AVG(ratio1), 2) as 平均值 FROM depth_ratios WHERE ratio1 IS NOT NULL
                            UNION ALL
                            SELECT '买卖前两档量深度比' as 项目, ROUND(MAX(ratio2), 2) as 最大值, ROUND(MIN(ratio2), 2) as 最小值, ROUND(AVG(ratio2), 2) as 平均值 FROM depth_ratios WHERE ratio2 IS NOT NULL
                            UNION ALL
                            SELECT '买卖前五档量深度比' as 项目, ROUND(MAX(ratio5), 2) as 最大值, ROUND(MIN(ratio5), 2) as 最小值, ROUND(AVG(ratio5), 2) as 平均值 FROM depth_ratios WHERE ratio5 IS NOT NULL
                        """,
                        "refId": "A"
                    }]
                },

                # 6. BTCUSDT价差对比 - 真实数据
                {
                    "id": 6,
                    "title": "💰 BTCUSDT 价差对比",
                    "type": "table",
                    "gridPos": {"h": 6, "w": 12, "x": 0, "y": 13},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            WITH spread_data AS (
                                SELECT
                                    (b.ask_price_1 - b.bid_price_1) as bitda_spread,
                                    (bn.ask_price_1 - bn.bid_price_1) as binance_spread,
                                    ABS(b.timestamp - bn.event_time) as time_diff
                                FROM bitda_depth b
                                JOIN binance_depth_5 bn ON bn.symbol = 'BTCUSDT'
                                    AND bn.event_time <= b.timestamp
                                    AND bn.event_time >= b.timestamp - 60000
                                WHERE b.symbol = 'BTCUSDT'
                                    AND b.timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 5 MINUTE)) * 1000
                                    AND b.bid_price_1 IS NOT NULL AND bn.bid_price_1 IS NOT NULL
                                ORDER BY b.timestamp DESC
                                LIMIT 100
                            )
                            SELECT '最近价差' as 项目,
                                ROUND((SELECT bitda_spread FROM spread_data ORDER BY time_diff ASC LIMIT 1), 4) as Bitda,
                                ROUND((SELECT binance_spread FROM spread_data ORDER BY time_diff ASC LIMIT 1), 4) as Binance,
                                CONCAT((SELECT time_diff FROM spread_data ORDER BY time_diff ASC LIMIT 1), 'ms') as 时间差
                            UNION ALL
                            SELECT '最大价差' as 项目, ROUND(MAX(bitda_spread), 4) as Bitda, ROUND(MAX(binance_spread), 4) as Binance, 'N/A' as 时间差 FROM spread_data
                            UNION ALL
                            SELECT '最小价差' as 项目, ROUND(MIN(bitda_spread), 4) as Bitda, ROUND(MIN(binance_spread), 4) as Binance, 'N/A' as 时间差 FROM spread_data
                            UNION ALL
                            SELECT '平均价差' as 项目, ROUND(AVG(bitda_spread), 4) as Bitda, ROUND(AVG(binance_spread), 4) as Binance, 'N/A' as 时间差 FROM spread_data
                        """,
                        "refId": "A"
                    }]
                },

                # 7. ETHUSDT价差对比 - 真实数据
                {
                    "id": 7,
                    "title": "💰 ETHUSDT 价差对比",
                    "type": "table",
                    "gridPos": {"h": 6, "w": 12, "x": 12, "y": 13},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            WITH spread_data AS (
                                SELECT
                                    (b.ask_price_1 - b.bid_price_1) as bitda_spread,
                                    (bn.ask_price_1 - bn.bid_price_1) as binance_spread,
                                    ABS(b.timestamp - bn.event_time) as time_diff
                                FROM bitda_depth b
                                JOIN binance_depth_5 bn ON bn.symbol = 'ETHUSDT'
                                    AND bn.event_time <= b.timestamp
                                    AND bn.event_time >= b.timestamp - 60000
                                WHERE b.symbol = 'ETHUSDT'
                                    AND b.timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 5 MINUTE)) * 1000
                                    AND b.bid_price_1 IS NOT NULL AND bn.bid_price_1 IS NOT NULL
                                ORDER BY b.timestamp DESC
                                LIMIT 100
                            )
                            SELECT '最近价差' as 项目,
                                ROUND((SELECT bitda_spread FROM spread_data ORDER BY time_diff ASC LIMIT 1), 4) as Bitda,
                                ROUND((SELECT binance_spread FROM spread_data ORDER BY time_diff ASC LIMIT 1), 4) as Binance,
                                CONCAT((SELECT time_diff FROM spread_data ORDER BY time_diff ASC LIMIT 1), 'ms') as 时间差
                            UNION ALL
                            SELECT '最大价差' as 项目, ROUND(MAX(bitda_spread), 4) as Bitda, ROUND(MAX(binance_spread), 4) as Binance, 'N/A' as 时间差 FROM spread_data
                            UNION ALL
                            SELECT '最小价差' as 项目, ROUND(MIN(bitda_spread), 4) as Bitda, ROUND(MIN(binance_spread), 4) as Binance, 'N/A' as 时间差 FROM spread_data
                            UNION ALL
                            SELECT '平均价差' as 项目, ROUND(AVG(bitda_spread), 4) as Bitda, ROUND(AVG(binance_spread), 4) as Binance, 'N/A' as 时间差 FROM spread_data
                        """,
                        "refId": "A"
                    }]
                }
            ],
            "time": {"from": "now-1h", "to": "now"},
            "refresh": "1m",  # 1分钟刷新
            "schemaVersion": 30,
            "version": 1
        },
        "overwrite": True
    }
    
    try:
        response = session.post(
            "http://localhost:3000/api/dashboards/db",
            json=dashboard_config,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            dashboard_url = f"http://localhost:3000{result['url']}"
            print(f"   ✅ 真实数据仪表板创建成功")
            print(f"   🌐 地址: {dashboard_url}")
            return dashboard_url
        else:
            print(f"   ❌ 创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return None

def main():
    """主函数"""
    print("🎨 创建使用真实数据的6个表格仪表板")
    print("=" * 60)
    print("🔧 配置:")
    print("   ⏰ 刷新频率: 1分钟")
    print("   📊 数据类型: 真实数据")
    print("   🕐 时间显示: 修复NoData问题")
    print()
    
    dashboard_url = create_real_data_dashboard()
    
    if dashboard_url:
        print(f"\n🎉 真实数据仪表板创建成功！")
        print(f"🌐 访问地址: {dashboard_url}")
        print(f"⏰ 1分钟自动刷新")
        print(f"📊 使用真实数据")
        print(f"🕐 显示真实数据库时间")
        
        # 打开浏览器
        try:
            import webbrowser
            webbrowser.open(dashboard_url)
            print(f"🌐 已自动打开浏览器")
        except:
            pass
    else:
        print(f"\n❌ 创建失败")

if __name__ == "__main__":
    main()
