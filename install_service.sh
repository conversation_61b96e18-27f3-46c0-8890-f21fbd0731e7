#!/bin/bash

# WebSocket数据收集器服务安装脚本
# 用于设置systemd服务，确保程序开机自启动

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要使用root用户运行此脚本"
        log_info "请使用普通用户运行: ./install_service.sh"
        exit 1
    fi
}

# 检查系统要求
check_requirements() {
    log_step "检查系统要求..."

    # 检查systemd
    if ! command -v systemctl &> /dev/null; then
        log_error "系统不支持systemd，无法安装服务"
        exit 1
    fi

    # 检查Python环境
    if ! command -v python &> /dev/null; then
        log_error "未找到Python，请先安装Python环境"
        exit 1
    fi

    # 检查conda环境
    if ! command -v conda &> /dev/null; then
        log_warn "未找到conda，将使用系统Python"
    fi

    # 检查MySQL
    if ! command -v mysql &> /dev/null; then
        log_warn "未找到MySQL客户端，请确保MySQL服务正在运行"
    fi

    log_info "系统要求检查完成"
}

# 安装Python依赖
install_dependencies() {
    log_step "安装Python依赖..."

    # 使用conda环境的pip直接安装依赖
    if [[ -f "/home/<USER>/anaconda3/bin/pip" ]]; then
        log_info "使用conda环境的pip安装依赖..."
        /home/<USER>/anaconda3/bin/pip install websockets mysql-connector-python python-dotenv psutil
    elif command -v pip &> /dev/null; then
        log_info "使用系统pip安装依赖..."
        pip install websockets mysql-connector-python python-dotenv psutil
    else
        log_error "未找到pip，请手动安装依赖"
        exit 1
    fi

    log_info "依赖安装完成"
}

# 创建日志目录
create_log_directory() {
    log_step "创建日志目录..."

    # 确保日志目录存在
    sudo mkdir -p /var/log/ws-data-collector
    sudo chown $USER:$USER /var/log/ws-data-collector
    sudo chmod 755 /var/log/ws-data-collector

    log_info "日志目录创建完成: /var/log/ws-data-collector"
}

# 安装systemd服务
install_systemd_service() {
    log_step "安装systemd服务..."

    # 获取当前目录
    CURRENT_DIR=$(pwd)
    SERVICE_FILE="$CURRENT_DIR/ws-data-collector.service"

    if [[ ! -f "$SERVICE_FILE" ]]; then
        log_error "服务文件不存在: $SERVICE_FILE"
        exit 1
    fi

    # 复制服务文件到systemd目录
    sudo cp "$SERVICE_FILE" /etc/systemd/system/
    sudo chmod 644 /etc/systemd/system/ws-data-collector.service

    # 重新加载systemd
    sudo systemctl daemon-reload

    log_info "systemd服务安装完成"
}

# 启用并启动服务
enable_and_start_service() {
    log_step "启用并启动服务..."

    # 停止当前运行的程序（如果有）
    log_info "停止当前运行的程序..."
    pkill -f ws_data_collector.py || true
    sleep 2

    # 启用服务（开机自启动）
    sudo systemctl enable ws-data-collector.service

    # 启动服务
    sudo systemctl start ws-data-collector.service

    # 检查服务状态
    sleep 3
    if sudo systemctl is-active --quiet ws-data-collector.service; then
        log_info "服务启动成功！"
    else
        log_error "服务启动失败，请检查日志"
        sudo systemctl status ws-data-collector.service
        exit 1
    fi
}

# 显示服务状态
show_service_status() {
    log_step "显示服务状态..."

    echo ""
    echo "=== 服务状态 ==="
    sudo systemctl status ws-data-collector.service --no-pager

    echo ""
    echo "=== 最新日志 ==="
    sudo journalctl -u ws-data-collector.service --no-pager -n 10
}

# 显示使用说明
show_usage_info() {
    log_step "服务管理命令..."

    echo ""
    echo "=== 常用命令 ==="
    echo "查看服务状态:    sudo systemctl status ws-data-collector"
    echo "启动服务:        sudo systemctl start ws-data-collector"
    echo "停止服务:        sudo systemctl stop ws-data-collector"
    echo "重启服务:        sudo systemctl restart ws-data-collector"
    echo "查看日志:        sudo journalctl -u ws-data-collector -f"
    echo "禁用开机自启:    sudo systemctl disable ws-data-collector"
    echo ""
    echo "=== 日志文件 ==="
    echo "应用日志:        /tmp/ws_data_collector.log"
    echo "系统日志:        sudo journalctl -u ws-data-collector"
    echo ""
    echo "=== 配置文件 ==="
    echo "环境配置:        $(pwd)/.env"
    echo "服务配置:        /etc/systemd/system/ws-data-collector.service"
}

# 主函数
main() {
    echo "=== WebSocket数据收集器服务安装程序 ==="
    echo ""

    check_root
    check_requirements
    install_dependencies
    create_log_directory
    install_systemd_service
    enable_and_start_service
    show_service_status
    show_usage_info

    echo ""
    log_info "🎉 服务安装完成！程序现在会在系统重启后自动启动。"
}

# 运行主函数
main "$@"
