#!/usr/bin/env python3
"""
清理历史数据并重新开始存储优化
删除2025年5月30日17点(UTC+8)之前的数据，使用新的存储逻辑
"""

import mysql.connector
from datetime import datetime, timezone, timedelta
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CleanAndRestartStorage:
    """清理历史数据并重新开始存储优化"""
    
    def __init__(self):
        self.config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'depth_db'
        }
        
        # 设置清理时间点：2025年5月30日17点(UTC+8)
        self.cutoff_time = datetime(2025, 5, 30, 17, 0, 0)
        
    def check_current_status(self):
        """检查当前数据库状态"""
        try:
            connection = mysql.connector.connect(**self.config)
            cursor = connection.cursor()
            
            logger.info("📊 检查当前数据库状态...")
            
            # 检查各表的数据量和时间范围
            tables_to_check = [
                'bitda_depth',
                'binance_depth_5', 
                'binance_bookticker',
                'bitda_ticker'
            ]
            
            for table in tables_to_check:
                try:
                    # 检查总记录数
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    total_count = cursor.fetchone()[0]
                    
                    # 检查最早和最新记录
                    cursor.execute(f"SELECT MIN(created_at), MAX(created_at) FROM {table}")
                    min_time, max_time = cursor.fetchone()
                    
                    # 检查需要删除的记录数
                    cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE created_at < %s", (self.cutoff_time,))
                    to_delete = cursor.fetchone()[0]
                    
                    logger.info(f"📋 {table}:")
                    logger.info(f"   总记录数: {total_count:,}")
                    logger.info(f"   时间范围: {min_time} ~ {max_time}")
                    logger.info(f"   需删除: {to_delete:,} 条 ({to_delete/total_count*100:.1f}%)")
                    
                except Exception as e:
                    logger.warning(f"检查表 {table} 失败: {e}")
            
            cursor.close()
            connection.close()
            
        except Exception as e:
            logger.error(f"检查数据库状态失败: {e}")
            raise
    
    def check_table_structure(self):
        """检查表结构，确认是否已有价格字段"""
        try:
            connection = mysql.connector.connect(**self.config)
            cursor = connection.cursor()
            
            logger.info("🔍 检查表结构...")
            
            # 检查bitda_depth表
            cursor.execute("DESCRIBE bitda_depth")
            bitda_columns = [col[0] for col in cursor.fetchall()]
            
            bitda_has_prices = any('price_' in col for col in bitda_columns)
            logger.info(f"📊 bitda_depth表: {'✅ 已有价格字段' if bitda_has_prices else '❌ 缺少价格字段'}")
            
            # 检查binance_depth_5表
            cursor.execute("DESCRIBE binance_depth_5")
            binance_columns = [col[0] for col in cursor.fetchall()]
            
            binance_has_prices = any('price_' in col for col in binance_columns)
            logger.info(f"📊 binance_depth_5表: {'✅ 已有价格字段' if binance_has_prices else '❌ 缺少价格字段'}")
            
            cursor.close()
            connection.close()
            
            return bitda_has_prices, binance_has_prices
            
        except Exception as e:
            logger.error(f"检查表结构失败: {e}")
            raise
    
    def clean_historical_data(self):
        """清理历史数据"""
        try:
            connection = mysql.connector.connect(**self.config)
            cursor = connection.cursor()
            
            logger.info(f"🧹 开始清理 {self.cutoff_time} 之前的历史数据...")
            
            # 要清理的表
            tables_to_clean = [
                'bitda_depth',
                'binance_depth_5',
                'binance_bookticker', 
                'bitda_ticker'
            ]
            
            total_deleted = 0
            
            for table in tables_to_clean:
                try:
                    # 检查要删除的记录数
                    cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE created_at < %s", (self.cutoff_time,))
                    to_delete = cursor.fetchone()[0]
                    
                    if to_delete > 0:
                        logger.info(f"🗑️  清理表 {table}: {to_delete:,} 条记录...")
                        
                        # 分批删除，避免锁表时间过长
                        batch_size = 10000
                        deleted_count = 0
                        
                        while True:
                            cursor.execute(f"""
                                DELETE FROM {table} 
                                WHERE created_at < %s 
                                LIMIT %s
                            """, (self.cutoff_time, batch_size))
                            
                            batch_deleted = cursor.rowcount
                            if batch_deleted == 0:
                                break
                                
                            deleted_count += batch_deleted
                            connection.commit()
                            
                            logger.info(f"   已删除: {deleted_count:,}/{to_delete:,} ({deleted_count/to_delete*100:.1f}%)")
                        
                        total_deleted += deleted_count
                        logger.info(f"✅ 表 {table} 清理完成: 删除 {deleted_count:,} 条记录")
                    else:
                        logger.info(f"✅ 表 {table}: 无需清理")
                        
                except Exception as e:
                    logger.error(f"清理表 {table} 失败: {e}")
                    continue
            
            logger.info(f"🎉 历史数据清理完成！总共删除 {total_deleted:,} 条记录")
            
            cursor.close()
            connection.close()
            
        except Exception as e:
            logger.error(f"清理历史数据失败: {e}")
            raise
    
    def ensure_table_structure(self):
        """确保表结构包含价格字段"""
        try:
            connection = mysql.connector.connect(**self.config)
            cursor = connection.cursor()
            
            logger.info("🔧 确保表结构包含价格字段...")
            
            # 为bitda_depth表添加价格字段（如果不存在）
            try:
                cursor.execute("DESCRIBE bitda_depth")
                columns = [col[0] for col in cursor.fetchall()]
                
                if 'bid_price_1' not in columns:
                    logger.info("添加bitda_depth表价格字段...")
                    alter_sql = """
                    ALTER TABLE bitda_depth 
                    ADD COLUMN bid_price_1 DECIMAL(15,2) COMMENT '买一价格',
                    ADD COLUMN ask_price_1 DECIMAL(15,2) COMMENT '卖一价格',
                    ADD COLUMN bid_qty_1 DECIMAL(20,4) COMMENT '买一数量',
                    ADD COLUMN ask_qty_1 DECIMAL(20,4) COMMENT '卖一数量',
                    ADD COLUMN bid_price_2 DECIMAL(15,2) COMMENT '买二价格',
                    ADD COLUMN ask_price_2 DECIMAL(15,2) COMMENT '卖二价格',
                    ADD COLUMN bid_qty_2 DECIMAL(20,4) COMMENT '买二数量',
                    ADD COLUMN ask_qty_2 DECIMAL(20,4) COMMENT '卖二数量',
                    ADD COLUMN bid_price_3 DECIMAL(15,2) COMMENT '买三价格',
                    ADD COLUMN ask_price_3 DECIMAL(15,2) COMMENT '卖三价格',
                    ADD COLUMN bid_qty_3 DECIMAL(20,4) COMMENT '买三数量',
                    ADD COLUMN ask_qty_3 DECIMAL(20,4) COMMENT '卖三数量',
                    ADD COLUMN bid_price_4 DECIMAL(15,2) COMMENT '买四价格',
                    ADD COLUMN ask_price_4 DECIMAL(15,2) COMMENT '卖四价格',
                    ADD COLUMN bid_qty_4 DECIMAL(20,4) COMMENT '买四数量',
                    ADD COLUMN ask_qty_4 DECIMAL(20,4) COMMENT '卖四数量',
                    ADD COLUMN bid_price_5 DECIMAL(15,2) COMMENT '买五价格',
                    ADD COLUMN ask_price_5 DECIMAL(15,2) COMMENT '卖五价格',
                    ADD COLUMN bid_qty_5 DECIMAL(20,4) COMMENT '买五数量',
                    ADD COLUMN ask_qty_5 DECIMAL(20,4) COMMENT '卖五数量'
                    """
                    cursor.execute(alter_sql)
                    logger.info("✅ bitda_depth表价格字段添加成功")
                else:
                    logger.info("✅ bitda_depth表价格字段已存在")
                    
            except Exception as e:
                logger.error(f"处理bitda_depth表失败: {e}")
            
            # 为binance_depth_5表添加价格字段（如果不存在）
            try:
                cursor.execute("DESCRIBE binance_depth_5")
                columns = [col[0] for col in cursor.fetchall()]
                
                if 'bid_price_1' not in columns:
                    logger.info("添加binance_depth_5表价格字段...")
                    alter_sql = """
                    ALTER TABLE binance_depth_5 
                    ADD COLUMN bid_price_1 DECIMAL(15,2) COMMENT '买一价格',
                    ADD COLUMN ask_price_1 DECIMAL(15,2) COMMENT '卖一价格',
                    ADD COLUMN bid_qty_1 DECIMAL(20,4) COMMENT '买一数量',
                    ADD COLUMN ask_qty_1 DECIMAL(20,4) COMMENT '卖一数量',
                    ADD COLUMN bid_price_2 DECIMAL(15,2) COMMENT '买二价格',
                    ADD COLUMN ask_price_2 DECIMAL(15,2) COMMENT '卖二价格',
                    ADD COLUMN bid_qty_2 DECIMAL(20,4) COMMENT '买二数量',
                    ADD COLUMN ask_qty_2 DECIMAL(20,4) COMMENT '卖二数量',
                    ADD COLUMN bid_price_3 DECIMAL(15,2) COMMENT '买三价格',
                    ADD COLUMN ask_price_3 DECIMAL(15,2) COMMENT '卖三价格',
                    ADD COLUMN bid_qty_3 DECIMAL(20,4) COMMENT '买三数量',
                    ADD COLUMN ask_qty_3 DECIMAL(20,4) COMMENT '卖三数量',
                    ADD COLUMN bid_price_4 DECIMAL(15,2) COMMENT '买四价格',
                    ADD COLUMN ask_price_4 DECIMAL(15,2) COMMENT '卖四价格',
                    ADD COLUMN bid_qty_4 DECIMAL(20,4) COMMENT '买四数量',
                    ADD COLUMN ask_qty_4 DECIMAL(20,4) COMMENT '卖四数量',
                    ADD COLUMN bid_price_5 DECIMAL(15,2) COMMENT '买五价格',
                    ADD COLUMN ask_price_5 DECIMAL(15,2) COMMENT '卖五价格',
                    ADD COLUMN bid_qty_5 DECIMAL(20,4) COMMENT '买五数量',
                    ADD COLUMN ask_qty_5 DECIMAL(20,4) COMMENT '卖五数量'
                    """
                    cursor.execute(alter_sql)
                    logger.info("✅ binance_depth_5表价格字段添加成功")
                else:
                    logger.info("✅ binance_depth_5表价格字段已存在")
                    
            except Exception as e:
                logger.error(f"处理binance_depth_5表失败: {e}")
            
            cursor.close()
            connection.close()
            
        except Exception as e:
            logger.error(f"确保表结构失败: {e}")
            raise
    
    def create_indexes(self):
        """创建优化索引"""
        try:
            connection = mysql.connector.connect(**self.config)
            cursor = connection.cursor()
            
            logger.info("📊 创建优化索引...")
            
            # 为bitda_depth创建索引
            try:
                cursor.execute("SHOW INDEX FROM bitda_depth WHERE Key_name = 'idx_price_match'")
                if not cursor.fetchone():
                    cursor.execute("""
                        CREATE INDEX idx_price_match 
                        ON bitda_depth (symbol, bid_price_1, ask_price_1, timestamp)
                    """)
                    logger.info("✅ bitda_depth价格匹配索引创建成功")
                else:
                    logger.info("✅ bitda_depth价格匹配索引已存在")
            except Exception as e:
                logger.warning(f"创建bitda_depth索引失败: {e}")
            
            # 为binance_depth_5创建索引
            try:
                cursor.execute("SHOW INDEX FROM binance_depth_5 WHERE Key_name = 'idx_binance_depth_match'")
                if not cursor.fetchone():
                    cursor.execute("""
                        CREATE INDEX idx_binance_depth_match 
                        ON binance_depth_5 (symbol, bid_price_1, ask_price_1, event_time)
                    """)
                    logger.info("✅ binance_depth_5价格匹配索引创建成功")
                else:
                    logger.info("✅ binance_depth_5价格匹配索引已存在")
            except Exception as e:
                logger.warning(f"创建binance_depth_5索引失败: {e}")
            
            cursor.close()
            connection.close()
            
        except Exception as e:
            logger.error(f"创建索引失败: {e}")
    
    def verify_final_status(self):
        """验证最终状态"""
        try:
            connection = mysql.connector.connect(**self.config)
            cursor = connection.cursor()
            
            logger.info("✅ 验证最终状态...")
            
            # 检查剩余数据
            tables = ['bitda_depth', 'binance_depth_5', 'binance_bookticker', 'bitda_ticker']
            
            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    total = cursor.fetchone()[0]
                    
                    cursor.execute(f"SELECT MIN(created_at), MAX(created_at) FROM {table}")
                    result = cursor.fetchone()
                    min_time, max_time = result if result[0] else (None, None)
                    
                    logger.info(f"📊 {table}: {total:,} 条记录")
                    if min_time and max_time:
                        logger.info(f"   时间范围: {min_time} ~ {max_time}")
                    
                except Exception as e:
                    logger.warning(f"检查表 {table} 失败: {e}")
            
            cursor.close()
            connection.close()
            
        except Exception as e:
            logger.error(f"验证最终状态失败: {e}")
    
    def execute_clean_restart(self):
        """执行完整的清理和重启流程"""
        logger.info("🚀 开始清理历史数据并重新开始存储优化")
        logger.info("=" * 60)
        logger.info(f"清理时间点: {self.cutoff_time} (UTC+8)")
        logger.info("=" * 60)
        
        try:
            # 1. 检查当前状态
            self.check_current_status()
            
            # 2. 检查表结构
            bitda_has_prices, binance_has_prices = self.check_table_structure()
            
            # 3. 确认清理
            print(f"\n⚠️  即将删除 {self.cutoff_time} 之前的所有历史数据")
            print("这个操作不可逆，请确认是否继续？(y/N)")
            response = input().strip().lower()
            
            if response != 'y':
                logger.info("❌ 用户取消操作")
                return False
            
            # 4. 清理历史数据
            self.clean_historical_data()
            
            # 5. 确保表结构
            self.ensure_table_structure()
            
            # 6. 创建索引
            self.create_indexes()
            
            # 7. 验证最终状态
            self.verify_final_status()
            
            logger.info("🎉 清理和重启完成！")
            logger.info("📋 下一步:")
            logger.info("  1. 重启数据采集程序（使用新的存储逻辑）")
            logger.info("  2. 验证新数据包含价格字段")
            logger.info("  3. 使用优化版延时处理器")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 清理和重启失败: {e}")
            return False

def main():
    """主函数"""
    print("🧹 清理历史数据并重新开始存储优化")
    print("=" * 50)
    print("功能:")
    print("  - 删除2025年5月30日17点(UTC+8)之前的历史数据")
    print("  - 确保表结构包含买一到买五价格字段")
    print("  - 创建优化索引")
    print("  - 为新的存储逻辑做准备")
    print()
    
    cleaner = CleanAndRestartStorage()
    success = cleaner.execute_clean_restart()
    
    if success:
        print("\n✅ 清理完成！现在可以重启数据采集程序使用新的存储逻辑")
    else:
        print("\n❌ 清理失败，请检查日志")

if __name__ == "__main__":
    main()
