#!/usr/bin/env python3
"""
快速测试深度价差分析逻辑
"""

import mysql.connector
from datetime import datetime, timedelta
import json

def quick_test():
    """快速测试分析逻辑"""
    print("🔍 快速测试深度价差分析逻辑")
    print("=" * 50)
    
    try:
        # 连接数据库
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='Linuxtest',
            database='depth_db',
            connection_timeout=10
        )
        cursor = connection.cursor()
        
        # 1. 获取一条最新的Bitda数据
        print("📊 获取Bitda样本数据...")
        cursor.execute("""
            SELECT 
                symbol, timestamp, bid_price_1, ask_price_1,
                bid_qty_1, ask_qty_1, bid_qty_2, ask_qty_2,
                bid_qty_3, ask_qty_3, bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5
            FROM bitda_depth 
            WHERE symbol = 'BTCUSDT' AND bid_price_1 IS NOT NULL
            ORDER BY created_at DESC 
            LIMIT 1
        """)
        
        bitda_result = cursor.fetchone()
        if not bitda_result:
            print("❌ 未找到Bitda数据")
            return
        
        symbol, bitda_ts, bid_price, ask_price = bitda_result[:4]
        bid_qtys = [float(q) if q else 0 for q in bitda_result[4:9]]
        ask_qtys = [float(q) if q else 0 for q in bitda_result[9:14]]
        
        # 计算买五卖五总量
        bitda_bid5_total = sum(bid_qtys)
        bitda_ask5_total = sum(ask_qtys)
        bitda_spread = float(ask_price - bid_price)
        
        print(f"   {symbol}: 时间戳{bitda_ts}")
        print(f"   买一价{bid_price} 卖一价{ask_price} 价差{bitda_spread:.4f}")
        print(f"   买一量{bid_qtys[0]} 卖一量{ask_qtys[0]}")
        print(f"   买五总量{bitda_bid5_total:.4f} 卖五总量{bitda_ask5_total:.4f}")
        
        # 2. 查找匹配的Binance数据
        print("\n🎯 查找匹配的Binance数据...")
        window_ms = 5 * 60 * 1000  # 5分钟窗口
        
        cursor.execute("""
            SELECT 
                event_time, bid_price_1, ask_price_1,
                bid_qty_1, ask_qty_1, bid_qty_2, ask_qty_2,
                bid_qty_3, ask_qty_3, bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5,
                ABS(event_time - %s) as time_diff
            FROM binance_depth_5 
            WHERE symbol = %s 
            AND event_time BETWEEN %s AND %s
            AND bid_price_1 IS NOT NULL
            ORDER BY time_diff
            LIMIT 1
        """, (bitda_ts, symbol, bitda_ts - window_ms, bitda_ts + window_ms))
        
        binance_result = cursor.fetchone()
        if not binance_result:
            print("❌ 未找到匹配的Binance数据")
            return
        
        bn_ts, bn_bid_price, bn_ask_price = binance_result[:3]
        bn_bid_qtys = [float(q) if q else 0 for q in binance_result[3:8]]
        bn_ask_qtys = [float(q) if q else 0 for q in binance_result[8:13]]
        time_diff = binance_result[13]
        
        # 计算Binance买五卖五总量
        bn_bid5_total = sum(bn_bid_qtys)
        bn_ask5_total = sum(bn_ask_qtys)
        bn_spread = float(bn_ask_price - bn_bid_price)
        
        print(f"   匹配成功! 时间差{time_diff}ms")
        print(f"   买一价{bn_bid_price} 卖一价{bn_ask_price} 价差{bn_spread:.4f}")
        print(f"   买一量{bn_bid_qtys[0]} 卖一量{bn_ask_qtys[0]}")
        print(f"   买五总量{bn_bid5_total:.4f} 卖五总量{bn_ask5_total:.4f}")
        
        # 3. 计算深度对比
        print("\n📊 深度对比分析...")
        
        def safe_ratio(a, b):
            return a / b if b > 0 else 0
        
        bid1_ratio = safe_ratio(bid_qtys[0], bn_bid_qtys[0])
        ask1_ratio = safe_ratio(ask_qtys[0], bn_ask_qtys[0])
        bid_ask1_ratio = safe_ratio(bid_qtys[0] + ask_qtys[0], bn_bid_qtys[0] + bn_ask_qtys[0])
        bid_ask2_ratio = safe_ratio(bid_qtys[0] + bid_qtys[1] + ask_qtys[0] + ask_qtys[1], 
                                   bn_bid_qtys[0] + bn_bid_qtys[1] + bn_ask_qtys[0] + bn_ask_qtys[1])
        bid_ask5_ratio = safe_ratio(bitda_bid5_total + bitda_ask5_total, bn_bid5_total + bn_ask5_total)
        
        print("   深度比值表格 (Bitda/Binance):")
        print("   ┌─────────┬─────────┬─────────┬─────────┐")
        print("   │   项目  │  Bitda  │ Binance │  深度比 │")
        print("   ├─────────┼─────────┼─────────┼─────────┤")
        print(f"   │  买一   │{bid_qtys[0]:8.4f} │{bn_bid_qtys[0]:8.4f} │{bid1_ratio:8.2f} │")
        print(f"   │  卖一   │{ask_qtys[0]:8.4f} │{bn_ask_qtys[0]:8.4f} │{ask1_ratio:8.2f} │")
        print(f"   │ 买一卖一│{bid_qtys[0]+ask_qtys[0]:8.4f} │{bn_bid_qtys[0]+bn_ask_qtys[0]:8.4f} │{bid_ask1_ratio:8.2f} │")
        print(f"   │ 买二卖二│{sum(bid_qtys[:2])+sum(ask_qtys[:2]):8.4f} │{sum(bn_bid_qtys[:2])+sum(bn_ask_qtys[:2]):8.4f} │{bid_ask2_ratio:8.2f} │")
        print(f"   │ 买五卖五│{bitda_bid5_total+bitda_ask5_total:8.4f} │{bn_bid5_total+bn_ask5_total:8.4f} │{bid_ask5_ratio:8.2f} │")
        print("   └─────────┴─────────┴─────────┴─────────┘")
        
        # 4. 价差对比分析
        print("\n💰 价差对比分析...")
        
        # 获取Binance价差统计
        cursor.execute("""
            SELECT bid_price_1, ask_price_1, bid_price_5, ask_price_5
            FROM binance_depth_5 
            WHERE symbol = %s AND event_time BETWEEN %s AND %s
            AND bid_price_1 IS NOT NULL
            ORDER BY event_time DESC
            LIMIT 100
        """, (symbol, bitda_ts - window_ms, bitda_ts))
        
        bn_spread_data = cursor.fetchall()
        if bn_spread_data:
            spreads = [float(row[1] - row[0]) for row in bn_spread_data]
            bid5_ask5_spreads = [float(row[3] - row[2]) for row in bn_spread_data if row[2] and row[3]]
            
            spreads.sort()
            n = len(spreads)
            
            bn_spread_latest = spreads[0] if spreads else 0
            bn_spread_max = max(spreads) if spreads else 0
            bn_spread_min = min(spreads) if spreads else 0
            bn_spread_avg = sum(spreads) / n if spreads else 0
            bn_spread_median = spreads[n//2] if spreads else 0
            
            bid5_ask5_max = max(bid5_ask5_spreads) if bid5_ask5_spreads else 0
            bid5_ask5_min = min(bid5_ask5_spreads) if bid5_ask5_spreads else 0
            
            print("   价差对比表格:")
            print("   ┌─────────────────┬─────────┬─────────┐")
            print("   │      项目       │  Bitda  │ Binance │")
            print("   ├─────────────────┼─────────┼─────────┤")
            print(f"   │ 买一卖一最近价差│{bitda_spread:8.4f} │{bn_spread_latest:8.4f} │")
            print(f"   │ 买一卖一最大价差│    -    │{bn_spread_max:8.4f} │")
            print(f"   │ 买一卖一最小价差│    -    │{bn_spread_min:8.4f} │")
            print(f"   │ 买一卖一平均价差│    -    │{bn_spread_avg:8.4f} │")
            print(f"   │ 买一卖一价差中位数│   -    │{bn_spread_median:8.4f} │")
            print(f"   │ 买五卖五最大价差│    -    │{bid5_ask5_max:8.4f} │")
            print(f"   │ 买五卖五最小价差│    -    │{bid5_ask5_min:8.4f} │")
            print("   └─────────────────┴─────────┴─────────┘")
            
            spread_diff = bitda_spread - bn_spread_latest
            print(f"\n   💡 价差差值: {spread_diff:.4f} (Bitda - Binance最近)")
            print(f"   📊 样本数量: {len(spreads)}条")
        
        cursor.close()
        connection.close()
        
        print("\n✅ 快速测试完成!")
        print("📋 逻辑验证:")
        print("   ✅ 深度对比: 根据Bitda时间戳匹配最近的Binance数据")
        print("   ✅ 深度比值: 计算各档位的数量比值")
        print("   ✅ 价差对比: 统计Binance时间窗口内的价差分布")
        print("   ✅ 时间匹配: 在5分钟窗口内找到最接近的数据")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    quick_test()
