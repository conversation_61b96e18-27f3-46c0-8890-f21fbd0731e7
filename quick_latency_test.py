#!/usr/bin/env python3
"""
快速延时测试 - 用少量数据验证逻辑
"""

import mysql.connector
import json
from datetime import datetime, timedelta

def quick_test():
    """快速测试延时匹配逻辑"""
    print("🧪 快速延时匹配测试")
    print("=" * 40)
    
    # 数据库配置
    source_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'Linuxtest',
        'database': 'depth_db'
    }
    
    target_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'Linuxtest',
        'database': 'ethusdt_latency_db'
    }
    
    try:
        # 连接数据库
        source_conn = mysql.connector.connect(**source_config)
        target_conn = mysql.connector.connect(**target_config)
        
        source_cursor = source_conn.cursor()
        target_cursor = target_conn.cursor()
        
        print("✅ 数据库连接成功")
        
        # 获取最近的一条Bitda数据
        print("\n📊 获取最新Bitda数据...")
        bitda_query = """
        SELECT timestamp, asks, bids, created_at
        FROM bitda_depth
        WHERE symbol = 'ETHUSDT'
        AND asks IS NOT NULL
        AND bids IS NOT NULL
        ORDER BY created_at DESC
        LIMIT 1
        """
        
        source_cursor.execute(bitda_query)
        bitda_record = source_cursor.fetchone()
        
        if not bitda_record:
            print("❌ 未找到Bitda数据")
            return
        
        bitda_timestamp, asks_json, bids_json, created_at = bitda_record
        print(f"📈 Bitda数据时间: {created_at}")
        
        # 解析深度数据
        try:
            asks = json.loads(asks_json)
            bids = json.loads(bids_json)
            
            # 买一价格 = bids中的最高价格
            bid_price_1 = max(float(bid[0]) for bid in bids)
            
            # 卖一价格 = asks中的最低价格  
            ask_price_1 = min(float(ask[0]) for ask in asks)
            
            print(f"💰 Bitda买一: {bid_price_1}, 卖一: {ask_price_1}")
            
        except Exception as e:
            print(f"❌ 解析Bitda数据失败: {e}")
            return
        
        # 查找匹配的Binance数据（简化查询）
        print(f"\n🔍 查找匹配的Binance数据...")
        print(f"   查找条件: 买一={bid_price_1}, 卖一={ask_price_1}")
        
        # 先检查是否有这样的价格组合
        check_query = """
        SELECT COUNT(*) FROM binance_bookticker
        WHERE symbol = 'ETHUSDT'
        AND bid_price = %s
        AND ask_price = %s
        """
        
        source_cursor.execute(check_query, (bid_price_1, ask_price_1))
        count = source_cursor.fetchone()[0]
        print(f"📊 找到 {count} 条匹配的价格组合")
        
        if count == 0:
            print("⚠️  无匹配的价格组合")
            
            # 查找最接近的价格
            print("\n🔍 查找最接近的价格...")
            close_query = """
            SELECT bid_price, ask_price, event_time, 
                   ABS(bid_price - %s) + ABS(ask_price - %s) as price_diff
            FROM binance_bookticker
            WHERE symbol = 'ETHUSDT'
            AND event_time < %s
            ORDER BY price_diff ASC
            LIMIT 5
            """
            
            source_cursor.execute(close_query, (bid_price_1, ask_price_1, bitda_timestamp))
            close_matches = source_cursor.fetchall()
            
            print("📋 最接近的价格:")
            for i, (bid, ask, event_time, diff) in enumerate(close_matches, 1):
                print(f"  {i}. 买一:{bid}, 卖一:{ask}, 差值:{diff:.4f}")
            
        else:
            # 查找首次出现时间
            match_query = """
            SELECT bid_price, ask_price, event_time
            FROM binance_bookticker
            WHERE symbol = 'ETHUSDT'
            AND bid_price = %s
            AND ask_price = %s
            AND event_time < %s
            ORDER BY event_time ASC
            LIMIT 1
            """
            
            source_cursor.execute(match_query, (bid_price_1, ask_price_1, bitda_timestamp))
            match = source_cursor.fetchone()
            
            if match:
                binance_bid, binance_ask, binance_timestamp = match
                latency_ms = bitda_timestamp - binance_timestamp
                
                print(f"✅ 找到完全匹配!")
                print(f"   Binance时间: {datetime.fromtimestamp(binance_timestamp/1000)}")
                print(f"   Bitda时间: {datetime.fromtimestamp(bitda_timestamp/1000)}")
                print(f"   延时: {latency_ms} ms")
                
                if 10 <= latency_ms <= 2000:
                    print(f"✅ 延时在有效范围内 (10-2000ms)")
                    
                    # 插入到延时数据库
                    insert_query = """
                    INSERT INTO ethusdt_latency_matches 
                    (bitda_timestamp, binance_timestamp, latency_ms, match_type, bitda_price, binance_price)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    """
                    
                    target_cursor.execute(insert_query, (
                        bitda_timestamp,
                        binance_timestamp,
                        latency_ms,
                        'complete',
                        float(bid_price_1),
                        float(binance_bid)
                    ))
                    
                    target_conn.commit()
                    print("✅ 数据已保存到延时数据库")
                    
                else:
                    print(f"⚠️  延时超出有效范围: {latency_ms}ms")
            else:
                print("❌ 未找到时间范围内的匹配")
        
        # 检查延时数据库中的记录
        print(f"\n📊 延时数据库统计:")
        target_cursor.execute("SELECT COUNT(*) FROM ethusdt_latency_matches")
        total_matches = target_cursor.fetchone()[0]
        print(f"   总匹配数: {total_matches}")
        
        if total_matches > 0:
            target_cursor.execute("""
                SELECT latency_ms, created_at FROM ethusdt_latency_matches
                ORDER BY created_at DESC LIMIT 3
            """)
            recent = target_cursor.fetchall()
            print("   最近3条记录:")
            for latency, created_at in recent:
                print(f"     - {latency}ms ({created_at})")
        
        source_cursor.close()
        target_cursor.close()
        source_conn.close()
        target_conn.close()
        
        print(f"\n🎉 测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    quick_test()
