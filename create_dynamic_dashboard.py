#!/usr/bin/env python3
"""
创建真正动态的Grafana仪表板
使用数据源查询而不是静态HTML
"""

import requests
import json
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DynamicDashboardCreator:
    """动态仪表板创建器"""
    
    def __init__(self):
        self.grafana_url = "http://localhost:3000"
        self.admin_user = "admin"
        self.admin_pass = "admin"
        self.session = requests.Session()
        self.session.auth = (self.admin_user, self.admin_pass)
    
    def create_mysql_datasource(self):
        """创建MySQL数据源"""
        print("📊 创建MySQL数据源...")
        
        datasource_config = {
            "name": "DepthDB",
            "type": "mysql",
            "url": "localhost:3306",
            "access": "proxy",
            "database": "depth_db",
            "user": "root",
            "password": "Linuxtest",
            "basicAuth": False,
            "isDefault": True,
            "jsonData": {
                "maxOpenConns": 0,
                "maxIdleConns": 2,
                "connMaxLifetime": 14400
            }
        }
        
        try:
            # 检查数据源是否已存在
            response = self.session.get(f"{self.grafana_url}/api/datasources/name/DepthDB")
            if response.status_code == 200:
                print("   ✅ MySQL数据源已存在")
                return True
            
            # 创建新数据源
            response = self.session.post(
                f"{self.grafana_url}/api/datasources",
                json=datasource_config,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                print("   ✅ MySQL数据源创建成功")
                return True
            else:
                print(f"   ❌ 创建数据源失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return False
                
        except Exception as e:
            print(f"   ❌ 创建数据源异常: {e}")
            return False
    
    def create_dynamic_dashboard(self):
        """创建真正动态的仪表板"""
        print("🎨 创建动态仪表板...")
        
        current_time = datetime.now()
        
        dashboard_config = {
            "dashboard": {
                "id": None,
                "title": f"🔄 动态深度对比分析 - {current_time.strftime('%H:%M:%S')}",
                "tags": ["dynamic", "depth", "real-time"],
                "timezone": "browser",
                "panels": [
                    # 标题面板
                    {
                        "id": 1,
                        "title": "",
                        "type": "text",
                        "gridPos": {"h": 3, "w": 24, "x": 0, "y": 0},
                        "targets": [],
                        "options": {
                            "mode": "html",
                            "content": f"""
                            <div style="
                                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                                color: white;
                                height: 100%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                border-radius: 8px;
                                font-family: Arial, sans-serif;
                            ">
                                <div style="text-align: center;">
                                    <h1 style="margin: 0; font-size: 24px;">🔄 动态深度对比分析 - 真正自动更新</h1>
                                    <p style="margin: 8px 0 0 0; font-size: 14px; opacity: 0.9;">
                                        📊 使用数据库查询 | ⏰ 每30秒自动刷新 | 🔄 真正动态数据
                                    </p>
                                </div>
                            </div>
                            """
                        }
                    },
                    
                    # BTCUSDT最新深度对比 - 使用表格面板
                    {
                        "id": 2,
                        "title": "📊 BTCUSDT 最新深度对比",
                        "type": "table",
                        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 3},
                        "targets": [
                            {
                                "datasource": {"type": "mysql", "uid": "DepthDB"},
                                "format": "table",
                                "rawSql": """
                                SELECT 
                                    '买一量' as 项目,
                                    ROUND(b.bid_qty_1, 2) as Bitda,
                                    ROUND(bn.bid_qty_1, 2) as Binance,
                                    ROUND(b.bid_qty_1 / bn.bid_qty_1, 2) as 深度比
                                FROM bitda_depth b
                                JOIN (
                                    SELECT timestamp as bt FROM bitda_depth 
                                    WHERE symbol = 'BTCUSDT' AND bid_price_1 IS NOT NULL 
                                    ORDER BY timestamp DESC LIMIT 1
                                ) latest ON b.timestamp = latest.bt
                                JOIN binance_depth_5 bn ON bn.symbol = 'BTCUSDT' 
                                    AND bn.event_time <= b.timestamp
                                    AND bn.bid_price_1 IS NOT NULL
                                WHERE b.symbol = 'BTCUSDT'
                                ORDER BY bn.event_time DESC
                                LIMIT 1
                                """,
                                "refId": "A"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "custom": {
                                    "align": "center",
                                    "displayMode": "basic"
                                }
                            }
                        }
                    },
                    
                    # ETHUSDT最新深度对比
                    {
                        "id": 3,
                        "title": "📊 ETHUSDT 最新深度对比", 
                        "type": "table",
                        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 3},
                        "targets": [
                            {
                                "datasource": {"type": "mysql", "uid": "DepthDB"},
                                "format": "table",
                                "rawSql": """
                                SELECT 
                                    '买一量' as 项目,
                                    ROUND(b.bid_qty_1, 2) as Bitda,
                                    ROUND(bn.bid_qty_1, 2) as Binance,
                                    ROUND(b.bid_qty_1 / bn.bid_qty_1, 2) as 深度比
                                FROM bitda_depth b
                                JOIN (
                                    SELECT timestamp as bt FROM bitda_depth 
                                    WHERE symbol = 'ETHUSDT' AND bid_price_1 IS NOT NULL 
                                    ORDER BY timestamp DESC LIMIT 1
                                ) latest ON b.timestamp = latest.bt
                                JOIN binance_depth_5 bn ON bn.symbol = 'ETHUSDT' 
                                    AND bn.event_time <= b.timestamp
                                    AND bn.bid_price_1 IS NOT NULL
                                WHERE b.symbol = 'ETHUSDT'
                                ORDER BY bn.event_time DESC
                                LIMIT 1
                                """,
                                "refId": "A"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "custom": {
                                    "align": "center",
                                    "displayMode": "basic"
                                }
                            }
                        }
                    },
                    
                    # 数据更新时间显示
                    {
                        "id": 4,
                        "title": "⏰ 数据更新时间",
                        "type": "stat",
                        "gridPos": {"h": 4, "w": 24, "x": 0, "y": 11},
                        "targets": [
                            {
                                "datasource": {"type": "mysql", "uid": "DepthDB"},
                                "format": "table",
                                "rawSql": """
                                SELECT 
                                    FROM_UNIXTIME(MAX(timestamp)/1000) as 最新数据时间
                                FROM bitda_depth 
                                WHERE symbol IN ('BTCUSDT', 'ETHUSDT')
                                """,
                                "refId": "A"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "custom": {
                                    "displayMode": "basic"
                                },
                                "color": {
                                    "mode": "thresholds"
                                },
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None}
                                    ]
                                }
                            }
                        },
                        "options": {
                            "reduceOptions": {
                                "values": False,
                                "calcs": ["lastNotNull"],
                                "fields": ""
                            },
                            "orientation": "auto",
                            "textMode": "auto",
                            "colorMode": "background",
                            "graphMode": "none",
                            "justifyMode": "center"
                        }
                    }
                ],
                "time": {"from": "now-1h", "to": "now"},
                "timepicker": {},
                "templating": {"list": []},
                "annotations": {"list": []},
                "refresh": "30s",  # 30秒自动刷新
                "schemaVersion": 30,
                "version": 1,
                "links": []
            },
            "overwrite": True
        }
        
        try:
            response = self.session.post(
                f"{self.grafana_url}/api/dashboards/db",
                json=dashboard_config,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                dashboard_url = f"{self.grafana_url}{result['url']}"
                print(f"   ✅ 动态仪表板创建成功")
                print(f"   🌐 访问地址: {dashboard_url}")
                return dashboard_url
            else:
                print(f"   ❌ 创建仪表板失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return None
                
        except Exception as e:
            print(f"   ❌ 创建仪表板异常: {e}")
            return None

def main():
    """主函数"""
    print("🔄 创建真正动态的Grafana仪表板")
    print("=" * 60)
    print("🎯 解决方案:")
    print("   1. 创建MySQL数据源")
    print("   2. 使用SQL查询获取实时数据")
    print("   3. 配置30秒自动刷新")
    print("   4. 真正的动态数据显示")
    print()
    
    creator = DynamicDashboardCreator()
    
    # 1. 创建数据源
    if not creator.create_mysql_datasource():
        print("❌ 数据源创建失败，无法继续")
        return
    
    # 2. 创建动态仪表板
    dashboard_url = creator.create_dynamic_dashboard()
    
    if dashboard_url:
        print(f"\n🎉 动态仪表板创建成功！")
        print(f"🌐 访问地址: {dashboard_url}")
        print(f"⏰ 自动刷新: 每30秒")
        print(f"📊 数据源: 直接查询MySQL数据库")
        print(f"🔄 真正动态: 数据会自动更新")
        
        print(f"\n💡 验证步骤:")
        print(f"   1. 打开仪表板页面")
        print(f"   2. 观察数据更新时间")
        print(f"   3. 等待30秒看数值是否变化")
        print(f"   4. 检查页面是否自动刷新")
    else:
        print(f"\n❌ 动态仪表板创建失败")

if __name__ == "__main__":
    main()
