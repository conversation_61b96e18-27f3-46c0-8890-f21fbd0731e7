#!/usr/bin/env python3
import mysql.connector

try:
    print("连接数据库...")
    conn = mysql.connector.connect(
        host='localhost',
        user='root',
        password='Linuxtest',
        database='depth_db',
        connection_timeout=5
    )
    
    cursor = conn.cursor()
    print("查询数据...")
    
    cursor.execute("SELECT COUNT(*) FROM bitda_depth WHERE symbol = 'BTCUSDT'")
    count = cursor.fetchone()[0]
    print(f"BTCUSDT数据量: {count}")
    
    cursor.close()
    conn.close()
    print("测试成功!")
    
except Exception as e:
    print(f"错误: {e}")
