#!/usr/bin/env python3
"""
创建ETHUSDT延时分析专用Grafana仪表板
连接到新的延时分析数据库
"""

import requests
import json
from datetime import datetime

class ETHUSDTGrafanaDashboard:
    """ETHUSDT Grafana仪表板创建器"""
    
    def __init__(self):
        self.grafana_url = "http://localhost:3000"
        self.admin_user = "admin"
        self.admin_pass = "admin"
        self.session = requests.Session()
        self.session.auth = (self.admin_user, self.admin_pass)
        
    def create_mysql_datasource(self):
        """创建延时分析数据库的MySQL数据源"""
        print("📊 创建延时分析数据库MySQL数据源...")
        
        # 删除旧的数据源
        try:
            response = self.session.get(f"{self.grafana_url}/api/datasources")
            if response.status_code == 200:
                datasources = response.json()
                for ds in datasources:
                    if ds.get('name') == 'ETHUSDT_Latency_MySQL':
                        self.session.delete(f"{self.grafana_url}/api/datasources/{ds['id']}")
                        print("  🗑️  删除旧数据源")
        except:
            pass
        
        # 创建新的MySQL数据源
        datasource_config = {
            "name": "ETHUSDT_Latency_MySQL",
            "type": "mysql",
            "access": "proxy",
            "url": "localhost:3306",
            "database": "ethusdt_latency_db",
            "user": "root",
            "secureJsonData": {
                "password": "Linuxtest"
            },
            "jsonData": {
                "maxOpenConns": 100,
                "maxIdleConns": 100,
                "connMaxLifetime": 14400
            },
            "isDefault": False
        }
        
        try:
            response = self.session.post(
                f"{self.grafana_url}/api/datasources",
                json=datasource_config,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 延时分析MySQL数据源创建成功")
                return result.get('datasource', {}).get('uid')
            else:
                print(f"❌ 数据源创建失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 数据源创建异常: {e}")
            return None
    
    def create_latency_dashboard(self, mysql_uid):
        """创建ETHUSDT延时分析仪表板"""
        print("📋 创建ETHUSDT延时分析仪表板...")
        
        dashboard_config = {
            "dashboard": {
                "id": None,
                "title": f"⚡ ETHUSDT延时分析 (方案B) - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                "tags": ["ethusdt", "latency", "方案B", "延时分析", "真实数据"],
                "timezone": "browser",
                "panels": [
                    # 实时延时值
                    {
                        "id": 1,
                        "title": "📊 当前延时 (实时)",
                        "type": "stat",
                        "gridPos": {"h": 6, "w": 6, "x": 0, "y": 0},
                        "targets": [
                            {
                                "datasource": {"type": "mysql", "uid": mysql_uid},
                                "refId": "A",
                                "rawSql": """
                                SELECT 
                                    UNIX_TIMESTAMP(updated_at) as time_sec,
                                    current_latency_ms as value
                                FROM ethusdt_realtime_status 
                                WHERE id = 1
                                """,
                                "format": "time_series"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None},
                                        {"color": "yellow", "value": 100},
                                        {"color": "orange", "value": 200},
                                        {"color": "red", "value": 500}
                                    ]
                                },
                                "unit": "ms",
                                "decimals": 0
                            }
                        },
                        "options": {
                            "colorMode": "background",
                            "graphMode": "area",
                            "justifyMode": "center",
                            "orientation": "auto",
                            "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": False},
                            "textMode": "auto"
                        }
                    },
                    # 1小时平均延时
                    {
                        "id": 2,
                        "title": "📈 平均延时 (1小时)",
                        "type": "stat",
                        "gridPos": {"h": 6, "w": 6, "x": 6, "y": 0},
                        "targets": [
                            {
                                "datasource": {"type": "mysql", "uid": mysql_uid},
                                "refId": "A",
                                "rawSql": """
                                SELECT 
                                    UNIX_TIMESTAMP(updated_at) as time_sec,
                                    avg_latency_1h as value
                                FROM ethusdt_realtime_status 
                                WHERE id = 1
                                """,
                                "format": "time_series"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None},
                                        {"color": "yellow", "value": 100},
                                        {"color": "red", "value": 200}
                                    ]
                                },
                                "unit": "ms",
                                "decimals": 2
                            }
                        },
                        "options": {
                            "colorMode": "value",
                            "graphMode": "none",
                            "justifyMode": "center",
                            "orientation": "auto",
                            "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": False},
                            "textMode": "auto"
                        }
                    },
                    # 1小时最大延时
                    {
                        "id": 3,
                        "title": "🔺 最大延时 (1小时)",
                        "type": "stat",
                        "gridPos": {"h": 6, "w": 6, "x": 12, "y": 0},
                        "targets": [
                            {
                                "datasource": {"type": "mysql", "uid": mysql_uid},
                                "refId": "A",
                                "rawSql": """
                                SELECT 
                                    UNIX_TIMESTAMP(updated_at) as time_sec,
                                    max_latency_1h as value
                                FROM ethusdt_realtime_status 
                                WHERE id = 1
                                """,
                                "format": "time_series"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None},
                                        {"color": "yellow", "value": 500},
                                        {"color": "red", "value": 1000}
                                    ]
                                },
                                "unit": "ms",
                                "decimals": 0
                            }
                        },
                        "options": {
                            "colorMode": "value",
                            "graphMode": "none",
                            "justifyMode": "center",
                            "orientation": "auto",
                            "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": False},
                            "textMode": "auto"
                        }
                    },
                    # 系统状态信息
                    {
                        "id": 4,
                        "title": "📊 系统状态",
                        "type": "text",
                        "gridPos": {"h": 6, "w": 6, "x": 18, "y": 0},
                        "options": {
                            "content": """
## ⚡ ETHUSDT延时分析系统

**架构**: 方案B (新数据库)  
**数据源**: ethusdt_latency_db  
**更新频率**: 实时  

### 📊 数据表
- **实时匹配**: ethusdt_latency_matches  
- **分钟统计**: ethusdt_latency_stats_minute  
- **小时统计**: ethusdt_latency_stats_hour  
- **实时状态**: ethusdt_realtime_status  

### 🎯 匹配逻辑
- **价格匹配**: 零误差  
- **延时范围**: 10-2000ms  
- **数据源**: Bitda vs Binance  
            """,
                            "mode": "markdown"
                        }
                    },
                    # 延时曲线图
                    {
                        "id": 5,
                        "title": "⚡ ETHUSDT延时曲线图 (实时数据)",
                        "type": "timeseries",
                        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 6},
                        "targets": [
                            {
                                "datasource": {"type": "mysql", "uid": mysql_uid},
                                "refId": "A",
                                "rawSql": """
                                SELECT 
                                    UNIX_TIMESTAMP(created_at) as time_sec,
                                    latency_ms as "延时(ms)"
                                FROM ethusdt_latency_matches 
                                WHERE created_at >= $__timeFrom()
                                AND created_at <= $__timeTo()
                                ORDER BY created_at
                                """,
                                "format": "time_series"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "palette-classic"},
                                "custom": {
                                    "axisLabel": "延时 (毫秒)",
                                    "axisPlacement": "auto",
                                    "drawStyle": "line",
                                    "fillOpacity": 10,
                                    "lineWidth": 1,
                                    "pointSize": 2,
                                    "showPoints": "never",
                                    "spanNulls": False
                                },
                                "mappings": [],
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None},
                                        {"color": "yellow", "value": 100},
                                        {"color": "orange", "value": 200},
                                        {"color": "red", "value": 500}
                                    ]
                                },
                                "unit": "ms"
                            }
                        },
                        "options": {
                            "legend": {"calcs": ["mean", "max", "min"], "displayMode": "table", "placement": "bottom"},
                            "tooltip": {"mode": "multi", "sort": "none"}
                        }
                    },
                    # 分钟级统计趋势
                    {
                        "id": 6,
                        "title": "📈 分钟级延时统计趋势",
                        "type": "timeseries",
                        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 14},
                        "targets": [
                            {
                                "datasource": {"type": "mysql", "uid": mysql_uid},
                                "refId": "A",
                                "rawSql": """
                                SELECT 
                                    UNIX_TIMESTAMP(minute_timestamp) as time_sec,
                                    avg_latency as "平均延时(ms)"
                                FROM ethusdt_latency_stats_minute 
                                WHERE minute_timestamp >= $__timeFrom()
                                AND minute_timestamp <= $__timeTo()
                                ORDER BY minute_timestamp
                                """,
                                "format": "time_series"
                            },
                            {
                                "datasource": {"type": "mysql", "uid": mysql_uid},
                                "refId": "B",
                                "rawSql": """
                                SELECT 
                                    UNIX_TIMESTAMP(minute_timestamp) as time_sec,
                                    max_latency as "最大延时(ms)"
                                FROM ethusdt_latency_stats_minute 
                                WHERE minute_timestamp >= $__timeFrom()
                                AND minute_timestamp <= $__timeTo()
                                ORDER BY minute_timestamp
                                """,
                                "format": "time_series"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "palette-classic"},
                                "custom": {
                                    "axisLabel": "延时 (毫秒)",
                                    "axisPlacement": "auto",
                                    "drawStyle": "line",
                                    "fillOpacity": 20,
                                    "lineWidth": 2,
                                    "pointSize": 3,
                                    "showPoints": "never"
                                },
                                "unit": "ms"
                            }
                        },
                        "options": {
                            "legend": {"calcs": ["mean", "max"], "displayMode": "list", "placement": "bottom"},
                            "tooltip": {"mode": "multi", "sort": "none"}
                        }
                    },
                    # 匹配数量统计
                    {
                        "id": 7,
                        "title": "📊 匹配数量统计",
                        "type": "timeseries",
                        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 14},
                        "targets": [
                            {
                                "datasource": {"type": "mysql", "uid": mysql_uid},
                                "refId": "A",
                                "rawSql": """
                                SELECT 
                                    UNIX_TIMESTAMP(minute_timestamp) as time_sec,
                                    total_matches as "总匹配数"
                                FROM ethusdt_latency_stats_minute 
                                WHERE minute_timestamp >= $__timeFrom()
                                AND minute_timestamp <= $__timeTo()
                                ORDER BY minute_timestamp
                                """,
                                "format": "time_series"
                            },
                            {
                                "datasource": {"type": "mysql", "uid": mysql_uid},
                                "refId": "B",
                                "rawSql": """
                                SELECT 
                                    UNIX_TIMESTAMP(minute_timestamp) as time_sec,
                                    bid_matches as "买一匹配"
                                FROM ethusdt_latency_stats_minute 
                                WHERE minute_timestamp >= $__timeFrom()
                                AND minute_timestamp <= $__timeTo()
                                ORDER BY minute_timestamp
                                """,
                                "format": "time_series"
                            },
                            {
                                "datasource": {"type": "mysql", "uid": mysql_uid},
                                "refId": "C",
                                "rawSql": """
                                SELECT 
                                    UNIX_TIMESTAMP(minute_timestamp) as time_sec,
                                    ask_matches as "卖一匹配"
                                FROM ethusdt_latency_stats_minute 
                                WHERE minute_timestamp >= $__timeFrom()
                                AND minute_timestamp <= $__timeTo()
                                ORDER BY minute_timestamp
                                """,
                                "format": "time_series"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "palette-classic"},
                                "custom": {
                                    "axisLabel": "匹配数量",
                                    "axisPlacement": "auto",
                                    "drawStyle": "line",
                                    "fillOpacity": 20,
                                    "lineWidth": 2,
                                    "pointSize": 3,
                                    "showPoints": "never"
                                },
                                "unit": "short"
                            }
                        },
                        "options": {
                            "legend": {"calcs": ["mean", "max"], "displayMode": "list", "placement": "bottom"},
                            "tooltip": {"mode": "multi", "sort": "none"}
                        }
                    }
                ],
                "time": {"from": "now-1h", "to": "now"},
                "timepicker": {},
                "templating": {"list": []},
                "annotations": {"list": []},
                "refresh": "30s",
                "schemaVersion": 30,
                "version": 1,
                "links": []
            },
            "overwrite": True
        }
        
        try:
            response = self.session.post(
                f"{self.grafana_url}/api/dashboards/db",
                json=dashboard_config,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                dashboard_url = f"{self.grafana_url}{result.get('url', '')}"
                print(f"✅ ETHUSDT延时仪表板创建成功")
                print(f"🌐 访问地址: {dashboard_url}")
                return dashboard_url
            else:
                print(f"❌ 仪表板创建失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ 仪表板创建异常: {e}")
        
        return None
    
    def setup_complete_dashboard(self):
        """完整设置ETHUSDT延时仪表板"""
        print("⚡ 开始创建ETHUSDT延时分析仪表板 (方案B)...")
        print("=" * 60)
        
        # 创建MySQL数据源
        mysql_uid = self.create_mysql_datasource()
        if not mysql_uid:
            print("❌ MySQL数据源创建失败")
            return False
        
        # 创建仪表板
        dashboard_url = self.create_latency_dashboard(mysql_uid)
        if not dashboard_url:
            print("❌ 仪表板创建失败")
            return False
        
        print("\n" + "=" * 60)
        print("🎉 ETHUSDT延时分析仪表板创建完成！")
        print(f"🌐 仪表板地址: {dashboard_url}")
        print("👤 登录信息: admin/admin")
        print("🔄 数据每30秒自动刷新")
        print("📊 显示真实的ETHUSDT延时数据")
        print("⚡ 包含实时值、平均值、最大值和延时曲线")
        print("📈 基于方案B的新延时分析数据库")
        print("=" * 60)
        
        return True

def main():
    """主函数"""
    dashboard = ETHUSDTGrafanaDashboard()
    
    if dashboard.setup_complete_dashboard():
        print("\n✅ ETHUSDT延时仪表板配置成功！")
        print("📊 现在显示的是来自新数据库的真实延时数据")
        print("⚡ 包含实时值、平均值、最大值和延时曲线图")
        print("🔄 数据每30秒自动刷新")
        print("📈 基于方案B架构，数据同步稳定")
        
        # 自动打开浏览器
        try:
            import webbrowser
            webbrowser.open("http://localhost:3000")
            print("🌐 浏览器已自动打开")
        except:
            print("🌐 请手动访问: http://localhost:3000")
    else:
        print("\n❌ 配置失败，请检查服务状态")

if __name__ == "__main__":
    main()
