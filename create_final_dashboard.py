#!/usr/bin/env python3
"""
创建最终的深度对比仪表板 - 使用简单可靠的SQL
"""

import requests
import json
from datetime import datetime

def create_final_depth_dashboard():
    """创建最终的深度对比仪表板"""
    print("🎨 创建最终深度对比仪表板...")
    
    grafana_url = "http://localhost:3000"
    session = requests.Session()
    session.auth = ("admin", "admin")
    
    datasource_uid = "benieev71k4cgc"  # DepthDB_Dynamic
    
    dashboard_config = {
        "dashboard": {
            "id": None,
            "title": "🔄 深度对比分析 - 最终版",
            "tags": ["depth", "final", "working"],
            "timezone": "browser",
            "panels": [
                # BTCUSDT深度对比
                {
                    "id": 1,
                    "title": "📊 BTCUSDT 深度对比",
                    "type": "table",
                    "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
                    "targets": [
                        {
                            "datasource": {
                                "type": "mysql",
                                "uid": datasource_uid
                            },
                            "format": "table",
                            "rawSql": """
                                SELECT 
                                    '买一量' as 项目,
                                    ROUND(
                                        (SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 
                                        2
                                    ) as Bitda,
                                    ROUND(
                                        (SELECT bid_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 
                                        2
                                    ) as Binance,
                                    ROUND(
                                        (SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) /
                                        (SELECT bid_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1),
                                        2
                                    ) as 深度比
                                
                                UNION ALL
                                
                                SELECT 
                                    '卖一量' as 项目,
                                    ROUND(
                                        (SELECT ask_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 
                                        2
                                    ) as Bitda,
                                    ROUND(
                                        (SELECT ask_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 
                                        2
                                    ) as Binance,
                                    ROUND(
                                        (SELECT ask_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) /
                                        (SELECT ask_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1),
                                        2
                                    ) as 深度比
                                
                                UNION ALL
                                
                                SELECT 
                                    '买一量+卖一量' as 项目,
                                    ROUND(
                                        (SELECT bid_qty_1 + ask_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 
                                        2
                                    ) as Bitda,
                                    ROUND(
                                        (SELECT bid_qty_1 + ask_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 
                                        2
                                    ) as Binance,
                                    ROUND(
                                        (SELECT bid_qty_1 + ask_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) /
                                        (SELECT bid_qty_1 + ask_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1),
                                        2
                                    ) as 深度比
                            """,
                            "refId": "A"
                        }
                    ]
                },
                
                # ETHUSDT深度对比
                {
                    "id": 2,
                    "title": "📊 ETHUSDT 深度对比",
                    "type": "table",
                    "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
                    "targets": [
                        {
                            "datasource": {
                                "type": "mysql",
                                "uid": datasource_uid
                            },
                            "format": "table",
                            "rawSql": """
                                SELECT 
                                    '买一量' as 项目,
                                    ROUND(
                                        (SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1), 
                                        2
                                    ) as Bitda,
                                    ROUND(
                                        (SELECT bid_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 
                                        2
                                    ) as Binance,
                                    ROUND(
                                        (SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) /
                                        (SELECT bid_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1),
                                        2
                                    ) as 深度比
                                
                                UNION ALL
                                
                                SELECT 
                                    '卖一量' as 项目,
                                    ROUND(
                                        (SELECT ask_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1), 
                                        2
                                    ) as Bitda,
                                    ROUND(
                                        (SELECT ask_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 
                                        2
                                    ) as Binance,
                                    ROUND(
                                        (SELECT ask_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) /
                                        (SELECT ask_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1),
                                        2
                                    ) as 深度比
                                
                                UNION ALL
                                
                                SELECT 
                                    '买一量+卖一量' as 项目,
                                    ROUND(
                                        (SELECT bid_qty_1 + ask_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1), 
                                        2
                                    ) as Bitda,
                                    ROUND(
                                        (SELECT bid_qty_1 + ask_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 
                                        2
                                    ) as Binance,
                                    ROUND(
                                        (SELECT bid_qty_1 + ask_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) /
                                        (SELECT bid_qty_1 + ask_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1),
                                        2
                                    ) as 深度比
                            """,
                            "refId": "A"
                        }
                    ]
                },
                
                # 数据更新时间
                {
                    "id": 3,
                    "title": "⏰ 数据更新时间",
                    "type": "stat",
                    "gridPos": {"h": 4, "w": 12, "x": 0, "y": 8},
                    "targets": [
                        {
                            "datasource": {
                                "type": "mysql",
                                "uid": datasource_uid
                            },
                            "format": "table",
                            "rawSql": """
                                SELECT FROM_UNIXTIME(MAX(timestamp)/1000) as 最新数据时间
                                FROM bitda_depth 
                                WHERE symbol IN ('BTCUSDT', 'ETHUSDT')
                            """,
                            "refId": "A"
                        }
                    ],
                    "options": {
                        "reduceOptions": {
                            "values": False,
                            "calcs": ["lastNotNull"]
                        },
                        "textMode": "auto",
                        "colorMode": "background"
                    }
                },
                
                # 刷新状态
                {
                    "id": 4,
                    "title": "🔄 刷新状态",
                    "type": "stat",
                    "gridPos": {"h": 4, "w": 12, "x": 12, "y": 8},
                    "targets": [
                        {
                            "datasource": {
                                "type": "mysql",
                                "uid": datasource_uid
                            },
                            "format": "table",
                            "rawSql": "SELECT '每10秒自动刷新' as 状态",
                            "refId": "A"
                        }
                    ],
                    "options": {
                        "reduceOptions": {
                            "values": False,
                            "calcs": ["lastNotNull"]
                        },
                        "textMode": "auto",
                        "colorMode": "background"
                    }
                }
            ],
            "time": {"from": "now-1h", "to": "now"},
            "refresh": "10s",  # 10秒自动刷新
            "schemaVersion": 30,
            "version": 1
        },
        "overwrite": True
    }
    
    try:
        response = session.post(
            f"{grafana_url}/api/dashboards/db",
            json=dashboard_config,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            dashboard_url = f"{grafana_url}{result['url']}"
            print(f"   ✅ 最终仪表板创建成功")
            print(f"   🌐 访问地址: {dashboard_url}")
            return dashboard_url
        else:
            print(f"   ❌ 创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return None

def main():
    """主函数"""
    print("🎨 创建最终深度对比仪表板")
    print("=" * 50)
    
    dashboard_url = create_final_depth_dashboard()
    
    if dashboard_url:
        print(f"\n🎉 最终仪表板创建成功！")
        print(f"🌐 访问地址: {dashboard_url}")
        print(f"⏰ 10秒自动刷新")
        print(f"📊 显示BTCUSDT和ETHUSDT深度对比")
        print(f"🔄 真正的动态数据更新")
        
        # 打开浏览器
        try:
            import webbrowser
            webbrowser.open(dashboard_url)
            print(f"🌐 已自动打开浏览器")
        except:
            print(f"💡 请手动打开浏览器访问")
    else:
        print(f"\n❌ 创建失败")

if __name__ == "__main__":
    main()
