#!/usr/bin/env python3
"""
创建优化的仪表板
使用更高效的查询避免超时
"""

import requests
import json

def create_optimized_dashboard():
    """创建优化的仪表板"""
    print("🎨 创建优化的仪表板...")
    
    session = requests.Session()
    session.auth = ("admin", "admin")
    datasource_uid = "cenigejcatslce"  # WorkingDepthDB
    
    dashboard_config = {
        "dashboard": {
            "id": None,
            "title": "🚀 优化版仪表板 - 1分钟刷新",
            "tags": ["optimized", "fast"],
            "timezone": "browser",
            "panels": [
                # 1. BTCUSDT买一量 - 最简单查询
                {
                    "id": 1,
                    "title": "📊 BTCUSDT 买一量",
                    "type": "stat",
                    "gridPos": {"h": 6, "w": 6, "x": 0, "y": 0},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": "SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1",
                        "refId": "A"
                    }],
                    "options": {
                        "reduceOptions": {"values": False, "calcs": ["lastNotNull"]},
                        "textMode": "auto", 
                        "colorMode": "background"
                    }
                },
                
                # 2. BTCUSDT卖一量
                {
                    "id": 2,
                    "title": "📊 BTCUSDT 卖一量",
                    "type": "stat",
                    "gridPos": {"h": 6, "w": 6, "x": 6, "y": 0},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": "SELECT ask_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1",
                        "refId": "A"
                    }],
                    "options": {
                        "reduceOptions": {"values": False, "calcs": ["lastNotNull"]},
                        "textMode": "auto", 
                        "colorMode": "background"
                    }
                },
                
                # 3. ETHUSDT买一量
                {
                    "id": 3,
                    "title": "📊 ETHUSDT 买一量",
                    "type": "stat",
                    "gridPos": {"h": 6, "w": 6, "x": 12, "y": 0},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": "SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY id DESC LIMIT 1",
                        "refId": "A"
                    }],
                    "options": {
                        "reduceOptions": {"values": False, "calcs": ["lastNotNull"]},
                        "textMode": "auto", 
                        "colorMode": "background"
                    }
                },
                
                # 4. ETHUSDT卖一量
                {
                    "id": 4,
                    "title": "📊 ETHUSDT 卖一量",
                    "type": "stat",
                    "gridPos": {"h": 6, "w": 6, "x": 18, "y": 0},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": "SELECT ask_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY id DESC LIMIT 1",
                        "refId": "A"
                    }],
                    "options": {
                        "reduceOptions": {"values": False, "calcs": ["lastNotNull"]},
                        "textMode": "auto", 
                        "colorMode": "background"
                    }
                },
                
                # 5. 简单深度对比表格
                {
                    "id": 5,
                    "title": "📊 简单深度对比",
                    "type": "table",
                    "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": """
                            SELECT 
                                symbol as 交易对,
                                ROUND(bid_qty_1, 2) as 买一量,
                                ROUND(ask_qty_1, 2) as 卖一量,
                                ROUND(bid_qty_1 + ask_qty_1, 2) as 总量
                            FROM bitda_depth 
                            WHERE symbol IN ('BTCUSDT', 'ETHUSDT')
                            ORDER BY id DESC 
                            LIMIT 2
                        """,
                        "refId": "A"
                    }]
                },
                
                # 6. 数据更新时间
                {
                    "id": 6,
                    "title": "⏰ 数据更新时间",
                    "type": "stat",
                    "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": "SELECT DATE_FORMAT(FROM_UNIXTIME(timestamp/1000), '%m-%d %H:%i:%s') as value FROM bitda_depth ORDER BY id DESC LIMIT 1",
                        "refId": "A"
                    }],
                    "options": {
                        "reduceOptions": {"values": False, "calcs": ["lastNotNull"]},
                        "textMode": "auto", 
                        "colorMode": "background"
                    }
                }
            ],
            "time": {"from": "now-1h", "to": "now"},
            "refresh": "1m",  # 1分钟刷新
            "schemaVersion": 30,
            "version": 1
        },
        "overwrite": True
    }
    
    try:
        print("   📡 发送请求到Grafana...")
        response = session.post(
            "http://localhost:3000/api/dashboards/db",
            json=dashboard_config,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"   📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            dashboard_url = f"http://localhost:3000{result['url']}"
            print(f"   ✅ 优化仪表板创建成功")
            print(f"   🌐 地址: {dashboard_url}")
            return dashboard_url
        else:
            print(f"   ❌ 创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return None

def test_optimized_query():
    """测试优化的查询"""
    print("🔍 测试优化的查询...")
    
    session = requests.Session()
    session.auth = ("admin", "admin")
    
    query_data = {
        "queries": [{
            "datasource": {
                "type": "mysql",
                "uid": "cenigejcatslce"
            },
            "rawSql": "SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1",
            "refId": "A",
            "format": "table"
        }]
    }
    
    try:
        response = session.post(
            "http://localhost:3000/api/ds/query",
            json=query_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"   📊 查询状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 优化查询成功: {result}")
            return True
        else:
            print(f"   ❌ 优化查询失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 查询异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 创建优化的仪表板")
    print("=" * 50)
    print("🔧 优化策略:")
    print("   🚀 使用ORDER BY id DESC代替timestamp")
    print("   📊 简化查询，减少复杂度")
    print("   ⏰ 1分钟刷新率")
    print("   🎯 专注核心数据显示")
    print()
    
    # 先测试优化查询
    if test_optimized_query():
        print("✅ 优化查询测试成功，继续创建仪表板")
        
        dashboard_url = create_optimized_dashboard()
        
        if dashboard_url:
            print(f"\n🎉 优化仪表板创建成功！")
            print(f"🌐 访问地址: {dashboard_url}")
            print(f"⏰ 1分钟自动刷新")
            print(f"🚀 使用优化查询，应该不会超时")
            
            # 打开浏览器
            try:
                import webbrowser
                webbrowser.open(dashboard_url)
                print(f"🌐 已自动打开浏览器")
            except:
                pass
        else:
            print(f"\n❌ 创建失败")
    else:
        print("❌ 优化查询测试失败，无法创建仪表板")

if __name__ == "__main__":
    main()
