#!/bin/bash

# Grafana和Prometheus启动脚本
# 用于快速启动数据可视化服务

echo "📊 Grafana和Prometheus启动工具"
echo "================================"

# 检查系统类型
detect_system() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    else
        echo "unknown"
    fi
}

# 检查服务是否运行
check_service() {
    local service_name=$1
    local port=$2
    
    if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
        echo "✅ $service_name 正在运行 (端口 $port)"
        return 0
    else
        echo "❌ $service_name 未运行 (端口 $port)"
        return 1
    fi
}

# 启动Grafana
start_grafana() {
    echo "🚀 启动Grafana..."
    
    local system=$(detect_system)
    
    case $system in
        "linux")
            # Linux系统
            if command -v systemctl &> /dev/null; then
                # 使用systemd
                sudo systemctl start grafana-server
                sudo systemctl enable grafana-server
                echo "✅ Grafana已通过systemd启动"
            elif command -v service &> /dev/null; then
                # 使用service
                sudo service grafana-server start
                echo "✅ Grafana已通过service启动"
            else
                echo "❌ 无法启动Grafana，请手动启动"
                return 1
            fi
            ;;
        "macos")
            # macOS系统
            if command -v brew &> /dev/null; then
                brew services start grafana
                echo "✅ Grafana已通过Homebrew启动"
            else
                echo "❌ 请先安装Homebrew和Grafana"
                return 1
            fi
            ;;
        *)
            echo "❌ 不支持的操作系统"
            return 1
            ;;
    esac
    
    # 等待服务启动
    echo "⏳ 等待Grafana启动..."
    sleep 5
    
    if check_service "Grafana" 3000; then
        echo "🌐 Grafana访问地址: http://localhost:3000"
        echo "👤 默认用户名/密码: admin/admin"
        return 0
    else
        echo "❌ Grafana启动失败"
        return 1
    fi
}

# 启动Prometheus
start_prometheus() {
    echo "🚀 启动Prometheus..."
    
    local system=$(detect_system)
    
    case $system in
        "linux")
            if command -v systemctl &> /dev/null; then
                sudo systemctl start prometheus
                sudo systemctl enable prometheus
                echo "✅ Prometheus已通过systemd启动"
            elif command -v service &> /dev/null; then
                sudo service prometheus start
                echo "✅ Prometheus已通过service启动"
            else
                echo "❌ 无法启动Prometheus，请手动启动"
                return 1
            fi
            ;;
        "macos")
            if command -v brew &> /dev/null; then
                brew services start prometheus
                echo "✅ Prometheus已通过Homebrew启动"
            else
                echo "❌ 请先安装Homebrew和Prometheus"
                return 1
            fi
            ;;
        *)
            echo "❌ 不支持的操作系统"
            return 1
            ;;
    esac
    
    # 等待服务启动
    echo "⏳ 等待Prometheus启动..."
    sleep 3
    
    if check_service "Prometheus" 9090; then
        echo "🌐 Prometheus访问地址: http://localhost:9090"
        return 0
    else
        echo "❌ Prometheus启动失败"
        return 1
    fi
}

# 启动数据导出器
start_exporter() {
    echo "🚀 启动数据导出器..."
    
    # 检查是否已经在运行
    if pgrep -f "prometheus_exporter.py" > /dev/null; then
        echo "✅ 数据导出器已在运行"
        return 0
    fi
    
    # 检查文件是否存在
    if [[ ! -f "prometheus_exporter.py" ]]; then
        echo "❌ 找不到prometheus_exporter.py文件"
        return 1
    fi
    
    # 启动导出器
    nohup python prometheus_exporter.py > prometheus_exporter.log 2>&1 &
    
    # 等待启动
    sleep 3
    
    if check_service "数据导出器" 8000; then
        echo "✅ 数据导出器启动成功"
        echo "📊 指标访问地址: http://localhost:8000/metrics"
        return 0
    else
        echo "❌ 数据导出器启动失败"
        return 1
    fi
}

# 生成仪表板配置
generate_dashboard() {
    echo "📊 生成Grafana仪表板配置..."
    
    if [[ -f "grafana_config.py" ]]; then
        python grafana_config.py
        echo "✅ 仪表板配置生成完成"
    else
        echo "❌ 找不到grafana_config.py文件"
        return 1
    fi
}

# 检查所有服务状态
check_all_services() {
    echo "🔍 检查服务状态..."
    echo "===================="
    
    check_service "Grafana" 3000
    check_service "Prometheus" 9090
    check_service "数据导出器" 8000
    
    echo ""
    echo "📊 数据采集进程:"
    ps aux | grep -E "(main\.py|ws_data_collector\.py)" | grep -v grep | while read line; do
        echo "  ✅ $line"
    done
}

# 停止所有服务
stop_all_services() {
    echo "⏹️  停止所有服务..."
    
    local system=$(detect_system)
    
    # 停止Grafana
    case $system in
        "linux")
            if command -v systemctl &> /dev/null; then
                sudo systemctl stop grafana-server
            elif command -v service &> /dev/null; then
                sudo service grafana-server stop
            fi
            ;;
        "macos")
            if command -v brew &> /dev/null; then
                brew services stop grafana
            fi
            ;;
    esac
    
    # 停止Prometheus
    case $system in
        "linux")
            if command -v systemctl &> /dev/null; then
                sudo systemctl stop prometheus
            elif command -v service &> /dev/null; then
                sudo service prometheus stop
            fi
            ;;
        "macos")
            if command -v brew &> /dev/null; then
                brew services stop prometheus
            fi
            ;;
    esac
    
    # 停止数据导出器
    pkill -f "prometheus_exporter.py"
    
    echo "✅ 所有服务已停止"
}

# 显示菜单
show_menu() {
    echo ""
    echo "请选择操作:"
    echo "1) 启动Grafana"
    echo "2) 启动Prometheus"
    echo "3) 启动数据导出器"
    echo "4) 生成仪表板配置"
    echo "5) 启动所有服务"
    echo "6) 检查服务状态"
    echo "7) 停止所有服务"
    echo "8) 打开浏览器"
    echo "9) 退出"
    echo ""
    read -p "请输入选项 (1-9): " choice
}

# 打开浏览器
open_browser() {
    echo "🌐 打开浏览器..."
    
    local system=$(detect_system)
    
    case $system in
        "linux")
            if command -v xdg-open &> /dev/null; then
                xdg-open http://localhost:3000 &
                xdg-open http://localhost:9090 &
            fi
            ;;
        "macos")
            open http://localhost:3000 &
            open http://localhost:9090 &
            ;;
    esac
    
    echo "✅ 浏览器已打开"
    echo "📊 Grafana: http://localhost:3000"
    echo "📈 Prometheus: http://localhost:9090"
}

# 主循环
while true; do
    show_menu
    
    case $choice in
        1)
            start_grafana
            ;;
        2)
            start_prometheus
            ;;
        3)
            start_exporter
            ;;
        4)
            generate_dashboard
            ;;
        5)
            start_prometheus
            start_grafana
            start_exporter
            ;;
        6)
            check_all_services
            ;;
        7)
            stop_all_services
            ;;
        8)
            open_browser
            ;;
        9)
            echo "👋 退出程序"
            exit 0
            ;;
        *)
            echo "❌ 无效选项，请重新选择"
            ;;
    esac
    
    echo ""
    read -p "按回车键继续..."
done
