{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "eeoj5w5v7wzr4d"}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [{"options": {"Ask Qty 1": {"text": "卖一量"}, "Bid Qty 1": {"text": "买一量"}, "Bid+Ask Qty 1": {"text": "买一量卖一量"}, "Bid+Ask Qty 1-2": {"text": "买卖前两档量"}, "Bid+Ask Qty 1-5": {"text": "买卖前五档量"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "item"}, "properties": [{"id": "displayName", "value": "项目"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "ratio"}, "properties": [{"id": "displayName", "value": "深度比"}]}]}, "gridPos": {"h": 12, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "12.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "eeoj5w5v7wzr4d"}, "format": "table", "rawSql": "WITH bitda AS (SELECT toFloat64(ask_qty_1) as ask_qty_1, to<PERSON><PERSON><PERSON>(bid_qty_1) as bid_qty_1, toFloat<PERSON>(bid_qty_2) as bid_qty_2, toFloat64(ask_qty_2) as ask_qty_2, toFloat<PERSON>(bid_qty_3) as bid_qty_3, toFloat<PERSON>(ask_qty_3) as ask_qty_3, toFloat<PERSON>(bid_qty_4) as bid_qty_4, toFloat64(ask_qty_4) as ask_qty_4, toFloat64(bid_qty_5) as bid_qty_5, toFloat64(ask_qty_5) as ask_qty_5 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), binance AS (SELECT toFloat64(ask_qty) as ask_qty, toFloat64(bid_qty) as bid_qty FROM crypto.binance_bookticker WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1) SELECT arrayJoin([('Ask Qty 1', round(bitda.ask_qty_1, 2), round(binance.ask_qty, 2), round(bitda.ask_qty_1 / binance.ask_qty, 2)), ('Bid Qty 1', round(bitda.bid_qty_1, 2), round(binance.bid_qty, 2), round(bitda.bid_qty_1 / binance.bid_qty, 2)), ('Bid+Ask Qty 1', round(bitda.bid_qty_1 + bitda.ask_qty_1, 2), round(binance.bid_qty + binance.ask_qty, 2), round((bitda.bid_qty_1 + bitda.ask_qty_1) / (binance.bid_qty + binance.ask_qty), 2)), ('Bid+Ask Qty 1-2', round(bitda.bid_qty_1 + bitda.bid_qty_2 + bitda.ask_qty_1 + bitda.ask_qty_2, 2), round(binance.bid_qty + binance.ask_qty, 2), round((bitda.bid_qty_1 + bitda.bid_qty_2 + bitda.ask_qty_1 + bitda.ask_qty_2) / (binance.bid_qty + binance.ask_qty), 2)), ('Bid+Ask Qty 1-5', round(bitda.bid_qty_1 + bitda.bid_qty_2 + bitda.bid_qty_3 + bitda.bid_qty_4 + bitda.bid_qty_5 + bitda.ask_qty_1 + bitda.ask_qty_2 + bitda.ask_qty_3 + bitda.ask_qty_4 + bitda.ask_qty_5, 2), round(binance.bid_qty + binance.ask_qty, 2), round((bitda.bid_qty_1 + bitda.bid_qty_2 + bitda.bid_qty_3 + bitda.bid_qty_4 + bitda.bid_qty_5 + bitda.ask_qty_1 + bitda.ask_qty_2 + bitda.ask_qty_3 + bitda.ask_qty_4 + bitda.ask_qty_5) / (binance.bid_qty + binance.ask_qty), 2))]) AS data, data.1 AS item, data.2 AS Bitda, data.3 AS Binance, data.4 AS ratio FROM bitda, binance", "refId": "A"}], "title": "📊 BTCUSDT 深度对比", "type": "table"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "eeoj5w5v7wzr4d"}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [{"options": {"Ask Qty 1": {"text": "卖一量"}, "Bid Qty 1": {"text": "买一量"}, "Bid+Ask Qty 1": {"text": "买一量卖一量"}, "Bid+Ask Qty 1-2": {"text": "买卖前两档量"}, "Bid+Ask Qty 1-5": {"text": "买卖前五档量"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "item"}, "properties": [{"id": "displayName", "value": "项目"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "ratio"}, "properties": [{"id": "displayName", "value": "深度比"}]}]}, "gridPos": {"h": 12, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "12.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "eeoj5w5v7wzr4d"}, "format": "table", "rawSql": "WITH bitda AS (SELECT toFloat64(ask_qty_1) as ask_qty_1, to<PERSON><PERSON><PERSON>(bid_qty_1) as bid_qty_1, toFloat<PERSON>(bid_qty_2) as bid_qty_2, toFloat64(ask_qty_2) as ask_qty_2, toFloat<PERSON>(bid_qty_3) as bid_qty_3, toFloat<PERSON>(ask_qty_3) as ask_qty_3, toFloat<PERSON>(bid_qty_4) as bid_qty_4, toFloat64(ask_qty_4) as ask_qty_4, toFloat64(bid_qty_5) as bid_qty_5, toFloat64(ask_qty_5) as ask_qty_5 FROM crypto.bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1), binance AS (SELECT toFloat64(ask_qty) as ask_qty, toFloat64(bid_qty) as bid_qty FROM crypto.binance_bookticker WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1) SELECT arrayJoin([('Ask Qty 1', round(bitda.ask_qty_1, 2), round(binance.ask_qty, 2), round(bitda.ask_qty_1 / binance.ask_qty, 2)), ('Bid Qty 1', round(bitda.bid_qty_1, 2), round(binance.bid_qty, 2), round(bitda.bid_qty_1 / binance.bid_qty, 2)), ('Bid+Ask Qty 1', round(bitda.bid_qty_1 + bitda.ask_qty_1, 2), round(binance.bid_qty + binance.ask_qty, 2), round((bitda.bid_qty_1 + bitda.ask_qty_1) / (binance.bid_qty + binance.ask_qty), 2)), ('Bid+Ask Qty 1-2', round(bitda.bid_qty_1 + bitda.bid_qty_2 + bitda.ask_qty_1 + bitda.ask_qty_2, 2), round(binance.bid_qty + binance.ask_qty, 2), round((bitda.bid_qty_1 + bitda.bid_qty_2 + bitda.ask_qty_1 + bitda.ask_qty_2) / (binance.bid_qty + binance.ask_qty), 2)), ('Bid+Ask Qty 1-5', round(bitda.bid_qty_1 + bitda.bid_qty_2 + bitda.bid_qty_3 + bitda.bid_qty_4 + bitda.bid_qty_5 + bitda.ask_qty_1 + bitda.ask_qty_2 + bitda.ask_qty_3 + bitda.ask_qty_4 + bitda.ask_qty_5, 2), round(binance.bid_qty + binance.ask_qty, 2), round((bitda.bid_qty_1 + bitda.bid_qty_2 + bitda.bid_qty_3 + bitda.bid_qty_4 + bitda.bid_qty_5 + bitda.ask_qty_1 + bitda.ask_qty_2 + bitda.ask_qty_3 + bitda.ask_qty_4 + bitda.ask_qty_5) / (binance.bid_qty + binance.ask_qty), 2))]) AS data, data.1 AS item, data.2 AS Bitda, data.3 AS Binance, data.4 AS ratio FROM bitda, binance", "refId": "A"}], "title": "📊 ETHUSDT 深度对比", "type": "table"}], "preload": false, "refresh": "1m", "schemaVersion": 41, "tags": ["depth-comparison", "clickhouse", "crypto"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "browser", "title": "ClickHouse深度对比仪表板", "uid": "clickhouse-depth-comparison", "version": 1, "weekStart": "monday"}