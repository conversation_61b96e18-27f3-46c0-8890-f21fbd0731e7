{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "eeoj5w5v7wzr4d"}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 12, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "12.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "eeoj5w5v7wzr4d"}, "format": "table", "rawSql": "WITH bitda_latest AS (\n  SELECT bid_qty_1, ask_qty_1, bid_qty_2, ask_qty_2, bid_qty_3, ask_qty_3, bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5\n  FROM crypto.bitda_depth \n  WHERE symbol = 'BTCUSDT' \n  ORDER BY timestamp DESC \n  LIMIT 1\n),\nbinance_latest AS (\n  SELECT bid_qty, ask_qty\n  FROM crypto.binance_bookticker \n  WHERE symbol = 'BTCUSDT' \n  ORDER BY event_time DESC \n  LIMIT 1\n)\nSELECT \n  'Ask Qty 1' as item,\n  round(b.ask_qty_1, 2) as Bitda,\n  round(bn.ask_qty, 2) as Binance,\n  round(b.ask_qty_1 / bn.ask_qty, 2) as ratio\nFROM bitda_latest b, binance_latest bn\nUNION ALL\nSELECT \n  'Bid Qty 1' as item,\n  round(b.bid_qty_1, 2) as Bitda,\n  round(bn.bid_qty, 2) as Binance,\n  round(b.bid_qty_1 / bn.bid_qty, 2) as ratio\nFROM bitda_latest b, binance_latest bn\nUNION ALL\nSELECT \n  'Bid+Ask Qty 1' as item,\n  round(b.bid_qty_1 + b.ask_qty_1, 2) as Bitda,\n  round(bn.bid_qty + bn.ask_qty, 2) as Binance,\n  round((b.bid_qty_1 + b.ask_qty_1) / (bn.bid_qty + bn.ask_qty), 2) as ratio\nFROM bitda_latest b, binance_latest bn\nUNION ALL\nSELECT \n  'Bid+Ask Qty 1-2' as item,\n  round(b.bid_qty_1 + b.bid_qty_2 + b.ask_qty_1 + b.ask_qty_2, 2) as Bitda,\n  round(bn.bid_qty + bn.ask_qty, 2) as Binance,\n  round((b.bid_qty_1 + b.bid_qty_2 + b.ask_qty_1 + b.ask_qty_2) / (bn.bid_qty + bn.ask_qty), 2) as ratio\nFROM bitda_latest b, binance_latest bn\nUNION ALL\nSELECT \n  'Bid+Ask Qty 1-5' as item,\n  round(b.bid_qty_1 + b.bid_qty_2 + b.bid_qty_3 + b.bid_qty_4 + b.bid_qty_5 + b.ask_qty_1 + b.ask_qty_2 + b.ask_qty_3 + b.ask_qty_4 + b.ask_qty_5, 2) as Bitda,\n  round(bn.bid_qty + bn.ask_qty, 2) as Binance,\n  round((b.bid_qty_1 + b.bid_qty_2 + b.bid_qty_3 + b.bid_qty_4 + b.bid_qty_5 + b.ask_qty_1 + b.ask_qty_2 + b.ask_qty_3 + b.ask_qty_4 + b.ask_qty_5) / (bn.bid_qty + bn.ask_qty), 2) as ratio\nFROM bitda_latest b, binance_latest bn", "refId": "A"}], "title": "📊 BTCUSDT 深度对比", "type": "table"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "eeoj5w5v7wzr4d"}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 12, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "12.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "eeoj5w5v7wzr4d"}, "format": "table", "rawSql": "WITH bitda_latest AS (\n  SELECT bid_qty_1, ask_qty_1, bid_qty_2, ask_qty_2, bid_qty_3, ask_qty_3, bid_qty_4, ask_qty_4, bid_qty_5, ask_qty_5\n  FROM crypto.bitda_depth \n  WHERE symbol = 'ETHUSDT' \n  ORDER BY timestamp DESC \n  LIMIT 1\n),\nbinance_latest AS (\n  SELECT bid_qty, ask_qty\n  FROM crypto.binance_bookticker \n  WHERE symbol = 'ETHUSDT' \n  ORDER BY event_time DESC \n  LIMIT 1\n)\nSELECT \n  'Ask Qty 1' as item,\n  round(b.ask_qty_1, 2) as Bitda,\n  round(bn.ask_qty, 2) as Binance,\n  round(b.ask_qty_1 / bn.ask_qty, 2) as ratio\nFROM bitda_latest b, binance_latest bn\nUNION ALL\nSELECT \n  'Bid Qty 1' as item,\n  round(b.bid_qty_1, 2) as Bitda,\n  round(bn.bid_qty, 2) as Binance,\n  round(b.bid_qty_1 / bn.bid_qty, 2) as ratio\nFROM bitda_latest b, binance_latest bn\nUNION ALL\nSELECT \n  'Bid+Ask Qty 1' as item,\n  round(b.bid_qty_1 + b.ask_qty_1, 2) as Bitda,\n  round(bn.bid_qty + bn.ask_qty, 2) as Binance,\n  round((b.bid_qty_1 + b.ask_qty_1) / (bn.bid_qty + bn.ask_qty), 2) as ratio\nFROM bitda_latest b, binance_latest bn\nUNION ALL\nSELECT \n  'Bid+Ask Qty 1-2' as item,\n  round(b.bid_qty_1 + b.bid_qty_2 + b.ask_qty_1 + b.ask_qty_2, 2) as Bitda,\n  round(bn.bid_qty + bn.ask_qty, 2) as Binance,\n  round((b.bid_qty_1 + b.bid_qty_2 + b.ask_qty_1 + b.ask_qty_2) / (bn.bid_qty + bn.ask_qty), 2) as ratio\nFROM bitda_latest b, binance_latest bn\nUNION ALL\nSELECT \n  'Bid+Ask Qty 1-5' as item,\n  round(b.bid_qty_1 + b.bid_qty_2 + b.bid_qty_3 + b.bid_qty_4 + b.bid_qty_5 + b.ask_qty_1 + b.ask_qty_2 + b.ask_qty_3 + b.ask_qty_4 + b.ask_qty_5, 2) as Bitda,\n  round(bn.bid_qty + bn.ask_qty, 2) as Binance,\n  round((b.bid_qty_1 + b.bid_qty_2 + b.bid_qty_3 + b.bid_qty_4 + b.bid_qty_5 + b.ask_qty_1 + b.ask_qty_2 + b.ask_qty_3 + b.ask_qty_4 + b.ask_qty_5) / (bn.bid_qty + bn.ask_qty), 2) as ratio\nFROM bitda_latest b, binance_latest bn", "refId": "A"}], "title": "📊 ETHUSDT 深度对比", "type": "table"}], "preload": false, "refresh": "1m", "schemaVersion": 41, "tags": ["depth-comparison", "clickhouse", "crypto"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "browser", "title": "ClickHouse深度对比仪表板", "uid": "clickhouse-depth-comparison", "version": 1, "weekStart": "monday"}