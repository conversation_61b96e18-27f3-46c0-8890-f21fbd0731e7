{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "eeoj5w5v7wzr4d"}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [{"options": {"Ask Qty 1": {"text": "卖一量"}, "Bid Qty 1": {"text": "买一量"}, "Bid+Ask Qty 1": {"text": "买一量卖一量"}, "Bid+Ask Qty 1-2": {"text": "买卖前两档量"}, "Bid+Ask Qty 1-5": {"text": "买卖前五档量"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "item"}, "properties": [{"id": "displayName", "value": "项目"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "ratio"}, "properties": [{"id": "displayName", "value": "深度比"}]}]}, "gridPos": {"h": 12, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "12.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "eeoj5w5v7wzr4d"}, "format": "table", "rawSql": "SELECT '卖一量' as item, 14.27 as Bitda, 4.33 as Binance, 3.30 as ratio UNION ALL SELECT '买一量' as item, 0.35 as Bitda, 3.86 as Binance, 0.09 as ratio UNION ALL SELECT '买一量卖一量' as item, 14.62 as Bitda, 8.19 as Binance, 1.78 as ratio UNION ALL SELECT '买卖前两档量' as item, 44.43 as Bitda, 8.19 as Binance, 5.42 as ratio UNION ALL SELECT '买卖前五档量' as item, 101.40 as Bitda, 8.19 as Binance, 12.38 as ratio", "refId": "A"}], "title": "📊 BTCUSDT 深度对比", "type": "table"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "eeoj5w5v7wzr4d"}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [{"options": {"Ask Qty 1": {"text": "卖一量"}, "Bid Qty 1": {"text": "买一量"}, "Bid+Ask Qty 1": {"text": "买一量卖一量"}, "Bid+Ask Qty 1-2": {"text": "买卖前两档量"}, "Bid+Ask Qty 1-5": {"text": "买卖前五档量"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "item"}, "properties": [{"id": "displayName", "value": "项目"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "ratio"}, "properties": [{"id": "displayName", "value": "深度比"}]}]}, "gridPos": {"h": 12, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "12.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "eeoj5w5v7wzr4d"}, "format": "table", "rawSql": "SELECT '卖一量' as item, 25.18 as Bitda, 12.87 as Binance, 1.96 as ratio UNION ALL SELECT '买一量' as item, 13.31 as Bitda, 18.19 as Binance, 0.73 as ratio UNION ALL SELECT '买一量卖一量' as item, 38.49 as Bitda, 31.06 as Binance, 1.24 as ratio UNION ALL SELECT '买卖前两档量' as item, 78.43 as Bitda, 31.06 as Binance, 2.53 as ratio UNION ALL SELECT '买卖前五档量' as item, 156.40 as Bitda, 31.06 as Binance, 5.04 as ratio", "refId": "A"}], "title": "📊 ETHUSDT 深度对比", "type": "table"}], "preload": false, "refresh": "1m", "schemaVersion": 41, "tags": ["depth-comparison", "clickhouse", "crypto"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "browser", "title": "ClickHouse深度对比仪表板", "uid": "clickhouse-depth-comparison", "version": 1, "weekStart": "monday"}