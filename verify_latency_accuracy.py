#!/usr/bin/env python3
"""
验证延时计算准确性
详细验证1667毫秒延时记录的计算过程
"""

import mysql.connector
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LatencyAccuracyVerifier:
    """延时计算准确性验证器"""
    
    def __init__(self):
        self.source_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'depth_db'
        }
        
        self.latency_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'ethusdt_latency_db'
        }
    
    def verify_specific_latency_record(self, target_latency_ms=1667):
        """验证特定延时记录的准确性"""
        logger.info(f"🔍 开始验证 {target_latency_ms}ms 延时记录的准确性...")
        logger.info("=" * 80)
        
        try:
            # 连接延时数据库
            latency_conn = mysql.connector.connect(**self.latency_config)
            latency_cursor = latency_conn.cursor()
            
            # 获取目标延时记录
            latency_cursor.execute("""
                SELECT 
                    id,
                    bitda_timestamp,
                    binance_timestamp,
                    latency_ms,
                    bitda_price,
                    binance_price,
                    created_at
                FROM ethusdt_latency_matches 
                WHERE latency_ms = %s
                LIMIT 1
            """, (target_latency_ms,))
            
            record = latency_cursor.fetchone()
            if not record:
                logger.error(f"❌ 未找到延时为 {target_latency_ms}ms 的记录")
                return False
            
            record_id, bitda_ts, binance_ts, latency_ms, bitda_price, binance_price, created_at = record
            
            logger.info("📋 延时记录详情:")
            logger.info(f"   记录ID: {record_id}")
            logger.info(f"   Bitda时间戳: {bitda_ts}")
            logger.info(f"   Binance时间戳: {binance_ts}")
            logger.info(f"   计算延时: {latency_ms}ms")
            logger.info(f"   Bitda价格: {bitda_price}")
            logger.info(f"   Binance价格: {binance_price}")
            logger.info(f"   记录创建时间: {created_at}")
            
            # 步骤1: 验证延时计算
            logger.info("\n🔢 步骤1: 验证延时计算")
            calculated_latency = bitda_ts - binance_ts
            logger.info(f"   手动计算: {bitda_ts} - {binance_ts} = {calculated_latency}ms")
            logger.info(f"   数据库记录: {latency_ms}ms")
            
            if calculated_latency == latency_ms:
                logger.info("   ✅ 延时计算正确")
            else:
                logger.error(f"   ❌ 延时计算错误！差异: {abs(calculated_latency - latency_ms)}ms")
                return False
            
            # 步骤2: 验证时间戳转换
            logger.info("\n⏰ 步骤2: 验证时间戳转换")
            bitda_datetime = datetime.fromtimestamp(bitda_ts / 1000)
            binance_datetime = datetime.fromtimestamp(binance_ts / 1000)
            
            logger.info(f"   Bitda时间: {bitda_datetime.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
            logger.info(f"   Binance时间: {binance_datetime.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
            logger.info(f"   时间差: {(bitda_datetime - binance_datetime).total_seconds() * 1000:.0f}ms")
            
            # 步骤3: 验证源数据存在性
            logger.info("\n📊 步骤3: 验证源数据存在性")
            source_conn = mysql.connector.connect(**self.source_config)
            source_cursor = source_conn.cursor()
            
            # 查找对应的Bitda记录
            source_cursor.execute("""
                SELECT timestamp, bid_price_1, ask_price_1, created_at
                FROM bitda_depth
                WHERE symbol = 'ETHUSDT'
                AND timestamp = %s
                AND (bid_price_1 = %s OR ask_price_1 = %s)
                LIMIT 1
            """, (bitda_ts, bitda_price, bitda_price))
            
            bitda_record = source_cursor.fetchone()
            if bitda_record:
                logger.info("   ✅ 找到对应的Bitda源记录:")
                logger.info(f"      时间戳: {bitda_record[0]}")
                logger.info(f"      买一价: {bitda_record[1]}")
                logger.info(f"      卖一价: {bitda_record[2]}")
                logger.info(f"      创建时间: {bitda_record[3]}")
            else:
                logger.warning("   ⚠️  未找到对应的Bitda源记录")
            
            # 查找对应的Binance记录
            source_cursor.execute("""
                SELECT bid_price, ask_price, event_time, created_at
                FROM binance_bookticker
                WHERE symbol = 'ETHUSDT'
                AND event_time = %s
                AND (bid_price = %s OR ask_price = %s)
                LIMIT 1
            """, (binance_ts, binance_price, binance_price))
            
            binance_record = source_cursor.fetchone()
            if binance_record:
                logger.info("   ✅ 找到对应的Binance源记录:")
                logger.info(f"      买一价: {binance_record[0]}")
                logger.info(f"      卖一价: {binance_record[1]}")
                logger.info(f"      事件时间: {binance_record[2]}")
                logger.info(f"      创建时间: {binance_record[3]}")
            else:
                logger.warning("   ⚠️  未找到对应的Binance源记录")
            
            # 步骤4: 验证价格匹配逻辑
            logger.info("\n💰 步骤4: 验证价格匹配逻辑")
            
            # 查找Binance在该时间戳附近的价格
            source_cursor.execute("""
                SELECT bid_price, ask_price, event_time
                FROM binance_bookticker
                WHERE symbol = 'ETHUSDT'
                AND event_time BETWEEN %s AND %s
                AND (bid_price = %s OR ask_price = %s)
                ORDER BY event_time ASC
                LIMIT 5
            """, (binance_ts - 1000, binance_ts + 1000, bitda_price, bitda_price))
            
            nearby_records = source_cursor.fetchall()
            if nearby_records:
                logger.info(f"   📋 在时间戳附近找到 {len(nearby_records)} 条匹配价格的Binance记录:")
                for i, (bid, ask, event_time) in enumerate(nearby_records):
                    time_diff = event_time - binance_ts
                    logger.info(f"      {i+1}. 买一:{bid}, 卖一:{ask}, 时间:{event_time}, 时差:{time_diff}ms")
            
            # 步骤5: 验证延时合理性
            logger.info("\n⚖️  步骤5: 验证延时合理性")
            logger.info(f"   延时值: {latency_ms}ms")
            
            if 10 <= latency_ms <= 2000:
                logger.info("   ✅ 延时值在合理范围内 (10-2000ms)")
            else:
                logger.warning(f"   ⚠️  延时值超出预期范围 (10-2000ms)")
            
            if latency_ms > 0:
                logger.info("   ✅ 延时为正值，符合Bitda晚于Binance的预期")
            else:
                logger.warning("   ⚠️  延时为负值或零，可能存在问题")
            
            # 步骤6: 验证数据时序
            logger.info("\n📅 步骤6: 验证数据时序")
            time_diff_seconds = (bitda_datetime - binance_datetime).total_seconds()
            logger.info(f"   时间差: {time_diff_seconds:.3f}秒")
            
            if time_diff_seconds > 0:
                logger.info("   ✅ Bitda时间晚于Binance时间，符合预期")
            else:
                logger.warning("   ⚠️  时间顺序异常")
            
            # 关闭连接
            source_cursor.close()
            source_conn.close()
            latency_cursor.close()
            latency_conn.close()
            
            logger.info("\n" + "=" * 80)
            logger.info("📋 验证结果总结:")
            logger.info(f"   ✅ 延时计算: {calculated_latency}ms (正确)")
            logger.info(f"   ✅ 时间转换: {bitda_datetime.strftime('%H:%M:%S.%f')[:-3]} - {binance_datetime.strftime('%H:%M:%S.%f')[:-3]}")
            logger.info(f"   ✅ 延时范围: {latency_ms}ms (合理)")
            logger.info(f"   ✅ 时序关系: Bitda晚于Binance {time_diff_seconds:.3f}秒")
            logger.info("   🎯 结论: 延时计算完全准确！")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 验证过程出错: {e}")
            return False
    
    def verify_multiple_records(self, count=5):
        """验证多个延时记录"""
        logger.info(f"\n🔍 验证最近 {count} 条延时记录...")
        
        try:
            latency_conn = mysql.connector.connect(**self.latency_config)
            latency_cursor = latency_conn.cursor()
            
            latency_cursor.execute("""
                SELECT 
                    bitda_timestamp,
                    binance_timestamp,
                    latency_ms,
                    bitda_price,
                    binance_price
                FROM ethusdt_latency_matches 
                ORDER BY created_at DESC
                LIMIT %s
            """, (count,))
            
            records = latency_cursor.fetchall()
            
            logger.info(f"📊 验证 {len(records)} 条记录:")
            logger.info("   序号 | Bitda时间戳    | Binance时间戳  | 计算延时 | 记录延时 | 状态")
            logger.info("   " + "-" * 70)
            
            all_correct = True
            for i, (bitda_ts, binance_ts, recorded_latency, bitda_price, binance_price) in enumerate(records):
                calculated_latency = bitda_ts - binance_ts
                status = "✅" if calculated_latency == recorded_latency else "❌"
                
                if calculated_latency != recorded_latency:
                    all_correct = False
                
                logger.info(f"   {i+1:2d}   | {bitda_ts} | {binance_ts} | {calculated_latency:4d}ms   | {recorded_latency:4d}ms   | {status}")
            
            latency_cursor.close()
            latency_conn.close()
            
            if all_correct:
                logger.info(f"\n✅ 所有 {len(records)} 条记录的延时计算都正确！")
            else:
                logger.warning(f"\n⚠️  发现延时计算错误的记录")
            
            return all_correct
            
        except Exception as e:
            logger.error(f"❌ 批量验证出错: {e}")
            return False

def main():
    """主函数"""
    print("🔍 延时计算准确性验证工具")
    print("=" * 50)
    print("功能:")
    print("  - 验证特定延时记录的计算准确性")
    print("  - 检查源数据的存在性")
    print("  - 验证价格匹配逻辑")
    print("  - 检查时间戳转换")
    print()
    
    verifier = LatencyAccuracyVerifier()
    
    # 验证1667ms的记录
    success1 = verifier.verify_specific_latency_record(1667)
    
    # 验证多个记录
    success2 = verifier.verify_multiple_records(5)
    
    if success1 and success2:
        print("\n🎉 延时计算验证完成：所有检查都通过！")
        print("✅ 延时计算算法完全准确")
        print("✅ 数据源匹配正确")
        print("✅ 时间戳处理正确")
    else:
        print("\n⚠️  验证发现问题，请检查详细日志")

if __name__ == "__main__":
    main()
