# 加密货币数据收集系统

这是一个专门收集 BTCUSDT 和 ETHUSDT 永续合约数据的系统，支持从 Bitda 和 Binance 交易所收集实时数据。

## 功能特性

- **多数据源收集**: 同时从 Bitda 和 Binance 收集数据
- **实时数据**: WebSocket 连接确保数据实时性
- **数据类型**: K线、成交、深度、行情、BookTicker 等
- **自动清理**: 数据保留3天，自动清理旧数据
- **空间监控**: 磁盘空间低于50GB时自动停止
- **错误恢复**: 自动重连和错误处理机制

## 项目结构

```
project/
├── collector.py      # 数据收集模块
├── storage.py        # 数据存储模块
├── main.py           # 主程序
├── start.py          # 启动脚本
├── test_system.py    # 系统测试
├── utils/            # 通用工具函数
│   ├── config.py     # 配置管理
│   ├── logging.py    # 日志管理
│   └── db.py         # 数据库连接管理
├── requirements.txt  # 依赖包
└── README.md         # 说明文档
```

## 数据库表结构

### Bitda 数据表
- `bitda_kline`: K线数据
- `bitda_trades`: 成交数据
- `bitda_depth`: 深度数据
- `bitda_ticker`: 行情数据

### Binance 数据表
- `binance_depth_5`: 5档深度数据
- `binance_bookticker`: BookTicker数据

## 安装和使用

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置数据库
确保 MySQL 数据库运行正常，并且配置正确（在 `utils/config.py` 中）。

### 3. 运行系统测试
```bash
python test_system.py
```

### 4. 启动数据收集
```bash
python start.py
```

或者直接运行主程序：
```bash
python main.py
```

## 配置说明

主要配置在 `utils/config.py` 中：

- `SYMBOLS`: 收集的交易对（默认 BTCUSDT, ETHUSDT）
- `DATA_RETENTION_DAYS`: 数据保留天数（默认3天）
- `DISK_SPACE_WARNING_GB`: 磁盘空间警告阈值（默认50GB）
- `BATCH_SIZE`: 批量保存数据大小（默认100条）

## 监控和日志

- 日志文件: `crypto_collector.log`
- 日志轮转: 10MB 文件大小，保留5个备份
- 实时监控: 磁盘空间、数据收集状态

## 停止程序

使用 Ctrl+C 或发送 SIGTERM 信号来优雅停止程序。程序会自动保存所有缓存的数据。

## 注意事项

1. 确保网络连接稳定，WebSocket 需要持续连接
2. 定期检查日志文件，监控系统运行状态
3. 数据库需要足够的存储空间
4. 建议在生产环境中使用进程管理器（如 systemd）运行
