#!/usr/bin/env python3
"""
重新创建数据源解决连接问题
"""

import requests
import json

def delete_all_mysql_datasources():
    """删除所有MySQL数据源"""
    print("🗑️ 删除所有MySQL数据源...")
    
    session = requests.Session()
    session.auth = ("admin", "admin")
    
    try:
        response = session.get("http://localhost:3000/api/datasources", timeout=10)
        if response.status_code == 200:
            datasources = response.json()
            
            for ds in datasources:
                if ds['type'] == 'mysql':
                    delete_response = session.delete(f"http://localhost:3000/api/datasources/{ds['id']}", timeout=10)
                    if delete_response.status_code == 200:
                        print(f"   ✅ 删除数据源: {ds['name']}")
                    else:
                        print(f"   ❌ 删除失败: {ds['name']}")
        
    except Exception as e:
        print(f"   ❌ 删除数据源失败: {e}")

def create_fresh_mysql_datasource():
    """创建全新的MySQL数据源"""
    print("📊 创建全新的MySQL数据源...")
    
    session = requests.Session()
    session.auth = ("admin", "admin")
    
    datasource_config = {
        "name": "FreshMysqlDB",
        "type": "mysql",
        "url": "localhost:3306",
        "access": "proxy",
        "database": "depth_db",
        "user": "root",
        "basicAuth": False,
        "isDefault": False,
        "jsonData": {
            "maxOpenConns": 5,  # 减少连接数
            "maxIdleConns": 1,  # 减少空闲连接
            "connMaxLifetime": 3600,  # 1小时
            "timeout": 30  # 30秒超时
        },
        "secureJsonData": {
            "password": "Linuxtest"
        }
    }
    
    try:
        response = session.post(
            "http://localhost:3000/api/datasources",
            json=datasource_config,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            datasource_uid = result['datasource']['uid']
            datasource_id = result['datasource']['id']
            print(f"   ✅ 数据源创建成功")
            print(f"   📊 UID: {datasource_uid}")
            print(f"   📊 ID: {datasource_id}")
            return datasource_uid
        else:
            print(f"   ❌ 创建数据源失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 创建数据源异常: {e}")
        return None

def test_new_datasource(datasource_uid):
    """测试新数据源"""
    print("🔍 测试新数据源...")
    
    session = requests.Session()
    session.auth = ("admin", "admin")
    
    query_data = {
        "queries": [{
            "datasource": {
                "type": "mysql",
                "uid": datasource_uid
            },
            "rawSql": "SELECT 1 as test",
            "refId": "A",
            "format": "table"
        }]
    }
    
    try:
        response = session.post(
            "http://localhost:3000/api/ds/query",
            json=query_data,
            headers={"Content-Type": "application/json"},
            timeout=15
        )
        
        print(f"   📊 查询状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 新数据源测试成功")
            return True
        else:
            print(f"   ❌ 新数据源测试失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试异常: {e}")
        return False

def create_minimal_dashboard(datasource_uid):
    """创建最小化仪表板"""
    print("🎨 创建最小化仪表板...")
    
    session = requests.Session()
    session.auth = ("admin", "admin")
    
    dashboard_config = {
        "dashboard": {
            "id": None,
            "title": "🔧 最小化测试仪表板",
            "tags": ["minimal", "test"],
            "timezone": "browser",
            "panels": [
                {
                    "id": 1,
                    "title": "📊 测试连接",
                    "type": "stat",
                    "gridPos": {"h": 6, "w": 12, "x": 0, "y": 0},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": "SELECT 1 as test",
                        "refId": "A"
                    }],
                    "options": {
                        "reduceOptions": {"values": False, "calcs": ["lastNotNull"]},
                        "textMode": "auto", 
                        "colorMode": "background"
                    }
                },
                {
                    "id": 2,
                    "title": "📊 数据计数",
                    "type": "stat",
                    "gridPos": {"h": 6, "w": 12, "x": 12, "y": 0},
                    "targets": [{
                        "datasource": {"type": "mysql", "uid": datasource_uid},
                        "format": "table",
                        "rawSql": "SELECT COUNT(*) as count FROM bitda_depth LIMIT 1",
                        "refId": "A"
                    }],
                    "options": {
                        "reduceOptions": {"values": False, "calcs": ["lastNotNull"]},
                        "textMode": "auto", 
                        "colorMode": "background"
                    }
                }
            ],
            "time": {"from": "now-1h", "to": "now"},
            "refresh": "1m",
            "schemaVersion": 30,
            "version": 1
        },
        "overwrite": True
    }
    
    try:
        response = session.post(
            "http://localhost:3000/api/dashboards/db",
            json=dashboard_config,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            dashboard_url = f"http://localhost:3000{result['url']}"
            print(f"   ✅ 最小化仪表板创建成功")
            print(f"   🌐 地址: {dashboard_url}")
            return dashboard_url
        else:
            print(f"   ❌ 创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return None

def main():
    """主函数"""
    print("🔧 重新创建数据源解决连接问题")
    print("=" * 50)
    
    # 1. 删除现有数据源
    delete_all_mysql_datasources()
    
    # 2. 创建新数据源
    datasource_uid = create_fresh_mysql_datasource()
    if not datasource_uid:
        print("❌ 数据源创建失败")
        return
    
    # 3. 测试新数据源
    if test_new_datasource(datasource_uid):
        print("✅ 新数据源测试成功")
        
        # 4. 创建最小化仪表板
        dashboard_url = create_minimal_dashboard(datasource_uid)
        if dashboard_url:
            print(f"\n🎉 重新创建成功！")
            print(f"🌐 访问地址: {dashboard_url}")
            print(f"📊 新数据源UID: {datasource_uid}")
            
            # 打开浏览器
            try:
                import webbrowser
                webbrowser.open(dashboard_url)
                print(f"🌐 已自动打开浏览器")
            except:
                pass
        else:
            print(f"\n❌ 仪表板创建失败")
    else:
        print("❌ 新数据源测试失败")

if __name__ == "__main__":
    main()
