#!/usr/bin/env python3
"""
创建完整的6个表格动态仪表板
包含深度对比、深度统计、价差对比
"""

import requests
import json
from datetime import datetime

def create_complete_dashboard():
    """创建包含6个表格的完整仪表板"""
    print("🎨 创建完整的6个表格动态仪表板...")
    
    session = requests.Session()
    session.auth = ("admin", "admin")
    
    # 使用已验证工作的数据源
    datasource_uid = "cenigejcatslce"  # WorkingDepthDB
    
    dashboard_config = {
        "dashboard": {
            "id": None,
            "title": "🔄 完整深度价差分析仪表板",
            "tags": ["complete", "depth", "spread"],
            "timezone": "browser",
            "panels": [
                # 1. BTCUSDT深度对比
                {
                    "id": 1,
                    "title": "📊 BTCUSDT 深度对比",
                    "type": "table",
                    "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
                    "targets": [
                        {
                            "datasource": {"type": "mysql", "uid": datasource_uid},
                            "format": "table",
                            "rawSql": """
                                SELECT 
                                    '买一量' as 项目,
                                    ROUND((SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
                                    ROUND((SELECT bid_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
                                    ROUND((SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                          (SELECT bid_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                                UNION ALL
                                SELECT 
                                    '卖一量' as 项目,
                                    ROUND((SELECT ask_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
                                    ROUND((SELECT ask_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
                                    ROUND((SELECT ask_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                          (SELECT ask_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                                UNION ALL
                                SELECT 
                                    '买一量卖一量' as 项目,
                                    ROUND((SELECT bid_qty_1 + ask_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
                                    ROUND((SELECT bid_qty_1 + ask_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
                                    ROUND((SELECT bid_qty_1 + ask_qty_1 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                          (SELECT bid_qty_1 + ask_qty_1 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                                UNION ALL
                                SELECT 
                                    '买卖前两档量' as 项目,
                                    ROUND((SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
                                    ROUND((SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
                                    ROUND((SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                          (SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                                UNION ALL
                                SELECT 
                                    '买卖前五档量' as 项目,
                                    ROUND((SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
                                    ROUND((SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
                                    ROUND((SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                          (SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                            """,
                            "refId": "A"
                        }
                    ]
                },
                
                # 2. ETHUSDT深度对比
                {
                    "id": 2,
                    "title": "📊 ETHUSDT 深度对比",
                    "type": "table",
                    "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
                    "targets": [
                        {
                            "datasource": {"type": "mysql", "uid": datasource_uid},
                            "format": "table",
                            "rawSql": """
                                SELECT 
                                    '买一量' as 项目,
                                    ROUND((SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
                                    ROUND((SELECT bid_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
                                    ROUND((SELECT bid_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                          (SELECT bid_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                                UNION ALL
                                SELECT 
                                    '卖一量' as 项目,
                                    ROUND((SELECT ask_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
                                    ROUND((SELECT ask_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
                                    ROUND((SELECT ask_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                          (SELECT ask_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                                UNION ALL
                                SELECT 
                                    '买一量卖一量' as 项目,
                                    ROUND((SELECT bid_qty_1 + ask_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
                                    ROUND((SELECT bid_qty_1 + ask_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
                                    ROUND((SELECT bid_qty_1 + ask_qty_1 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                          (SELECT bid_qty_1 + ask_qty_1 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                                UNION ALL
                                SELECT 
                                    '买卖前两档量' as 项目,
                                    ROUND((SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
                                    ROUND((SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
                                    ROUND((SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                          (SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                                UNION ALL
                                SELECT 
                                    '买卖前五档量' as 项目,
                                    ROUND((SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
                                    ROUND((SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
                                    ROUND((SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) / 
                                          (SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
                            """,
                            "refId": "A"
                        }
                    ]
                },

                # 3. BTCUSDT深度统计
                {
                    "id": 3,
                    "title": "📈 BTCUSDT 深度统计",
                    "type": "table",
                    "gridPos": {"h": 6, "w": 12, "x": 0, "y": 8},
                    "targets": [
                        {
                            "datasource": {"type": "mysql", "uid": datasource_uid},
                            "format": "table",
                            "rawSql": """
                                WITH depth_stats AS (
                                    SELECT
                                        (b.bid_qty_1 + b.ask_qty_1) / (bn.bid_qty_1 + bn.ask_qty_1) as bid_ask1_ratio,
                                        (b.bid_qty_1 + b.bid_qty_2 + b.ask_qty_1 + b.ask_qty_2) / (bn.bid_qty_1 + bn.bid_qty_2 + bn.ask_qty_1 + bn.ask_qty_2) as bid_ask2_ratio,
                                        (b.bid_qty_1 + b.bid_qty_2 + b.bid_qty_3 + b.bid_qty_4 + b.bid_qty_5 + b.ask_qty_1 + b.ask_qty_2 + b.ask_qty_3 + b.ask_qty_4 + b.ask_qty_5) /
                                        (bn.bid_qty_1 + bn.bid_qty_2 + bn.bid_qty_3 + bn.bid_qty_4 + bn.bid_qty_5 + bn.ask_qty_1 + bn.ask_qty_2 + bn.ask_qty_3 + bn.ask_qty_4 + bn.ask_qty_5) as bid_ask5_ratio
                                    FROM bitda_depth b
                                    JOIN binance_depth_5 bn ON bn.symbol = 'BTCUSDT'
                                        AND bn.event_time <= b.timestamp
                                        AND bn.event_time >= b.timestamp - 60000
                                    WHERE b.symbol = 'BTCUSDT'
                                        AND b.timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 MINUTE)) * 1000
                                        AND b.bid_price_1 IS NOT NULL AND bn.bid_price_1 IS NOT NULL
                                )
                                SELECT '买一量卖一量深度比' as 项目, ROUND(MAX(bid_ask1_ratio), 2) as 最大值, ROUND(MIN(bid_ask1_ratio), 2) as 最小值, ROUND(AVG(bid_ask1_ratio), 2) as 平均值 FROM depth_stats
                                UNION ALL
                                SELECT '买卖前两档量深度比' as 项目, ROUND(MAX(bid_ask2_ratio), 2) as 最大值, ROUND(MIN(bid_ask2_ratio), 2) as 最小值, ROUND(AVG(bid_ask2_ratio), 2) as 平均值 FROM depth_stats
                                UNION ALL
                                SELECT '买卖前五档量深度比' as 项目, ROUND(MAX(bid_ask5_ratio), 2) as 最大值, ROUND(MIN(bid_ask5_ratio), 2) as 最小值, ROUND(AVG(bid_ask5_ratio), 2) as 平均值 FROM depth_stats
                            """,
                            "refId": "A"
                        }
                    ]
                },

                # 4. ETHUSDT深度统计
                {
                    "id": 4,
                    "title": "📈 ETHUSDT 深度统计",
                    "type": "table",
                    "gridPos": {"h": 6, "w": 12, "x": 12, "y": 8},
                    "targets": [
                        {
                            "datasource": {"type": "mysql", "uid": datasource_uid},
                            "format": "table",
                            "rawSql": """
                                WITH depth_stats AS (
                                    SELECT
                                        (b.bid_qty_1 + b.ask_qty_1) / (bn.bid_qty_1 + bn.ask_qty_1) as bid_ask1_ratio,
                                        (b.bid_qty_1 + b.bid_qty_2 + b.ask_qty_1 + b.ask_qty_2) / (bn.bid_qty_1 + bn.bid_qty_2 + bn.ask_qty_1 + bn.ask_qty_2) as bid_ask2_ratio,
                                        (b.bid_qty_1 + b.bid_qty_2 + b.bid_qty_3 + b.bid_qty_4 + b.bid_qty_5 + b.ask_qty_1 + b.ask_qty_2 + b.ask_qty_3 + b.ask_qty_4 + b.ask_qty_5) /
                                        (bn.bid_qty_1 + bn.bid_qty_2 + bn.bid_qty_3 + bn.bid_qty_4 + bn.bid_qty_5 + bn.ask_qty_1 + bn.ask_qty_2 + bn.ask_qty_3 + bn.ask_qty_4 + bn.ask_qty_5) as bid_ask5_ratio
                                    FROM bitda_depth b
                                    JOIN binance_depth_5 bn ON bn.symbol = 'ETHUSDT'
                                        AND bn.event_time <= b.timestamp
                                        AND bn.event_time >= b.timestamp - 60000
                                    WHERE b.symbol = 'ETHUSDT'
                                        AND b.timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 MINUTE)) * 1000
                                        AND b.bid_price_1 IS NOT NULL AND bn.bid_price_1 IS NOT NULL
                                )
                                SELECT '买一量卖一量深度比' as 项目, ROUND(MAX(bid_ask1_ratio), 2) as 最大值, ROUND(MIN(bid_ask1_ratio), 2) as 最小值, ROUND(AVG(bid_ask1_ratio), 2) as 平均值 FROM depth_stats
                                UNION ALL
                                SELECT '买卖前两档量深度比' as 项目, ROUND(MAX(bid_ask2_ratio), 2) as 最大值, ROUND(MIN(bid_ask2_ratio), 2) as 最小值, ROUND(AVG(bid_ask2_ratio), 2) as 平均值 FROM depth_stats
                                UNION ALL
                                SELECT '买卖前五档量深度比' as 项目, ROUND(MAX(bid_ask5_ratio), 2) as 最大值, ROUND(MIN(bid_ask5_ratio), 2) as 最小值, ROUND(AVG(bid_ask5_ratio), 2) as 平均值 FROM depth_stats
                            """,
                            "refId": "A"
                        }
                    ]
                }
            ],
            "time": {"from": "now-1h", "to": "now"},
            "refresh": "10s",
            "schemaVersion": 30,
            "version": 1
        },
        "overwrite": True
    }
    
    try:
        response = session.post(
            "http://localhost:3000/api/dashboards/db",
            json=dashboard_config,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            dashboard_url = f"http://localhost:3000{result['url']}"
            print(f"   ✅ 完整仪表板创建成功")
            print(f"   🌐 地址: {dashboard_url}")
            return dashboard_url
        else:
            print(f"   ❌ 创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return None

def main():
    """主函数"""
    print("🎨 创建完整的6个表格动态仪表板")
    print("=" * 60)
    print("📊 包含内容:")
    print("   1. BTCUSDT深度对比")
    print("   2. ETHUSDT深度对比") 
    print("   3. BTCUSDT深度统计")
    print("   4. ETHUSDT深度统计")
    print("   5. BTCUSDT价差对比")
    print("   6. ETHUSDT价差对比")
    print()
    
    dashboard_url = create_complete_dashboard()
    
    if dashboard_url:
        print(f"\n🎉 完整仪表板创建成功！")
        print(f"🌐 访问地址: {dashboard_url}")
        print(f"⏰ 10秒自动刷新")
        print(f"📊 包含所有6个表格")
        print(f"🔄 真正的动态数据更新")
        
        # 打开浏览器
        try:
            import webbrowser
            webbrowser.open(dashboard_url)
            print(f"🌐 已自动打开浏览器")
        except:
            pass
    else:
        print(f"\n❌ 创建失败")

if __name__ == "__main__":
    main()
