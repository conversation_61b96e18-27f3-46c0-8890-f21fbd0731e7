#!/usr/bin/env python3
"""
优化Binance Depth5表 - 提取5档深度价格到独立字段
解决JSON查询性能问题
"""

import mysql.connector
import json
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BinanceDepth5Optimizer:
    """Binance Depth5表优化器"""
    
    def __init__(self):
        self.config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'depth_db'
        }
    
    def add_price_columns(self):
        """添加5档价格字段"""
        try:
            connection = mysql.connector.connect(**self.config)
            cursor = connection.cursor()
            
            logger.info("添加Binance 5档价格字段...")
            
            # 检查字段是否已存在
            cursor.execute("DESCRIBE binance_depth_5")
            columns = [col[0] for col in cursor.fetchall()]
            
            if 'bid_price_1' not in columns:
                alter_sql = """
                ALTER TABLE binance_depth_5 
                ADD COLUMN bid_price_1 DECIMAL(15,2) COMMENT '买一价格',
                ADD COLUMN ask_price_1 DECIMAL(15,2) COMMENT '卖一价格',
                ADD COLUMN bid_qty_1 DECIMAL(20,4) COMMENT '买一数量',
                ADD COLUMN ask_qty_1 DECIMAL(20,4) COMMENT '卖一数量',
                ADD COLUMN bid_price_2 DECIMAL(15,2) COMMENT '买二价格',
                ADD COLUMN ask_price_2 DECIMAL(15,2) COMMENT '卖二价格',
                ADD COLUMN bid_qty_2 DECIMAL(20,4) COMMENT '买二数量',
                ADD COLUMN ask_qty_2 DECIMAL(20,4) COMMENT '卖二数量',
                ADD COLUMN bid_price_3 DECIMAL(15,2) COMMENT '买三价格',
                ADD COLUMN ask_price_3 DECIMAL(15,2) COMMENT '卖三价格',
                ADD COLUMN bid_qty_3 DECIMAL(20,4) COMMENT '买三数量',
                ADD COLUMN ask_qty_3 DECIMAL(20,4) COMMENT '卖三数量',
                ADD COLUMN bid_price_4 DECIMAL(15,2) COMMENT '买四价格',
                ADD COLUMN ask_price_4 DECIMAL(15,2) COMMENT '卖四价格',
                ADD COLUMN bid_qty_4 DECIMAL(20,4) COMMENT '买四数量',
                ADD COLUMN ask_qty_4 DECIMAL(20,4) COMMENT '卖四数量',
                ADD COLUMN bid_price_5 DECIMAL(15,2) COMMENT '买五价格',
                ADD COLUMN ask_price_5 DECIMAL(15,2) COMMENT '卖五价格',
                ADD COLUMN bid_qty_5 DECIMAL(20,4) COMMENT '买五数量',
                ADD COLUMN ask_qty_5 DECIMAL(20,4) COMMENT '卖五数量'
                """
                cursor.execute(alter_sql)
                logger.info("✅ 字段添加成功")
            else:
                logger.info("✅ 字段已存在，跳过添加")
            
            cursor.close()
            connection.close()
            
        except Exception as e:
            logger.error(f"添加字段失败: {e}")
            raise
    
    def extract_prices_batch(self, batch_size=1000):
        """批量提取价格数据"""
        try:
            connection = mysql.connector.connect(**self.config)
            cursor = connection.cursor()
            
            # 获取需要处理的记录数
            cursor.execute("""
                SELECT COUNT(*) FROM binance_depth_5 
                WHERE symbol IN ('BTCUSDT', 'ETHUSDT')
                AND bid_price_1 IS NULL 
                AND asks IS NOT NULL 
                AND bids IS NOT NULL
            """)
            total_records = cursor.fetchone()[0]
            
            if total_records == 0:
                logger.info("✅ 所有记录已处理完成")
                return
            
            logger.info(f"📊 需要处理 {total_records} 条记录")
            
            processed = 0
            
            while processed < total_records:
                # 获取一批数据
                cursor.execute("""
                    SELECT id, asks, bids FROM binance_depth_5 
                    WHERE symbol IN ('BTCUSDT', 'ETHUSDT')
                    AND bid_price_1 IS NULL 
                    AND asks IS NOT NULL 
                    AND bids IS NOT NULL
                    ORDER BY id
                    LIMIT %s
                """, (batch_size,))
                
                records = cursor.fetchall()
                if not records:
                    break
                
                # 处理这批数据
                updates = []
                for record_id, asks_json, bids_json in records:
                    try:
                        # 解析JSON
                        asks = json.loads(asks_json)
                        bids = json.loads(bids_json)
                        
                        if asks and bids:
                            # 提取5档深度
                            bid_prices = [None] * 5
                            bid_qtys = [None] * 5
                            ask_prices = [None] * 5
                            ask_qtys = [None] * 5
                            
                            # 买单（已按价格排序，取前5档）
                            for i in range(min(5, len(bids))):
                                bid_prices[i] = float(bids[i][0])
                                bid_qtys[i] = float(bids[i][1])
                            
                            # 卖单（已按价格排序，取前5档）
                            for i in range(min(5, len(asks))):
                                ask_prices[i] = float(asks[i][0])
                                ask_qtys[i] = float(asks[i][1])
                            
                            updates.append((
                                bid_prices[0], ask_prices[0], bid_qtys[0], ask_qtys[0],
                                bid_prices[1], ask_prices[1], bid_qtys[1], ask_qtys[1],
                                bid_prices[2], ask_prices[2], bid_qtys[2], ask_qtys[2],
                                bid_prices[3], ask_prices[3], bid_qtys[3], ask_qtys[3],
                                bid_prices[4], ask_prices[4], bid_qtys[4], ask_qtys[4],
                                record_id
                            ))
                    
                    except Exception as e:
                        logger.warning(f"解析记录 {record_id} 失败: {e}")
                        continue
                
                # 批量更新
                if updates:
                    update_sql = """
                    UPDATE binance_depth_5 SET 
                        bid_price_1 = %s, ask_price_1 = %s, bid_qty_1 = %s, ask_qty_1 = %s,
                        bid_price_2 = %s, ask_price_2 = %s, bid_qty_2 = %s, ask_qty_2 = %s,
                        bid_price_3 = %s, ask_price_3 = %s, bid_qty_3 = %s, ask_qty_3 = %s,
                        bid_price_4 = %s, ask_price_4 = %s, bid_qty_4 = %s, ask_qty_4 = %s,
                        bid_price_5 = %s, ask_price_5 = %s, bid_qty_5 = %s, ask_qty_5 = %s
                    WHERE id = %s
                    """
                    cursor.executemany(update_sql, updates)
                    connection.commit()
                    
                    processed += len(updates)
                    progress = (processed / total_records) * 100
                    logger.info(f"📈 处理进度: {processed}/{total_records} ({progress:.1f}%)")
            
            cursor.close()
            connection.close()
            
            logger.info(f"✅ 价格提取完成，共处理 {processed} 条记录")
            
        except Exception as e:
            logger.error(f"批量提取价格失败: {e}")
            raise
    
    def create_indexes(self):
        """创建优化索引"""
        try:
            connection = mysql.connector.connect(**self.config)
            cursor = connection.cursor()
            
            logger.info("创建Binance深度匹配索引...")
            
            # 检查索引是否已存在
            cursor.execute("SHOW INDEX FROM binance_depth_5 WHERE Key_name = 'idx_binance_depth_match'")
            if cursor.fetchone():
                logger.info("✅ 索引已存在，跳过创建")
            else:
                # 创建深度匹配索引
                index_sql = """
                CREATE INDEX idx_binance_depth_match 
                ON binance_depth_5 (symbol, bid_price_1, ask_price_1, event_time)
                """
                cursor.execute(index_sql)
                logger.info("✅ Binance深度匹配索引创建成功")
            
            cursor.close()
            connection.close()
            
        except Exception as e:
            logger.error(f"创建索引失败: {e}")
            raise
    
    def verify_optimization(self):
        """验证优化效果"""
        try:
            connection = mysql.connector.connect(**self.config)
            cursor = connection.cursor()
            
            logger.info("验证优化效果...")
            
            # 检查提取的价格数据
            cursor.execute("""
                SELECT COUNT(*) as total,
                       COUNT(bid_price_1) as with_bid_price,
                       COUNT(ask_price_1) as with_ask_price
                FROM binance_depth_5 
                WHERE symbol IN ('BTCUSDT', 'ETHUSDT')
            """)
            
            total, with_bid, with_ask = cursor.fetchone()
            logger.info(f"📊 数据统计:")
            logger.info(f"   总记录数: {total}")
            logger.info(f"   有买一价格: {with_bid} ({with_bid/total*100:.1f}%)")
            logger.info(f"   有卖一价格: {with_ask} ({with_ask/total*100:.1f}%)")
            
            # 测试查询性能
            logger.info("测试查询性能...")
            start_time = datetime.now()
            
            cursor.execute("""
                SELECT COUNT(*) FROM binance_depth_5 
                WHERE symbol = 'ETHUSDT' 
                AND bid_price_1 BETWEEN 2600 AND 2700
                AND ask_price_1 BETWEEN 2600 AND 2700
            """)
            
            result = cursor.fetchone()[0]
            end_time = datetime.now()
            query_time = (end_time - start_time).total_seconds()
            
            logger.info(f"📈 查询结果: 找到 {result} 条匹配记录")
            logger.info(f"⏱️  查询耗时: {query_time:.3f} 秒")
            
            if query_time < 1.0:
                logger.info("✅ 查询性能优秀 (<1秒)")
            elif query_time < 5.0:
                logger.info("✅ 查询性能良好 (<5秒)")
            else:
                logger.warning("⚠️  查询性能需要进一步优化")
            
            cursor.close()
            connection.close()
            
        except Exception as e:
            logger.error(f"验证失败: {e}")
    
    def optimize_all(self):
        """执行完整优化流程"""
        logger.info("🚀 开始Binance Depth5表优化")
        logger.info("=" * 50)
        
        try:
            # 1. 添加字段
            self.add_price_columns()
            
            # 2. 提取价格数据
            self.extract_prices_batch()
            
            # 3. 创建索引
            self.create_indexes()
            
            # 4. 验证效果
            self.verify_optimization()
            
            logger.info("🎉 优化完成！")
            logger.info("现在可以使用优化后的查询:")
            logger.info("SELECT * FROM binance_depth_5 WHERE symbol='ETHUSDT' AND bid_price_1=? AND ask_price_1=?")
            
        except Exception as e:
            logger.error(f"❌ 优化失败: {e}")
            raise

def main():
    """主函数"""
    print("🔧 Binance Depth5表性能优化工具")
    print("=" * 50)
    print("功能:")
    print("  - 提取JSON中的5档深度价格到独立字段")
    print("  - 创建高效的深度匹配索引")
    print("  - 大幅提升深度对比查询性能")
    print()
    
    optimizer = BinanceDepth5Optimizer()
    optimizer.optimize_all()

if __name__ == "__main__":
    main()
