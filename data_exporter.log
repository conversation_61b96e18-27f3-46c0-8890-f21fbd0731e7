2025-05-30 07:48:18,664 - utils.db - INFO - 数据库连接池创建成功
----------------------------------------
Exception occurred during processing of request from ('127.0.0.1', 42224)
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.12/socketserver.py", line 318, in _handle_request_noblock
    self.process_request(request, client_address)
  File "/home/<USER>/anaconda3/lib/python3.12/socketserver.py", line 349, in process_request
    self.finish_request(request, client_address)
  File "/home/<USER>/anaconda3/lib/python3.12/socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "/home/<USER>/project/WS_DATA_ALL/simple_data_exporter.py", line 70, in __init__
    super().__init__(*args, **kwargs)
  File "/home/<USER>/anaconda3/lib/python3.12/socketserver.py", line 761, in __init__
    self.handle()
  File "/home/<USER>/anaconda3/lib/python3.12/http/server.py", line 436, in handle
    self.handle_one_request()
  File "/home/<USER>/anaconda3/lib/python3.12/http/server.py", line 424, in handle_one_request
    method()
  File "/home/<USER>/project/WS_DATA_ALL/simple_data_exporter.py", line 75, in do_GET
    self.send_json_response(self.exporter.get_funding_rates())
  File "/home/<USER>/project/WS_DATA_ALL/simple_data_exporter.py", line 103, in send_json_response
    self.wfile.write(json.dumps(data, indent=2, ensure_ascii=False).encode('utf-8'))
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/json/encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/json/encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "/home/<USER>/anaconda3/lib/python3.12/json/encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "/home/<USER>/anaconda3/lib/python3.12/json/encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "/home/<USER>/anaconda3/lib/python3.12/json/encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type datetime is not JSON serializable
----------------------------------------
2025-05-30 07:48:19,095 - analyzer.latency_analyzer - WARNING - 没有找到ETHUSDT的延时匹配数据
----------------------------------------
Exception occurred during processing of request from ('127.0.0.1', 42232)
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.12/socketserver.py", line 318, in _handle_request_noblock
    self.process_request(request, client_address)
  File "/home/<USER>/anaconda3/lib/python3.12/socketserver.py", line 349, in process_request
    self.finish_request(request, client_address)
  File "/home/<USER>/anaconda3/lib/python3.12/socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "/home/<USER>/project/WS_DATA_ALL/simple_data_exporter.py", line 70, in __init__
    super().__init__(*args, **kwargs)
  File "/home/<USER>/anaconda3/lib/python3.12/socketserver.py", line 761, in __init__
    self.handle()
  File "/home/<USER>/anaconda3/lib/python3.12/http/server.py", line 436, in handle
    self.handle_one_request()
  File "/home/<USER>/anaconda3/lib/python3.12/http/server.py", line 424, in handle_one_request
    method()
  File "/home/<USER>/project/WS_DATA_ALL/simple_data_exporter.py", line 79, in do_GET
    self.send_json_response(self.exporter.get_depth_comparison())
  File "/home/<USER>/project/WS_DATA_ALL/simple_data_exporter.py", line 103, in send_json_response
    self.wfile.write(json.dumps(data, indent=2, ensure_ascii=False).encode('utf-8'))
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/json/encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/json/encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "/home/<USER>/anaconda3/lib/python3.12/json/encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "/home/<USER>/anaconda3/lib/python3.12/json/encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "/home/<USER>/anaconda3/lib/python3.12/json/encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type datetime is not JSON serializable
----------------------------------------
----------------------------------------
Exception occurred during processing of request from ('127.0.0.1', 42246)
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.12/socketserver.py", line 318, in _handle_request_noblock
    self.process_request(request, client_address)
  File "/home/<USER>/anaconda3/lib/python3.12/socketserver.py", line 349, in process_request
    self.finish_request(request, client_address)
  File "/home/<USER>/anaconda3/lib/python3.12/socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "/home/<USER>/project/WS_DATA_ALL/simple_data_exporter.py", line 70, in __init__
    super().__init__(*args, **kwargs)
  File "/home/<USER>/anaconda3/lib/python3.12/socketserver.py", line 761, in __init__
    self.handle()
  File "/home/<USER>/anaconda3/lib/python3.12/http/server.py", line 436, in handle
    self.handle_one_request()
  File "/home/<USER>/anaconda3/lib/python3.12/http/server.py", line 424, in handle_one_request
    method()
  File "/home/<USER>/project/WS_DATA_ALL/simple_data_exporter.py", line 81, in do_GET
    self.send_json_response(self.exporter.get_price_deviation())
  File "/home/<USER>/project/WS_DATA_ALL/simple_data_exporter.py", line 103, in send_json_response
    self.wfile.write(json.dumps(data, indent=2, ensure_ascii=False).encode('utf-8'))
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/json/encoder.py", line 202, in encode
    chunks = list(chunks)
             ^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/json/encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "/home/<USER>/anaconda3/lib/python3.12/json/encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "/home/<USER>/anaconda3/lib/python3.12/json/encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "/home/<USER>/anaconda3/lib/python3.12/json/encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type datetime is not JSON serializable
----------------------------------------
