2025-05-29 17:47:48,229 - utils.db - INFO - 数据库连接池创建成功
2025-05-29 17:47:48,233 - __main__ - INFO - 开始收集指标数据...
2025-05-29 20:26:02,364 - analyzer.latency_analyzer - INFO - 延时分析完成，匹配次数: 1661
2025-05-29 20:26:02,478 - analyzer.kline_analyzer - INFO - 分析BTCUSDT的K线数据...
2025-05-29 20:26:06,390 - analyzer.kline_analyzer - INFO - BTCUSDT分析完成，发现1162个连续相同K线序列
2025-05-29 20:26:06,442 - analyzer.kline_analyzer - INFO - 分析ETHUSDT的K线数据...
2025-05-29 20:26:07,285 - analyzer.kline_analyzer - INFO - ETHUSDT分析完成，发现1169个连续相同K线序列
2025-05-29 20:26:07,287 - analyzer.depth_analyzer - INFO - 分析BTCUSDT的深度数据...
2025-05-29 20:27:33,357 - analyzer.depth_analyzer - INFO - BTCUSDT深度分析完成，对比数据点: 3312
2025-05-29 20:27:33,485 - analyzer.depth_analyzer - INFO - 分析ETHUSDT的深度数据...
2025-05-29 20:29:05,638 - analyzer.depth_analyzer - INFO - ETHUSDT深度分析完成，对比数据点: 4933
2025-05-29 20:29:05,641 - analyzer.price_analyzer - INFO - 分析BTCUSDT的价格差值...
2025-05-29 20:29:06,762 - analyzer.price_analyzer - INFO - BTCUSDT价格分析完成，数据点: 3557
2025-05-29 20:29:06,810 - analyzer.price_analyzer - INFO - 分析ETHUSDT的价格差值...
2025-05-29 20:29:06,974 - analyzer.price_analyzer - INFO - ETHUSDT价格分析完成，数据点: 3607
2025-05-29 20:29:06,975 - analyzer.funding_analyzer - INFO - 分析BTCUSDT的资金费率...
2025-05-29 20:29:09,935 - analyzer.funding_analyzer - INFO - BTCUSDT资金费率分析完成，数据点: 23516
2025-05-29 20:29:09,936 - analyzer.funding_analyzer - INFO - 分析ETHUSDT的资金费率...
2025-05-29 20:29:10,605 - analyzer.funding_analyzer - INFO - ETHUSDT资金费率分析完成，数据点: 23515
2025-05-29 20:29:10,608 - __main__ - INFO - 指标数据收集完成
2025-05-29 20:34:19,769 - __main__ - INFO - 开始收集指标数据...
2025-05-29 20:34:49,769 - __main__ - INFO - 开始收集指标数据...
2025-05-29 20:35:19,793 - __main__ - INFO - 开始收集指标数据...
2025-05-29 20:35:49,768 - __main__ - INFO - 开始收集指标数据...
2025-05-29 20:36:19,768 - __main__ - INFO - 开始收集指标数据...
2025-05-29 20:36:49,768 - __main__ - INFO - 开始收集指标数据...
2025-05-29 20:37:19,795 - __main__ - INFO - 开始收集指标数据...
2025-05-29 20:37:50,448 - __main__ - INFO - 开始收集指标数据...
2025-05-29 20:38:19,786 - __main__ - INFO - 开始收集指标数据...
2025-05-29 20:38:49,795 - __main__ - INFO - 开始收集指标数据...
2025-05-29 20:39:19,787 - __main__ - INFO - 开始收集指标数据...
2025-05-29 20:39:49,787 - __main__ - INFO - 开始收集指标数据...
2025-05-29 20:39:49,787 - utils.db - ERROR - 执行SQL查询失败: Failed getting connection; pool exhausted
2025-05-29 20:39:49,787 - analyzer.latency_analyzer - ERROR - 延时分析失败: Failed getting connection; pool exhausted
2025-05-29 20:39:49,788 - analyzer.kline_analyzer - INFO - 分析BTCUSDT的K线数据...
2025-05-29 20:39:49,788 - utils.db - ERROR - 执行SQL查询失败: Failed getting connection; pool exhausted
2025-05-29 20:39:49,788 - analyzer.kline_analyzer - ERROR - K线分析失败: Failed getting connection; pool exhausted
2025-05-29 20:39:49,788 - analyzer.depth_analyzer - INFO - 分析BTCUSDT的深度数据...
2025-05-29 20:39:49,788 - utils.db - ERROR - 执行SQL查询失败: Failed getting connection; pool exhausted
2025-05-29 20:39:49,806 - analyzer.depth_analyzer - ERROR - 深度分析失败: Failed getting connection; pool exhausted
2025-05-29 20:39:49,806 - analyzer.price_analyzer - INFO - 分析BTCUSDT的价格差值...
2025-05-29 20:39:49,807 - utils.db - ERROR - 执行SQL查询失败: Failed getting connection; pool exhausted
2025-05-29 20:39:49,807 - analyzer.price_analyzer - ERROR - 价格分析失败: Failed getting connection; pool exhausted
2025-05-29 20:39:49,807 - analyzer.funding_analyzer - INFO - 分析BTCUSDT的资金费率...
2025-05-29 20:39:49,807 - utils.db - ERROR - 执行SQL查询失败: Failed getting connection; pool exhausted
2025-05-29 20:39:49,807 - analyzer.funding_analyzer - ERROR - 资金费率分析失败: Failed getting connection; pool exhausted
2025-05-29 20:39:49,807 - __main__ - INFO - 指标数据收集完成
2025-05-29 20:42:04,727 - utils.db - INFO - 数据库连接池创建成功
2025-05-29 20:42:04,738 - __main__ - INFO - 开始收集指标数据...
2025-05-30 05:26:21,276 - analyzer.latency_analyzer - INFO - 延时分析完成，匹配次数: 3607
2025-05-30 05:26:21,482 - analyzer.kline_analyzer - INFO - 分析BTCUSDT的K线数据...
2025-05-30 05:26:30,336 - analyzer.kline_analyzer - INFO - BTCUSDT分析完成，发现1112个连续相同K线序列
2025-05-30 05:26:30,336 - analyzer.kline_analyzer - INFO - 分析ETHUSDT的K线数据...
2025-05-30 05:26:30,724 - analyzer.kline_analyzer - INFO - ETHUSDT分析完成，发现1011个连续相同K线序列
2025-05-30 05:26:30,725 - analyzer.depth_analyzer - INFO - 分析BTCUSDT的深度数据...
2025-05-30 05:27:07,361 - analyzer.depth_analyzer - INFO - BTCUSDT深度分析完成，对比数据点: 3070
2025-05-30 05:27:07,361 - analyzer.depth_analyzer - INFO - 分析ETHUSDT的深度数据...
2025-05-30 05:28:09,571 - analyzer.depth_analyzer - INFO - ETHUSDT深度分析完成，对比数据点: 3228
2025-05-30 05:28:09,572 - analyzer.price_analyzer - INFO - 分析BTCUSDT的价格差值...
2025-05-30 05:28:10,642 - analyzer.price_analyzer - INFO - BTCUSDT价格分析完成，数据点: 3600
2025-05-30 05:28:10,642 - analyzer.price_analyzer - INFO - 分析ETHUSDT的价格差值...
2025-05-30 05:28:10,918 - analyzer.price_analyzer - INFO - ETHUSDT价格分析完成，数据点: 3600
2025-05-30 05:28:10,918 - analyzer.funding_analyzer - INFO - 分析BTCUSDT的资金费率...
2025-05-30 05:28:18,961 - analyzer.funding_analyzer - INFO - BTCUSDT资金费率分析完成，数据点: 55751
2025-05-30 05:28:18,962 - analyzer.funding_analyzer - INFO - 分析ETHUSDT的资金费率...
2025-05-30 05:28:20,590 - analyzer.funding_analyzer - INFO - ETHUSDT资金费率分析完成，数据点: 55799
2025-05-30 05:28:20,601 - __main__ - INFO - 指标数据收集完成
2025-05-30 05:33:49,768 - __main__ - INFO - 开始收集指标数据...
2025-05-30 05:34:19,776 - __main__ - INFO - 开始收集指标数据...
2025-05-30 05:34:49,767 - __main__ - INFO - 开始收集指标数据...
2025-05-30 05:35:19,768 - __main__ - INFO - 开始收集指标数据...
2025-05-30 05:35:49,767 - __main__ - INFO - 开始收集指标数据...
2025-05-30 05:36:19,768 - __main__ - INFO - 开始收集指标数据...
2025-05-30 05:36:49,773 - __main__ - INFO - 开始收集指标数据...
2025-05-30 05:37:19,767 - __main__ - INFO - 开始收集指标数据...
2025-05-30 05:37:49,768 - __main__ - INFO - 开始收集指标数据...
2025-05-30 05:38:19,767 - __main__ - INFO - 开始收集指标数据...
2025-05-30 05:38:49,769 - __main__ - INFO - 开始收集指标数据...
2025-05-30 05:38:49,769 - utils.db - ERROR - 执行SQL查询失败: Failed getting connection; pool exhausted
2025-05-30 05:38:49,769 - analyzer.latency_analyzer - ERROR - 延时分析失败: Failed getting connection; pool exhausted
2025-05-30 05:38:49,769 - analyzer.kline_analyzer - INFO - 分析BTCUSDT的K线数据...
2025-05-30 05:38:49,769 - utils.db - ERROR - 执行SQL查询失败: Failed getting connection; pool exhausted
2025-05-30 05:38:49,769 - analyzer.kline_analyzer - ERROR - K线分析失败: Failed getting connection; pool exhausted
2025-05-30 05:38:49,769 - analyzer.depth_analyzer - INFO - 分析BTCUSDT的深度数据...
2025-05-30 05:38:49,769 - utils.db - ERROR - 执行SQL查询失败: Failed getting connection; pool exhausted
2025-05-30 05:38:49,769 - analyzer.depth_analyzer - ERROR - 深度分析失败: Failed getting connection; pool exhausted
2025-05-30 05:38:49,769 - analyzer.price_analyzer - INFO - 分析BTCUSDT的价格差值...
2025-05-30 05:38:49,770 - utils.db - ERROR - 执行SQL查询失败: Failed getting connection; pool exhausted
2025-05-30 05:38:49,770 - analyzer.price_analyzer - ERROR - 价格分析失败: Failed getting connection; pool exhausted
2025-05-30 05:38:49,770 - analyzer.funding_analyzer - INFO - 分析BTCUSDT的资金费率...
2025-05-30 05:38:49,770 - utils.db - ERROR - 执行SQL查询失败: Failed getting connection; pool exhausted
2025-05-30 05:38:49,770 - analyzer.funding_analyzer - ERROR - 资金费率分析失败: Failed getting connection; pool exhausted
2025-05-30 05:38:49,770 - __main__ - INFO - 指标数据收集完成
2025-05-30 05:44:19,769 - __main__ - INFO - 开始收集指标数据...
2025-05-30 05:44:19,769 - utils.db - ERROR - 执行SQL查询失败: Failed getting connection; pool exhausted
2025-05-30 05:44:19,769 - analyzer.latency_analyzer - ERROR - 延时分析失败: Failed getting connection; pool exhausted
2025-05-30 05:44:19,770 - analyzer.kline_analyzer - INFO - 分析BTCUSDT的K线数据...
2025-05-30 05:44:19,770 - utils.db - ERROR - 执行SQL查询失败: Failed getting connection; pool exhausted
2025-05-30 05:44:19,770 - analyzer.kline_analyzer - ERROR - K线分析失败: Failed getting connection; pool exhausted
2025-05-30 05:44:19,773 - analyzer.depth_analyzer - INFO - 分析BTCUSDT的深度数据...
2025-05-30 05:44:19,773 - utils.db - ERROR - 执行SQL查询失败: Failed getting connection; pool exhausted
2025-05-30 05:44:19,773 - analyzer.depth_analyzer - ERROR - 深度分析失败: Failed getting connection; pool exhausted
2025-05-30 05:44:19,773 - analyzer.price_analyzer - INFO - 分析BTCUSDT的价格差值...
2025-05-30 05:44:19,773 - utils.db - ERROR - 执行SQL查询失败: Failed getting connection; pool exhausted
2025-05-30 05:44:19,773 - analyzer.price_analyzer - ERROR - 价格分析失败: Failed getting connection; pool exhausted
2025-05-30 05:44:19,774 - analyzer.funding_analyzer - INFO - 分析BTCUSDT的资金费率...
2025-05-30 05:44:19,774 - utils.db - ERROR - 执行SQL查询失败: Failed getting connection; pool exhausted
2025-05-30 05:44:19,774 - analyzer.funding_analyzer - ERROR - 资金费率分析失败: Failed getting connection; pool exhausted
2025-05-30 05:44:19,774 - __main__ - INFO - 指标数据收集完成
2025-05-30 05:49:20,797 - __main__ - INFO - 开始收集指标数据...
2025-05-30 05:49:50,716 - __main__ - INFO - 开始收集指标数据...
2025-05-30 05:49:50,716 - utils.db - ERROR - 执行SQL查询失败: Failed getting connection; pool exhausted
2025-05-30 05:49:50,717 - analyzer.latency_analyzer - ERROR - 延时分析失败: Failed getting connection; pool exhausted
2025-05-30 05:49:50,717 - analyzer.kline_analyzer - INFO - 分析BTCUSDT的K线数据...
2025-05-30 05:49:50,717 - utils.db - ERROR - 执行SQL查询失败: Failed getting connection; pool exhausted
2025-05-30 05:49:50,718 - analyzer.kline_analyzer - ERROR - K线分析失败: Failed getting connection; pool exhausted
2025-05-30 05:49:50,718 - analyzer.depth_analyzer - INFO - 分析BTCUSDT的深度数据...
2025-05-30 05:49:50,718 - utils.db - ERROR - 执行SQL查询失败: Failed getting connection; pool exhausted
2025-05-30 05:49:50,718 - analyzer.depth_analyzer - ERROR - 深度分析失败: Failed getting connection; pool exhausted
2025-05-30 05:49:50,718 - analyzer.price_analyzer - INFO - 分析BTCUSDT的价格差值...
2025-05-30 05:49:50,718 - utils.db - ERROR - 执行SQL查询失败: Failed getting connection; pool exhausted
2025-05-30 05:49:50,718 - analyzer.price_analyzer - ERROR - 价格分析失败: Failed getting connection; pool exhausted
2025-05-30 05:49:50,718 - analyzer.funding_analyzer - INFO - 分析BTCUSDT的资金费率...
2025-05-30 05:49:50,723 - utils.db - ERROR - 执行SQL查询失败: Failed getting connection; pool exhausted
2025-05-30 05:49:50,723 - analyzer.funding_analyzer - ERROR - 资金费率分析失败: Failed getting connection; pool exhausted
2025-05-30 05:49:50,723 - __main__ - INFO - 指标数据收集完成
2025-05-30 05:55:19,830 - __main__ - INFO - 开始收集指标数据...
2025-05-30 05:55:49,906 - __main__ - INFO - 开始收集指标数据...
2025-05-30 05:56:45,237 - utils.db - INFO - 数据库连接池创建成功
2025-05-30 05:56:45,248 - __main__ - INFO - 开始收集指标数据...
2025-05-30 07:55:40,040 - utils.db - INFO - 数据库连接池创建成功
2025-05-30 07:55:40,044 - __main__ - INFO - 开始收集指标数据...
2025-05-31 15:26:29,734 - analyzer.latency_analyzer - INFO - 延时分析完成，匹配次数: 3587
2025-05-31 15:26:30,587 - analyzer.kline_analyzer - INFO - 分析BTCUSDT的K线数据...
2025-05-31 15:26:34,086 - analyzer.kline_analyzer - INFO - BTCUSDT分析完成，发现1938个连续相同K线序列
2025-05-31 15:26:34,093 - analyzer.kline_analyzer - INFO - 分析ETHUSDT的K线数据...
2025-05-31 15:26:36,394 - analyzer.kline_analyzer - INFO - ETHUSDT分析完成，发现1556个连续相同K线序列
2025-05-31 15:26:36,709 - analyzer.depth_analyzer - WARNING - 没有找到BTCUSDT的深度匹配数据
2025-05-31 15:26:37,083 - analyzer.depth_analyzer - WARNING - 没有找到ETHUSDT的深度匹配数据
2025-05-31 15:26:37,110 - __main__ - ERROR - 收集指标数据失败: object dict can't be used in 'await' expression
2025-05-31 15:26:49,772 - __main__ - INFO - 开始收集指标数据...
2025-05-31 15:27:19,777 - __main__ - INFO - 开始收集指标数据...
2025-05-31 15:27:49,768 - __main__ - INFO - 开始收集指标数据...
2025-05-31 15:28:19,811 - __main__ - INFO - 开始收集指标数据...
2025-05-31 15:28:49,789 - __main__ - INFO - 开始收集指标数据...
2025-05-31 15:29:19,774 - __main__ - INFO - 开始收集指标数据...
2025-05-31 15:29:49,789 - __main__ - INFO - 开始收集指标数据...
2025-05-31 15:30:19,769 - __main__ - INFO - 开始收集指标数据...
2025-05-31 15:30:49,821 - __main__ - INFO - 开始收集指标数据...
2025-05-31 15:31:39,271 - utils.db - INFO - 数据库连接池创建成功
2025-05-31 15:31:39,332 - __main__ - INFO - 开始收集指标数据...
2025-06-01 13:26:21,288 - analyzer.latency_analyzer - INFO - 延时分析完成，匹配次数: 7130
2025-06-01 13:26:21,643 - analyzer.kline_analyzer - INFO - 分析BTCUSDT的K线数据...
2025-06-01 13:26:23,520 - analyzer.kline_analyzer - INFO - BTCUSDT分析完成，发现1892个连续相同K线序列
2025-06-01 13:26:23,520 - analyzer.kline_analyzer - INFO - 分析ETHUSDT的K线数据...
2025-06-01 13:26:24,247 - analyzer.kline_analyzer - INFO - ETHUSDT分析完成，发现1656个连续相同K线序列
2025-06-01 13:26:24,456 - analyzer.depth_analyzer - WARNING - 没有找到BTCUSDT的深度匹配数据
2025-06-01 13:26:24,603 - analyzer.depth_analyzer - WARNING - 没有找到ETHUSDT的深度匹配数据
2025-06-01 13:26:24,604 - __main__ - ERROR - 收集指标数据失败: object dict can't be used in 'await' expression
2025-06-01 13:26:49,865 - __main__ - INFO - 开始收集指标数据...
2025-06-01 13:27:19,773 - __main__ - INFO - 开始收集指标数据...
2025-06-01 13:27:49,789 - __main__ - INFO - 开始收集指标数据...
2025-06-01 13:28:19,803 - __main__ - INFO - 开始收集指标数据...
2025-06-01 13:29:15,728 - utils.db - INFO - 数据库连接池创建成功
2025-06-01 13:29:15,750 - __main__ - INFO - 开始收集指标数据...
2025-06-03 01:39:30,730 - analyzer.latency_analyzer - INFO - 延时分析完成，匹配次数: 7180
2025-06-03 01:39:31,543 - analyzer.kline_analyzer - INFO - 分析BTCUSDT的K线数据...
2025-06-03 01:39:45,676 - analyzer.kline_analyzer - INFO - BTCUSDT分析完成，发现1584个连续相同K线序列
2025-06-03 01:39:45,834 - analyzer.kline_analyzer - INFO - 分析ETHUSDT的K线数据...
2025-06-03 01:39:53,370 - analyzer.kline_analyzer - INFO - ETHUSDT分析完成，发现1634个连续相同K线序列
2025-06-03 01:39:54,659 - analyzer.depth_analyzer - WARNING - 没有找到BTCUSDT的深度匹配数据
2025-06-03 01:39:55,072 - analyzer.depth_analyzer - WARNING - 没有找到ETHUSDT的深度匹配数据
2025-06-03 01:39:55,073 - __main__ - ERROR - 收集指标数据失败: object dict can't be used in 'await' expression
2025-06-03 01:40:19,799 - __main__ - INFO - 开始收集指标数据...
2025-06-03 01:40:49,771 - __main__ - INFO - 开始收集指标数据...
2025-06-03 01:41:19,769 - __main__ - INFO - 开始收集指标数据...
2025-06-03 01:41:49,769 - __main__ - INFO - 开始收集指标数据...
2025-06-03 01:42:19,799 - __main__ - INFO - 开始收集指标数据...
2025-06-03 01:42:49,833 - __main__ - INFO - 开始收集指标数据...
2025-06-03 01:43:38,191 - utils.db - INFO - 数据库连接池创建成功
2025-06-03 01:43:38,269 - __main__ - INFO - 开始收集指标数据...
2025-06-04 19:05:14,267 - analyzer.latency_analyzer - INFO - 延时分析完成，匹配次数: 7032
2025-06-04 19:05:14,718 - analyzer.kline_analyzer - INFO - 分析BTCUSDT的K线数据...
2025-06-04 19:05:16,576 - analyzer.kline_analyzer - INFO - BTCUSDT分析完成，发现1197个连续相同K线序列
2025-06-04 19:05:16,577 - analyzer.kline_analyzer - INFO - 分析ETHUSDT的K线数据...
2025-06-04 19:05:17,167 - analyzer.kline_analyzer - INFO - ETHUSDT分析完成，发现1072个连续相同K线序列
2025-06-04 19:05:17,490 - analyzer.depth_analyzer - WARNING - 没有找到BTCUSDT的深度匹配数据
2025-06-04 19:05:17,620 - analyzer.depth_analyzer - WARNING - 没有找到ETHUSDT的深度匹配数据
2025-06-04 19:05:17,621 - __main__ - ERROR - 收集指标数据失败: object dict can't be used in 'await' expression
2025-06-04 19:05:19,786 - __main__ - INFO - 开始收集指标数据...
2025-06-04 19:05:49,780 - __main__ - INFO - 开始收集指标数据...
2025-06-04 19:06:19,786 - __main__ - INFO - 开始收集指标数据...
2025-06-04 19:06:49,783 - __main__ - INFO - 开始收集指标数据...
2025-06-04 19:07:20,291 - __main__ - INFO - 开始收集指标数据...
2025-06-04 19:07:49,822 - __main__ - INFO - 开始收集指标数据...
2025-06-04 19:08:19,848 - __main__ - INFO - 开始收集指标数据...
2025-06-04 19:09:00,267 - utils.db - INFO - 数据库连接池创建成功
2025-06-04 19:09:00,342 - __main__ - INFO - 开始收集指标数据...
2025-06-07 20:33:53,162 - analyzer.latency_analyzer - INFO - 延时分析完成，匹配次数: 7654
2025-06-07 20:33:53,606 - analyzer.kline_analyzer - INFO - 分析BTCUSDT的K线数据...
2025-06-07 20:33:54,395 - analyzer.kline_analyzer - INFO - BTCUSDT分析完成，发现1个连续相同K线序列
2025-06-07 20:33:54,418 - analyzer.kline_analyzer - INFO - 分析ETHUSDT的K线数据...
2025-06-07 20:33:54,767 - analyzer.kline_analyzer - INFO - ETHUSDT分析完成，发现2个连续相同K线序列
2025-06-07 20:33:54,992 - analyzer.depth_analyzer - WARNING - 没有找到BTCUSDT的深度匹配数据
2025-06-07 20:33:55,104 - analyzer.depth_analyzer - WARNING - 没有找到ETHUSDT的深度匹配数据
2025-06-07 20:33:55,182 - __main__ - ERROR - 收集指标数据失败: object dict can't be used in 'await' expression
2025-06-07 20:34:19,816 - __main__ - INFO - 开始收集指标数据...
2025-06-07 20:34:49,780 - __main__ - INFO - 开始收集指标数据...
2025-06-07 20:35:19,806 - __main__ - INFO - 开始收集指标数据...
2025-06-07 20:35:49,892 - __main__ - INFO - 开始收集指标数据...
2025-06-07 20:36:19,803 - __main__ - INFO - 开始收集指标数据...
2025-06-07 20:36:50,474 - __main__ - INFO - 开始收集指标数据...
2025-06-07 20:37:43,264 - utils.db - INFO - 数据库连接池创建成功
2025-06-07 20:37:43,312 - __main__ - INFO - 开始收集指标数据...
2025-06-08 01:59:13,969 - analyzer.latency_analyzer - INFO - 延时分析完成，匹配次数: 3588
2025-06-08 01:59:14,201 - analyzer.kline_analyzer - INFO - 分析BTCUSDT的K线数据...
2025-06-08 01:59:14,542 - analyzer.kline_analyzer - INFO - BTCUSDT分析完成，发现4个连续相同K线序列
2025-06-08 01:59:14,542 - analyzer.kline_analyzer - INFO - 分析ETHUSDT的K线数据...
2025-06-08 01:59:14,657 - analyzer.kline_analyzer - INFO - ETHUSDT分析完成，发现1个连续相同K线序列
2025-06-08 01:59:14,704 - analyzer.depth_analyzer - WARNING - 没有找到BTCUSDT的深度匹配数据
2025-06-08 01:59:14,708 - analyzer.depth_analyzer - WARNING - 没有找到ETHUSDT的深度匹配数据
2025-06-08 01:59:14,708 - __main__ - ERROR - 收集指标数据失败: object dict can't be used in 'await' expression
2025-06-08 01:59:19,822 - __main__ - INFO - 开始收集指标数据...
2025-06-08 01:59:50,446 - __main__ - INFO - 开始收集指标数据...
2025-06-08 02:00:19,784 - __main__ - INFO - 开始收集指标数据...
2025-06-08 02:00:50,324 - __main__ - INFO - 开始收集指标数据...
2025-06-08 02:01:20,019 - __main__ - INFO - 开始收集指标数据...
2025-06-08 02:01:51,192 - __main__ - INFO - 开始收集指标数据...
2025-06-08 02:02:19,867 - __main__ - INFO - 开始收集指标数据...
2025-06-08 02:02:51,407 - __main__ - INFO - 开始收集指标数据...
2025-06-08 02:03:46,914 - utils.db - INFO - 数据库连接池创建成功
2025-06-08 02:03:46,997 - __main__ - INFO - 开始收集指标数据...
2025-06-08 06:27:42,432 - analyzer.latency_analyzer - INFO - 延时分析完成，匹配次数: 3586
2025-06-08 06:27:42,567 - analyzer.kline_analyzer - INFO - 分析BTCUSDT的K线数据...
2025-06-08 06:27:43,097 - analyzer.kline_analyzer - INFO - BTCUSDT分析完成，发现8个连续相同K线序列
2025-06-08 06:27:43,097 - analyzer.kline_analyzer - INFO - 分析ETHUSDT的K线数据...
2025-06-08 06:27:43,200 - analyzer.kline_analyzer - INFO - ETHUSDT分析完成，发现2个连续相同K线序列
2025-06-08 06:27:43,234 - analyzer.depth_analyzer - WARNING - 没有找到BTCUSDT的深度匹配数据
2025-06-08 06:27:43,238 - analyzer.depth_analyzer - WARNING - 没有找到ETHUSDT的深度匹配数据
2025-06-08 06:27:43,239 - __main__ - ERROR - 收集指标数据失败: object dict can't be used in 'await' expression
2025-06-08 06:27:49,840 - __main__ - INFO - 开始收集指标数据...
2025-06-08 06:28:19,788 - __main__ - INFO - 开始收集指标数据...
2025-06-08 06:28:49,969 - __main__ - INFO - 开始收集指标数据...
2025-06-08 06:29:19,907 - __main__ - INFO - 开始收集指标数据...
2025-06-08 06:29:50,006 - __main__ - INFO - 开始收集指标数据...
2025-06-08 06:30:20,512 - __main__ - INFO - 开始收集指标数据...
2025-06-08 06:30:50,594 - __main__ - INFO - 开始收集指标数据...
2025-06-08 06:31:20,077 - __main__ - INFO - 开始收集指标数据...
2025-06-08 06:31:49,961 - __main__ - INFO - 开始收集指标数据...
2025-06-08 06:32:20,387 - __main__ - INFO - 开始收集指标数据...
2025-06-08 06:32:49,997 - __main__ - INFO - 开始收集指标数据...
2025-06-08 06:35:42,937 - utils.db - INFO - 数据库连接池创建成功
2025-06-08 06:35:43,036 - __main__ - INFO - 开始收集指标数据...
2025-06-08 09:32:50,276 - analyzer.latency_analyzer - INFO - 延时分析完成，匹配次数: 3588
2025-06-08 09:32:50,375 - analyzer.kline_analyzer - INFO - 分析BTCUSDT的K线数据...
2025-06-08 09:32:50,625 - analyzer.kline_analyzer - INFO - BTCUSDT分析完成，发现3个连续相同K线序列
2025-06-08 09:32:50,625 - analyzer.kline_analyzer - INFO - 分析ETHUSDT的K线数据...
2025-06-08 09:32:50,794 - analyzer.kline_analyzer - INFO - ETHUSDT分析完成，发现0个连续相同K线序列
2025-06-08 09:32:50,891 - analyzer.depth_analyzer - WARNING - 没有找到BTCUSDT的深度匹配数据
2025-06-08 09:32:50,896 - analyzer.depth_analyzer - WARNING - 没有找到ETHUSDT的深度匹配数据
2025-06-08 09:32:50,896 - __main__ - ERROR - 收集指标数据失败: object dict can't be used in 'await' expression
2025-06-08 09:33:19,770 - __main__ - INFO - 开始收集指标数据...
2025-06-08 09:33:50,412 - __main__ - INFO - 开始收集指标数据...
2025-06-08 09:34:19,805 - __main__ - INFO - 开始收集指标数据...
2025-06-08 09:34:50,042 - __main__ - INFO - 开始收集指标数据...
2025-06-08 09:35:20,246 - __main__ - INFO - 开始收集指标数据...
2025-06-08 09:35:50,059 - __main__ - INFO - 开始收集指标数据...
2025-06-08 09:36:20,325 - __main__ - INFO - 开始收集指标数据...
2025-06-08 09:37:18,095 - utils.db - INFO - 数据库连接池创建成功
2025-06-08 09:37:18,136 - __main__ - INFO - 开始收集指标数据...
2025-06-08 17:25:42,368 - analyzer.latency_analyzer - INFO - 延时分析完成，匹配次数: 3592
2025-06-08 17:25:42,495 - analyzer.kline_analyzer - INFO - 分析BTCUSDT的K线数据...
2025-06-08 17:25:42,694 - analyzer.kline_analyzer - INFO - BTCUSDT分析完成，发现3个连续相同K线序列
2025-06-08 17:25:42,695 - analyzer.kline_analyzer - INFO - 分析ETHUSDT的K线数据...
2025-06-08 17:25:42,753 - analyzer.kline_analyzer - INFO - ETHUSDT分析完成，发现0个连续相同K线序列
2025-06-08 17:25:42,788 - analyzer.depth_analyzer - WARNING - 没有找到BTCUSDT的深度匹配数据
2025-06-08 17:25:42,792 - analyzer.depth_analyzer - WARNING - 没有找到ETHUSDT的深度匹配数据
2025-06-08 17:25:42,793 - __main__ - ERROR - 收集指标数据失败: object dict can't be used in 'await' expression
2025-06-08 17:25:49,796 - __main__ - INFO - 开始收集指标数据...
2025-06-08 17:26:19,798 - __main__ - INFO - 开始收集指标数据...
2025-06-08 17:26:49,781 - __main__ - INFO - 开始收集指标数据...
2025-06-08 17:27:19,781 - __main__ - INFO - 开始收集指标数据...
2025-06-08 17:27:50,176 - __main__ - INFO - 开始收集指标数据...
2025-06-08 17:28:19,830 - __main__ - INFO - 开始收集指标数据...
2025-06-08 17:28:50,288 - __main__ - INFO - 开始收集指标数据...
2025-06-08 17:29:20,496 - __main__ - INFO - 开始收集指标数据...
2025-06-08 17:29:50,581 - __main__ - INFO - 开始收集指标数据...
2025-06-08 17:30:40,456 - utils.db - INFO - 数据库连接池创建成功
2025-06-08 17:30:40,521 - __main__ - INFO - 开始收集指标数据...
2025-06-09 01:29:32,189 - analyzer.latency_analyzer - INFO - 延时分析完成，匹配次数: 3590
2025-06-09 01:29:32,418 - analyzer.kline_analyzer - INFO - 分析BTCUSDT的K线数据...
2025-06-09 01:29:32,789 - analyzer.kline_analyzer - INFO - BTCUSDT分析完成，发现0个连续相同K线序列
2025-06-09 01:29:32,789 - analyzer.kline_analyzer - INFO - 分析ETHUSDT的K线数据...
2025-06-09 01:29:32,898 - analyzer.kline_analyzer - INFO - ETHUSDT分析完成，发现1个连续相同K线序列
2025-06-09 01:29:32,924 - analyzer.depth_analyzer - WARNING - 没有找到BTCUSDT的深度匹配数据
2025-06-09 01:29:32,928 - analyzer.depth_analyzer - WARNING - 没有找到ETHUSDT的深度匹配数据
2025-06-09 01:29:32,929 - __main__ - ERROR - 收集指标数据失败: object dict can't be used in 'await' expression
2025-06-09 01:29:49,783 - __main__ - INFO - 开始收集指标数据...
2025-06-09 01:30:21,636 - __main__ - INFO - 开始收集指标数据...
2025-06-09 01:30:50,015 - __main__ - INFO - 开始收集指标数据...
2025-06-09 01:31:21,724 - __main__ - INFO - 开始收集指标数据...
2025-06-09 01:31:50,387 - __main__ - INFO - 开始收集指标数据...
2025-06-09 01:32:20,768 - __main__ - INFO - 开始收集指标数据...
2025-06-09 01:33:19,930 - utils.db - INFO - 数据库连接池创建成功
2025-06-09 01:33:20,022 - __main__ - INFO - 开始收集指标数据...
2025-06-09 09:02:34,830 - analyzer.latency_analyzer - INFO - 延时分析完成，匹配次数: 3596
2025-06-09 09:02:35,184 - analyzer.kline_analyzer - INFO - 分析BTCUSDT的K线数据...
2025-06-09 09:02:44,912 - analyzer.kline_analyzer - INFO - BTCUSDT分析完成，发现1341个连续相同K线序列
2025-06-09 09:02:44,914 - analyzer.kline_analyzer - INFO - 分析ETHUSDT的K线数据...
2025-06-09 09:02:51,178 - analyzer.kline_analyzer - INFO - ETHUSDT分析完成，发现1176个连续相同K线序列
2025-06-09 09:02:51,343 - analyzer.depth_analyzer - WARNING - 没有找到BTCUSDT的深度匹配数据
2025-06-09 09:02:51,433 - analyzer.depth_analyzer - WARNING - 没有找到ETHUSDT的深度匹配数据
2025-06-09 09:02:51,456 - __main__ - ERROR - 收集指标数据失败: object dict can't be used in 'await' expression
2025-06-09 09:03:19,813 - __main__ - INFO - 开始收集指标数据...
2025-06-09 09:03:49,772 - __main__ - INFO - 开始收集指标数据...
2025-06-09 09:04:19,796 - __main__ - INFO - 开始收集指标数据...
2025-06-09 09:04:50,197 - __main__ - INFO - 开始收集指标数据...
2025-06-09 09:05:20,469 - __main__ - INFO - 开始收集指标数据...
2025-06-09 09:05:49,818 - __main__ - INFO - 开始收集指标数据...
2025-06-09 09:06:41,399 - utils.db - INFO - 数据库连接池创建成功
2025-06-09 09:06:41,473 - __main__ - INFO - 开始收集指标数据...
2025-06-09 20:44:54,183 - analyzer.latency_analyzer - INFO - 延时分析完成，匹配次数: 3590
2025-06-09 20:44:54,548 - analyzer.kline_analyzer - INFO - 分析BTCUSDT的K线数据...
2025-06-09 20:45:06,172 - analyzer.kline_analyzer - INFO - BTCUSDT分析完成，发现1296个连续相同K线序列
2025-06-09 20:45:06,274 - analyzer.kline_analyzer - INFO - 分析ETHUSDT的K线数据...
2025-06-09 20:45:07,065 - analyzer.kline_analyzer - INFO - ETHUSDT分析完成，发现1024个连续相同K线序列
2025-06-09 20:45:07,384 - analyzer.depth_analyzer - WARNING - 没有找到BTCUSDT的深度匹配数据
2025-06-09 20:45:07,570 - analyzer.depth_analyzer - WARNING - 没有找到ETHUSDT的深度匹配数据
2025-06-09 20:45:07,612 - __main__ - ERROR - 收集指标数据失败: object dict can't be used in 'await' expression
2025-06-09 20:45:19,809 - __main__ - INFO - 开始收集指标数据...
2025-06-09 20:45:49,781 - __main__ - INFO - 开始收集指标数据...
2025-06-09 20:46:20,918 - __main__ - INFO - 开始收集指标数据...
2025-06-09 20:46:51,252 - __main__ - INFO - 开始收集指标数据...
2025-06-09 20:47:19,790 - __main__ - INFO - 开始收集指标数据...
2025-06-09 20:47:49,955 - __main__ - INFO - 开始收集指标数据...
2025-06-09 20:48:24,673 - utils.db - INFO - 数据库连接池创建成功
2025-06-09 20:48:24,734 - __main__ - INFO - 开始收集指标数据...
2025-06-10 09:03:46,994 - analyzer.latency_analyzer - INFO - 延时分析完成，匹配次数: 3593
2025-06-10 09:03:47,648 - analyzer.kline_analyzer - INFO - 分析BTCUSDT的K线数据...
2025-06-10 09:03:48,987 - analyzer.kline_analyzer - INFO - BTCUSDT分析完成，发现3个连续相同K线序列
2025-06-10 09:03:48,987 - analyzer.kline_analyzer - INFO - 分析ETHUSDT的K线数据...
2025-06-10 09:03:49,351 - analyzer.kline_analyzer - INFO - ETHUSDT分析完成，发现8个连续相同K线序列
2025-06-10 09:03:49,831 - analyzer.depth_analyzer - WARNING - 没有找到BTCUSDT的深度匹配数据
2025-06-10 09:03:50,333 - analyzer.depth_analyzer - WARNING - 没有找到ETHUSDT的深度匹配数据
2025-06-10 09:03:50,333 - __main__ - ERROR - 收集指标数据失败: object dict can't be used in 'await' expression
2025-06-10 09:04:19,863 - __main__ - INFO - 开始收集指标数据...
2025-06-10 09:04:49,769 - __main__ - INFO - 开始收集指标数据...
2025-06-10 09:05:19,775 - __main__ - INFO - 开始收集指标数据...
2025-06-10 09:05:49,774 - __main__ - INFO - 开始收集指标数据...
2025-06-10 09:06:19,788 - __main__ - INFO - 开始收集指标数据...
2025-06-10 09:07:13,049 - utils.db - INFO - 数据库连接池创建成功
2025-06-10 09:07:13,110 - __main__ - INFO - 开始收集指标数据...
2025-06-10 09:30:57,956 - utils.db - ERROR - 创建数据库连接池失败: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 795, in open_connection
    self.sock.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/project/WS_DATA_ALL/prometheus_exporter.py", line 12, in <module>
    from analyzer import (
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/__init__.py", line 9, in <module>
    from .latency_analyzer import LatencyAnalyzer
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/latency_analyzer.py", line 11, in <module>
    from utils.db import db_manager
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 86, in <module>
    db_manager = DatabaseManager()
                 ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 17, in __init__
    self._create_connection_pool()
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 22, in _create_connection_pool
    self.connection_pool = pooling.MySQLConnectionPool(
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 490, in __init__
    self.add_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 613, in add_connection
    cnx = connect(**self._cnx_config)  # type: ignore[assignment]
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 323, in connect
    return MySQLConnection(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 185, in __init__
    self.connect(**kwargs)
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/abstracts.py", line 1605, in connect
    self._open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 411, in _open_connection
    raise err
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 382, in _open_connection
    self._socket.open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 806, in open_connection
    raise InterfaceError(
mysql.connector.errors.InterfaceError: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
2025-06-10 09:31:10,347 - utils.db - ERROR - 创建数据库连接池失败: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 795, in open_connection
    self.sock.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/project/WS_DATA_ALL/prometheus_exporter.py", line 12, in <module>
    from analyzer import (
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/__init__.py", line 9, in <module>
    from .latency_analyzer import LatencyAnalyzer
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/latency_analyzer.py", line 11, in <module>
    from utils.db import db_manager
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 86, in <module>
    db_manager = DatabaseManager()
                 ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 17, in __init__
    self._create_connection_pool()
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 22, in _create_connection_pool
    self.connection_pool = pooling.MySQLConnectionPool(
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 490, in __init__
    self.add_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 613, in add_connection
    cnx = connect(**self._cnx_config)  # type: ignore[assignment]
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 323, in connect
    return MySQLConnection(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 185, in __init__
    self.connect(**kwargs)
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/abstracts.py", line 1605, in connect
    self._open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 411, in _open_connection
    raise err
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 382, in _open_connection
    self._socket.open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 806, in open_connection
    raise InterfaceError(
mysql.connector.errors.InterfaceError: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
2025-06-10 09:31:21,560 - utils.db - ERROR - 创建数据库连接池失败: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 795, in open_connection
    self.sock.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/project/WS_DATA_ALL/prometheus_exporter.py", line 12, in <module>
    from analyzer import (
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/__init__.py", line 9, in <module>
    from .latency_analyzer import LatencyAnalyzer
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/latency_analyzer.py", line 11, in <module>
    from utils.db import db_manager
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 86, in <module>
    db_manager = DatabaseManager()
                 ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 17, in __init__
    self._create_connection_pool()
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 22, in _create_connection_pool
    self.connection_pool = pooling.MySQLConnectionPool(
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 490, in __init__
    self.add_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 613, in add_connection
    cnx = connect(**self._cnx_config)  # type: ignore[assignment]
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 323, in connect
    return MySQLConnection(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 185, in __init__
    self.connect(**kwargs)
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/abstracts.py", line 1605, in connect
    self._open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 411, in _open_connection
    raise err
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 382, in _open_connection
    self._socket.open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 806, in open_connection
    raise InterfaceError(
mysql.connector.errors.InterfaceError: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
2025-06-10 09:31:32,536 - utils.db - ERROR - 创建数据库连接池失败: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 795, in open_connection
    self.sock.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/project/WS_DATA_ALL/prometheus_exporter.py", line 12, in <module>
    from analyzer import (
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/__init__.py", line 9, in <module>
    from .latency_analyzer import LatencyAnalyzer
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/latency_analyzer.py", line 11, in <module>
    from utils.db import db_manager
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 86, in <module>
    db_manager = DatabaseManager()
                 ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 17, in __init__
    self._create_connection_pool()
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 22, in _create_connection_pool
    self.connection_pool = pooling.MySQLConnectionPool(
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 490, in __init__
    self.add_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 613, in add_connection
    cnx = connect(**self._cnx_config)  # type: ignore[assignment]
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 323, in connect
    return MySQLConnection(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 185, in __init__
    self.connect(**kwargs)
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/abstracts.py", line 1605, in connect
    self._open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 411, in _open_connection
    raise err
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 382, in _open_connection
    self._socket.open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 806, in open_connection
    raise InterfaceError(
mysql.connector.errors.InterfaceError: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
2025-06-10 09:31:43,527 - utils.db - ERROR - 创建数据库连接池失败: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 795, in open_connection
    self.sock.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/project/WS_DATA_ALL/prometheus_exporter.py", line 12, in <module>
    from analyzer import (
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/__init__.py", line 9, in <module>
    from .latency_analyzer import LatencyAnalyzer
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/latency_analyzer.py", line 11, in <module>
    from utils.db import db_manager
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 86, in <module>
    db_manager = DatabaseManager()
                 ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 17, in __init__
    self._create_connection_pool()
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 22, in _create_connection_pool
    self.connection_pool = pooling.MySQLConnectionPool(
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 490, in __init__
    self.add_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 613, in add_connection
    cnx = connect(**self._cnx_config)  # type: ignore[assignment]
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 323, in connect
    return MySQLConnection(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 185, in __init__
    self.connect(**kwargs)
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/abstracts.py", line 1605, in connect
    self._open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 411, in _open_connection
    raise err
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 382, in _open_connection
    self._socket.open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 806, in open_connection
    raise InterfaceError(
mysql.connector.errors.InterfaceError: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
2025-06-10 09:31:55,263 - utils.db - ERROR - 创建数据库连接池失败: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 795, in open_connection
    self.sock.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/project/WS_DATA_ALL/prometheus_exporter.py", line 12, in <module>
    from analyzer import (
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/__init__.py", line 9, in <module>
    from .latency_analyzer import LatencyAnalyzer
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/latency_analyzer.py", line 11, in <module>
    from utils.db import db_manager
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 86, in <module>
    db_manager = DatabaseManager()
                 ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 17, in __init__
    self._create_connection_pool()
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 22, in _create_connection_pool
    self.connection_pool = pooling.MySQLConnectionPool(
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 490, in __init__
    self.add_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 613, in add_connection
    cnx = connect(**self._cnx_config)  # type: ignore[assignment]
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 323, in connect
    return MySQLConnection(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 185, in __init__
    self.connect(**kwargs)
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/abstracts.py", line 1605, in connect
    self._open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 411, in _open_connection
    raise err
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 382, in _open_connection
    self._socket.open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 806, in open_connection
    raise InterfaceError(
mysql.connector.errors.InterfaceError: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
2025-06-10 09:32:06,675 - utils.db - ERROR - 创建数据库连接池失败: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 795, in open_connection
    self.sock.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/project/WS_DATA_ALL/prometheus_exporter.py", line 12, in <module>
    from analyzer import (
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/__init__.py", line 9, in <module>
    from .latency_analyzer import LatencyAnalyzer
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/latency_analyzer.py", line 11, in <module>
    from utils.db import db_manager
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 86, in <module>
    db_manager = DatabaseManager()
                 ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 17, in __init__
    self._create_connection_pool()
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 22, in _create_connection_pool
    self.connection_pool = pooling.MySQLConnectionPool(
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 490, in __init__
    self.add_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 613, in add_connection
    cnx = connect(**self._cnx_config)  # type: ignore[assignment]
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 323, in connect
    return MySQLConnection(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 185, in __init__
    self.connect(**kwargs)
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/abstracts.py", line 1605, in connect
    self._open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 411, in _open_connection
    raise err
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 382, in _open_connection
    self._socket.open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 806, in open_connection
    raise InterfaceError(
mysql.connector.errors.InterfaceError: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
2025-06-10 09:32:17,931 - utils.db - ERROR - 创建数据库连接池失败: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 795, in open_connection
    self.sock.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/project/WS_DATA_ALL/prometheus_exporter.py", line 12, in <module>
    from analyzer import (
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/__init__.py", line 9, in <module>
    from .latency_analyzer import LatencyAnalyzer
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/latency_analyzer.py", line 11, in <module>
    from utils.db import db_manager
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 86, in <module>
    db_manager = DatabaseManager()
                 ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 17, in __init__
    self._create_connection_pool()
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 22, in _create_connection_pool
    self.connection_pool = pooling.MySQLConnectionPool(
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 490, in __init__
    self.add_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 613, in add_connection
    cnx = connect(**self._cnx_config)  # type: ignore[assignment]
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 323, in connect
    return MySQLConnection(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 185, in __init__
    self.connect(**kwargs)
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/abstracts.py", line 1605, in connect
    self._open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 411, in _open_connection
    raise err
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 382, in _open_connection
    self._socket.open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 806, in open_connection
    raise InterfaceError(
mysql.connector.errors.InterfaceError: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
2025-06-10 09:32:29,016 - utils.db - ERROR - 创建数据库连接池失败: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 795, in open_connection
    self.sock.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/project/WS_DATA_ALL/prometheus_exporter.py", line 12, in <module>
    from analyzer import (
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/__init__.py", line 9, in <module>
    from .latency_analyzer import LatencyAnalyzer
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/latency_analyzer.py", line 11, in <module>
    from utils.db import db_manager
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 86, in <module>
    db_manager = DatabaseManager()
                 ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 17, in __init__
    self._create_connection_pool()
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 22, in _create_connection_pool
    self.connection_pool = pooling.MySQLConnectionPool(
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 490, in __init__
    self.add_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 613, in add_connection
    cnx = connect(**self._cnx_config)  # type: ignore[assignment]
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 323, in connect
    return MySQLConnection(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 185, in __init__
    self.connect(**kwargs)
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/abstracts.py", line 1605, in connect
    self._open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 411, in _open_connection
    raise err
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 382, in _open_connection
    self._socket.open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 806, in open_connection
    raise InterfaceError(
mysql.connector.errors.InterfaceError: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
2025-06-10 09:32:40,093 - utils.db - ERROR - 创建数据库连接池失败: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 795, in open_connection
    self.sock.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/project/WS_DATA_ALL/prometheus_exporter.py", line 12, in <module>
    from analyzer import (
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/__init__.py", line 9, in <module>
    from .latency_analyzer import LatencyAnalyzer
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/latency_analyzer.py", line 11, in <module>
    from utils.db import db_manager
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 86, in <module>
    db_manager = DatabaseManager()
                 ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 17, in __init__
    self._create_connection_pool()
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 22, in _create_connection_pool
    self.connection_pool = pooling.MySQLConnectionPool(
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 490, in __init__
    self.add_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 613, in add_connection
    cnx = connect(**self._cnx_config)  # type: ignore[assignment]
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 323, in connect
    return MySQLConnection(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 185, in __init__
    self.connect(**kwargs)
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/abstracts.py", line 1605, in connect
    self._open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 411, in _open_connection
    raise err
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 382, in _open_connection
    self._socket.open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 806, in open_connection
    raise InterfaceError(
mysql.connector.errors.InterfaceError: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
2025-06-10 09:32:51,043 - utils.db - ERROR - 创建数据库连接池失败: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 795, in open_connection
    self.sock.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/project/WS_DATA_ALL/prometheus_exporter.py", line 12, in <module>
    from analyzer import (
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/__init__.py", line 9, in <module>
    from .latency_analyzer import LatencyAnalyzer
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/latency_analyzer.py", line 11, in <module>
    from utils.db import db_manager
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 86, in <module>
    db_manager = DatabaseManager()
                 ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 17, in __init__
    self._create_connection_pool()
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 22, in _create_connection_pool
    self.connection_pool = pooling.MySQLConnectionPool(
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 490, in __init__
    self.add_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 613, in add_connection
    cnx = connect(**self._cnx_config)  # type: ignore[assignment]
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 323, in connect
    return MySQLConnection(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 185, in __init__
    self.connect(**kwargs)
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/abstracts.py", line 1605, in connect
    self._open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 411, in _open_connection
    raise err
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 382, in _open_connection
    self._socket.open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 806, in open_connection
    raise InterfaceError(
mysql.connector.errors.InterfaceError: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
2025-06-10 09:33:02,468 - utils.db - ERROR - 创建数据库连接池失败: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 795, in open_connection
    self.sock.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/project/WS_DATA_ALL/prometheus_exporter.py", line 12, in <module>
    from analyzer import (
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/__init__.py", line 9, in <module>
    from .latency_analyzer import LatencyAnalyzer
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/latency_analyzer.py", line 11, in <module>
    from utils.db import db_manager
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 86, in <module>
    db_manager = DatabaseManager()
                 ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 17, in __init__
    self._create_connection_pool()
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 22, in _create_connection_pool
    self.connection_pool = pooling.MySQLConnectionPool(
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 490, in __init__
    self.add_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 613, in add_connection
    cnx = connect(**self._cnx_config)  # type: ignore[assignment]
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 323, in connect
    return MySQLConnection(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 185, in __init__
    self.connect(**kwargs)
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/abstracts.py", line 1605, in connect
    self._open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 411, in _open_connection
    raise err
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 382, in _open_connection
    self._socket.open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 806, in open_connection
    raise InterfaceError(
mysql.connector.errors.InterfaceError: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
2025-06-10 09:33:14,138 - utils.db - ERROR - 创建数据库连接池失败: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 795, in open_connection
    self.sock.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/project/WS_DATA_ALL/prometheus_exporter.py", line 12, in <module>
    from analyzer import (
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/__init__.py", line 9, in <module>
    from .latency_analyzer import LatencyAnalyzer
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/latency_analyzer.py", line 11, in <module>
    from utils.db import db_manager
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 86, in <module>
    db_manager = DatabaseManager()
                 ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 17, in __init__
    self._create_connection_pool()
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 22, in _create_connection_pool
    self.connection_pool = pooling.MySQLConnectionPool(
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 490, in __init__
    self.add_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 613, in add_connection
    cnx = connect(**self._cnx_config)  # type: ignore[assignment]
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 323, in connect
    return MySQLConnection(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 185, in __init__
    self.connect(**kwargs)
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/abstracts.py", line 1605, in connect
    self._open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 411, in _open_connection
    raise err
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 382, in _open_connection
    self._socket.open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 806, in open_connection
    raise InterfaceError(
mysql.connector.errors.InterfaceError: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
2025-06-10 09:33:25,368 - utils.db - ERROR - 创建数据库连接池失败: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 795, in open_connection
    self.sock.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/project/WS_DATA_ALL/prometheus_exporter.py", line 12, in <module>
    from analyzer import (
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/__init__.py", line 9, in <module>
    from .latency_analyzer import LatencyAnalyzer
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/latency_analyzer.py", line 11, in <module>
    from utils.db import db_manager
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 86, in <module>
    db_manager = DatabaseManager()
                 ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 17, in __init__
    self._create_connection_pool()
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 22, in _create_connection_pool
    self.connection_pool = pooling.MySQLConnectionPool(
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 490, in __init__
    self.add_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 613, in add_connection
    cnx = connect(**self._cnx_config)  # type: ignore[assignment]
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 323, in connect
    return MySQLConnection(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 185, in __init__
    self.connect(**kwargs)
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/abstracts.py", line 1605, in connect
    self._open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 411, in _open_connection
    raise err
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 382, in _open_connection
    self._socket.open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 806, in open_connection
    raise InterfaceError(
mysql.connector.errors.InterfaceError: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
2025-06-10 09:33:37,075 - utils.db - ERROR - 创建数据库连接池失败: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 795, in open_connection
    self.sock.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/project/WS_DATA_ALL/prometheus_exporter.py", line 12, in <module>
    from analyzer import (
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/__init__.py", line 9, in <module>
    from .latency_analyzer import LatencyAnalyzer
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/latency_analyzer.py", line 11, in <module>
    from utils.db import db_manager
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 86, in <module>
    db_manager = DatabaseManager()
                 ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 17, in __init__
    self._create_connection_pool()
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 22, in _create_connection_pool
    self.connection_pool = pooling.MySQLConnectionPool(
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 490, in __init__
    self.add_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 613, in add_connection
    cnx = connect(**self._cnx_config)  # type: ignore[assignment]
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 323, in connect
    return MySQLConnection(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 185, in __init__
    self.connect(**kwargs)
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/abstracts.py", line 1605, in connect
    self._open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 411, in _open_connection
    raise err
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 382, in _open_connection
    self._socket.open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 806, in open_connection
    raise InterfaceError(
mysql.connector.errors.InterfaceError: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
2025-06-10 09:33:49,354 - utils.db - ERROR - 创建数据库连接池失败: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 795, in open_connection
    self.sock.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/project/WS_DATA_ALL/prometheus_exporter.py", line 12, in <module>
    from analyzer import (
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/__init__.py", line 9, in <module>
    from .latency_analyzer import LatencyAnalyzer
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/latency_analyzer.py", line 11, in <module>
    from utils.db import db_manager
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 86, in <module>
    db_manager = DatabaseManager()
                 ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 17, in __init__
    self._create_connection_pool()
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 22, in _create_connection_pool
    self.connection_pool = pooling.MySQLConnectionPool(
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 490, in __init__
    self.add_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 613, in add_connection
    cnx = connect(**self._cnx_config)  # type: ignore[assignment]
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 323, in connect
    return MySQLConnection(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 185, in __init__
    self.connect(**kwargs)
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/abstracts.py", line 1605, in connect
    self._open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 411, in _open_connection
    raise err
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 382, in _open_connection
    self._socket.open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 806, in open_connection
    raise InterfaceError(
mysql.connector.errors.InterfaceError: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
2025-06-10 09:34:00,633 - utils.db - ERROR - 创建数据库连接池失败: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 795, in open_connection
    self.sock.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/project/WS_DATA_ALL/prometheus_exporter.py", line 12, in <module>
    from analyzer import (
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/__init__.py", line 9, in <module>
    from .latency_analyzer import LatencyAnalyzer
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/latency_analyzer.py", line 11, in <module>
    from utils.db import db_manager
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 86, in <module>
    db_manager = DatabaseManager()
                 ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 17, in __init__
    self._create_connection_pool()
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 22, in _create_connection_pool
    self.connection_pool = pooling.MySQLConnectionPool(
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 490, in __init__
    self.add_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 613, in add_connection
    cnx = connect(**self._cnx_config)  # type: ignore[assignment]
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 323, in connect
    return MySQLConnection(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 185, in __init__
    self.connect(**kwargs)
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/abstracts.py", line 1605, in connect
    self._open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 411, in _open_connection
    raise err
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 382, in _open_connection
    self._socket.open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 806, in open_connection
    raise InterfaceError(
mysql.connector.errors.InterfaceError: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
2025-06-10 09:34:11,836 - utils.db - ERROR - 创建数据库连接池失败: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 795, in open_connection
    self.sock.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/project/WS_DATA_ALL/prometheus_exporter.py", line 12, in <module>
    from analyzer import (
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/__init__.py", line 9, in <module>
    from .latency_analyzer import LatencyAnalyzer
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/latency_analyzer.py", line 11, in <module>
    from utils.db import db_manager
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 86, in <module>
    db_manager = DatabaseManager()
                 ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 17, in __init__
    self._create_connection_pool()
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 22, in _create_connection_pool
    self.connection_pool = pooling.MySQLConnectionPool(
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 490, in __init__
    self.add_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 613, in add_connection
    cnx = connect(**self._cnx_config)  # type: ignore[assignment]
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 323, in connect
    return MySQLConnection(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 185, in __init__
    self.connect(**kwargs)
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/abstracts.py", line 1605, in connect
    self._open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 411, in _open_connection
    raise err
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 382, in _open_connection
    self._socket.open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 806, in open_connection
    raise InterfaceError(
mysql.connector.errors.InterfaceError: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
2025-06-10 09:34:23,139 - utils.db - ERROR - 创建数据库连接池失败: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 795, in open_connection
    self.sock.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/project/WS_DATA_ALL/prometheus_exporter.py", line 12, in <module>
    from analyzer import (
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/__init__.py", line 9, in <module>
    from .latency_analyzer import LatencyAnalyzer
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/latency_analyzer.py", line 11, in <module>
    from utils.db import db_manager
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 86, in <module>
    db_manager = DatabaseManager()
                 ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 17, in __init__
    self._create_connection_pool()
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 22, in _create_connection_pool
    self.connection_pool = pooling.MySQLConnectionPool(
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 490, in __init__
    self.add_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 613, in add_connection
    cnx = connect(**self._cnx_config)  # type: ignore[assignment]
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 323, in connect
    return MySQLConnection(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 185, in __init__
    self.connect(**kwargs)
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/abstracts.py", line 1605, in connect
    self._open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 411, in _open_connection
    raise err
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 382, in _open_connection
    self._socket.open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 806, in open_connection
    raise InterfaceError(
mysql.connector.errors.InterfaceError: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
2025-06-10 09:34:34,455 - utils.db - ERROR - 创建数据库连接池失败: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 795, in open_connection
    self.sock.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/project/WS_DATA_ALL/prometheus_exporter.py", line 12, in <module>
    from analyzer import (
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/__init__.py", line 9, in <module>
    from .latency_analyzer import LatencyAnalyzer
  File "/home/<USER>/project/WS_DATA_ALL/analyzer/latency_analyzer.py", line 11, in <module>
    from utils.db import db_manager
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 86, in <module>
    db_manager = DatabaseManager()
                 ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 17, in __init__
    self._create_connection_pool()
  File "/home/<USER>/project/WS_DATA_ALL/utils/db.py", line 22, in _create_connection_pool
    self.connection_pool = pooling.MySQLConnectionPool(
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 490, in __init__
    self.add_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 613, in add_connection
    cnx = connect(**self._cnx_config)  # type: ignore[assignment]
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/pooling.py", line 323, in connect
    return MySQLConnection(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 185, in __init__
    self.connect(**kwargs)
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/abstracts.py", line 1605, in connect
    self._open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 411, in _open_connection
    raise err
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/connection.py", line 382, in _open_connection
    self._socket.open_connection()
  File "/home/<USER>/anaconda3/lib/python3.12/site-packages/mysql/connector/network.py", line 806, in open_connection
    raise InterfaceError(
mysql.connector.errors.InterfaceError: 2003: Can't connect to MySQL server on 'localhost:3306' (Errno 111: Connection refused)
2025-06-10 09:34:49,262 - utils.db - INFO - 数据库连接池创建成功
2025-06-10 09:34:49,330 - __main__ - INFO - 开始收集指标数据...
2025-06-10 14:49:01,260 - utils.db - INFO - 数据库连接池创建成功
2025-06-10 14:49:01,264 - __main__ - INFO - 开始收集指标数据...
2025-06-10 14:50:45,889 - utils.db - INFO - 数据库连接池创建成功
2025-06-10 14:50:45,892 - __main__ - INFO - 开始收集指标数据...
2025-06-10 14:51:04,840 - utils.db - INFO - 数据库连接池创建成功
2025-06-10 14:51:04,843 - __main__ - INFO - 开始收集指标数据...
