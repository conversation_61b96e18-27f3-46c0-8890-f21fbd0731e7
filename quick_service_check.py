#!/usr/bin/env python3
"""
快速检查延时计算服务状态
"""

import subprocess
import mysql.connector
from datetime import datetime, timedelta
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_process():
    """检查进程状态"""
    try:
        result = subprocess.run(['pgrep', '-f', 'ethusdt_realtime_processor'], 
                              capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0 and result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            logger.info(f"✅ 延时处理器正在运行，PID: {', '.join(pids)}")
            return True
        else:
            logger.warning("❌ 延时处理器进程未运行")
            return False
    except Exception as e:
        logger.error(f"❌ 检查进程失败: {e}")
        return False

def check_database_activity():
    """检查数据库活动"""
    try:
        config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'ethusdt_latency_db'
        }
        
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        # 检查最近10分钟的活动
        cursor.execute("""
            SELECT 
                COUNT(*) as recent_matches,
                MAX(created_at) as latest_time
            FROM ethusdt_latency_matches 
            WHERE created_at >= NOW() - INTERVAL 10 MINUTE
        """)
        
        result = cursor.fetchone()
        recent_matches, latest_time = result
        
        if recent_matches > 0:
            time_ago = (datetime.now() - latest_time).total_seconds() / 60
            logger.info(f"✅ 数据库活跃: 最近10分钟有{recent_matches}个匹配")
            logger.info(f"   最新记录: {latest_time} ({time_ago:.1f}分钟前)")
            db_active = True
        else:
            logger.warning("❌ 数据库无活动: 最近10分钟无新匹配")
            db_active = False
        
        cursor.close()
        connection.close()
        
        return db_active
        
    except Exception as e:
        logger.error(f"❌ 检查数据库活动失败: {e}")
        return False

def check_log_file():
    """检查日志文件"""
    log_file = "/home/<USER>/project/WS_DATA_ALL/ethusdt_realtime_processor.log"
    
    try:
        if os.path.exists(log_file):
            stat = os.stat(log_file)
            mod_time = datetime.fromtimestamp(stat.st_mtime)
            time_ago = (datetime.now() - mod_time).total_seconds() / 60
            
            if time_ago < 5:
                logger.info(f"✅ 日志文件活跃: {time_ago:.1f}分钟前更新")
                return True
            else:
                logger.warning(f"⚠️  日志文件较旧: {time_ago:.1f}分钟前更新")
                return False
        else:
            logger.warning("❌ 日志文件不存在")
            return False
            
    except Exception as e:
        logger.error(f"❌ 检查日志文件失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 快速延时计算服务状态检查")
    print("=" * 40)
    
    # 检查各项状态
    process_ok = check_process()
    db_ok = check_database_activity()
    log_ok = check_log_file()
    
    # 综合评估
    print("\n📋 状态总结:")
    checks = [
        ("进程运行", process_ok),
        ("数据库活动", db_ok),
        ("日志活动", log_ok)
    ]
    
    passed = 0
    for name, status in checks:
        status_icon = "✅" if status else "❌"
        print(f"   {status_icon} {name}: {'正常' if status else '异常'}")
        if status:
            passed += 1
    
    overall_score = passed / len(checks) * 100
    print(f"\n🎯 总体状态: {overall_score:.0f}% ({passed}/{len(checks)} 项正常)")
    
    if overall_score >= 67:  # 2/3通过
        print("🎉 服务状态良好")
        return True
    else:
        print("⚠️  服务状态异常，需要检查")
        return False

if __name__ == "__main__":
    main()
