#!/usr/bin/env python3
"""
验证新的存储逻辑
测试买一到买五价格字段是否正确提取和存储
"""

import mysql.connector
import json
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StorageLogicVerifier:
    """存储逻辑验证器"""

    def __init__(self):
        self.config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'depth_db'
        }

    def check_table_structure(self):
        """检查表结构"""
        try:
            connection = mysql.connector.connect(**self.config)
            cursor = connection.cursor()

            logger.info("🔍 检查表结构...")

            # 检查bitda_depth表
            cursor.execute("DESCRIBE bitda_depth")
            bitda_columns = cursor.fetchall()

            logger.info("📊 bitda_depth表字段:")
            price_fields = []
            for col in bitda_columns:
                col_name = col[0]
                if 'price_' in col_name or 'qty_' in col_name:
                    price_fields.append(col_name)
                    logger.info(f"   ✅ {col_name}: {col[1]}")

            logger.info(f"   总价格字段数: {len(price_fields)}")

            # 检查binance_depth_5表
            cursor.execute("DESCRIBE binance_depth_5")
            binance_columns = cursor.fetchall()

            logger.info("📊 binance_depth_5表字段:")
            binance_price_fields = []
            for col in binance_columns:
                col_name = col[0]
                if 'price_' in col_name or 'qty_' in col_name:
                    binance_price_fields.append(col_name)
                    logger.info(f"   ✅ {col_name}: {col[1]}")

            logger.info(f"   总价格字段数: {len(binance_price_fields)}")

            cursor.close()
            connection.close()

            return len(price_fields) >= 20, len(binance_price_fields) >= 20

        except Exception as e:
            logger.error(f"检查表结构失败: {e}")
            return False, False

    def check_recent_data(self):
        """检查最近的数据"""
        try:
            connection = mysql.connector.connect(**self.config)
            cursor = connection.cursor()

            logger.info("📊 检查最近数据...")

            # 检查bitda_depth最近数据
            cursor.execute("""
                SELECT COUNT(*) as total,
                       COUNT(bid_price_1) as with_bid1,
                       COUNT(ask_price_1) as with_ask1,
                       COUNT(bid_price_5) as with_bid5,
                       COUNT(ask_price_5) as with_ask5,
                       MAX(created_at) as latest_time
                FROM bitda_depth
                WHERE created_at >= NOW() - INTERVAL 30 MINUTE
            """)

            result = cursor.fetchone()
            if result:
                total, with_bid1, with_ask1, with_bid5, with_ask5, latest_time = result
                logger.info(f"📋 bitda_depth (最近30分钟):")
                logger.info(f"   总记录数: {total}")
                logger.info(f"   有买一价格: {with_bid1} ({with_bid1/total*100:.1f}%)" if total > 0 else "   无数据")
                logger.info(f"   有卖一价格: {with_ask1} ({with_ask1/total*100:.1f}%)" if total > 0 else "   无数据")
                logger.info(f"   有买五价格: {with_bid5} ({with_bid5/total*100:.1f}%)" if total > 0 else "   无数据")
                logger.info(f"   有卖五价格: {with_ask5} ({with_ask5/total*100:.1f}%)" if total > 0 else "   无数据")
                logger.info(f"   最新时间: {latest_time}")

            # 检查binance_depth_5最近数据
            cursor.execute("""
                SELECT COUNT(*) as total,
                       COUNT(bid_price_1) as with_bid1,
                       COUNT(ask_price_1) as with_ask1,
                       COUNT(bid_price_5) as with_bid5,
                       COUNT(ask_price_5) as with_ask5,
                       MAX(created_at) as latest_time
                FROM binance_depth_5
                WHERE created_at >= NOW() - INTERVAL 30 MINUTE
            """)

            result = cursor.fetchone()
            if result:
                total, with_bid1, with_ask1, with_bid5, with_ask5, latest_time = result
                logger.info(f"📋 binance_depth_5 (最近30分钟):")
                logger.info(f"   总记录数: {total}")
                logger.info(f"   有买一价格: {with_bid1} ({with_bid1/total*100:.1f}%)" if total > 0 else "   无数据")
                logger.info(f"   有卖一价格: {with_ask1} ({with_ask1/total*100:.1f}%)" if total > 0 else "   无数据")
                logger.info(f"   有买五价格: {with_bid5} ({with_bid5/total*100:.1f}%)" if total > 0 else "   无数据")
                logger.info(f"   有卖五价格: {with_ask5} ({with_ask5/total*100:.1f}%)" if total > 0 else "   无数据")
                logger.info(f"   最新时间: {latest_time}")

            cursor.close()
            connection.close()

        except Exception as e:
            logger.error(f"检查最近数据失败: {e}")

    def test_price_extraction(self):
        """测试价格提取逻辑"""
        try:
            connection = mysql.connector.connect(**self.config)
            cursor = connection.cursor()

            logger.info("🧪 测试价格提取逻辑...")

            # 获取一条有JSON数据的记录
            cursor.execute("""
                SELECT id, asks, bids, bid_price_1, ask_price_1, bid_price_5, ask_price_5
                FROM bitda_depth
                WHERE asks IS NOT NULL
                AND bids IS NOT NULL
                ORDER BY created_at DESC
                LIMIT 1
            """)

            result = cursor.fetchone()
            if result:
                record_id, asks_json, bids_json, stored_bid1, stored_ask1, stored_bid5, stored_ask5 = result

                logger.info(f"📊 测试记录ID: {record_id}")

                # 手动解析JSON验证
                try:
                    asks = json.loads(asks_json)
                    bids = json.loads(bids_json)

                    # 计算买一到买五
                    sorted_bids = sorted(bids, key=lambda x: float(x[0]), reverse=True)
                    sorted_asks = sorted(asks, key=lambda x: float(x[0]))

                    calculated_bid1 = float(sorted_bids[0][0]) if sorted_bids else None
                    calculated_ask1 = float(sorted_asks[0][0]) if sorted_asks else None
                    calculated_bid5 = float(sorted_bids[4][0]) if len(sorted_bids) >= 5 else None
                    calculated_ask5 = float(sorted_asks[4][0]) if len(sorted_asks) >= 5 else None

                    # 验证逻辑正确性（考虑Decimal和float的比较）
                    def compare_prices(calc, stored):
                        if calc is None and stored is None:
                            return True
                        if calc is None or stored is None:
                            return False
                        return abs(float(calc) - float(stored)) < 0.001  # 允许小数点精度误差

                    bid1_match = compare_prices(calculated_bid1, stored_bid1)
                    ask1_match = compare_prices(calculated_ask1, stored_ask1)
                    bid5_match = compare_prices(calculated_bid5, stored_bid5)
                    ask5_match = compare_prices(calculated_ask5, stored_ask5)

                    logger.info("💰 价格对比:")
                    logger.info(f"   买一价格 - 计算值: {calculated_bid1}, 存储值: {stored_bid1}, 匹配: {'✅' if bid1_match else '❌'}")
                    logger.info(f"   卖一价格 - 计算值: {calculated_ask1}, 存储值: {stored_ask1}, 匹配: {'✅' if ask1_match else '❌'}")
                    logger.info(f"   买五价格 - 计算值: {calculated_bid5}, 存储值: {stored_bid5}, 匹配: {'✅' if bid5_match else '❌'}")
                    logger.info(f"   卖五价格 - 计算值: {calculated_ask5}, 存储值: {stored_ask5}, 匹配: {'✅' if ask5_match else '❌'}")

                    if (bid1_match and ask1_match and bid5_match and ask5_match):
                        logger.info("✅ 价格提取逻辑验证通过")
                        return True
                    else:
                        logger.error("❌ 价格提取逻辑验证失败")
                        return False

                except Exception as e:
                    logger.error(f"解析JSON失败: {e}")
                    return False
            else:
                logger.warning("⚠️  未找到测试数据")
                return False

            cursor.close()
            connection.close()

        except Exception as e:
            logger.error(f"测试价格提取失败: {e}")
            return False

    def test_query_performance(self):
        """测试查询性能"""
        try:
            connection = mysql.connector.connect(**self.config)
            cursor = connection.cursor()

            logger.info("⚡ 测试查询性能...")

            # 测试价格字段查询
            start_time = datetime.now()
            cursor.execute("""
                SELECT COUNT(*) FROM bitda_depth
                WHERE symbol = 'ETHUSDT'
                AND bid_price_1 IS NOT NULL
                AND ask_price_1 IS NOT NULL
                LIMIT 1000
            """)
            result = cursor.fetchone()[0]
            end_time = datetime.now()

            query_time = (end_time - start_time).total_seconds()
            logger.info(f"📈 价格字段查询:")
            logger.info(f"   结果数量: {result}")
            logger.info(f"   查询耗时: {query_time:.3f} 秒")

            if query_time < 0.1:
                logger.info("✅ 查询性能优秀 (<0.1秒)")
            elif query_time < 1.0:
                logger.info("✅ 查询性能良好 (<1秒)")
            else:
                logger.warning("⚠️  查询性能需要优化")

            # 测试价格匹配查询
            start_time = datetime.now()
            cursor.execute("""
                SELECT COUNT(*) FROM bitda_depth
                WHERE symbol = 'ETHUSDT'
                AND bid_price_1 BETWEEN 2600 AND 2700
                AND ask_price_1 BETWEEN 2600 AND 2700
                LIMIT 1000
            """)
            result = cursor.fetchone()[0]
            end_time = datetime.now()

            match_time = (end_time - start_time).total_seconds()
            logger.info(f"📈 价格匹配查询:")
            logger.info(f"   结果数量: {result}")
            logger.info(f"   查询耗时: {match_time:.3f} 秒")

            cursor.close()
            connection.close()

            return query_time < 1.0 and match_time < 1.0

        except Exception as e:
            logger.error(f"测试查询性能失败: {e}")
            return False

    def check_data_collection_status(self):
        """检查数据采集状态"""
        try:
            connection = mysql.connector.connect(**self.config)
            cursor = connection.cursor()

            logger.info("📡 检查数据采集状态...")

            # 检查最近5分钟的数据
            cursor.execute("""
                SELECT COUNT(*) FROM bitda_depth
                WHERE created_at >= NOW() - INTERVAL 5 MINUTE
            """)
            recent_bitda = cursor.fetchone()[0]

            cursor.execute("""
                SELECT COUNT(*) FROM binance_depth_5
                WHERE created_at >= NOW() - INTERVAL 5 MINUTE
            """)
            recent_binance = cursor.fetchone()[0]

            cursor.execute("""
                SELECT COUNT(*) FROM binance_bookticker
                WHERE created_at >= NOW() - INTERVAL 5 MINUTE
            """)
            recent_bookticker = cursor.fetchone()[0]

            logger.info(f"📊 最近5分钟数据:")
            logger.info(f"   bitda_depth: {recent_bitda} 条")
            logger.info(f"   binance_depth_5: {recent_binance} 条")
            logger.info(f"   binance_bookticker: {recent_bookticker} 条")

            if recent_bitda > 0 and recent_binance > 0 and recent_bookticker > 0:
                logger.info("✅ 数据采集正常运行")
                return True
            else:
                logger.warning("⚠️  数据采集可能已停止")
                return False

            cursor.close()
            connection.close()

        except Exception as e:
            logger.error(f"检查数据采集状态失败: {e}")
            return False

    def verify_all(self):
        """执行完整验证"""
        logger.info("🔍 开始验证新的存储逻辑")
        logger.info("=" * 50)

        results = {}

        # 1. 检查表结构
        bitda_ok, binance_ok = self.check_table_structure()
        results['table_structure'] = bitda_ok and binance_ok

        # 2. 检查最近数据
        self.check_recent_data()

        # 3. 测试价格提取逻辑
        results['price_extraction'] = self.test_price_extraction()

        # 4. 测试查询性能
        results['query_performance'] = self.test_query_performance()

        # 5. 检查数据采集状态
        results['data_collection'] = self.check_data_collection_status()

        # 总结
        logger.info("=" * 50)
        logger.info("📋 验证结果总结:")

        all_passed = True
        for test_name, passed in results.items():
            status = "✅ 通过" if passed else "❌ 失败"
            logger.info(f"   {test_name}: {status}")
            if not passed:
                all_passed = False

        if all_passed:
            logger.info("🎉 所有验证通过！新的存储逻辑工作正常")
            logger.info("📋 下一步建议:")
            logger.info("   1. 重启数据采集程序以使用新存储逻辑")
            logger.info("   2. 测试延时处理器性能")
            logger.info("   3. 验证Grafana数据显示")
        else:
            logger.error("❌ 部分验证失败，需要检查和修复")

        return all_passed

def main():
    """主函数"""
    print("🔍 新存储逻辑验证工具")
    print("=" * 50)
    print("功能:")
    print("  - 检查表结构是否包含买一到买五字段")
    print("  - 验证价格提取逻辑正确性")
    print("  - 测试查询性能")
    print("  - 检查数据采集状态")
    print()

    verifier = StorageLogicVerifier()
    success = verifier.verify_all()

    if success:
        print("\n✅ 验证完成！新存储逻辑工作正常")
    else:
        print("\n❌ 验证发现问题，请检查日志")

if __name__ == "__main__":
    main()
