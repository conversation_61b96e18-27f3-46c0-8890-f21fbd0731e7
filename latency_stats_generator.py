#!/usr/bin/env python3
"""
延时统计数据生成器
生成分钟级和小时级的延时统计数据
"""

import mysql.connector
from datetime import datetime, timedelta
import json
import statistics

class LatencyStatsGenerator:
    """延时统计数据生成器"""
    
    def __init__(self):
        self.config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'ethusdt_latency_db'
        }
    
    def get_connection(self):
        """获取数据库连接"""
        return mysql.connector.connect(**self.config)
    
    def generate_minute_stats(self, target_minute: datetime = None):
        """生成指定分钟的统计数据"""
        if target_minute is None:
            # 默认处理上一分钟的数据
            target_minute = datetime.now().replace(second=0, microsecond=0) - timedelta(minutes=1)
        
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 查询该分钟内的所有延时数据
            start_time = target_minute
            end_time = target_minute + timedelta(minutes=1)
            
            query = """
            SELECT latency_ms, match_type, price_spread
            FROM ethusdt_latency_matches 
            WHERE created_at >= %s AND created_at < %s
            """
            
            cursor.execute(query, (start_time, end_time))
            records = cursor.fetchall()
            
            if not records:
                print(f"⚠️  {target_minute.strftime('%Y-%m-%d %H:%M')} 无延时数据")
                return False
            
            # 计算统计数据
            latencies = [record[0] for record in records]
            bid_matches = len([r for r in records if r[1] == 'bid'])
            ask_matches = len([r for r in records if r[1] == 'ask'])
            price_spreads = [record[2] for record in records if record[2] is not None]
            
            stats = {
                'minute_timestamp': target_minute,
                'total_matches': len(records),
                'bid_matches': bid_matches,
                'ask_matches': ask_matches,
                'avg_latency': round(statistics.mean(latencies), 2),
                'min_latency': min(latencies),
                'max_latency': max(latencies),
                'median_latency': int(statistics.median(latencies)),
                'p95_latency': int(statistics.quantiles(latencies, n=20)[18]) if len(latencies) >= 20 else max(latencies),
                'std_latency': round(statistics.stdev(latencies) if len(latencies) > 1 else 0, 2),
                'avg_price_spread': round(statistics.mean(price_spreads), 4) if price_spreads else 0,
                'data_quality': 1.0000
            }
            
            # 插入或更新分钟统计
            insert_query = """
            INSERT INTO ethusdt_latency_stats_minute 
            (minute_timestamp, total_matches, bid_matches, ask_matches, 
             avg_latency, min_latency, max_latency, median_latency, p95_latency,
             std_latency, avg_price_spread, data_quality)
            VALUES (%(minute_timestamp)s, %(total_matches)s, %(bid_matches)s, %(ask_matches)s,
                    %(avg_latency)s, %(min_latency)s, %(max_latency)s, %(median_latency)s, %(p95_latency)s,
                    %(std_latency)s, %(avg_price_spread)s, %(data_quality)s)
            ON DUPLICATE KEY UPDATE
                total_matches = VALUES(total_matches),
                bid_matches = VALUES(bid_matches),
                ask_matches = VALUES(ask_matches),
                avg_latency = VALUES(avg_latency),
                min_latency = VALUES(min_latency),
                max_latency = VALUES(max_latency),
                median_latency = VALUES(median_latency),
                p95_latency = VALUES(p95_latency),
                std_latency = VALUES(std_latency),
                avg_price_spread = VALUES(avg_price_spread),
                data_quality = VALUES(data_quality)
            """
            
            cursor.execute(insert_query, stats)
            conn.commit()
            
            print(f"✅ {target_minute.strftime('%Y-%m-%d %H:%M')} 分钟统计: {stats['total_matches']}条匹配, 平均延时{stats['avg_latency']}ms")
            
            cursor.close()
            conn.close()
            
            return True
            
        except Exception as e:
            print(f"❌ 生成分钟统计失败: {e}")
            return False
    
    def generate_hour_stats(self, target_hour: datetime = None):
        """生成指定小时的统计数据"""
        if target_hour is None:
            # 默认处理上一小时的数据
            target_hour = datetime.now().replace(minute=0, second=0, microsecond=0) - timedelta(hours=1)
        
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 查询该小时内的所有延时数据
            start_time = target_hour
            end_time = target_hour + timedelta(hours=1)
            
            query = """
            SELECT latency_ms, match_type, price_spread
            FROM ethusdt_latency_matches 
            WHERE created_at >= %s AND created_at < %s
            """
            
            cursor.execute(query, (start_time, end_time))
            records = cursor.fetchall()
            
            if not records:
                print(f"⚠️  {target_hour.strftime('%Y-%m-%d %H:00')} 无延时数据")
                return False
            
            # 计算统计数据
            latencies = [record[0] for record in records]
            bid_matches = len([r for r in records if r[1] == 'bid'])
            ask_matches = len([r for r in records if r[1] == 'ask'])
            price_spreads = [record[2] for record in records if record[2] is not None]
            
            # 计算延时分布
            distribution = self.calculate_latency_distribution(latencies)
            
            stats = {
                'hour_timestamp': target_hour,
                'total_matches': len(records),
                'bid_matches': bid_matches,
                'ask_matches': ask_matches,
                'avg_latency': round(statistics.mean(latencies), 2),
                'min_latency': min(latencies),
                'max_latency': max(latencies),
                'median_latency': int(statistics.median(latencies)),
                'p95_latency': int(statistics.quantiles(latencies, n=20)[18]) if len(latencies) >= 20 else max(latencies),
                'std_latency': round(statistics.stdev(latencies) if len(latencies) > 1 else 0, 2),
                'avg_price_spread': round(statistics.mean(price_spreads), 4) if price_spreads else 0,
                'data_quality': 1.0000,
                'latency_distribution': json.dumps(distribution)
            }
            
            # 插入或更新小时统计
            insert_query = """
            INSERT INTO ethusdt_latency_stats_hour 
            (hour_timestamp, total_matches, bid_matches, ask_matches, 
             avg_latency, min_latency, max_latency, median_latency, p95_latency,
             std_latency, avg_price_spread, data_quality, latency_distribution)
            VALUES (%(hour_timestamp)s, %(total_matches)s, %(bid_matches)s, %(ask_matches)s,
                    %(avg_latency)s, %(min_latency)s, %(max_latency)s, %(median_latency)s, %(p95_latency)s,
                    %(std_latency)s, %(avg_price_spread)s, %(data_quality)s, %(latency_distribution)s)
            ON DUPLICATE KEY UPDATE
                total_matches = VALUES(total_matches),
                bid_matches = VALUES(bid_matches),
                ask_matches = VALUES(ask_matches),
                avg_latency = VALUES(avg_latency),
                min_latency = VALUES(min_latency),
                max_latency = VALUES(max_latency),
                median_latency = VALUES(median_latency),
                p95_latency = VALUES(p95_latency),
                std_latency = VALUES(std_latency),
                avg_price_spread = VALUES(avg_price_spread),
                data_quality = VALUES(data_quality),
                latency_distribution = VALUES(latency_distribution)
            """
            
            cursor.execute(insert_query, stats)
            
            # 同时更新延时分布表
            self.update_latency_distribution(cursor, target_hour, 'hour', distribution)
            
            conn.commit()
            
            print(f"✅ {target_hour.strftime('%Y-%m-%d %H:00')} 小时统计: {stats['total_matches']}条匹配, 平均延时{stats['avg_latency']}ms")
            
            cursor.close()
            conn.close()
            
            return True
            
        except Exception as e:
            print(f"❌ 生成小时统计失败: {e}")
            return False
    
    def calculate_latency_distribution(self, latencies):
        """计算延时分布"""
        ranges = [
            (0, 50, '0-50ms'),
            (51, 100, '51-100ms'),
            (101, 200, '101-200ms'),
            (201, 500, '201-500ms'),
            (501, 1000, '501-1000ms'),
            (1001, 2000, '1001-2000ms'),
            (2001, float('inf'), '2000ms+')
        ]
        
        total = len(latencies)
        distribution = {}
        
        for min_val, max_val, label in ranges:
            count = len([l for l in latencies if min_val <= l <= max_val])
            percentage = round(count / total * 100, 2) if total > 0 else 0
            
            distribution[label] = {
                'count': count,
                'percentage': percentage,
                'range_min': min_val,
                'range_max': max_val if max_val != float('inf') else 9999
            }
        
        return distribution
    
    def update_latency_distribution(self, cursor, period_timestamp, time_period, distribution):
        """更新延时分布表"""
        try:
            for range_label, data in distribution.items():
                insert_query = """
                INSERT INTO ethusdt_latency_distribution 
                (time_period, period_timestamp, latency_range, range_min, range_max, 
                 match_count, percentage)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                    match_count = VALUES(match_count),
                    percentage = VALUES(percentage)
                """
                
                cursor.execute(insert_query, (
                    time_period, period_timestamp, range_label,
                    data['range_min'], data['range_max'],
                    data['count'], data['percentage']
                ))
                
        except Exception as e:
            print(f"❌ 更新延时分布失败: {e}")
    
    def update_realtime_status(self):
        """更新实时状态"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 计算最近1小时的统计
            query = """
            SELECT 
                COUNT(*) as total_matches,
                AVG(latency_ms) as avg_latency,
                MIN(latency_ms) as min_latency,
                MAX(latency_ms) as max_latency,
                MAX(created_at) as last_match_time
            FROM ethusdt_latency_matches 
            WHERE created_at >= NOW() - INTERVAL 1 HOUR
            """
            
            cursor.execute(query)
            result = cursor.fetchone()
            
            if result:
                total_matches, avg_latency, min_latency, max_latency, last_match_time = result
                
                # 获取最新的延时和价格
                cursor.execute("""
                    SELECT latency_ms, bitda_price, binance_price 
                    FROM ethusdt_latency_matches 
                    ORDER BY created_at DESC 
                    LIMIT 1
                """)
                latest = cursor.fetchone()
                
                current_latency = latest[0] if latest else None
                last_bitda_price = latest[1] if latest else None
                last_binance_price = latest[2] if latest else None
                
                # 计算每分钟匹配率
                match_rate = total_matches / 60 if total_matches else 0
                
                # 更新实时状态
                update_query = """
                UPDATE ethusdt_realtime_status SET
                    current_latency_ms = %s,
                    avg_latency_1h = %s,
                    max_latency_1h = %s,
                    min_latency_1h = %s,
                    total_matches_1h = %s,
                    last_match_time = %s,
                    last_bitda_price = %s,
                    last_binance_price = %s,
                    match_rate_per_minute = %s,
                    updated_at = NOW()
                WHERE id = 1
                """
                
                cursor.execute(update_query, (
                    current_latency, avg_latency, max_latency, min_latency,
                    total_matches, last_match_time, last_bitda_price,
                    last_binance_price, match_rate
                ))
                
                conn.commit()
                
                print(f"✅ 实时状态更新: 当前延时{current_latency}ms, 1小时平均{avg_latency:.2f}ms")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            print(f"❌ 更新实时状态失败: {e}")
    
    def generate_recent_stats(self, hours: int = 2):
        """生成最近几小时的统计数据"""
        print(f"📊 生成最近{hours}小时的统计数据...")
        
        current_time = datetime.now()
        
        # 生成分钟级统计
        print("📈 生成分钟级统计...")
        minute_count = 0
        for i in range(hours * 60):
            target_minute = current_time.replace(second=0, microsecond=0) - timedelta(minutes=i+1)
            if self.generate_minute_stats(target_minute):
                minute_count += 1
        
        print(f"✅ 完成 {minute_count} 个分钟统计")
        
        # 生成小时级统计
        print("📊 生成小时级统计...")
        hour_count = 0
        for i in range(hours):
            target_hour = current_time.replace(minute=0, second=0, microsecond=0) - timedelta(hours=i+1)
            if self.generate_hour_stats(target_hour):
                hour_count += 1
        
        print(f"✅ 完成 {hour_count} 个小时统计")
        
        # 更新实时状态
        self.update_realtime_status()
        
        return minute_count, hour_count

def main():
    """主函数"""
    print("📊 ETHUSDT延时统计数据生成器")
    print("=" * 50)
    
    generator = LatencyStatsGenerator()
    
    # 生成最近2小时的统计数据
    minute_count, hour_count = generator.generate_recent_stats(2)
    
    print(f"\n🎉 统计数据生成完成!")
    print(f"  - 分钟级统计: {minute_count} 个")
    print(f"  - 小时级统计: {hour_count} 个")
    print("  - 实时状态已更新")

if __name__ == "__main__":
    main()
