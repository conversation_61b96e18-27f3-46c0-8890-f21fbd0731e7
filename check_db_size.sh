#!/bin/bash

# MySQL数据库空间监控脚本
# 使用方法: bash check_db_size.sh

DB_USER="root"
DB_PASS="Mactest168"
DB_NAME="depth_db"

echo "📊 MySQL数据库存储空间监控报告"
echo "=================================="
echo "数据库: $DB_NAME"
echo "时间: $(date)"
echo ""

# 总体大小
echo "🎯 总体存储情况:"
mysql -u $DB_USER -p$DB_PASS -e "
SELECT
    ROUND(SUM(data_length + index_length) / 1024 / 1024 / 1024, 2) AS 'Total Size (GB)',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Total Size (MB)'
FROM information_schema.tables
WHERE table_schema = '$DB_NAME';
" 2>/dev/null

echo ""

# 各表详情
echo "📋 各表存储详情:"
mysql -u $DB_USER -p$DB_PASS -e "
SELECT
    table_name AS 'Table',
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)',
    table_rows AS 'Rows',
    ROUND(((data_length + index_length) / table_rows / 1024), 2) AS 'Avg KB/Row'
FROM information_schema.TABLES
WHERE table_schema = '$DB_NAME' AND table_rows > 0
ORDER BY (data_length + index_length) DESC;
" 2>/dev/null

echo ""

# 最近3天数据
echo "📈 最近3天数据增长:"
mysql -u $DB_USER -p$DB_PASS -e "
SELECT
    DATE(created_at) as 'Date',
    COUNT(*) as 'Binance Records',
    ROUND(COUNT(*) / 1000000, 2) as 'Million Records'
FROM $DB_NAME.binance_raw_data
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 3 DAY)
GROUP BY DATE(created_at)
ORDER BY Date DESC;
" 2>/dev/null

echo ""

# 清理建议
echo "💡 清理建议:"
mysql -u $DB_USER -p$DB_PASS -e "
SELECT
    'Records older than 1 day' as 'Category',
    COUNT(*) as 'Records to Clean',
    ROUND(COUNT(*) / 1000000, 2) as 'Million Records'
FROM $DB_NAME.binance_raw_data
WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 DAY)
UNION ALL
SELECT
    'Exchange records older than 1 day',
    COUNT(*),
    ROUND(COUNT(*) / 1000000, 2)
FROM $DB_NAME.exchange_raw_data
WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 DAY);
" 2>/dev/null

echo ""

# 自动清理状态
echo "🔧 自动清理状态:"
mysql -u $DB_USER -p$DB_PASS -e "
SELECT
    Name as 'Event Name',
    Status,
    Starts as 'Next Run',
    Interval_value as 'Interval',
    Interval_field as 'Unit'
FROM information_schema.EVENTS
WHERE EVENT_SCHEMA = '$DB_NAME';
" 2>/dev/null

echo ""
echo "✅ 监控完成"
