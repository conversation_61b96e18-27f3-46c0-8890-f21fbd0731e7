"""
系统测试脚本
"""
import asyncio
import time
from utils.db import db_manager
from utils.logging import setup_logger
from storage import storage

logger = setup_logger(__name__)

async def test_database_connection():
    """测试数据库连接"""
    logger.info("测试数据库连接...")
    try:
        result = db_manager.execute_query("SELECT 1", fetch=True)
        logger.info(f"数据库连接成功: {result}")
        return True
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return False

async def test_table_creation():
    """测试表创建"""
    logger.info("测试表创建...")
    try:
        # 检查所有表是否存在
        tables = [
            'bitda_kline', 'bitda_trades', 'bitda_depth', 'bitda_ticker',
            'binance_depth_5', 'binance_bookticker'
        ]
        
        for table in tables:
            result = db_manager.execute_query(
                "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = %s",
                (table,),
                fetch=True
            )
            if result[0][0] > 0:
                logger.info(f"表 {table} 存在")
            else:
                logger.error(f"表 {table} 不存在")
                return False
        
        logger.info("所有表创建成功")
        return True
    except Exception as e:
        logger.error(f"表创建测试失败: {e}")
        return False

async def test_data_insertion():
    """测试数据插入"""
    logger.info("测试数据插入...")
    try:
        # 测试Bitda K线数据插入
        test_kline_data = [{
            'symbol': 'BTCUSDT',
            'timestamp': int(time.time()),
            'open_price': 50000.0,
            'high_price': 50100.0,
            'low_price': 49900.0,
            'close_price': 50050.0,
            'volume': 100.0,
            'amount': 5005000.0,
            'period': '1min'
        }]
        
        storage.save_bitda_kline(test_kline_data)
        logger.info("Bitda K线数据插入测试成功")
        
        # 测试Binance BookTicker数据插入
        test_bookticker_data = [{
            'symbol': 'BTCUSDT',
            'update_id': 123456789,
            'bid_price': 50000.0,
            'bid_qty': 1.0,
            'ask_price': 50001.0,
            'ask_qty': 1.0,
            'event_time': int(time.time() * 1000),
            'trade_time': int(time.time() * 1000)
        }]
        
        storage.save_binance_bookticker(test_bookticker_data)
        logger.info("Binance BookTicker数据插入测试成功")
        
        return True
    except Exception as e:
        logger.error(f"数据插入测试失败: {e}")
        return False

async def test_disk_space_check():
    """测试磁盘空间检查"""
    logger.info("测试磁盘空间检查...")
    try:
        has_space = storage.check_disk_space()
        logger.info(f"磁盘空间检查结果: {'充足' if has_space else '不足'}")
        return True
    except Exception as e:
        logger.error(f"磁盘空间检查失败: {e}")
        return False

async def test_data_cleanup():
    """测试数据清理"""
    logger.info("测试数据清理...")
    try:
        deleted_count = storage.cleanup_old_data()
        logger.info(f"数据清理测试成功，清理了 {deleted_count} 条记录")
        return True
    except Exception as e:
        logger.error(f"数据清理测试失败: {e}")
        return False

async def run_all_tests():
    """运行所有测试"""
    logger.info("开始系统测试...")
    logger.info("=" * 50)
    
    tests = [
        ("数据库连接", test_database_connection),
        ("表创建", test_table_creation),
        ("数据插入", test_data_insertion),
        ("磁盘空间检查", test_disk_space_check),
        ("数据清理", test_data_cleanup)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"运行测试: {test_name}")
        try:
            result = await test_func()
            if result:
                logger.info(f"✓ {test_name} 通过")
                passed += 1
            else:
                logger.error(f"✗ {test_name} 失败")
        except Exception as e:
            logger.error(f"✗ {test_name} 异常: {e}")
        
        logger.info("-" * 30)
    
    logger.info("=" * 50)
    logger.info(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！系统准备就绪。")
        return True
    else:
        logger.error("❌ 部分测试失败，请检查系统配置。")
        return False

if __name__ == "__main__":
    asyncio.run(run_all_tests())
