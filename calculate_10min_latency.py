#!/usr/bin/env python3
"""
计算UTC+8时间2025年5月30日12:00-12:10的延时数据
"""

import mysql.connector
import json
from datetime import datetime, <PERSON><PERSON><PERSON>

def calculate_10min_latency():
    """计算10分钟的延时数据"""
    
    # 数据库配置
    source_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'Linuxtest',
        'database': 'depth_db'
    }
    
    target_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'Linuxtest',
        'database': 'ethusdt_latency_db'
    }
    
    # 时间段设置 (UTC+8: 12:00-12:10 = UTC: 04:00-04:10)
    start_time = datetime(2025, 5, 30, 4, 0, 0)   # UTC时间
    end_time = datetime(2025, 5, 30, 4, 10, 0)    # UTC时间
    
    print("⚡ ETHUSDT延时数据计算 (10分钟)")
    print("=" * 50)
    print(f"时间段 (UTC+8): 2025-05-30 12:00:00 ~ 12:10:00")
    print(f"时间段 (UTC): {start_time} ~ {end_time}")
    
    try:
        # 连接数据库
        source_conn = mysql.connector.connect(**source_config)
        target_conn = mysql.connector.connect(**target_config)
        
        source_cursor = source_conn.cursor()
        target_cursor = target_conn.cursor()
        
        # 清理旧数据
        print("\n🗑️  清理旧延时数据...")
        target_cursor.execute("DELETE FROM ethusdt_latency_matches")
        target_conn.commit()
        
        # 查询Bitda数据
        print("📊 查询Bitda数据...")
        bitda_query = """
        SELECT timestamp, asks, bids, created_at
        FROM bitda_depth 
        WHERE symbol = 'ETHUSDT'
        AND created_at >= %s 
        AND created_at < %s
        AND asks IS NOT NULL 
        AND bids IS NOT NULL
        ORDER BY timestamp
        """
        
        source_cursor.execute(bitda_query, (start_time, end_time))
        bitda_records = source_cursor.fetchall()
        
        print(f"📋 获取到 {len(bitda_records)} 条Bitda记录")
        
        if len(bitda_records) == 0:
            print("❌ 没有找到Bitda数据")
            return False
        
        matches_found = 0
        total_processed = 0
        latency_list = []
        error_count = 0
        
        print("\n🔄 开始处理数据...")
        
        # 处理每条Bitda记录
        for i, record in enumerate(bitda_records):
            bitda_timestamp, asks_json, bids_json, created_at = record
            total_processed += 1
            
            if i % 20 == 0:  # 每20条显示进度
                print(f"  处理进度: {i+1}/{len(bitda_records)}")
            
            try:
                # 解析JSON数据
                asks_data = json.loads(asks_json) if isinstance(asks_json, str) else asks_json
                bids_data = json.loads(bids_json) if isinstance(bids_json, str) else bids_json
                
                if not asks_data or not bids_data:
                    error_count += 1
                    continue
                
                # 计算买一卖一价格
                ask_prices = [float(item[0]) for item in asks_data]
                bid_prices = [float(item[0]) for item in bids_data]
                
                ask_price_1 = min(ask_prices)  # 卖一价格
                bid_price_1 = max(bid_prices)  # 买一价格
                
                ask_qty_1 = float([item[1] for item in asks_data if float(item[0]) == ask_price_1][0])
                bid_qty_1 = float([item[1] for item in bids_data if float(item[0]) == bid_price_1][0])
                
                # 查找买一卖一价格完全匹配的Binance数据 (使用首次出现时间)
                binance_query = """
                SELECT bid_price, ask_price, bid_qty, ask_qty, event_time
                FROM binance_bookticker 
                WHERE symbol = 'ETHUSDT'
                AND bid_price = %s
                AND ask_price = %s
                AND event_time < %s
                ORDER BY event_time ASC
                LIMIT 1
                """
                
                source_cursor.execute(binance_query, (bid_price_1, ask_price_1, bitda_timestamp))
                binance_match = source_cursor.fetchone()
                
                if binance_match:
                    binance_bid_price, binance_ask_price, binance_bid_qty, binance_ask_qty, binance_timestamp = binance_match
                    latency = bitda_timestamp - binance_timestamp
                    
                    if 10 <= latency <= 2000:  # 有效延时范围
                        # 插入匹配记录
                        insert_query = """
                        INSERT INTO ethusdt_latency_matches 
                        (bitda_timestamp, binance_timestamp, latency_ms, match_type, 
                         bitda_price, binance_price, bitda_qty, binance_qty, 
                         price_spread, match_quality, created_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """
                        
                        price_spread = ask_price_1 - bid_price_1
                        
                        target_cursor.execute(insert_query, (
                            bitda_timestamp, binance_timestamp, latency, 'complete',
                            (bid_price_1 + ask_price_1) / 2,  # 中间价
                            (binance_bid_price + binance_ask_price) / 2,  # Binance中间价
                            (bid_qty_1 + ask_qty_1) / 2,  # 平均数量
                            (binance_bid_qty + binance_ask_qty) / 2,  # Binance平均数量
                            price_spread, 1.0000, created_at
                        ))
                        
                        matches_found += 1
                        latency_list.append(latency)
                        
                        # 显示前10条匹配详情
                        if matches_found <= 10:
                            utc8_time = created_at + timedelta(hours=8)
                            print(f"  ✅ 匹配 {matches_found}: {utc8_time.strftime('%H:%M:%S')} 延时={latency}ms 买一={bid_price_1} 卖一={ask_price_1}")
                
            except Exception as e:
                error_count += 1
                if error_count <= 3:  # 只显示前3个错误
                    print(f"  ❌ 处理记录失败: {e}")
                continue
        
        # 提交事务
        target_conn.commit()
        
        # 显示处理结果
        print(f"\n📈 处理完成:")
        print(f"  - 处理记录: {total_processed}")
        print(f"  - 完全匹配: {matches_found}")
        print(f"  - 匹配率: {matches_found/total_processed*100:.2f}%")
        print(f"  - 错误数量: {error_count}")
        
        if latency_list:
            print(f"\n📊 延时统计:")
            print(f"  - 平均延时: {sum(latency_list)/len(latency_list):.2f}ms")
            print(f"  - 最小延时: {min(latency_list)}ms")
            print(f"  - 最大延时: {max(latency_list)}ms")
            print(f"  - 中位数延时: {sorted(latency_list)[len(latency_list)//2]}ms")
            
            # 延时分布
            ranges = [(10, 50), (51, 100), (101, 200), (201, 500), (501, 1000), (1001, 2000)]
            print(f"\n📈 延时分布:")
            for min_val, max_val in ranges:
                count = len([l for l in latency_list if min_val <= l <= max_val])
                if count > 0:
                    percentage = count / len(latency_list) * 100
                    print(f"  - {min_val}-{max_val}ms: {count}条 ({percentage:.1f}%)")
            
            # 显示一些具体的延时值
            print(f"\n📋 延时样本 (前10个):")
            for i, latency in enumerate(latency_list[:10], 1):
                print(f"  {i}. {latency}ms")
        
        # 查询最终数据库状态
        target_cursor.execute("""
            SELECT COUNT(*) as total, AVG(latency_ms) as avg_lat, 
                   MIN(latency_ms) as min_lat, MAX(latency_ms) as max_lat,
                   MIN(created_at) as earliest, MAX(created_at) as latest
            FROM ethusdt_latency_matches
        """)
        
        final_result = target_cursor.fetchone()
        if final_result and final_result[0] > 0:
            total, avg_lat, min_lat, max_lat, earliest, latest = final_result
            print(f"\n📋 数据库最终状态:")
            print(f"  - 总匹配数: {total}")
            print(f"  - 平均延时: {avg_lat:.2f}ms")
            print(f"  - 延时范围: {min_lat}ms ~ {max_lat}ms")
            print(f"  - 时间范围: {(earliest + timedelta(hours=8)).strftime('%H:%M:%S')} ~ {(latest + timedelta(hours=8)).strftime('%H:%M:%S')} (UTC+8)")
        
        source_cursor.close()
        target_cursor.close()
        source_conn.close()
        target_conn.close()
        
        return matches_found > 0
        
    except Exception as e:
        print(f"❌ 计算失败: {e}")
        return False

def main():
    """主函数"""
    if calculate_10min_latency():
        print("\n🎉 延时数据计算完成！")
        print("📊 数据已保存到 ethusdt_latency_matches 表")
        print("🌐 可在Grafana查看: http://localhost:3000")
        print("📈 数据涵盖UTC+8时间 2025-05-30 12:00-12:10")
    else:
        print("\n❌ 计算失败")

if __name__ == "__main__":
    main()
