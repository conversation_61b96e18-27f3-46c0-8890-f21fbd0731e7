#!/bin/bash

# WebSocket数据收集器日志查看脚本
# 使用方法: bash view_logs.sh [选项]

LOG_FILE="/tmp/ws_data_collector.log"
TEST_LOG_FILE="/tmp/testprint.log"
HISTORY_LOG_FILE="/Users/<USER>/code/Bit/WS_DATA_Collector/ws_data_collector.log"

echo "📊 WebSocket数据收集器日志查看工具"
echo "=================================="

# 检查参数
case "$1" in
    "real-time"|"rt")
        echo "🔄 实时查看日志 (按Ctrl+C退出)..."
        tail -f "$LOG_FILE"
        ;;
    "errors"|"err")
        echo "❌ 查看错误日志..."
        grep -i "error\|失败\|exception" "$LOG_FILE" | tail -20
        ;;
    "connections"|"conn")
        echo "🔗 查看连接状态..."
        grep -i "连接\|websocket\|proxy\|socks" "$LOG_FILE" | tail -20
        ;;
    "data"|"save")
        echo "💾 查看数据保存情况..."
        grep "成功保存" "$LOG_FILE" | tail -20
        ;;
    "prices"|"price")
        echo "💰 查看价格信息..."
        grep "报价\|价格" "$LOG_FILE" | tail -20
        ;;
    "stats"|"统计")
        echo "📈 日志统计信息..."
        echo "总行数: $(wc -l < "$LOG_FILE")"
        echo "错误数: $(grep -c -i "error\|失败" "$LOG_FILE")"
        echo "成功保存次数: $(grep -c "成功保存" "$LOG_FILE")"
        echo "连接事件: $(grep -c "连接" "$LOG_FILE")"
        ;;
    "test")
        echo "🧪 查看测试日志..."
        if [ -f "$TEST_LOG_FILE" ]; then
            tail -20 "$TEST_LOG_FILE"
        else
            echo "测试日志文件不存在"
        fi
        ;;
    "history"|"hist")
        echo "📚 查看历史日志..."
        tail -50 "$HISTORY_LOG_FILE"
        ;;
    "help"|"-h"|"--help"|"")
        echo "使用方法:"
        echo "  bash view_logs.sh [选项]"
        echo ""
        echo "选项:"
        echo "  real-time, rt     实时查看日志"
        echo "  errors, err       查看错误日志"
        echo "  connections, conn 查看连接状态"
        echo "  data, save        查看数据保存情况"
        echo "  prices, price     查看价格信息"
        echo "  stats, 统计       显示日志统计"
        echo "  test              查看测试日志"
        echo "  history, hist     查看历史日志"
        echo "  help, -h          显示帮助"
        echo ""
        echo "示例:"
        echo "  bash view_logs.sh rt          # 实时查看"
        echo "  bash view_logs.sh errors      # 查看错误"
        echo "  bash view_logs.sh stats       # 查看统计"
        ;;
    *)
        echo "❓ 未知选项: $1"
        echo "使用 'bash view_logs.sh help' 查看帮助"
        ;;
esac
