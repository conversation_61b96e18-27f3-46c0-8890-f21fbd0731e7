-- 更新清理事件为1天清理
USE depth_db;

-- 删除现有事件
DROP EVENT IF EXISTS cleanup_old_data;

-- 创建新的1天清理事件
DELIMITER $$
CREATE EVENT cleanup_old_data
ON SCHEDULE EVERY 1 DAY
STARTS TIMESTAMP(CURDATE() + INTERVAL 1 DAY, '02:00:00')
DO
BEGIN
    DELETE FROM depth_matches WHERE timestamp < DATE_SUB(NOW(), INTERVAL 1 DAY);
    DELETE FROM binance_raw_data WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 DAY);
    DELETE FROM exchange_raw_data WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 DAY);
    DELETE FROM depth_stats WHERE timestamp < DATE_SUB(NOW(), INTERVAL 1 DAY);
END$$
DELIMITER ;

-- 显示事件状态
SHOW EVENTS FROM depth_db;
