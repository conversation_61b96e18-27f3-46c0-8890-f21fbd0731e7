#!/usr/bin/env python3
"""
ETHUSDT延时数据自动处理器
定期处理新数据并更新统计
"""

import time
import schedule
from datetime import datetime, timedelta
import logging
from test_latency_processor import TestLatencyProcessor
from latency_stats_generator import LatencyStatsGenerator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ethusdt_auto_processor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ETHUSDTAutoProcessor:
    """ETHUSDT自动处理器"""

    def __init__(self):
        self.processor = TestLatencyProcessor()
        self.stats_generator = LatencyStatsGenerator()
        self.last_process_time = datetime.now() - timedelta(minutes=5)

    def process_recent_data(self):
        """处理最近的数据"""
        try:
            current_time = datetime.now()
            logger.info(f"开始处理数据: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")

            # 处理最近5分钟的数据
            success = self.processor.test_small_batch()

            if success:
                logger.info("✅ 延时数据处理成功")

                # 生成最新的分钟统计
                target_minute = current_time.replace(second=0, microsecond=0) - timedelta(minutes=1)
                if self.stats_generator.generate_minute_stats(target_minute):
                    logger.info("✅ 分钟统计生成成功")

                # 更新实时状态
                self.stats_generator.update_realtime_status()
                logger.info("✅ 实时状态更新成功")

                self.last_process_time = current_time

            else:
                logger.warning("⚠️  延时数据处理未找到匹配")

        except Exception as e:
            logger.error(f"❌ 数据处理失败: {e}")

    def generate_hourly_stats(self):
        """生成小时统计"""
        try:
            current_time = datetime.now()
            target_hour = current_time.replace(minute=0, second=0, microsecond=0) - timedelta(hours=1)

            logger.info(f"生成小时统计: {target_hour.strftime('%Y-%m-%d %H:00')}")

            if self.stats_generator.generate_hour_stats(target_hour):
                logger.info("✅ 小时统计生成成功")
            else:
                logger.warning("⚠️  小时统计生成失败")

        except Exception as e:
            logger.error(f"❌ 小时统计生成失败: {e}")

    def health_check(self):
        """健康检查"""
        try:
            import mysql.connector

            # 检查延时分析数据库连接
            conn = mysql.connector.connect(
                host='localhost',
                user='root',
                password='Linuxtest',
                database='ethusdt_latency_db'
            )

            cursor = conn.cursor()

            # 检查最近的数据
            cursor.execute("""
                SELECT COUNT(*) as recent_matches
                FROM ethusdt_latency_matches
                WHERE created_at >= NOW() - INTERVAL 10 MINUTE
            """)

            recent_matches = cursor.fetchone()[0]

            # 检查实时状态
            cursor.execute("SELECT updated_at FROM ethusdt_realtime_status WHERE id = 1")
            last_update = cursor.fetchone()[0]

            cursor.close()
            conn.close()

            logger.info(f"健康检查: 最近10分钟匹配数={recent_matches}, 最后更新={last_update}")

            # 如果最近没有数据，记录警告
            if recent_matches == 0:
                logger.warning("⚠️  最近10分钟无新的延时匹配数据")

            return True

        except Exception as e:
            logger.error(f"❌ 健康检查失败: {e}")
            return False

    def start_scheduler(self):
        """启动调度器"""
        logger.info("🚀 启动ETHUSDT延时数据自动处理器")

        # 每1分钟处理一次新数据
        schedule.every(1).minutes.do(self.process_recent_data)

        # 每小时生成统计数据
        schedule.every().hour.at(":05").do(self.generate_hourly_stats)

        # 每10分钟进行健康检查
        schedule.every(10).minutes.do(self.health_check)

        logger.info("📅 调度任务已设置:")
        logger.info("  - 每1分钟: 处理新的延时数据")
        logger.info("  - 每小时: 生成小时统计")
        logger.info("  - 每10分钟: 健康检查")

        # 立即执行一次
        logger.info("🔄 立即执行初始处理...")
        self.process_recent_data()
        self.health_check()

        # 开始调度循环
        logger.info("⏰ 开始调度循环...")
        while True:
            try:
                schedule.run_pending()
                time.sleep(30)  # 每30秒检查一次

            except KeyboardInterrupt:
                logger.info("⏹️  收到停止信号，正在退出...")
                break
            except Exception as e:
                logger.error(f"❌ 调度器异常: {e}")
                time.sleep(60)  # 出错后等待1分钟再继续

def main():
    """主函数"""
    print("🚀 ETHUSDT延时数据自动处理器")
    print("=" * 50)
    print("📊 功能:")
    print("  - 每1分钟处理新的延时数据")
    print("  - 每小时生成统计数据")
    print("  - 每10分钟健康检查")
    print("  - 自动更新Grafana数据源")
    print("\n⚠️  按 Ctrl+C 停止程序")
    print("=" * 50)

    processor = ETHUSDTAutoProcessor()
    processor.start_scheduler()

if __name__ == "__main__":
    main()
