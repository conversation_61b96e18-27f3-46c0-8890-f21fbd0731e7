#!/usr/bin/env python3
"""
简单数据导出器
为Grafana提供实时数据
"""

import os
import time
import json
from http.server import HTTPServer, BaseHTTPRequestHandler
from datetime import datetime

# 设置环境变量
os.environ['DB_HOST'] = 'localhost'
os.environ['DB_USER'] = 'root'
os.environ['DB_PASSWORD'] = 'Linuxtest'
os.environ['DB_NAME'] = 'depth_db'

class DataExporter:
    """数据导出器"""
    
    def __init__(self):
        self.port = 8000
        
    def get_funding_rates(self):
        """获取资金费率数据"""
        try:
            from analyzer.funding_analyzer import FundingAnalyzer
            analyzer = FundingAnalyzer()
            return analyzer.get_latest_funding_rates()
        except Exception as e:
            print(f"获取资金费率失败: {e}")
            return {}
    
    def get_latency_data(self):
        """获取延时数据"""
        try:
            from analyzer.latency_analyzer import LatencyAnalyzer
            analyzer = LatencyAnalyzer()
            return analyzer.analyze_ethusdt_latency(hours=1)
        except Exception as e:
            print(f"获取延时数据失败: {e}")
            return {}
    
    def get_depth_comparison(self):
        """获取深度对比数据"""
        try:
            from analyzer.depth_analyzer import DepthAnalyzer
            analyzer = DepthAnalyzer()
            return analyzer.get_latest_depth_comparison()
        except Exception as e:
            print(f"获取深度对比失败: {e}")
            return {}
    
    def get_price_deviation(self):
        """获取价格偏差数据"""
        try:
            from analyzer.price_analyzer import PriceAnalyzer
            analyzer = PriceAnalyzer()
            return analyzer.get_latest_price_deviation()
        except Exception as e:
            print(f"获取价格偏差失败: {e}")
            return {}

class DataHandler(BaseHTTPRequestHandler):
    """HTTP请求处理器"""
    
    def __init__(self, *args, **kwargs):
        self.exporter = DataExporter()
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/api/funding-rates':
            self.send_json_response(self.exporter.get_funding_rates())
        elif self.path == '/api/latency':
            self.send_json_response(self.exporter.get_latency_data())
        elif self.path == '/api/depth-comparison':
            self.send_json_response(self.exporter.get_depth_comparison())
        elif self.path == '/api/price-deviation':
            self.send_json_response(self.exporter.get_price_deviation())
        elif self.path == '/api/all-data':
            # 返回所有数据
            all_data = {
                'funding_rates': self.exporter.get_funding_rates(),
                'latency': self.exporter.get_latency_data(),
                'depth_comparison': self.exporter.get_depth_comparison(),
                'price_deviation': self.exporter.get_price_deviation(),
                'timestamp': datetime.now().isoformat()
            }
            self.send_json_response(all_data)
        elif self.path == '/':
            self.send_html_response()
        else:
            self.send_404()
    
    def send_json_response(self, data):
        """发送JSON响应"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data, indent=2, ensure_ascii=False).encode('utf-8'))
    
    def send_html_response(self):
        """发送HTML响应"""
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>加密货币数据导出器</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .container { max-width: 800px; margin: 0 auto; }
                .endpoint { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
                .endpoint h3 { margin-top: 0; color: #333; }
                .endpoint a { color: #007bff; text-decoration: none; }
                .endpoint a:hover { text-decoration: underline; }
                .status { padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; margin: 20px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🚀 加密货币数据导出器</h1>
                <div class="status">
                    <strong>状态:</strong> 运行中 | <strong>时间:</strong> """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + """
                </div>
                
                <h2>📊 可用的API端点</h2>
                
                <div class="endpoint">
                    <h3>资金费率数据</h3>
                    <p><a href="/api/funding-rates">/api/funding-rates</a></p>
                    <p>获取BTCUSDT和ETHUSDT的最新资金费率数据</p>
                </div>
                
                <div class="endpoint">
                    <h3>延时分析数据</h3>
                    <p><a href="/api/latency">/api/latency</a></p>
                    <p>获取ETHUSDT的延时分析数据</p>
                </div>
                
                <div class="endpoint">
                    <h3>深度对比数据</h3>
                    <p><a href="/api/depth-comparison">/api/depth-comparison</a></p>
                    <p>获取Bitda vs Binance的深度对比数据</p>
                </div>
                
                <div class="endpoint">
                    <h3>价格偏差数据</h3>
                    <p><a href="/api/price-deviation">/api/price-deviation</a></p>
                    <p>获取标记价格与最新价格的偏差数据</p>
                </div>
                
                <div class="endpoint">
                    <h3>所有数据</h3>
                    <p><a href="/api/all-data">/api/all-data</a></p>
                    <p>获取所有分析数据的汇总</p>
                </div>
                
                <h2>🔧 Grafana配置</h2>
                <p>在Grafana中添加数据源:</p>
                <ul>
                    <li>类型: JSON API</li>
                    <li>URL: http://localhost:8000/api/all-data</li>
                    <li>访问: Server (default)</li>
                </ul>
            </div>
        </body>
        </html>
        """
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_404(self):
        """发送404响应"""
        self.send_response(404)
        self.send_header('Content-type', 'text/plain')
        self.end_headers()
        self.wfile.write(b'404 Not Found')
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def main():
    """主函数"""
    print("🚀 启动数据导出器...")
    print(f"📡 监听端口: 8000")
    print(f"🌐 访问地址: http://localhost:8000")
    print(f"📊 API文档: http://localhost:8000")
    print("按 Ctrl+C 停止服务")
    
    try:
        server = HTTPServer(('localhost', 8000), DataHandler)
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n⏹️  服务已停止")
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")

if __name__ == "__main__":
    main()
