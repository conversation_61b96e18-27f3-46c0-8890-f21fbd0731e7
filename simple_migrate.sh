#!/bin/bash

# 🚀 简单迁移脚本
# 使用命令行工具直接迁移

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# ClickHouse连接信息
CH_URL="***************************************/"

log_info "🚀 开始ClickHouse简单迁移"
echo "=" * 50

# 1. 创建数据库
log_info "📊 创建ClickHouse数据库..."
curl -s -X POST "$CH_URL" -d "CREATE DATABASE IF NOT EXISTS crypto"
if [ $? -eq 0 ]; then
    log_success "crypto数据库创建成功"
else
    log_error "crypto数据库创建失败"
    exit 1
fi

# 2. 创建bitda_depth表
log_info "🏗️ 创建bitda_depth表..."
curl -s -X POST "$CH_URL" -d "
CREATE TABLE IF NOT EXISTS crypto.bitda_depth (
    id UInt64,
    symbol String,
    timestamp UInt64,
    bid_price_1 Decimal(15,2),
    ask_price_1 Decimal(15,2),
    bid_qty_1 Decimal(20,4),
    ask_qty_1 Decimal(20,4),
    bid_price_2 Decimal(15,2),
    ask_price_2 Decimal(15,2),
    bid_qty_2 Decimal(20,4),
    ask_qty_2 Decimal(20,4),
    bid_price_3 Decimal(15,2),
    ask_price_3 Decimal(15,2),
    bid_qty_3 Decimal(20,4),
    ask_qty_3 Decimal(20,4),
    bid_price_4 Decimal(15,2),
    ask_price_4 Decimal(15,2),
    bid_qty_4 Decimal(20,4),
    ask_qty_4 Decimal(20,4),
    bid_price_5 Decimal(15,2),
    ask_price_5 Decimal(15,2),
    bid_qty_5 Decimal(20,4),
    ask_qty_5 Decimal(20,4),
    asks String,
    bids String,
    created_at DateTime DEFAULT now()
) ENGINE = MergeTree()
ORDER BY (symbol, timestamp)
PARTITION BY toYYYYMM(toDateTime(timestamp/1000))
"

if [ $? -eq 0 ]; then
    log_success "bitda_depth表创建成功"
else
    log_error "bitda_depth表创建失败"
    exit 1
fi

# 3. 创建binance_depth_5表
log_info "🏗️ 创建binance_depth_5表..."
curl -s -X POST "$CH_URL" -d "
CREATE TABLE IF NOT EXISTS crypto.binance_depth_5 (
    id UInt64,
    symbol String,
    event_time UInt64,
    bid_price_1 Decimal(15,2),
    ask_price_1 Decimal(15,2),
    bid_qty_1 Decimal(20,4),
    ask_qty_1 Decimal(20,4),
    bid_price_2 Decimal(15,2),
    ask_price_2 Decimal(15,2),
    bid_qty_2 Decimal(20,4),
    ask_qty_2 Decimal(20,4),
    bid_price_3 Decimal(15,2),
    ask_price_3 Decimal(15,2),
    bid_qty_3 Decimal(20,4),
    ask_qty_3 Decimal(20,4),
    bid_price_4 Decimal(15,2),
    ask_price_4 Decimal(15,2),
    bid_qty_4 Decimal(20,4),
    ask_qty_4 Decimal(20,4),
    bid_price_5 Decimal(15,2),
    ask_price_5 Decimal(15,2),
    bid_qty_5 Decimal(20,4),
    ask_qty_5 Decimal(20,4),
    created_at DateTime DEFAULT now()
) ENGINE = MergeTree()
ORDER BY (symbol, event_time)
PARTITION BY toYYYYMM(toDateTime(event_time/1000))
"

if [ $? -eq 0 ]; then
    log_success "binance_depth_5表创建成功"
else
    log_error "binance_depth_5表创建失败"
    exit 1
fi

# 4. 测试查询
log_info "🔍 测试ClickHouse查询..."
result=$(curl -s "$CH_URL?query=SELECT%20COUNT(*)%20FROM%20crypto.bitda_depth")
log_success "bitda_depth表记录数: $result"

result=$(curl -s "$CH_URL?query=SELECT%20COUNT(*)%20FROM%20crypto.binance_depth_5")
log_success "binance_depth_5表记录数: $result"

# 5. 迁移样本数据
log_info "📦 迁移样本数据..."

# 导出MySQL数据到CSV
log_info "   📊 导出MySQL数据..."
mysql -u root -pLinuxtest depth_db -e "
SELECT id, symbol, timestamp, 
       COALESCE(bid_price_1, 0), COALESCE(ask_price_1, 0), 
       COALESCE(bid_qty_1, 0), COALESCE(ask_qty_1, 0),
       COALESCE(bid_price_2, 0), COALESCE(ask_price_2, 0), 
       COALESCE(bid_qty_2, 0), COALESCE(ask_qty_2, 0),
       COALESCE(bid_price_3, 0), COALESCE(ask_price_3, 0), 
       COALESCE(bid_qty_3, 0), COALESCE(ask_qty_3, 0),
       COALESCE(bid_price_4, 0), COALESCE(ask_price_4, 0), 
       COALESCE(bid_qty_4, 0), COALESCE(ask_qty_4, 0),
       COALESCE(bid_price_5, 0), COALESCE(ask_price_5, 0), 
       COALESCE(bid_qty_5, 0), COALESCE(ask_qty_5, 0),
       COALESCE(asks, '[]'), COALESCE(bids, '[]')
FROM bitda_depth 
ORDER BY id DESC 
LIMIT 1000
" --batch --raw > /tmp/bitda_depth.tsv 2>/dev/null

if [ -f /tmp/bitda_depth.tsv ]; then
    lines=$(wc -l < /tmp/bitda_depth.tsv)
    log_success "导出bitda_depth数据: $lines 行"
    
    # 导入到ClickHouse
    log_info "   📥 导入到ClickHouse..."
    curl -s -X POST "$CH_URL?query=INSERT%20INTO%20crypto.bitda_depth%20FORMAT%20TSV" --data-binary @/tmp/bitda_depth.tsv
    
    if [ $? -eq 0 ]; then
        log_success "bitda_depth数据导入成功"
    else
        log_error "bitda_depth数据导入失败"
    fi
    
    rm -f /tmp/bitda_depth.tsv
else
    log_error "导出bitda_depth数据失败"
fi

# 导出binance_depth_5数据
mysql -u root -pLinuxtest depth_db -e "
SELECT id, symbol, event_time, 
       COALESCE(bid_price_1, 0), COALESCE(ask_price_1, 0), 
       COALESCE(bid_qty_1, 0), COALESCE(ask_qty_1, 0),
       COALESCE(bid_price_2, 0), COALESCE(ask_price_2, 0), 
       COALESCE(bid_qty_2, 0), COALESCE(ask_qty_2, 0),
       COALESCE(bid_price_3, 0), COALESCE(ask_price_3, 0), 
       COALESCE(bid_qty_3, 0), COALESCE(ask_qty_3, 0),
       COALESCE(bid_price_4, 0), COALESCE(ask_price_4, 0), 
       COALESCE(bid_qty_4, 0), COALESCE(ask_qty_4, 0),
       COALESCE(bid_price_5, 0), COALESCE(ask_price_5, 0), 
       COALESCE(bid_qty_5, 0), COALESCE(ask_qty_5, 0)
FROM binance_depth_5 
ORDER BY id DESC 
LIMIT 1000
" --batch --raw > /tmp/binance_depth_5.tsv 2>/dev/null

if [ -f /tmp/binance_depth_5.tsv ]; then
    lines=$(wc -l < /tmp/binance_depth_5.tsv)
    log_success "导出binance_depth_5数据: $lines 行"
    
    # 导入到ClickHouse
    curl -s -X POST "$CH_URL?query=INSERT%20INTO%20crypto.binance_depth_5%20FORMAT%20TSV" --data-binary @/tmp/binance_depth_5.tsv
    
    if [ $? -eq 0 ]; then
        log_success "binance_depth_5数据导入成功"
    else
        log_error "binance_depth_5数据导入失败"
    fi
    
    rm -f /tmp/binance_depth_5.tsv
else
    log_error "导出binance_depth_5数据失败"
fi

# 6. 验证迁移结果
log_info "🔍 验证迁移结果..."
result=$(curl -s "$CH_URL?query=SELECT%20COUNT(*)%20FROM%20crypto.bitda_depth")
log_success "ClickHouse bitda_depth记录数: $result"

result=$(curl -s "$CH_URL?query=SELECT%20COUNT(*)%20FROM%20crypto.binance_depth_5")
log_success "ClickHouse binance_depth_5记录数: $result"

# 7. 测试深度对比查询
log_info "🔍 测试深度对比查询..."
result=$(curl -s "$CH_URL" -d "
SELECT 
    '买一量' as 项目,
    (SELECT ROUND(bid_qty_1, 2) FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1) as Bitda,
    (SELECT ROUND(bid_qty_1, 2) FROM crypto.binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY id DESC LIMIT 1) as Binance
")

if [ $? -eq 0 ]; then
    log_success "深度对比查询成功"
    echo "$result"
else
    log_error "深度对比查询失败"
fi

echo ""
echo "=" * 50
log_success "🎉 ClickHouse迁移完成！"
echo ""
echo "📋 下一步:"
echo "  1. 修改代码使用ClickHouse"
echo "  2. 配置Grafana ClickHouse数据源"
echo "  3. 测试完整功能"
echo ""
echo "🔧 ClickHouse信息:"
echo "  - HTTP端口: 8123"
echo "  - TCP端口: 9000"
echo "  - 数据库: crypto"
echo "  - 用户: default"
echo "  - 密码: Linuxtest"
