#!/usr/bin/env python3
"""
测试解析bitda_depth表中的JSON数据
"""

import os
import json

# 设置环境变量
os.environ['DB_HOST'] = 'localhost'
os.environ['DB_USER'] = 'root'
os.environ['DB_PASSWORD'] = 'Linuxtest'
os.environ['DB_NAME'] = 'depth_db'

def test_json_parsing():
    """测试JSON数据解析"""
    try:
        from utils.db import db_manager
        
        # 获取一条包含JSON数据的记录
        query = """
        SELECT asks, bids, timestamp, created_at
        FROM bitda_depth 
        WHERE symbol = 'ETHUSDT' 
        AND asks IS NOT NULL 
        AND bids IS NOT NULL 
        LIMIT 1
        """
        
        result = db_manager.execute_query(query, fetch=True)
        
        if result and result[0]:
            row = result[0]
            asks_json = row[0]
            bids_json = row[1]
            timestamp = row[2]
            created_at = row[3]
            
            print("📊 Bitda深度数据解析测试")
            print("=" * 50)
            print(f"时间戳: {timestamp}")
            print(f"入库时间: {created_at}")
            print()
            
            # 解析asks (卖单)
            if asks_json:
                asks_data = json.loads(asks_json) if isinstance(asks_json, str) else asks_json
                print("📈 卖单数据 (asks):")
                print(f"  总档数: {len(asks_data)}")
                if asks_data:
                    # 卖一价格 (最小价格)
                    ask_prices = [float(item[0]) for item in asks_data]
                    ask_price_1 = min(ask_prices)
                    ask_qty_1 = None
                    
                    # 找到卖一对应的数量
                    for item in asks_data:
                        if float(item[0]) == ask_price_1:
                            ask_qty_1 = float(item[1])
                            break
                    
                    print(f"  卖一价格: {ask_price_1}")
                    print(f"  卖一数量: {ask_qty_1}")
                    print(f"  前5档: {asks_data[:5]}")
            
            print()
            
            # 解析bids (买单)
            if bids_json:
                bids_data = json.loads(bids_json) if isinstance(bids_json, str) else bids_json
                print("📉 买单数据 (bids):")
                print(f"  总档数: {len(bids_data)}")
                if bids_data:
                    # 买一价格 (最大价格)
                    bid_prices = [float(item[0]) for item in bids_data]
                    bid_price_1 = max(bid_prices)
                    bid_qty_1 = None
                    
                    # 找到买一对应的数量
                    for item in bids_data:
                        if float(item[0]) == bid_price_1:
                            bid_qty_1 = float(item[1])
                            break
                    
                    print(f"  买一价格: {bid_price_1}")
                    print(f"  买一数量: {bid_qty_1}")
                    print(f"  前5档: {bids_data[:5]}")
            
            print()
            print("✅ JSON解析成功！")
            
            return {
                'ask_price_1': ask_price_1,
                'ask_qty_1': ask_qty_1,
                'bid_price_1': bid_price_1,
                'bid_qty_1': bid_qty_1,
                'timestamp': timestamp
            }
            
        else:
            print("❌ 没有找到包含JSON数据的记录")
            return None
            
    except Exception as e:
        print(f"❌ JSON解析失败: {e}")
        return None

def compare_with_binance():
    """与Binance数据对比"""
    try:
        from utils.db import db_manager
        
        # 获取Binance最新数据
        binance_query = """
        SELECT bid_price, ask_price, event_time
        FROM binance_bookticker 
        WHERE symbol = 'ETHUSDT' 
        ORDER BY event_time DESC 
        LIMIT 1
        """
        
        binance_result = db_manager.execute_query(binance_query, fetch=True)
        
        if binance_result and binance_result[0]:
            binance_row = binance_result[0]
            binance_bid = float(binance_row[0])
            binance_ask = float(binance_row[1])
            binance_time = binance_row[2]
            
            print("🔄 Binance vs Bitda 数据对比")
            print("=" * 50)
            print(f"Binance买一价: {binance_bid}")
            print(f"Binance卖一价: {binance_ask}")
            print(f"Binance时间戳: {binance_time}")
            print()
            
            # 获取Bitda数据进行对比
            bitda_data = test_json_parsing()
            if bitda_data:
                print(f"Bitda买一价: {bitda_data['bid_price_1']}")
                print(f"Bitda卖一价: {bitda_data['ask_price_1']}")
                print(f"Bitda时间戳: {bitda_data['timestamp']}")
                print()
                
                # 检查价格匹配
                bid_match = binance_bid == bitda_data['bid_price_1']
                ask_match = binance_ask == bitda_data['ask_price_1']
                
                print("🎯 价格匹配检查:")
                print(f"  买一价匹配: {'✅' if bid_match else '❌'} ({binance_bid} vs {bitda_data['bid_price_1']})")
                print(f"  卖一价匹配: {'✅' if ask_match else '❌'} ({binance_ask} vs {bitda_data['ask_price_1']})")
                
                if bid_match or ask_match:
                    # 计算延时
                    if bitda_data['timestamp'] > binance_time:
                        latency = bitda_data['timestamp'] - binance_time
                        print(f"  延时计算: {latency}ms")
                    else:
                        print("  ⚠️  时间戳顺序异常")
                
        else:
            print("❌ 没有找到Binance数据")
            
    except Exception as e:
        print(f"❌ 对比失败: {e}")

def main():
    """主函数"""
    print("🧪 Bitda深度数据JSON解析测试")
    print("=" * 60)
    
    # 测试JSON解析
    test_json_parsing()
    
    print("\n" + "=" * 60)
    
    # 与Binance数据对比
    compare_with_binance()

if __name__ == "__main__":
    main()
