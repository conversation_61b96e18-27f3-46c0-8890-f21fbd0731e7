#!/usr/bin/env python3
"""
测试ClickHouse查询脚本
用于验证Grafana仪表板中的SQL查询是否正确
"""

import os
import sys
from clickhouse_driver import Client
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def get_clickhouse_client():
    """获取ClickHouse客户端"""
    try:
        client = Client(
            host=os.getenv('CH_HOST', 'localhost'),
            port=int(os.getenv('CH_PORT', '9000')),
            user=os.getenv('CH_USER', 'default'),
            password=os.getenv('CH_PASSWORD', 'Linuxtest'),
            database=os.getenv('CH_DATABASE', 'crypto')
        )
        return client
    except Exception as e:
        print(f"❌ 连接ClickHouse失败: {e}")
        return None

def test_table_exists(client, table_name):
    """测试表是否存在"""
    try:
        query = f"SELECT COUNT(*) FROM {table_name} LIMIT 1"
        result = client.execute(query)
        print(f"✅ 表 {table_name} 存在")
        return True
    except Exception as e:
        print(f"❌ 表 {table_name} 不存在或无法访问: {e}")
        return False

def test_data_count(client, table_name, symbol=None):
    """测试数据数量"""
    try:
        if symbol:
            query = f"SELECT COUNT(*) FROM {table_name} WHERE symbol = '{symbol}'"
        else:
            query = f"SELECT COUNT(*) FROM {table_name}"
        
        result = client.execute(query)
        count = result[0][0]
        
        if symbol:
            print(f"📊 表 {table_name} 中 {symbol} 数据: {count} 条")
        else:
            print(f"📊 表 {table_name} 总数据: {count} 条")
        
        return count
    except Exception as e:
        print(f"❌ 查询数据数量失败: {e}")
        return 0

def test_btcusdt_depth_query(client):
    """测试BTCUSDT深度对比查询"""
    print("\n🔍 测试BTCUSDT深度对比查询...")
    
    query = """
    SELECT 
      '卖一量' as 项目,
      round((SELECT ask_qty_1 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
      round((SELECT ask_qty_1 FROM crypto.binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
      round((SELECT ask_qty_1 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) / 
            (SELECT ask_qty_1 FROM crypto.binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
    UNION ALL
    SELECT 
      '买一量' as 项目,
      round((SELECT bid_qty_1 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
      round((SELECT bid_qty_1 FROM crypto.binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
      round((SELECT bid_qty_1 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) / 
            (SELECT bid_qty_1 FROM crypto.binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
    UNION ALL
    SELECT 
      '买一量卖一量' as 项目,
      round((SELECT bid_qty_1 + ask_qty_1 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
      round((SELECT bid_qty_1 + ask_qty_1 FROM crypto.binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
      round((SELECT bid_qty_1 + ask_qty_1 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) / 
            (SELECT bid_qty_1 + ask_qty_1 FROM crypto.binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
    UNION ALL
    SELECT 
      '买卖前两档量' as 项目,
      round((SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
      round((SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM crypto.binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
      round((SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) / 
            (SELECT bid_qty_1 + bid_qty_2 + ask_qty_1 + ask_qty_2 FROM crypto.binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
    UNION ALL
    SELECT 
      '买卖前五档量' as 项目,
      round((SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
      round((SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM crypto.binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
      round((SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM crypto.bitda_depth WHERE symbol = 'BTCUSDT' ORDER BY timestamp DESC LIMIT 1) / 
            (SELECT bid_qty_1 + bid_qty_2 + bid_qty_3 + bid_qty_4 + bid_qty_5 + ask_qty_1 + ask_qty_2 + ask_qty_3 + ask_qty_4 + ask_qty_5 FROM crypto.binance_depth_5 WHERE symbol = 'BTCUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
    """
    
    try:
        result = client.execute(query)
        
        print("📊 BTCUSDT深度对比结果:")
        print("| 项目 | Bitda | Binance | 深度比 |")
        print("|------|-------|---------|--------|")
        
        for row in result:
            项目, bitda, binance, 深度比 = row
            print(f"| {项目} | {bitda} | {binance} | {深度比} |")
        
        return True
        
    except Exception as e:
        print(f"❌ BTCUSDT查询失败: {e}")
        return False

def test_ethusdt_depth_query(client):
    """测试ETHUSDT深度对比查询"""
    print("\n🔍 测试ETHUSDT深度对比查询...")
    
    # 简化查询，只测试基本功能
    query = """
    SELECT 
      '买一量' as 项目,
      round((SELECT bid_qty_1 FROM crypto.bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1), 2) as Bitda,
      round((SELECT bid_qty_1 FROM crypto.binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as Binance,
      round((SELECT bid_qty_1 FROM crypto.bitda_depth WHERE symbol = 'ETHUSDT' ORDER BY timestamp DESC LIMIT 1) / 
            (SELECT bid_qty_1 FROM crypto.binance_depth_5 WHERE symbol = 'ETHUSDT' ORDER BY event_time DESC LIMIT 1), 2) as 深度比
    """
    
    try:
        result = client.execute(query)
        
        print("📊 ETHUSDT深度对比结果:")
        print("| 项目 | Bitda | Binance | 深度比 |")
        print("|------|-------|---------|--------|")
        
        for row in result:
            项目, bitda, binance, 深度比 = row
            print(f"| {项目} | {bitda} | {binance} | {深度比} |")
        
        return True
        
    except Exception as e:
        print(f"❌ ETHUSDT查询失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 ClickHouse查询测试")
    print("=" * 50)
    
    # 1. 连接ClickHouse
    client = get_clickhouse_client()
    if not client:
        print("❌ 无法连接ClickHouse，请检查服务状态和配置")
        sys.exit(1)
    
    print("✅ ClickHouse连接成功")
    
    # 2. 测试表存在性
    print("\n📋 检查表结构...")
    bitda_exists = test_table_exists(client, "crypto.bitda_depth")
    binance_exists = test_table_exists(client, "crypto.binance_depth_5")
    
    if not (bitda_exists and binance_exists):
        print("❌ 必要的表不存在，请先创建表并导入数据")
        sys.exit(1)
    
    # 3. 测试数据数量
    print("\n📊 检查数据数量...")
    btc_bitda_count = test_data_count(client, "crypto.bitda_depth", "BTCUSDT")
    btc_binance_count = test_data_count(client, "crypto.binance_depth_5", "BTCUSDT")
    eth_bitda_count = test_data_count(client, "crypto.bitda_depth", "ETHUSDT")
    eth_binance_count = test_data_count(client, "crypto.binance_depth_5", "ETHUSDT")
    
    # 4. 测试查询
    if btc_bitda_count > 0 and btc_binance_count > 0:
        test_btcusdt_depth_query(client)
    else:
        print("⚠️ BTCUSDT数据不足，跳过查询测试")
    
    if eth_bitda_count > 0 and eth_binance_count > 0:
        test_ethusdt_depth_query(client)
    else:
        print("⚠️ ETHUSDT数据不足，跳过查询测试")
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    
    # 5. 给出建议
    if btc_bitda_count == 0 or btc_binance_count == 0 or eth_bitda_count == 0 or eth_binance_count == 0:
        print("\n💡 建议:")
        print("  1. 检查数据采集服务是否正常运行")
        print("  2. 确认数据库表结构正确")
        print("  3. 验证数据导入流程")

if __name__ == "__main__":
    main()
