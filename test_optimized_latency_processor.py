#!/usr/bin/env python3
"""
测试优化版延时处理器性能
验证新的结构化数据存储对延时分析的性能提升
"""

import mysql.connector
import time
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LatencyProcessorTester:
    """延时处理器性能测试器"""
    
    def __init__(self):
        self.source_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'depth_db'
        }
        
        self.target_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Linuxtest',
            'database': 'ethusdt_latency_db'
        }
    
    def check_data_availability(self):
        """检查数据可用性"""
        try:
            connection = mysql.connector.connect(**self.source_config)
            cursor = connection.cursor()
            
            logger.info("📊 检查数据可用性...")
            
            # 检查Bitda深度数据
            cursor.execute("""
                SELECT COUNT(*) as total,
                       COUNT(bid_price_1) as with_prices,
                       MIN(created_at) as earliest,
                       MAX(created_at) as latest
                FROM bitda_depth 
                WHERE symbol = 'ETHUSDT'
                AND created_at >= NOW() - INTERVAL 30 MINUTE
            """)
            
            result = cursor.fetchone()
            total, with_prices, earliest, latest = result
            
            logger.info(f"📋 Bitda ETHUSDT数据 (最近30分钟):")
            logger.info(f"   总记录数: {total}")
            logger.info(f"   有价格字段: {with_prices} ({with_prices/total*100:.1f}%)" if total > 0 else "   无数据")
            logger.info(f"   时间范围: {earliest} ~ {latest}")
            
            # 检查Binance BookTicker数据
            cursor.execute("""
                SELECT COUNT(*) as total,
                       MIN(created_at) as earliest,
                       MAX(created_at) as latest
                FROM binance_bookticker 
                WHERE symbol = 'ETHUSDT'
                AND created_at >= NOW() - INTERVAL 30 MINUTE
            """)
            
            result = cursor.fetchone()
            total, earliest, latest = result
            
            logger.info(f"📋 Binance ETHUSDT BookTicker数据 (最近30分钟):")
            logger.info(f"   总记录数: {total}")
            logger.info(f"   时间范围: {earliest} ~ {latest}")
            
            cursor.close()
            connection.close()
            
            return with_prices > 0 and total > 0
            
        except Exception as e:
            logger.error(f"检查数据可用性失败: {e}")
            return False
    
    def test_old_vs_new_query_performance(self):
        """测试旧查询方式 vs 新查询方式的性能对比"""
        try:
            connection = mysql.connector.connect(**self.source_config)
            cursor = connection.cursor()
            
            logger.info("⚡ 测试查询性能对比...")
            
            # 获取一个测试时间窗口
            test_time = datetime.now() - timedelta(minutes=5)
            start_time = test_time.replace(second=0, microsecond=0)
            end_time = start_time + timedelta(minutes=1)
            
            logger.info(f"测试时间窗口: {start_time} ~ {end_time}")
            
            # 测试1: 新方式 - 直接使用价格字段
            logger.info("🚀 测试新方式 (使用价格字段)...")
            new_start = time.time()
            
            new_query = """
            SELECT timestamp, bid_price_1, ask_price_1, created_at
            FROM bitda_depth
            WHERE symbol = 'ETHUSDT'
            AND created_at >= %s
            AND created_at < %s
            AND bid_price_1 IS NOT NULL
            AND ask_price_1 IS NOT NULL
            ORDER BY timestamp
            LIMIT 100
            """
            
            cursor.execute(new_query, (start_time, end_time))
            new_results = cursor.fetchall()
            new_end = time.time()
            new_time = new_end - new_start
            
            logger.info(f"   结果数量: {len(new_results)}")
            logger.info(f"   查询耗时: {new_time:.4f} 秒")
            
            # 测试2: 旧方式 - 使用JSON解析（如果有JSON字段）
            logger.info("🐌 测试旧方式 (JSON解析)...")
            old_start = time.time()
            
            old_query = """
            SELECT timestamp, asks, bids, created_at
            FROM bitda_depth
            WHERE symbol = 'ETHUSDT'
            AND created_at >= %s
            AND created_at < %s
            AND asks IS NOT NULL
            AND bids IS NOT NULL
            ORDER BY timestamp
            LIMIT 100
            """
            
            cursor.execute(old_query, (start_time, end_time))
            old_results = cursor.fetchall()
            old_end = time.time()
            old_time = old_end - old_start
            
            logger.info(f"   结果数量: {len(old_results)}")
            logger.info(f"   查询耗时: {old_time:.4f} 秒")
            
            # 性能对比
            if old_time > 0:
                speedup = old_time / new_time
                logger.info(f"🎯 性能提升: {speedup:.1f}倍 ({old_time:.4f}s → {new_time:.4f}s)")
            
            cursor.close()
            connection.close()
            
            return new_time, old_time, len(new_results)
            
        except Exception as e:
            logger.error(f"性能测试失败: {e}")
            return None, None, 0
    
    def test_price_matching_performance(self):
        """测试价格匹配性能"""
        try:
            connection = mysql.connector.connect(**self.source_config)
            cursor = connection.cursor()
            
            logger.info("🎯 测试价格匹配性能...")
            
            # 获取一些Bitda价格进行匹配测试
            cursor.execute("""
                SELECT bid_price_1, ask_price_1, timestamp
                FROM bitda_depth
                WHERE symbol = 'ETHUSDT'
                AND bid_price_1 IS NOT NULL
                AND ask_price_1 IS NOT NULL
                AND created_at >= NOW() - INTERVAL 10 MINUTE
                ORDER BY created_at DESC
                LIMIT 10
            """)
            
            bitda_prices = cursor.fetchall()
            
            if not bitda_prices:
                logger.warning("无Bitda价格数据进行测试")
                return
            
            logger.info(f"测试 {len(bitda_prices)} 个价格匹配...")
            
            total_matches = 0
            total_time = 0
            
            for bid_price, ask_price, timestamp in bitda_prices:
                start_time = time.time()
                
                # 价格匹配查询
                match_query = """
                SELECT bid_price, ask_price, event_time
                FROM binance_bookticker
                WHERE symbol = 'ETHUSDT'
                AND bid_price = %s
                AND ask_price = %s
                AND event_time < %s
                ORDER BY event_time ASC
                LIMIT 1
                """
                
                cursor.execute(match_query, (bid_price, ask_price, timestamp))
                match = cursor.fetchone()
                
                end_time = time.time()
                query_time = end_time - start_time
                total_time += query_time
                
                if match:
                    total_matches += 1
                    binance_bid, binance_ask, binance_timestamp = match
                    latency = timestamp - binance_timestamp
                    logger.info(f"   ✅ 匹配: {bid_price}/{ask_price}, 延时: {latency}ms, 查询: {query_time:.4f}s")
                else:
                    logger.info(f"   ❌ 无匹配: {bid_price}/{ask_price}, 查询: {query_time:.4f}s")
            
            avg_time = total_time / len(bitda_prices)
            match_rate = total_matches / len(bitda_prices) * 100
            
            logger.info(f"📊 匹配测试结果:")
            logger.info(f"   总匹配数: {total_matches}/{len(bitda_prices)}")
            logger.info(f"   匹配率: {match_rate:.1f}%")
            logger.info(f"   平均查询时间: {avg_time:.4f}秒")
            logger.info(f"   总耗时: {total_time:.4f}秒")
            
            cursor.close()
            connection.close()
            
            return total_matches, match_rate, avg_time
            
        except Exception as e:
            logger.error(f"价格匹配测试失败: {e}")
            return 0, 0, 0
    
    def test_latency_database_connection(self):
        """测试延时数据库连接"""
        try:
            connection = mysql.connector.connect(**self.target_config)
            cursor = connection.cursor()
            
            logger.info("🔗 测试延时数据库连接...")
            
            # 检查表是否存在
            cursor.execute("SHOW TABLES LIKE 'ethusdt_latency_matches'")
            table_exists = cursor.fetchone() is not None
            
            if table_exists:
                # 检查最近数据
                cursor.execute("""
                    SELECT COUNT(*) as total,
                           AVG(latency_ms) as avg_latency,
                           MAX(created_at) as latest_time
                    FROM ethusdt_latency_matches
                    WHERE created_at >= NOW() - INTERVAL 1 HOUR
                """)
                
                result = cursor.fetchone()
                total, avg_latency, latest_time = result
                
                logger.info(f"📊 延时数据库状态:")
                logger.info(f"   最近1小时记录数: {total}")
                logger.info(f"   平均延时: {avg_latency:.2f}ms" if avg_latency else "   平均延时: 无数据")
                logger.info(f"   最新记录时间: {latest_time}")
                
                # 检查实时状态表
                cursor.execute("SHOW TABLES LIKE 'ethusdt_realtime_status'")
                status_table_exists = cursor.fetchone() is not None
                
                if status_table_exists:
                    cursor.execute("SELECT * FROM ethusdt_realtime_status WHERE id = 1")
                    status = cursor.fetchone()
                    if status:
                        logger.info("✅ 实时状态表正常")
                    else:
                        logger.warning("⚠️  实时状态表无数据")
                else:
                    logger.warning("⚠️  实时状态表不存在")
                
            else:
                logger.error("❌ ethusdt_latency_matches表不存在")
                return False
            
            cursor.close()
            connection.close()
            
            return True
            
        except Exception as e:
            logger.error(f"延时数据库连接测试失败: {e}")
            return False
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        logger.info("🧪 开始延时处理器综合性能测试")
        logger.info("=" * 60)
        
        results = {}
        
        # 1. 检查数据可用性
        logger.info("1️⃣ 检查数据可用性...")
        results['data_available'] = self.check_data_availability()
        
        # 2. 测试查询性能对比
        logger.info("\n2️⃣ 测试查询性能对比...")
        new_time, old_time, result_count = self.test_old_vs_new_query_performance()
        results['query_performance'] = {
            'new_time': new_time,
            'old_time': old_time,
            'result_count': result_count,
            'speedup': old_time / new_time if old_time and new_time else None
        }
        
        # 3. 测试价格匹配性能
        logger.info("\n3️⃣ 测试价格匹配性能...")
        matches, match_rate, avg_time = self.test_price_matching_performance()
        results['price_matching'] = {
            'matches': matches,
            'match_rate': match_rate,
            'avg_time': avg_time
        }
        
        # 4. 测试延时数据库连接
        logger.info("\n4️⃣ 测试延时数据库连接...")
        results['database_connection'] = self.test_latency_database_connection()
        
        # 总结
        logger.info("\n" + "=" * 60)
        logger.info("📋 测试结果总结:")
        
        all_passed = True
        
        if results['data_available']:
            logger.info("   ✅ 数据可用性: 通过")
        else:
            logger.error("   ❌ 数据可用性: 失败")
            all_passed = False
        
        if results['query_performance']['speedup']:
            speedup = results['query_performance']['speedup']
            logger.info(f"   ✅ 查询性能: 提升 {speedup:.1f}倍")
        else:
            logger.error("   ❌ 查询性能: 测试失败")
            all_passed = False
        
        if results['price_matching']['match_rate'] > 0:
            rate = results['price_matching']['match_rate']
            avg_time = results['price_matching']['avg_time']
            logger.info(f"   ✅ 价格匹配: {rate:.1f}% 匹配率, {avg_time:.4f}s 平均耗时")
        else:
            logger.warning("   ⚠️  价格匹配: 无匹配数据")
        
        if results['database_connection']:
            logger.info("   ✅ 数据库连接: 正常")
        else:
            logger.error("   ❌ 数据库连接: 失败")
            all_passed = False
        
        if all_passed:
            logger.info("\n🎉 所有测试通过！延时处理器准备就绪")
            logger.info("📋 建议:")
            logger.info("   1. 启动优化版延时处理器")
            logger.info("   2. 监控处理性能和匹配率")
            logger.info("   3. 验证Grafana数据显示")
        else:
            logger.error("\n❌ 部分测试失败，需要修复问题")
        
        return all_passed

def main():
    """主函数"""
    print("🧪 延时处理器性能测试工具")
    print("=" * 50)
    print("功能:")
    print("  - 验证新存储逻辑的数据可用性")
    print("  - 对比旧查询方式 vs 新查询方式性能")
    print("  - 测试价格匹配算法性能")
    print("  - 验证延时数据库连接")
    print()
    
    tester = LatencyProcessorTester()
    success = tester.run_comprehensive_test()
    
    if success:
        print("\n✅ 测试完成！系统准备就绪")
    else:
        print("\n❌ 测试发现问题，请检查日志")

if __name__ == "__main__":
    main()
