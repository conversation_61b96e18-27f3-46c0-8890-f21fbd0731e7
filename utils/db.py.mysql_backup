"""
数据库连接管理模块
"""
import mysql.connector
from mysql.connector import pooling
from typing import Optional
from utils.config import DB_CONFIG
from utils.logging import setup_logger

logger = setup_logger(__name__)

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.connection_pool: Optional[pooling.MySQLConnectionPool] = None
        self._create_connection_pool()
    
    def _create_connection_pool(self):
        """创建数据库连接池"""
        try:
            self.connection_pool = pooling.MySQLConnectionPool(
                pool_name="crypto_collector_pool",
                pool_size=10,
                **DB_CONFIG
            )
            logger.info("数据库连接池创建成功")
        except Exception as e:
            logger.error(f"创建数据库连接池失败: {e}")
            raise
    
    def get_connection(self):
        """获取数据库连接"""
        if not self.connection_pool:
            raise Exception("数据库连接池未初始化")
        return self.connection_pool.get_connection()
    
    def execute_query(self, query: str, params: tuple = None, fetch: bool = False):
        """执行SQL查询"""
        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute(query, params)
            
            if fetch:
                return cursor.fetchall()
            else:
                conn.commit()
                return cursor.rowcount
                
        except Exception as e:
            logger.error(f"执行SQL查询失败: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    def execute_many(self, query: str, params_list: list):
        """批量执行SQL"""
        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.executemany(query, params_list)
            conn.commit()
            return cursor.rowcount
        except Exception as e:
            logger.error(f"批量执行SQL失败: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

# 全局数据库管理器实例
db_manager = DatabaseManager()
