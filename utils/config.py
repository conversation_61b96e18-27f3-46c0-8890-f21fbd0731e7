"""
配置管理模块 - ClickHouse版本
"""
import os
from typing import Dict, List

# 加载.env文件 - 简化版本
def load_env():
    try:
        with open('.env', 'r') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
    except FileNotFoundError:
        pass

load_env()

# ClickHouse数据库配置
CLICKHOUSE_CONFIG = {
    'host': os.getenv('CH_HOST', 'localhost'),
    'user': os.getenv('CH_USER', 'default'),
    'password': os.getenv('CH_PASSWORD', 'Linuxtest'),
    'database': os.getenv('CH_DATABASE', 'crypto'),
    'port': int(os.getenv('CH_PORT', '9000')),
}

# HTTP接口配置
CLICKHOUSE_HTTP_URL = f"http://{CLICKHOUSE_CONFIG['user']}:{CLICKHOUSE_CONFIG['password']}@{CLICKHOUSE_CONFIG['host']}:8123/"

# 保留原MySQL配置作为备份
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', 'Linuxtest'),
    'database': os.getenv('DB_NAME', 'depth_db'),
    'port': int(os.getenv('DB_PORT', '3306')),
    'charset': 'utf8mb4',
    'use_unicode': True,
}

# 交易对配置
SYMBOLS = ['BTCUSDT', 'ETHUSDT']

# WebSocket配置
BITDA_WS_URL = 'wss://ws.bitda.com/wsf'
BINANCE_WS_URL = 'wss://stream.binance.com:9443/ws'

# Bitda WebSocket 配置
BITDA_WS_CONFIG = {
    'url': 'wss://ws.bitda.com/wsf',
    'ping_interval': 30,
    'reconnect_interval': 5,
    'max_reconnect_attempts': 10
}

# Binance WebSocket 配置
BINANCE_WS_CONFIG = {
    'base_url': 'wss://stream.binance.com:9443/ws/',
    'ping_interval': 30,
    'reconnect_interval': 5,
    'max_reconnect_attempts': 10
}

# 数据处理配置
QUEUE_SIZE = 10000
BATCH_SIZE = 100

# 数据保留配置
DATA_RETENTION_DAYS = 3
DISK_SPACE_WARNING_GB = 50

# 日志配置
LOG_LEVEL = 'INFO'
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

LOG_CONFIG = {
    'level': LOG_LEVEL,
    'format': LOG_FORMAT,
    'file': 'crypto_collector.log',
    'max_bytes': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}
