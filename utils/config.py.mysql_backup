"""
配置管理模块
"""
import os
from typing import Dict, List

# 加载.env文件
from dotenv import load_dotenv
load_dotenv()

# 数据库配置
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),     # 使用.env文件中的配置
    'user': os.getenv('DB_USER', 'root'),          # 使用.env文件中的配置
    'password': os.getenv('DB_PASSWORD', 'Linuxtest'),
    'database': os.getenv('DB_NAME', 'depth_db'),
    'port': int(os.getenv('DB_PORT', '3306')),
    'charset': 'utf8mb4',
    'use_unicode': True,
    'get_warnings': True,
    'autocommit': True,
    'raise_on_warnings': False,
}

# 交易对配置
SYMBOLS = ['BTCUSDT', 'ETHUSDT']

# Bitda WebSocket 配置
BITDA_WS_CONFIG = {
    'url': 'wss://ws.bitda.com/wsf',  # 正确的Bitda WebSocket地址
    'ping_interval': 30,
    'reconnect_interval': 5,
    'max_reconnect_attempts': 10
}

# Binance WebSocket 配置
BINANCE_WS_CONFIG = {
    'base_url': 'wss://fstream.binance.com/ws/',
    'ping_interval': 30,
    'reconnect_interval': 5,
    'max_reconnect_attempts': 10
}

# 数据保留配置
DATA_RETENTION_DAYS = 3

# 系统监控配置
DISK_SPACE_WARNING_GB = 50
QUEUE_SIZE = 10000
BATCH_SIZE = 10  # 降低批量大小，让Bitda深度数据更快保存

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': 'crypto_collector.log',
    'max_bytes': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}
