"""
日志管理模块
"""
import logging
import logging.handlers
from utils.config import LOG_CONFIG

def setup_logger(name: str) -> logging.Logger:
    """设置日志记录器"""
    logger = logging.getLogger(name)
    
    if logger.handlers:
        return logger
    
    logger.setLevel(getattr(logging, LOG_CONFIG['level']))
    
    # 创建格式化器
    formatter = logging.Formatter(LOG_CONFIG['format'])
    
    # 创建文件处理器（轮转日志）
    file_handler = logging.handlers.RotatingFileHandler(
        LOG_CONFIG['file'],
        maxBytes=LOG_CONFIG['max_bytes'],
        backupCount=LOG_CONFIG['backup_count']
    )
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    return logger
