"""
数据库连接管理模块 - ClickHouse版本
"""
import requests
import json
from typing import Optional, List, Any
from utils.config import CLICKHOUSE_HTTP_URL
from utils.logging import setup_logger

logger = setup_logger(__name__)

class ClickHouseManager:
    """ClickHouse数据库管理器"""
    
    def __init__(self):
        self.http_url = CLICKHOUSE_HTTP_URL
        logger.info("ClickHouse管理器初始化成功")
    
    def execute_query(self, query: str, params: tuple = None, fetch: bool = False):
        """执行ClickHouse查询"""
        try:
            # 处理参数化查询
            if params:
                # 简单的参数替换（生产环境建议使用更安全的方法）
                for param in params:
                    if isinstance(param, str):
                        query = query.replace('%s', f"'{param}'", 1)
                    else:
                        query = query.replace('%s', str(param), 1)
            
            response = requests.post(self.http_url, data=query, timeout=30)
            
            if response.status_code == 200:
                if fetch:
                    # 返回查询结果
                    result_text = response.text.strip()
                    if result_text:
                        lines = result_text.split('\n')
                        return [line.split('\t') for line in lines]
                    return []
                else:
                    # 返回影响行数（对于INSERT/UPDATE/DELETE）
                    return 1
            else:
                logger.error(f"ClickHouse查询失败: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"执行ClickHouse查询失败: {e}")
            return None
    
    def execute_many(self, query: str, params_list: list):
        """批量执行SQL"""
        try:
            # 构建批量插入语句
            if 'INSERT INTO' in query.upper():
                # 提取表名和字段
                parts = query.split('VALUES')
                if len(parts) == 2:
                    insert_part = parts[0].strip()
                    
                    # 构建VALUES部分
                    values_list = []
                    for params in params_list:
                        value_str = "("
                        for i, param in enumerate(params):
                            if i > 0:
                                value_str += ", "
                            if isinstance(param, str):
                                value_str += f"'{param.replace(chr(39), chr(39)+chr(39))}'"
                            elif param is None:
                                value_str += "NULL"
                            else:
                                value_str += str(param)
                        value_str += ")"
                        values_list.append(value_str)
                    
                    full_query = f"{insert_part} VALUES {', '.join(values_list)}"
                    return self.execute_query(full_query)
            
            return None
            
        except Exception as e:
            logger.error(f"批量执行ClickHouse查询失败: {e}")
            return None

# 全局ClickHouse管理器实例
ch_manager = ClickHouseManager()

# 为了兼容性，保留db_manager别名
db_manager = ch_manager
